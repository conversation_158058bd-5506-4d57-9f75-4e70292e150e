#!/usr/bin/env python3
"""
完整系统功能验证脚本
测试登录、认证、数据加载等所有功能
"""

import requests
import json
import time
from datetime import datetime

def test_login():
    """测试登录功能"""
    print("🔐 测试登录功能...")
    
    try:
        response = requests.post(
            'http://localhost:8001/api/v1/auth/login',
            json={'username': 'admin', 'password': 'admin123'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 登录成功!")
            print(f"   用户: {data['user']['username']}")
            print(f"   邮箱: {data['user']['email']}")
            print(f"   管理员: {data['user']['is_superuser']}")
            print(f"   Token: {data['access_token'][:50]}...")
            return data['access_token']
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_user_info(token):
    """测试获取用户信息"""
    print("\n👤 测试获取用户信息...")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(
            'http://localhost:8001/api/v1/auth/me',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取用户信息成功!")
            print(f"   ID: {data['id']}")
            print(f"   用户名: {data['username']}")
            print(f"   邮箱: {data['email']}")
            print(f"   状态: {'激活' if data['is_active'] else '未激活'}")
            return True
        else:
            print(f"❌ 获取用户信息失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 获取用户信息异常: {e}")
        return False

def test_market_data(token):
    """测试市场数据API"""
    print("\n📊 测试市场数据API...")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(
            'http://localhost:8001/api/v1/market/overview',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取市场数据成功!")
            market_data = data['data']
            print(f"   市场状态: {market_data['market_status']}")
            print(f"   总股票数: {market_data['total_stocks']}")
            print(f"   上涨股票: {market_data['rising_stocks']}")
            print(f"   下跌股票: {market_data['falling_stocks']}")
            print(f"   总成交量: {market_data['total_volume']:,}")
            return True
        else:
            print(f"❌ 获取市场数据失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 获取市场数据异常: {e}")
        return False

def test_trading_account(token):
    """测试交易账户API"""
    print("\n💰 测试交易账户API...")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(
            'http://localhost:8001/api/v1/trading/account',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取交易账户成功!")
            account_data = data['data']
            balance = account_data['balance']
            print(f"   账户ID: {account_data['account_id']}")
            print(f"   总资产: ¥{balance['total_equity']:,.2f}")
            print(f"   现金: ¥{balance['cash']:,.2f}")
            print(f"   市值: ¥{balance['market_value']:,.2f}")
            print(f"   可用现金: ¥{balance['available_cash']:,.2f}")
            return True
        else:
            print(f"❌ 获取交易账户失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 获取交易账户异常: {e}")
        return False

def test_unauthorized_access():
    """测试未授权访问"""
    print("\n🔒 测试未授权访问...")
    
    try:
        # 不带token访问需要认证的API
        response = requests.get(
            'http://localhost:8001/api/v1/market/overview',
            timeout=10
        )
        
        if response.status_code == 401:
            print("✅ 未授权访问正确返回401")
            return True
        else:
            print(f"❌ 未授权访问应该返回401，实际返回: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试未授权访问异常: {e}")
        return False

def test_invalid_token():
    """测试无效token"""
    print("\n🔑 测试无效token...")
    
    try:
        headers = {'Authorization': 'Bearer invalid_token_12345'}
        response = requests.get(
            'http://localhost:8001/api/v1/auth/me',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 401:
            print("✅ 无效token正确返回401")
            return True
        else:
            print(f"❌ 无效token应该返回401，实际返回: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试无效token异常: {e}")
        return False

def test_demo_user():
    """测试demo用户登录"""
    print("\n👥 测试demo用户登录...")
    
    try:
        response = requests.post(
            'http://localhost:8001/api/v1/auth/login',
            json={'username': 'demo', 'password': 'demo123'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ demo用户登录成功!")
            print(f"   用户: {data['user']['username']}")
            print(f"   管理员权限: {data['user']['is_superuser']}")
            return True
        else:
            print(f"❌ demo用户登录失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ demo用户登录异常: {e}")
        return False

def test_wrong_password():
    """测试错误密码"""
    print("\n🚫 测试错误密码...")
    
    try:
        response = requests.post(
            'http://localhost:8001/api/v1/auth/login',
            json={'username': 'admin', 'password': 'wrong_password'},
            timeout=10
        )
        
        if response.status_code == 401:
            print("✅ 错误密码正确返回401")
            return True
        else:
            print(f"❌ 错误密码应该返回401，实际返回: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试错误密码异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始完整系统功能验证...")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试登录功能
    token = test_login()
    test_results.append(("登录功能", token is not None))
    
    if token:
        # 2. 测试获取用户信息
        result = test_user_info(token)
        test_results.append(("用户信息", result))
        
        # 3. 测试市场数据
        result = test_market_data(token)
        test_results.append(("市场数据", result))
        
        # 4. 测试交易账户
        result = test_trading_account(token)
        test_results.append(("交易账户", result))
    
    # 5. 测试安全性
    result = test_unauthorized_access()
    test_results.append(("未授权访问", result))
    
    result = test_invalid_token()
    test_results.append(("无效token", result))
    
    # 6. 测试demo用户
    result = test_demo_user()
    test_results.append(("demo用户", result))
    
    # 7. 测试错误密码
    result = test_wrong_password()
    test_results.append(("错误密码", result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:<15} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常！")
        print("\n💡 可用账户:")
        print("   - admin / admin123 (管理员)")
        print("   - demo / demo123 (演示用户)")
        print("\n📡 API端点:")
        print("   - 认证服务: http://localhost:8001")
        print("   - 前端应用: http://localhost:5173")
        return True
    else:
        print(f"⚠️ 有 {total - passed} 个测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 系统验证完成！")
    else:
        print("\n❌ 系统验证失败！")
