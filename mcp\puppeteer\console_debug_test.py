#!/usr/bin/env python3
"""
控制台调试测试 - 监听浏览器控制台输出
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def console_debug_test():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 监听控制台消息
        console_messages = []
        
        def handle_console(msg):
            message = f"[{msg.type.upper()}] {msg.text}"
            console_messages.append(message)
            logger.info(f"Console: {message}")
        
        def handle_page_error(error):
            error_message = f"Page Error: {error}"
            console_messages.append(error_message)
            logger.error(error_message)
        
        page.on('console', handle_console)
        page.on('pageerror', handle_page_error)
        
        try:
            logger.info("访问交易中心页面...")
            
            await page.goto('http://localhost:5173/trading/center', wait_until='networkidle', timeout=60000)
            
            # 等待更长时间，观察控制台输出
            await page.wait_for_timeout(10000)
            
            # 检查页面内容
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 检查DOM结构
            dom_info = await page.evaluate("""
                () => {
                    const info = {
                        hasApp: !!document.querySelector('#app'),
                        appContent: document.querySelector('#app')?.innerHTML?.length || 0,
                        hasTradingCenter: !!document.querySelector('.trading-center'),
                        hasPageTitle: !!document.querySelector('.page-title'),
                        hasNavRight: !!document.querySelector('.nav-right'),
                        bodyContent: document.body.innerHTML.length,
                        routerView: !!document.querySelector('router-view'),
                        mainElement: !!document.querySelector('main')
                    };
                    
                    // 查找所有可能的容器元素
                    const containers = [];
                    document.querySelectorAll('div').forEach(div => {
                        if (div.className) {
                            containers.push(div.className);
                        }
                    });
                    info.containerClasses = containers.slice(0, 10); // 只取前10个
                    
                    return info;
                }
            """)
            
            logger.info("DOM结构信息:")
            for key, value in dom_info.items():
                logger.info(f"  {key}: {value}")
            
            # 检查Vue实例
            vue_info = await page.evaluate("""
                () => {
                    const info = {
                        hasVue: typeof window.Vue !== 'undefined',
                        hasVueApp: !!window.__VUE_APP__,
                        vueDevtools: !!window.__VUE_DEVTOOLS_GLOBAL_HOOK__
                    };
                    
                    // 检查Vue应用实例
                    const app = document.querySelector('#app');
                    if (app && app.__vue_app__) {
                        info.vueAppMounted = true;
                    }
                    
                    return info;
                }
            """)
            
            logger.info("Vue信息:")
            for key, value in vue_info.items():
                logger.info(f"  {key}: {value}")
            
            # 截图
            await page.screenshot(path='screenshots/console_debug.png', full_page=True)
            logger.info("📸 完整页面截图已保存")
            
            # 输出所有控制台消息
            logger.info("\n所有控制台消息:")
            for i, msg in enumerate(console_messages, 1):
                logger.info(f"  {i}. {msg}")
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            await page.screenshot(path='screenshots/console_debug_error.png')
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(console_debug_test())
