#!/usr/bin/env python3
"""
API端点测试脚本
直接测试所有API端点是否正常工作
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_api_endpoint(session, name, url):
    """测试单个API端点"""
    try:
        print(f"🔗 测试 {name}: {url}")
        
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
            status = response.status
            content = await response.text()
            
            if status == 200:
                try:
                    data = json.loads(content)
                    print(f"   ✅ {name} - 200 OK")
                    
                    # 显示数据摘要
                    if 'data' in data:
                        if 'indices' in data['data']:
                            print(f"      📊 指数数据: {len(data['data']['indices'])}个")
                        if 'stocks' in data['data']:
                            print(f"      📈 股票数据: {len(data['data']['stocks'])}个")
                        if 'sectors' in data['data']:
                            print(f"      🏢 板块数据: {len(data['data']['sectors'])}个")
                        if 'rankings' in data['data']:
                            print(f"      🏆 排行数据: {len(data['data']['rankings'])}个")
                    
                    return True
                except json.JSONDecodeError:
                    print(f"   ❌ {name} - 响应不是有效JSON")
                    return False
            else:
                print(f"   ❌ {name} - {status}")
                print(f"      错误内容: {content[:200]}...")
                return False
                
    except Exception as e:
        print(f"   ❌ {name} - 请求失败: {e}")
        return False

async def test_all_endpoints():
    """测试所有API端点"""
    base_url = "http://localhost:8000"
    
    endpoints = [
        ("市场概览", f"{base_url}/api/v1/market/overview"),
        ("股票列表", f"{base_url}/api/v1/market/stocks?pageSize=10"),
        ("板块数据", f"{base_url}/api/v1/market/sectors"),
        ("涨跌幅排行榜", f"{base_url}/api/v1/market/rankings?type=change_percent&limit=10"),
        ("成交量排行榜", f"{base_url}/api/v1/market/rankings?type=turnover&limit=10"),
        ("用户信息", f"{base_url}/api/v1/user/profile"),
        ("交易账户", f"{base_url}/api/v1/trading/account"),
    ]
    
    print("🧪 开始测试所有API端点...")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for name, url in endpoints:
            result = await test_api_endpoint(session, name, url)
            results.append((name, result))
            await asyncio.sleep(1)  # 避免请求过快
        
        print("\n📊 测试结果总结:")
        print("=" * 30)
        
        success_count = sum(1 for _, result in results if result)
        total_count = len(results)
        
        for name, result in results:
            status = "✅" if result else "❌"
            print(f"  {status} {name}")
        
        print(f"\n成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("🎉 所有API端点测试通过！")
        else:
            print("⚠️ 部分API端点存在问题，需要检查")

if __name__ == "__main__":
    asyncio.run(test_all_endpoints())
