"""
交易终端API
提供完整的交易界面所需的所有功能
"""
from datetime import datetime, timedelta
from typing import List, Optional, Dict
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, WebSocket
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.db.models.user import User
from app.services.mock_market_service import mock_market_service
from app.schemas.trading import OrderRequest, OrderType, OrderStatus

router = APIRouter()

# 模拟数据存储
orders_db = {}
positions_db = {}
trades_db = {}


@router.get("/terminal/overview")
async def get_terminal_overview(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取交易终端概览数据
    
    包括账户信息、持仓概览、今日交易统计等
    """
    # 获取账户信息
    account = {
        "total_assets": 1000000.0,
        "available_cash": 500000.0,
        "market_value": 450000.0,
        "frozen_cash": 50000.0,
        "total_profit": 25000.0,
        "total_profit_rate": 2.5,
        "day_profit": 3500.0,
        "day_profit_rate": 0.35
    }
    
    # 获取持仓概览
    positions_summary = {
        "total_positions": 5,
        "profitable_count": 3,
        "loss_count": 2,
        "total_profit": 15000.0,
        "total_profit_rate": 3.33
    }
    
    # 今日交易统计
    today_stats = {
        "order_count": 12,
        "filled_count": 10,
        "cancelled_count": 2,
        "buy_amount": 150000.0,
        "sell_amount": 120000.0,
        "commission": 150.0
    }
    
    # 风险指标
    risk_metrics = {
        "position_ratio": 0.45,  # 持仓比例
        "concentration": 0.25,   # 集中度
        "leverage": 1.0,         # 杠杆率
        "risk_level": "medium"   # 风险等级
    }
    
    return {
        "success": True,
        "data": {
            "account": account,
            "positions_summary": positions_summary,
            "today_stats": today_stats,
            "risk_metrics": risk_metrics,
            "last_update": datetime.now().isoformat()
        }
    }


@router.post("/terminal/quick-order")
async def create_quick_order(
    symbol: str,
    side: str,  # BUY/SELL
    quantity: int,
    order_type: str = "MARKET",
    price: Optional[float] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    快速下单
    
    - **symbol**: 股票代码
    - **side**: 买卖方向(BUY/SELL)
    - **quantity**: 数量（股）
    - **order_type**: 订单类型(MARKET/LIMIT)
    - **price**: 限价（限价单必填）
    """
    # 获取实时行情
    quote = await mock_market_service.get_realtime_quote(symbol)
    
    # 验证订单
    if order_type == "LIMIT" and not price:
        raise HTTPException(status_code=400, detail="限价单必须指定价格")
    
    if order_type == "MARKET":
        price = quote["price"]
    
    # 创建订单
    order_id = f"QO{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
    
    order = {
        "order_id": order_id,
        "user_id": current_user.id,
        "symbol": symbol,
        "name": quote["name"],
        "side": side,
        "order_type": order_type,
        "price": price,
        "quantity": quantity,
        "filled_quantity": 0,
        "status": "PENDING",
        "create_time": datetime.now(),
        "update_time": datetime.now()
    }
    
    # 模拟订单执行
    if order_type == "MARKET":
        # 市价单立即成交
        order["filled_quantity"] = quantity
        order["status"] = "FILLED"
        order["filled_price"] = price
        order["commission"] = round(price * quantity * 0.0003, 2)  # 万三手续费
        
        # 创建成交记录
        trade_id = f"T{order_id}"
        trades_db[trade_id] = {
            "trade_id": trade_id,
            "order_id": order_id,
            "symbol": symbol,
            "price": price,
            "quantity": quantity,
            "side": side,
            "commission": order["commission"],
            "trade_time": datetime.now()
        }
    
    orders_db[order_id] = order
    
    return {
        "success": True,
        "message": "订单已提交",
        "data": {
            "order_id": order_id,
            "symbol": symbol,
            "side": side,
            "price": price,
            "quantity": quantity,
            "status": order["status"]
        }
    }


@router.get("/terminal/order-book/{symbol}")
async def get_order_book(symbol: str):
    """
    获取订单簿（买卖盘）
    
    - **symbol**: 股票代码
    """
    depth = await mock_market_service.get_market_depth(symbol)
    quote = await mock_market_service.get_realtime_quote(symbol)
    
    # 格式化订单簿数据
    order_book = {
        "symbol": symbol,
        "name": quote["name"],
        "current_price": quote["price"],
        "timestamp": datetime.now().isoformat(),
        "bids": depth["bids"][:5],  # 买五档
        "asks": depth["asks"][:5],  # 卖五档
        "spread": round(depth["asks"][0]["price"] - depth["bids"][0]["price"], 2),
        "spread_rate": round((depth["asks"][0]["price"] - depth["bids"][0]["price"]) / quote["price"] * 100, 4)
    }
    
    return {
        "success": True,
        "data": order_book
    }


@router.get("/terminal/positions/detail")
async def get_positions_detail(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取持仓明细
    
    返回每个持仓的详细信息，包括盈亏、可用数量等
    """
    # 模拟持仓数据
    user_positions = []
    
    sample_positions = [
        {"symbol": "000001", "quantity": 1000, "avg_cost": 12.5},
        {"symbol": "600036", "quantity": 500, "avg_cost": 37.8},
        {"symbol": "000858", "quantity": 300, "avg_cost": 165.2},
    ]
    
    for pos in sample_positions:
        # 获取实时行情
        quote = await mock_market_service.get_realtime_quote(pos["symbol"])
        
        position = {
            "symbol": pos["symbol"],
            "name": quote["name"],
            "quantity": pos["quantity"],
            "available_quantity": pos["quantity"],  # 可卖数量
            "avg_cost": pos["avg_cost"],
            "current_price": quote["price"],
            "market_value": quote["price"] * pos["quantity"],
            "cost_value": pos["avg_cost"] * pos["quantity"],
            "profit": (quote["price"] - pos["avg_cost"]) * pos["quantity"],
            "profit_rate": (quote["price"] - pos["avg_cost"]) / pos["avg_cost"] * 100,
            "day_profit": quote["change"] * pos["quantity"],
            "day_profit_rate": quote["change_percent"],
            "position_ratio": round(quote["price"] * pos["quantity"] / 1000000 * 100, 2)  # 持仓占比
        }
        
        user_positions.append(position)
    
    # 计算汇总数据
    summary = {
        "total_positions": len(user_positions),
        "total_market_value": sum(p["market_value"] for p in user_positions),
        "total_profit": sum(p["profit"] for p in user_positions),
        "total_profit_rate": sum(p["profit"] for p in user_positions) / sum(p["cost_value"] for p in user_positions) * 100 if user_positions else 0,
        "day_profit": sum(p["day_profit"] for p in user_positions),
    }
    
    return {
        "success": True,
        "data": {
            "positions": user_positions,
            "summary": summary
        }
    }


@router.get("/terminal/orders/today")
async def get_today_orders(
    status: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取今日订单
    
    - **status**: 订单状态筛选(PENDING/FILLED/CANCELLED)
    """
    # 筛选用户今日订单
    today = datetime.now().date()
    user_orders = []
    
    for order in orders_db.values():
        if (order["user_id"] == current_user.id and 
            order["create_time"].date() == today):
            
            if status and order["status"] != status:
                continue
                
            user_orders.append({
                "order_id": order["order_id"],
                "symbol": order["symbol"],
                "name": order.get("name", ""),
                "side": order["side"],
                "order_type": order["order_type"],
                "price": order["price"],
                "quantity": order["quantity"],
                "filled_quantity": order["filled_quantity"],
                "status": order["status"],
                "create_time": order["create_time"].isoformat(),
                "update_time": order["update_time"].isoformat(),
                "filled_price": order.get("filled_price"),
                "commission": order.get("commission", 0)
            })
    
    # 按时间倒序排列
    user_orders.sort(key=lambda x: x["create_time"], reverse=True)
    
    return {
        "success": True,
        "data": user_orders,
        "count": len(user_orders)
    }


@router.get("/terminal/trades/today")
async def get_today_trades(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取今日成交
    """
    today = datetime.now().date()
    user_trades = []
    
    for trade in trades_db.values():
        order = orders_db.get(trade["order_id"])
        if order and order["user_id"] == current_user.id:
            if trade["trade_time"].date() == today:
                user_trades.append({
                    "trade_id": trade["trade_id"],
                    "order_id": trade["order_id"],
                    "symbol": trade["symbol"],
                    "price": trade["price"],
                    "quantity": trade["quantity"],
                    "amount": trade["price"] * trade["quantity"],
                    "side": trade["side"],
                    "commission": trade["commission"],
                    "trade_time": trade["trade_time"].isoformat()
                })
    
    # 按时间倒序排列
    user_trades.sort(key=lambda x: x["trade_time"], reverse=True)
    
    # 计算统计
    stats = {
        "total_trades": len(user_trades),
        "buy_amount": sum(t["amount"] for t in user_trades if t["side"] == "BUY"),
        "sell_amount": sum(t["amount"] for t in user_trades if t["side"] == "SELL"),
        "total_commission": sum(t["commission"] for t in user_trades)
    }
    
    return {
        "success": True,
        "data": user_trades,
        "stats": stats,
        "count": len(user_trades)
    }


@router.post("/terminal/orders/{order_id}/cancel")
async def cancel_order(
    order_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    撤销订单
    
    - **order_id**: 订单ID
    """
    order = orders_db.get(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    if order["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此订单")
    
    if order["status"] != "PENDING":
        raise HTTPException(status_code=400, detail="订单状态不允许撤销")
    
    # 更新订单状态
    order["status"] = "CANCELLED"
    order["update_time"] = datetime.now()
    
    return {
        "success": True,
        "message": "订单已撤销",
        "order_id": order_id
    }


@router.get("/terminal/hot-stocks")
async def get_hot_stocks():
    """
    获取热门股票
    
    返回成交活跃、涨跌幅大的股票
    """
    # 获取所有股票行情
    all_stocks = await mock_market_service.get_stock_list()
    
    hot_stocks = []
    for stock in all_stocks[:10]:  # 模拟取前10只
        quote = await mock_market_service.get_realtime_quote(stock["code"])
        
        hot_stocks.append({
            "symbol": stock["code"],
            "name": stock["name"],
            "price": quote["price"],
            "change_percent": quote["change_percent"],
            "volume": quote["volume"],
            "amount": quote["amount"],
            "reason": "成交活跃" if quote["volume"] > 30000000 else "涨幅居前"
        })
    
    # 按涨跌幅排序
    hot_stocks.sort(key=lambda x: abs(x["change_percent"]), reverse=True)
    
    return {
        "success": True,
        "data": hot_stocks,
        "update_time": datetime.now().isoformat()
    }


@router.get("/terminal/preset-orders")
async def get_preset_orders(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取预设订单模板
    
    返回常用的订单设置模板
    """
    presets = [
        {
            "id": "1",
            "name": "买入100股",
            "side": "BUY",
            "quantity": 100,
            "order_type": "LIMIT"
        },
        {
            "id": "2",
            "name": "卖出全部",
            "side": "SELL",
            "quantity": -1,  # -1表示全部
            "order_type": "MARKET"
        },
        {
            "id": "3",
            "name": "买入1000股",
            "side": "BUY",
            "quantity": 1000,
            "order_type": "LIMIT"
        },
        {
            "id": "4",
            "name": "卖出50%",
            "side": "SELL",
            "quantity": -0.5,  # 负小数表示百分比
            "order_type": "LIMIT"
        }
    ]
    
    return {
        "success": True,
        "data": presets
    }


@router.get("/terminal/keyboard-shortcuts")
async def get_keyboard_shortcuts():
    """
    获取快捷键配置
    
    返回交易终端的快捷键设置
    """
    shortcuts = {
        "F1": "买入",
        "F2": "卖出",
        "F3": "撤单",
        "F4": "查询持仓",
        "F5": "刷新行情",
        "F6": "查询订单",
        "Ctrl+B": "快速买入",
        "Ctrl+S": "快速卖出",
        "Ctrl+C": "撤销最新订单",
        "Ctrl+Q": "查询资金",
        "ESC": "清空输入"
    }
    
    return {
        "success": True,
        "data": shortcuts
    }


@router.websocket("/terminal/ws")
async def terminal_websocket(websocket: WebSocket):
    """
    交易终端WebSocket连接
    
    推送实时订单状态、成交回报、持仓变化等
    """
    await websocket.accept()
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_json()
            
            if data.get("type") == "ping":
                # 心跳响应
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            
            elif data.get("type") == "subscribe":
                # 订阅消息
                await websocket.send_json({
                    "type": "subscribed",
                    "channels": data.get("channels", []),
                    "message": "订阅成功"
                })
                
                # TODO: 实现实时推送逻辑
            
    except Exception as e:
        await websocket.send_json({
            "type": "error",
            "message": str(e)
        })


@router.get("/health")
async def health_check():
    """
    交易终端模块健康检查
    """
    return {
        "status": "healthy",
        "module": "trading_terminal",
        "timestamp": datetime.now().isoformat(),
        "active_orders": len([o for o in orders_db.values() if o["status"] == "PENDING"])
    }