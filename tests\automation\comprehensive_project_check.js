#!/usr/bin/env node

/**
 * 量化投资平台全面项目检查脚本
 * 使用Puppeteer进行端到端测试和功能验证
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class ProjectChecker {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = {
            timestamp: new Date().toISOString(),
            backend: {
                status: 'unknown',
                apis: [],
                errors: []
            },
            frontend: {
                status: 'unknown',
                pages: [],
                components: [],
                errors: []
            },
            integration: {
                status: 'unknown',
                tests: [],
                errors: []
            },
            summary: {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                issues: []
            }
        };
    }

    async init() {
        console.log('🚀 启动项目检查...');
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        
        // 监听控制台消息
        this.page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                this.results.frontend.errors.push({
                    type: 'console_error',
                    message: text,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // 监听页面错误
        this.page.on('pageerror', error => {
            this.results.frontend.errors.push({
                type: 'page_error',
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
        });
    }

    async checkBackendAPIs() {
        console.log('🔍 检查后端API...');
        const apis = [
            { name: 'Health Check', url: 'http://localhost:8000/health', method: 'GET' },
            { name: 'API Docs', url: 'http://localhost:8000/docs', method: 'GET' },
            { name: 'OpenAPI Schema', url: 'http://localhost:8000/openapi.json', method: 'GET' },
            { name: 'Market Data', url: 'http://localhost:8000/api/v1/market/stocks', method: 'GET' },
            { name: 'User Auth', url: 'http://localhost:8000/api/v1/auth/login', method: 'POST' },
            { name: 'Strategy List', url: 'http://localhost:8000/api/v1/strategies', method: 'GET' }
        ];

        for (const api of apis) {
            try {
                const response = await this.page.evaluate(async (apiInfo) => {
                    const res = await fetch(apiInfo.url, { method: apiInfo.method });
                    return {
                        status: res.status,
                        statusText: res.statusText,
                        headers: Object.fromEntries(res.headers.entries())
                    };
                }, api);

                this.results.backend.apis.push({
                    name: api.name,
                    url: api.url,
                    method: api.method,
                    status: response.status,
                    success: response.status < 400,
                    response: response
                });

                this.results.summary.totalTests++;
                if (response.status < 400) {
                    this.results.summary.passedTests++;
                } else {
                    this.results.summary.failedTests++;
                }

            } catch (error) {
                this.results.backend.errors.push({
                    api: api.name,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                this.results.summary.totalTests++;
                this.results.summary.failedTests++;
            }
        }

        this.results.backend.status = this.results.backend.errors.length === 0 ? 'healthy' : 'issues';
    }

    async checkFrontendPages() {
        console.log('🎨 检查前端页面...');
        const pages = [
            { name: 'Home', path: '/', selector: 'body' },
            { name: 'Market', path: '/market', selector: '.market-container' },
            { name: 'Trading', path: '/trading', selector: '.trading-container' },
            { name: 'Strategy', path: '/strategy', selector: '.strategy-container' },
            { name: 'Portfolio', path: '/portfolio', selector: '.portfolio-container' },
            { name: 'Backtest', path: '/backtest', selector: '.backtest-container' }
        ];

        for (const pageInfo of pages) {
            try {
                console.log(`  📄 检查页面: ${pageInfo.name}`);
                await this.page.goto(`http://localhost:5173${pageInfo.path}`, { 
                    waitUntil: 'networkidle0',
                    timeout: 10000 
                });

                // 等待页面加载
                await this.page.waitForTimeout(2000);

                // 检查页面标题
                const title = await this.page.title();
                
                // 检查是否有Vue应用
                const hasVueApp = await this.page.evaluate(() => {
                    return !!window.Vue || !!document.querySelector('#app').__vue__;
                });

                // 检查路由是否正确
                const currentPath = await this.page.evaluate(() => window.location.pathname);

                // 截图
                const screenshotPath = `screenshot_${pageInfo.name.toLowerCase()}_${Date.now()}.png`;
                await this.page.screenshot({ path: screenshotPath, fullPage: true });

                this.results.frontend.pages.push({
                    name: pageInfo.name,
                    path: pageInfo.path,
                    title: title,
                    hasVueApp: hasVueApp,
                    currentPath: currentPath,
                    screenshot: screenshotPath,
                    success: true
                });

                this.results.summary.totalTests++;
                this.results.summary.passedTests++;

            } catch (error) {
                console.log(`  ❌ 页面 ${pageInfo.name} 检查失败: ${error.message}`);
                this.results.frontend.errors.push({
                    page: pageInfo.name,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                this.results.summary.totalTests++;
                this.results.summary.failedTests++;
            }
        }

        this.results.frontend.status = this.results.frontend.errors.length === 0 ? 'healthy' : 'issues';
    }

    async checkIntegration() {
        console.log('🔗 检查前后端集成...');
        
        try {
            // 检查API调用
            await this.page.goto('http://localhost:5173/', { waitUntil: 'networkidle0' });
            
            // 检查是否能成功调用后端API
            const apiCallResult = await this.page.evaluate(async () => {
                try {
                    const response = await fetch('/api/v1/market/stocks');
                    return {
                        success: true,
                        status: response.status,
                        data: await response.text()
                    };
                } catch (error) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
            });

            this.results.integration.tests.push({
                name: 'API调用测试',
                result: apiCallResult,
                success: apiCallResult.success
            });

            this.results.summary.totalTests++;
            if (apiCallResult.success) {
                this.results.summary.passedTests++;
            } else {
                this.results.summary.failedTests++;
            }

        } catch (error) {
            this.results.integration.errors.push({
                test: 'API Integration',
                error: error.message,
                timestamp: new Date().toISOString()
            });
            this.results.summary.totalTests++;
            this.results.summary.failedTests++;
        }

        this.results.integration.status = this.results.integration.errors.length === 0 ? 'healthy' : 'issues';
    }

    async generateReport() {
        console.log('📊 生成检查报告...');
        
        // 计算成功率
        const successRate = this.results.summary.totalTests > 0 
            ? (this.results.summary.passedTests / this.results.summary.totalTests * 100).toFixed(2)
            : 0;

        // 生成总结
        this.results.summary.successRate = `${successRate}%`;
        this.results.summary.overallStatus = successRate >= 80 ? 'healthy' : 'needs_attention';

        // 收集所有问题
        this.results.summary.issues = [
            ...this.results.backend.errors.map(e => ({ source: 'backend', ...e })),
            ...this.results.frontend.errors.map(e => ({ source: 'frontend', ...e })),
            ...this.results.integration.errors.map(e => ({ source: 'integration', ...e }))
        ];

        // 保存报告
        const reportPath = `project_check_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));

        console.log(`\n📋 检查完成！报告已保存到: ${reportPath}`);
        console.log(`📊 总体状态: ${this.results.summary.overallStatus}`);
        console.log(`✅ 成功率: ${successRate}% (${this.results.summary.passedTests}/${this.results.summary.totalTests})`);
        
        if (this.results.summary.issues.length > 0) {
            console.log(`⚠️  发现 ${this.results.summary.issues.length} 个问题`);
        }

        return reportPath;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.init();
            await this.checkBackendAPIs();
            await this.checkFrontendPages();
            await this.checkIntegration();
            const reportPath = await this.generateReport();
            return reportPath;
        } catch (error) {
            console.error('❌ 检查过程中发生错误:', error);
            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

// 运行检查
if (require.main === module) {
    const checker = new ProjectChecker();
    checker.run()
        .then(reportPath => {
            console.log(`\n🎉 项目检查完成！报告: ${reportPath}`);
            process.exit(0);
        })
        .catch(error => {
            console.error('💥 检查失败:', error);
            process.exit(1);
        });
}

module.exports = ProjectChecker;
