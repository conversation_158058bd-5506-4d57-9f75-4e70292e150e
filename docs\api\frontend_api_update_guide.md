# 前端API更新指南

## 1. 更新API端点配置

将以下端点更新到前端的API配置文件中：

### 认证API
- POST `/api/v1/auth/register` - 用户注册
- POST `/api/v1/auth/login` - 用户登录
- GET `/api/v1/auth/me` - 获取当前用户
- POST `/api/v1/auth/logout` - 用户登出
- POST `/api/v1/auth/change-password` - 修改密码

### 交易API
- POST `/api/v1/trading/orders` - 创建订单
- GET `/api/v1/trading/orders` - 获取订单列表
- DELETE `/api/v1/trading/orders/{order_id}` - 取消订单
- GET `/api/v1/trading/positions` - 获取持仓
- GET `/api/v1/trading/account` - 获取账户信息

### 策略API
- POST `/api/v1/strategy/strategies` - 创建策略
- GET `/api/v1/strategy/strategies` - 获取策略列表
- PUT `/api/v1/strategy/strategies/{strategy_id}` - 更新策略
- POST `/api/v1/strategy/strategies/{strategy_id}/backtest` - 运行回测
- POST `/api/v1/strategy/strategies/import` - 导入策略

### 风控API
- GET `/api/v1/risk/metrics` - 获取风险指标
- GET `/api/v1/risk/limits` - 获取风控限制
- PUT `/api/v1/risk/limits` - 更新风控限制
- GET `/api/v1/risk/alerts` - 获取风险告警
- POST `/api/v1/risk/check-order` - 检查订单风险

## 2. 更新请求格式

确保请求体格式与后端API匹配：

### 登录请求
```json
{
    "username": "string",
    "password": "string"
}
```

### 创建订单请求
```json
{
    "symbol": "string",
    "direction": "BUY" | "SELL",
    "order_type": "MARKET" | "LIMIT",
    "price": number,
    "volume": number
}
```

## 3. 处理响应格式

所有API响应都遵循统一格式：

```json
{
    "success": boolean,
    "message": "string",
    "data": object | array
}
```

## 4. 错误处理

- 401: 未授权，需要重新登录
- 403: 无权限
- 404: 资源不存在
- 405: 方法不允许（检查HTTP方法）
- 422: 请求参数验证失败
