#!/usr/bin/env python3
"""
权限修复测试 - 验证permissions错误是否已修复
"""

import asyncio
import json
from datetime import datetime
from puppeteer import <PERSON><PERSON>erManager

async def test_permissions_fix():
    """测试权限错误是否已修复"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 测试权限修复...")
        
        # 监听控制台错误
        console_errors = []
        
        def handle_console(msg):
            if msg.type in ['error', 'warning']:
                console_errors.append({
                    "type": msg.type,
                    "text": msg.text,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"[CONSOLE {msg.type.upper()}] {msg.text}")
        
        page.on('console', handle_console)
        
        # 监听页面错误
        page_errors = []
        
        def handle_page_error(error):
            page_errors.append({
                "message": str(error),
                "timestamp": datetime.now().isoformat()
            })
            print(f"[PAGE ERROR] {error}")
        
        page.on('pageerror', handle_page_error)
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 等待演示登录按钮
        await page.wait_for_selector('button:has-text("演示登录")', timeout=10000)
        print("✅ 找到演示登录按钮")
        
        # 点击演示登录按钮
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        print("🖱️ 已点击演示登录按钮")
        
        # 等待登录处理和可能的页面跳转
        print("⏳ 等待登录处理和页面跳转...")
        await page.wait_for_timeout(8000)
        
        # 检查最终URL
        final_url = page.url
        print(f"📍 最终URL: {final_url}")
        
        # 检查用户状态
        user_state = await page.evaluate('''
            () => {
                try {
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    if (pinia && pinia._s) {
                        const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                        if (userStore) {
                            return {
                                isLoggedIn: userStore.isLoggedIn,
                                hasToken: !!userStore.token,
                                permissions: userStore.permissions,
                                userInfo: userStore.userInfo
                            };
                        }
                    }
                    return { error: 'User store not found' };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f"👤 用户状态: {user_state}")
        
        # 分析错误
        permissions_errors = []
        for error in console_errors + page_errors:
            error_text = error.get('text', error.get('message', ''))
            if 'permissions' in error_text.lower() or 'undefined' in error_text.lower():
                permissions_errors.append(error)
        
        print(f"\n📊 错误分析:")
        print(f"   - 控制台错误: {len(console_errors)}")
        print(f"   - 页面错误: {len(page_errors)}")
        print(f"   - 权限相关错误: {len(permissions_errors)}")
        
        if permissions_errors:
            print("\n❌ 仍有权限相关错误:")
            for error in permissions_errors:
                print(f"   - {error}")
        else:
            print("\n✅ 没有权限相关错误！")
        
        # 检查登录是否成功
        login_success = user_state.get('isLoggedIn', False)
        has_permissions = user_state.get('permissions') is not None
        
        print(f"\n🔍 登录状态检查:")
        print(f"   - 登录成功: {login_success}")
        print(f"   - 有权限数据: {has_permissions}")
        print(f"   - 权限列表: {user_state.get('permissions', 'N/A')}")
        
        # 检查页面跳转
        redirect_success = '/test-slider' in final_url or final_url != 'http://localhost:5173/login'
        print(f"   - 页面跳转: {redirect_success}")
        
        # 生成测试报告
        test_result = "PASS" if (len(permissions_errors) == 0 and login_success and has_permissions) else "FAIL"
        
        test_report = {
            "test_time": datetime.now().isoformat(),
            "test_result": test_result,
            "login_successful": login_success,
            "has_permissions": has_permissions,
            "redirect_successful": redirect_success,
            "final_url": final_url,
            "user_state": user_state,
            "console_errors": console_errors,
            "page_errors": page_errors,
            "permissions_errors": permissions_errors
        }
        
        with open('permissions_fix_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 测试报告已保存: permissions_fix_test_report.json")
        
        # 截图
        screenshot_name = f"permissions_fix_test_{datetime.now().strftime('%H%M%S')}.png"
        await page.screenshot(path=screenshot_name, full_page=True)
        print(f"📸 测试截图已保存: {screenshot_name}")
        
        if test_result == "PASS":
            print("\n🎉 权限错误修复成功！登录功能完全正常！")
        else:
            print("\n⚠️ 仍需要进一步修复")
        
    except Exception as e:
        print(f"💥 测试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_permissions_fix())
