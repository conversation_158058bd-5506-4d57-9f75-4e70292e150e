#!/usr/bin/env python3
"""
数据库配置管理
支持SQLite和PostgreSQL的动态配置
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class DatabaseConfig:
    """数据库配置类"""
    url: str
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> DatabaseConfig:
        """加载数据库配置"""
        # 从环境变量读取配置
        database_url = os.getenv('DATABASE_URL')
        
        if not database_url:
            # 默认使用SQLite
            project_root = Path(__file__).parent
            db_dir = project_root / 'data'
            db_dir.mkdir(exist_ok=True)
            db_path = db_dir / 'quantplatform.db'
            database_url = f'sqlite:///{db_path}'
        
        return DatabaseConfig(
            url=database_url,
            echo=os.getenv('DB_ECHO', 'false').lower() == 'true',
            pool_size=int(os.getenv('DB_POOL_SIZE', '10')),
            max_overflow=int(os.getenv('DB_MAX_OVERFLOW', '20')),
            pool_timeout=int(os.getenv('DB_POOL_TIMEOUT', '30')),
            pool_recycle=int(os.getenv('DB_POOL_RECYCLE', '3600'))
        )
    
    def get_database_url(self) -> str:
        """获取数据库URL"""
        return self.config.url
    
    def get_async_database_url(self) -> str:
        """获取异步数据库URL"""
        url = self.config.url
        if url.startswith('sqlite://'):
            return url.replace('sqlite://', 'sqlite+aiosqlite://')
        elif url.startswith('postgresql://'):
            return url.replace('postgresql://', 'postgresql+asyncpg://')
        return url
    
    def get_engine_config(self) -> Dict[str, Any]:
        """获取SQLAlchemy引擎配置"""
        config = {
            'echo': self.config.echo,
        }
        
        # SQLite配置
        if self.config.url.startswith('sqlite'):
            config.update({
                'connect_args': {'check_same_thread': False},
                'poolclass': None  # SQLite不需要连接池
            })
        else:
            # PostgreSQL配置
            config.update({
                'pool_size': self.config.pool_size,
                'max_overflow': self.config.max_overflow,
                'pool_timeout': self.config.pool_timeout,
                'pool_recycle': self.config.pool_recycle,
            })
        
        return config
    
    def is_sqlite(self) -> bool:
        """判断是否使用SQLite"""
        return self.config.url.startswith('sqlite')
    
    def is_postgresql(self) -> bool:
        """判断是否使用PostgreSQL"""
        return self.config.url.startswith('postgresql')

# 全局数据库管理器实例
db_manager = DatabaseManager()

def get_database_config() -> DatabaseConfig:
    """获取数据库配置"""
    return db_manager.config

def get_database_url() -> str:
    """获取数据库URL"""
    return db_manager.get_database_url()

def get_async_database_url() -> str:
    """获取异步数据库URL"""
    return db_manager.get_async_database_url()

def get_engine_config() -> Dict[str, Any]:
    """获取引擎配置"""
    return db_manager.get_engine_config()

def print_database_info():
    """打印数据库配置信息"""
    config = get_database_config()
    print("📊 数据库配置信息:")
    print(f"  - 数据库类型: {'SQLite' if db_manager.is_sqlite() else 'PostgreSQL'}")
    print(f"  - 数据库URL: {config.url}")
    print(f"  - 调试模式: {config.echo}")
    
    if not db_manager.is_sqlite():
        print(f"  - 连接池大小: {config.pool_size}")
        print(f"  - 最大溢出: {config.max_overflow}")
        print(f"  - 连接超时: {config.pool_timeout}s")
        print(f"  - 连接回收: {config.pool_recycle}s")

if __name__ == "__main__":
    print_database_info()