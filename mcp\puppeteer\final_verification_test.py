#!/usr/bin/env python3
"""
最终验证测试
验证所有修复的问题是否都已解决
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

async def final_verification_test():
    """最终验证测试"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🎯 开始最终验证测试...")
        
        # 测试结果收集
        test_results = {
            "test_time": datetime.now().isoformat(),
            "tests": {}
        }
        
        # 1. 测试Service Worker修复
        print("\n1️⃣ 测试Service Worker修复...")
        await page.goto('http://localhost:5173/', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        sw_test = await page.evaluate('''
            async () => {
                if ('serviceWorker' in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.getRegistration();
                        const swResponse = await fetch('/sw.js');
                        return {
                            registered: !!registration,
                            sw_file_ok: swResponse.ok,
                            content_type: swResponse.headers.get('content-type'),
                            status: swResponse.status
                        };
                    } catch (error) {
                        return { error: error.message };
                    }
                } else {
                    return { error: 'Service Worker not supported' };
                }
            }
        ''')
        
        test_results["tests"]["service_worker"] = {
            "status": "PASS" if sw_test.get('registered') and sw_test.get('sw_file_ok') else "FAIL",
            "details": sw_test
        }
        
        if test_results["tests"]["service_worker"]["status"] == "PASS":
            print("   ✅ Service Worker修复成功")
        else:
            print("   ❌ Service Worker仍有问题")
        
        # 2. 测试登录API修复
        print("\n2️⃣ 测试登录API修复...")
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(2000)
        
        # 监听网络请求
        login_requests = []
        login_responses = []
        
        async def handle_request(request):
            if '/auth/login' in request.url:
                login_requests.append({
                    "url": request.url,
                    "method": request.method
                })
        
        async def handle_response(response):
            if '/auth/login' in response.url:
                login_responses.append({
                    "url": response.url,
                    "status": response.status
                })
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        # 查找并点击演示登录按钮
        demo_button = await page.query_selector('.demo-login, .demo-btn, button:has-text("演示登录")')
        if demo_button:
            await demo_button.click()
            await page.wait_for_timeout(3000)
        
        test_results["tests"]["login_api"] = {
            "status": "PASS" if any(r['status'] == 200 for r in login_responses) else "FAIL",
            "details": {
                "requests": len(login_requests),
                "responses": len(login_responses),
                "successful_responses": len([r for r in login_responses if r['status'] == 200])
            }
        }
        
        if test_results["tests"]["login_api"]["status"] == "PASS":
            print("   ✅ 登录API修复成功")
        else:
            print("   ❌ 登录API仍有问题")
        
        # 3. 测试页面访问
        print("\n3️⃣ 测试页面访问...")
        pages_to_test = [
            ('/', '首页'),
            ('/strategy', '策略中心'),
            ('/portfolio', '投资组合'),
            ('/risk', '风险管理')
        ]
        
        page_results = {}
        for path, name in pages_to_test:
            try:
                await page.goto(f'http://localhost:5173{path}', wait_until='domcontentloaded', timeout=10000)
                await page.wait_for_timeout(2000)
                
                # 检查页面是否正确加载
                page_title = await page.title()
                current_url = page.url
                
                page_results[name] = {
                    "accessible": True,
                    "title": page_title,
                    "url": current_url
                }
                print(f"   ✅ {name} 访问成功")
                
            except Exception as e:
                page_results[name] = {
                    "accessible": False,
                    "error": str(e)
                }
                print(f"   ❌ {name} 访问失败: {e}")
        
        test_results["tests"]["page_access"] = {
            "status": "PASS" if sum(1 for p in page_results.values() if p['accessible']) >= 3 else "FAIL",
            "details": page_results
        }
        
        # 4. 测试API通信
        print("\n4️⃣ 测试API通信...")
        await page.goto('http://localhost:5173/', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        api_test = await page.evaluate('''
            async () => {
                const results = {};
                const apis = [
                    '/api/v1/market/overview',
                    '/api/v1/user/info',
                    '/api/v1/trading/orders'
                ];
                
                for (const api of apis) {
                    try {
                        const response = await fetch(`http://localhost:8000${api}`);
                        results[api] = {
                            status: response.status,
                            ok: response.ok
                        };
                    } catch (error) {
                        results[api] = {
                            error: error.message
                        };
                    }
                }
                
                return results;
            }
        ''')
        
        successful_apis = sum(1 for result in api_test.values() if result.get('ok'))
        test_results["tests"]["api_communication"] = {
            "status": "PASS" if successful_apis >= 2 else "FAIL",
            "details": {
                "total_apis": len(api_test),
                "successful_apis": successful_apis,
                "results": api_test
            }
        }
        
        if test_results["tests"]["api_communication"]["status"] == "PASS":
            print("   ✅ API通信正常")
        else:
            print("   ❌ API通信有问题")
        
        # 5. 计算总体结果
        passed_tests = sum(1 for test in test_results["tests"].values() if test["status"] == "PASS")
        total_tests = len(test_results["tests"])
        
        test_results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": f"{(passed_tests / total_tests * 100):.1f}%",
            "overall_status": "PASS" if passed_tests >= 3 else "FAIL"
        }
        
        # 截图记录
        await page.screenshot(path='final_verification_test.png', full_page=True)
        print("\n📸 测试截图已保存: final_verification_test.png")
        
        # 保存报告
        with open('final_verification_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
            
        print("📄 测试报告已保存: final_verification_report.json")
        
        # 输出总结
        print(f"\n🎯 最终验证结果:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {test_results['summary']['success_rate']}")
        print(f"   总体状态: {'✅ 通过' if test_results['summary']['overall_status'] == 'PASS' else '❌ 失败'}")
        
        return test_results
        
    except Exception as e:
        print(f"💥 测试失败: {e}")
        return None
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(final_verification_test())
