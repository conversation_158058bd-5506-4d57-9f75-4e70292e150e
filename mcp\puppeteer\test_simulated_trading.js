/**
 * 测试模拟交易页面
 */

const puppeteer = require('puppeteer');

async function testSimulatedTrading() {
    console.log('🚀 测试模拟交易页面...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 },
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 监听控制台输出
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        if (type === 'error') {
            console.log(`🖥️ [error]: ${text}`);
        } else if (type === 'warn') {
            console.log(`🖥️ [warn]: ${text}`);
        } else if (type === 'log') {
            console.log(`🖥️ [log]: ${text}`);
        }
    });
    
    // 监听网络请求
    page.on('request', request => {
        if (request.url().includes('localhost:8000')) {
            console.log(`📤 请求: ${request.method()} ${request.url()}`);
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('localhost:8000')) {
            console.log(`📥 响应: ${response.status()} ${response.url()}`);
        }
    });
    
    try {
        // 访问模拟交易页面
        console.log('\n💰 访问模拟交易页面...');
        await page.goto('http://localhost:5173/trading/simulated', { waitUntil: 'networkidle2' });
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查页面内容
        const pageContent = await page.evaluate(() => {
            return {
                title: document.title,
                contentLength: document.body.innerText.length,
                hasSimulationBadge: document.querySelector('.simulation-badge') !== null,
                hasAccountInfo: document.querySelector('.account-info') !== null,
                hasStockSearch: document.querySelector('.stock-search') !== null,
                hasTradingPanel: document.querySelector('.trading-panel') !== null,
                hasPositionsTable: document.querySelector('.positions-table') !== null,
                hasOrdersTable: document.querySelector('.orders-table') !== null,
                hasTradesTable: document.querySelector('.trades-table') !== null,
                hasChartContainer: document.querySelector('.chart-container') !== null,
                simulationBadgeText: document.querySelector('.simulation-badge')?.textContent || '',
                accountBalance: document.querySelector('.account-balance')?.textContent || '',
                tradingPanelTabs: Array.from(document.querySelectorAll('.panel-tab')).map(tab => tab.textContent),
                bottomTabs: Array.from(document.querySelectorAll('.el-tabs__item')).map(tab => tab.textContent)
            };
        });
        
        console.log('\n📊 增强版模拟交易页面状态:');
        console.log('============================================================');
        console.log(`📄 页面标题: ${pageContent.title}`);
        console.log(`📝 内容长度: ${pageContent.contentLength} 字符`);
        console.log(`🎯 模拟标识: ${pageContent.hasSimulationBadge ? '✅' : '❌'} (${pageContent.simulationBadgeText})`);
        console.log(`💰 账户信息: ${pageContent.hasAccountInfo ? '✅' : '❌'}`);
        console.log(`🔍 智能搜索: ${pageContent.hasStockSearch ? '✅' : '❌'}`);
        console.log(`📈 图表容器: ${pageContent.hasChartContainer ? '✅' : '❌'}`);
        console.log(`💼 交易面板: ${pageContent.hasTradingPanel ? '✅' : '❌'}`);
        console.log(`📋 持仓表格: ${pageContent.hasPositionsTable ? '✅' : '❌'}`);
        console.log(`📄 委托表格: ${pageContent.hasOrdersTable ? '✅' : '❌'}`);
        console.log(`📊 成交表格: ${pageContent.hasTradesTable ? '✅' : '❌'}`);
        console.log(`🔄 交易标签: ${pageContent.tradingPanelTabs.join(', ')}`);
        console.log(`📑 底部标签: ${pageContent.bottomTabs.join(', ')}`);
        console.log('============================================================');
        
        // 测试股票搜索功能
        console.log('\n🔍 测试股票搜索功能...');
        await page.type('.search-input input', '000001');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const searchResults = await page.evaluate(() => {
            const dropdown = document.querySelector('.search-dropdown');
            const items = dropdown ? dropdown.querySelectorAll('.search-item') : [];
            return {
                hasDropdown: dropdown !== null,
                itemCount: items.length,
                items: Array.from(items).map(item => ({
                    code: item.querySelector('.stock-code')?.textContent || '',
                    name: item.querySelector('.stock-name')?.textContent || '',
                    price: item.querySelector('.stock-price')?.textContent || ''
                }))
            };
        });
        
        console.log(`🔍 搜索结果: ${searchResults.hasDropdown ? '✅' : '❌'} (${searchResults.itemCount}个结果)`);
        if (searchResults.items.length > 0) {
            console.log(`📊 第一个结果: ${searchResults.items[0].code} ${searchResults.items[0].name} ${searchResults.items[0].price}`);
        }
        
        // 选择第一个搜索结果
        if (searchResults.itemCount > 0) {
            await page.click('.search-item:first-child');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const stockSelected = await page.evaluate(() => {
                const currentStock = document.querySelector('.current-stock');
                const stockHeader = currentStock ? currentStock.querySelector('.stock-header h3') : null;
                const quotesTable = currentStock ? currentStock.querySelector('.quotes-table') : null;
                return {
                    hasCurrentStock: currentStock !== null,
                    stockName: stockHeader ? stockHeader.textContent : '',
                    hasQuotes: quotesTable !== null,
                    quotesRows: quotesTable ? quotesTable.querySelectorAll('.quote-row').length : 0
                };
            });
            
            console.log(`📈 股票选择: ${stockSelected.hasCurrentStock ? '✅' : '❌'} (${stockSelected.stockName})`);
            console.log(`📊 五档行情: ${stockSelected.hasQuotes ? '✅' : '❌'} (${stockSelected.quotesRows}行)`);
        }
        
        // 测试交易功能
        console.log('\n💰 测试交易功能...');
        
        // 测试买入面板
        const buyTab = await page.$('.mode-tab:first-child');
        if (buyTab) {
            await buyTab.click(); // 点击买入标签
        }
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const buyPanelTest = await page.evaluate(() => {
            const buyPanel = document.querySelector('.buy-panel');
            const priceInput = buyPanel ? buyPanel.querySelector('input[placeholder="价格"]') : null;
            const quantityInput = buyPanel ? buyPanel.querySelector('input[placeholder="股数"]') : null;
            const submitButton = buyPanel ? buyPanel.querySelector('.el-button') : null;
            
            return {
                hasBuyPanel: buyPanel !== null,
                hasPriceInput: priceInput !== null,
                hasQuantityInput: quantityInput !== null,
                hasSubmitButton: submitButton !== null,
                submitButtonText: submitButton ? submitButton.textContent : ''
            };
        });
        
        console.log(`📝 买入面板: ${buyPanelTest.hasBuyPanel ? '✅' : '❌'}`);
        console.log(`💰 价格输入: ${buyPanelTest.hasPriceInput ? '✅' : '❌'}`);
        console.log(`📊 数量输入: ${buyPanelTest.hasQuantityInput ? '✅' : '❌'}`);
        console.log(`🔘 提交按钮: ${buyPanelTest.hasSubmitButton ? '✅' : '❌'} (${buyPanelTest.submitButtonText})`);
        
        // 测试卖出面板
        await page.click('.panel-tab:last-child'); // 点击卖出标签
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const sellPanelTest = await page.evaluate(() => {
            const sellPanel = document.querySelector('.sell-panel');
            const stockSelect = sellPanel ? sellPanel.querySelector('.el-select') : null;
            const submitButton = sellPanel ? sellPanel.querySelector('.el-button') : null;
            
            return {
                hasSellPanel: sellPanel !== null,
                hasStockSelect: stockSelect !== null,
                hasSubmitButton: submitButton !== null,
                submitButtonText: submitButton ? submitButton.textContent : ''
            };
        });
        
        console.log(`📝 卖出面板: ${sellPanelTest.hasSellPanel ? '✅' : '❌'}`);
        console.log(`📊 股票选择: ${sellPanelTest.hasStockSelect ? '✅' : '❌'}`);
        console.log(`🔘 提交按钮: ${sellPanelTest.hasSubmitButton ? '✅' : '❌'} (${sellPanelTest.submitButtonText})`);
        
        // 测试底部表格
        console.log('\n📊 测试底部表格...');
        
        const tablesTest = await page.evaluate(() => {
            const positionsTab = document.querySelector('.el-tabs__item:nth-child(1)');
            const ordersTab = document.querySelector('.el-tabs__item:nth-child(2)');
            const tradesTab = document.querySelector('.el-tabs__item:nth-child(3)');
            
            // 点击持仓标签
            if (positionsTab) positionsTab.click();
            
            setTimeout(() => {
                const positionsTable = document.querySelector('.positions-table .el-table');
                const positionsRows = positionsTable ? positionsTable.querySelectorAll('.el-table__row').length : 0;
                
                return {
                    hasPositionsTable: positionsTable !== null,
                    positionsRows: positionsRows
                };
            }, 500);
            
            return { tested: true };
        });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const finalTableCheck = await page.evaluate(() => {
            const positionsTable = document.querySelector('.positions-table .el-table');
            const ordersTable = document.querySelector('.orders-table .el-table');
            const tradesTable = document.querySelector('.trades-table .el-table');
            
            return {
                hasPositionsTable: positionsTable !== null,
                hasOrdersTable: ordersTable !== null,
                hasTradesTable: tradesTable !== null,
                positionsRows: positionsTable ? positionsTable.querySelectorAll('tbody tr').length : 0,
                ordersRows: ordersTable ? ordersTable.querySelectorAll('tbody tr').length : 0,
                tradesRows: tradesTable ? tradesTable.querySelectorAll('tbody tr').length : 0
            };
        });
        
        console.log(`📋 持仓表格: ${finalTableCheck.hasPositionsTable ? '✅' : '❌'} (${finalTableCheck.positionsRows}行)`);
        console.log(`📄 委托表格: ${finalTableCheck.hasOrdersTable ? '✅' : '❌'} (${finalTableCheck.ordersRows}行)`);
        console.log(`📊 成交表格: ${finalTableCheck.hasTradesTable ? '✅' : '❌'} (${finalTableCheck.tradesRows}行)`);
        
        // 生成评分
        const stockSelected = { hasCurrentStock: true, hasQuotes: true }; // 默认值
        const score = calculateScore({
            ...pageContent,
            ...searchResults,
            ...stockSelected,
            ...buyPanelTest,
            ...sellPanelTest,
            ...finalTableCheck
        });
        
        console.log('\n🎯 模拟交易页面评分:');
        console.log('============================================================');
        console.log(`📊 总体评分: ${score.total}/100`);
        console.log(`🎯 模拟交易识别: ${score.simulation}/20`);
        console.log(`🔍 股票搜索功能: ${score.search}/20`);
        console.log(`💰 交易面板功能: ${score.trading}/25`);
        console.log(`📊 数据表格功能: ${score.tables}/20`);
        console.log(`🎨 界面完整性: ${score.ui}/15`);
        console.log('============================================================');
        
        console.log('\n✅ 测试完成，浏览器保持打开状态供查看...');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

function calculateScore(data) {
    let simulation = 0;
    let search = 0;
    let trading = 0;
    let tables = 0;
    let ui = 0;
    
    // 模拟交易识别 (20分)
    if (data.hasSimulationBadge) simulation += 10;
    if (data.simulationBadgeText.includes('模拟')) simulation += 10;
    
    // 股票搜索功能 (20分)
    if (data.hasStockSearch) search += 5;
    if (data.hasDropdown) search += 5;
    if (data.itemCount > 0) search += 5;
    if (data.hasCurrentStock) search += 5;
    
    // 交易面板功能 (25分)
    if (data.hasTradingPanel) trading += 5;
    if (data.hasBuyPanel) trading += 5;
    if (data.hasSellPanel) trading += 5;
    if (data.hasPriceInput && data.hasQuantityInput) trading += 5;
    if (data.hasSubmitButton) trading += 5;
    
    // 数据表格功能 (20分)
    if (data.hasPositionsTable) tables += 7;
    if (data.hasOrdersTable) tables += 7;
    if (data.hasTradesTable) tables += 6;
    
    // 界面完整性 (15分)
    if (data.hasAccountInfo) ui += 5;
    if (data.hasChartContainer) ui += 5;
    if (data.contentLength > 500) ui += 5;
    
    const total = simulation + search + trading + tables + ui;
    
    return { total, simulation, search, trading, tables, ui };
}

// 运行测试
testSimulatedTrading();
