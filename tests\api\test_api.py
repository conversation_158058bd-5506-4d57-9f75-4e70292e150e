#!/usr/bin/env python3
"""
API功能测试脚本
测试量化交易平台的各个API端点
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_api_endpoint(endpoint, method="GET", data=None, description=""):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🧪 测试: {description}")
    print(f"📍 {method} {url}")
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=5)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=5)
        
        print(f"✅ 状态码: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print(f"📄 响应: {json.dumps(result, ensure_ascii=False, indent=2)[:200]}...")
        else:
            print(f"📄 响应: {response.text[:200]}...")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API功能测试")
    print("=" * 50)
    
    # 测试基础端点
    tests = [
        ("/", "GET", None, "根路径健康检查"),
        ("/api/v1/health", "GET", None, "健康检查"),
        ("/api/v1/market/stocks", "GET", None, "获取股票列表"),
        ("/api/v1/market/indices", "GET", None, "获取指数数据"),
        ("/api/captcha/slider", "GET", None, "获取滑块验证码"),
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for endpoint, method, data, description in tests:
        if test_api_endpoint(endpoint, method, data, description):
            success_count += 1
        time.sleep(0.5)  # 避免请求过快
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有API测试通过！")
    else:
        print("⚠️ 部分API测试失败，请检查后端服务")

if __name__ == "__main__":
    main()
