#!/usr/bin/env python3
"""
回测系统完整性测试脚本
测试所有新增功能：AkShare数据源、pyecharts可视化、集成回测引擎
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.akshare_data_source import get_akshare_client
from app.services.backtest_visualizer import BacktestVisualizer
from app.services.backtest_engine_integrated import get_integrated_backtest_engine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_akshare_data_source():
    """测试AkShare数据源"""
    logger.info("🧪 测试AkShare数据源...")
    
    try:
        akshare_client = await get_akshare_client()
        
        # 测试状态检查
        is_available = await akshare_client.is_available()
        logger.info(f"   AkShare可用性: {is_available}")
        
        # 测试股票列表获取
        stock_list = await akshare_client.get_stock_list("A股")
        logger.info(f"   获取股票列表: {len(stock_list)} 只股票")
        
        if stock_list:
            logger.info(f"   示例股票: {stock_list[0]}")
        
        # 测试历史数据获取
        test_symbol = "000001"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        
        stock_data = await akshare_client.get_stock_data(
            symbol=test_symbol,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"   获取 {test_symbol} 历史数据: {len(stock_data)} 条记录")
        
        if not stock_data.empty:
            logger.info(f"   数据列: {list(stock_data.columns)}")
            logger.info(f"   最新价格: {stock_data['close'].iloc[-1]}")
        
        # 测试实时数据获取
        realtime_data = await akshare_client.get_realtime_data([test_symbol])
        logger.info(f"   获取实时数据: {len(realtime_data)} 只股票")
        
        if realtime_data:
            logger.info(f"   实时数据示例: {realtime_data[test_symbol] if test_symbol in realtime_data else '无数据'}")
        
        logger.info("✅ AkShare数据源测试完成\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ AkShare数据源测试失败: {str(e)}\n")
        return False


async def test_visualization_module():
    """测试可视化模块"""
    logger.info("🧪 测试可视化模块...")
    
    try:
        visualizer = BacktestVisualizer()
        
        # 创建模拟回测数据
        mock_equity_data = []
        base_value = 100000
        
        for i in range(30):
            date = (datetime.now() - timedelta(days=30-i)).strftime("%Y-%m-%d")
            value = base_value * (1 + 0.001 * (i + (i % 3 - 1) * 0.5))
            
            mock_equity_data.append({
                "date": date,
                "value": value,
                "return": (value - base_value) / base_value
            })
        
        mock_positions = [
            {"symbol": "000001", "market_value": 25000},
            {"symbol": "000002", "market_value": 35000},
            {"symbol": "600036", "market_value": 40000}
        ]
        
        mock_backtest_result = {
            "equity_curve": mock_equity_data,
            "positions": mock_positions,
            "trades": [
                {
                    "timestamp": "2024-01-15",
                    "symbol": "000001", 
                    "action": "买入",
                    "pnl": 1200
                },
                {
                    "timestamp": "2024-01-20",
                    "symbol": "000002",
                    "action": "卖出", 
                    "pnl": -800
                }
            ]
        }
        
        # 测试可视化数据生成
        visualization_data = await visualizer.generate_visualization_data(mock_backtest_result)
        
        logger.info(f"   生成可视化图表: {len(visualization_data)} 个图表")
        logger.info(f"   图表类型: {list(visualization_data.keys())}")
        
        # 测试具体图表生成
        if visualization_data.get("equity_curve"):
            logger.info("   权益曲线图: ✅")
        
        if visualization_data.get("position_allocation"):
            logger.info("   持仓分配图: ✅")
        
        # 测试pyecharts图表生成
        try:
            html_chart = await visualizer.create_pyecharts_line_chart(mock_equity_data)
            if html_chart:
                logger.info("   pyecharts线图生成: ✅")
            else:
                logger.info("   pyecharts线图生成: ⚠️ (pyecharts可能未安装)")
        except Exception as e:
            logger.warning(f"   pyecharts图表生成警告: {str(e)}")
        
        logger.info("✅ 可视化模块测试完成\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ 可视化模块测试失败: {str(e)}\n")
        return False


async def test_integrated_backtest_engine():
    """测试集成回测引擎"""
    logger.info("🧪 测试集成回测引擎...")
    
    try:
        engine = await get_integrated_backtest_engine()
        
        # 配置回测参数
        strategy_config = {
            "type": "simple_ma",
            "parameters": {
                "short_window": 5,
                "long_window": 20
            }
        }
        
        data_config = {
            "symbols": ["000001", "000002"],
            "start_date": (datetime.now() - timedelta(days=60)).strftime("%Y-%m-%d"),
            "end_date": datetime.now().strftime("%Y-%m-%d"),
            "source": "akshare"
        }
        
        backtest_config = {
            "initial_capital": 100000,
            "commission_rate": 0.0003,
            "stamp_duty_rate": 0.001,
            "slippage_rate": 0.0001
        }
        
        logger.info("   开始执行综合回测...")
        
        # 运行回测
        result = await engine.run_comprehensive_backtest(
            strategy_config=strategy_config,
            data_config=data_config,
            backtest_config=backtest_config
        )
        
        logger.info("   回测执行完成")
        
        # 验证结果
        if "backtest_info" in result:
            info = result["backtest_info"]
            logger.info(f"   回测时间: {info['start_date']} 到 {info['end_date']}")
            logger.info(f"   初始资金: ¥{info['initial_capital']:,.2f}")
            logger.info(f"   最终资金: ¥{info['final_capital']:,.2f}")
        
        if "metrics" in result:
            metrics = result["metrics"]
            logger.info(f"   总收益率: {metrics['total_return']:.2f}%")
            logger.info(f"   年化收益率: {metrics['annual_return']:.2f}%")
            logger.info(f"   最大回撤: {metrics['max_drawdown']:.2f}%")
            logger.info(f"   夏普比率: {metrics['sharpe_ratio']:.2f}")
            logger.info(f"   总交易次数: {metrics['total_trades']}")
        
        if "equity_curve" in result:
            logger.info(f"   权益曲线点数: {len(result['equity_curve'])}")
        
        if "visualization" in result:
            vis_data = result["visualization"]
            logger.info(f"   可视化图表: {len(vis_data)} 个")
        
        if "analysis" in result:
            logger.info("   高级分析: ✅")
        
        logger.info("✅ 集成回测引擎测试完成\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成回测引擎测试失败: {str(e)}\n")
        return False


async def test_dependencies():
    """测试依赖包"""
    logger.info("🧪 测试依赖包...")
    
    # 测试pyecharts
    try:
        import pyecharts
        logger.info(f"   pyecharts版本: {pyecharts.__version__} ✅")
    except ImportError:
        logger.warning("   pyecharts: ❌ 未安装")
    
    # 测试akshare
    try:
        import akshare as ak
        logger.info(f"   akshare: ✅ 已安装")
    except ImportError:
        logger.warning("   akshare: ❌ 未安装")
    
    # 测试其他依赖
    required_packages = ['pandas', 'numpy', 'datetime']
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"   {package}: ✅")
        except ImportError:
            logger.error(f"   {package}: ❌ 未安装")
    
    logger.info("✅ 依赖包测试完成\n")


async def run_full_system_test():
    """运行完整系统测试"""
    logger.info("🚀 开始回测系统完整性测试")
    logger.info("=" * 50)
    
    test_results = []
    
    # 测试依赖包
    await test_dependencies()
    
    # 测试各个模块
    tests = [
        ("AkShare数据源", test_akshare_data_source),
        ("可视化模块", test_visualization_module),
        ("集成回测引擎", test_integrated_backtest_engine)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 时发生异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 汇总结果
    logger.info("=" * 50)
    logger.info("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 所有测试通过！回测系统已完全修复和增强。")
    else:
        logger.warning("⚠️  部分测试失败，请检查相关模块。")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(run_full_system_test())