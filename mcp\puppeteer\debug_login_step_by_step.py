#!/usr/bin/env python3
"""
逐步调试登录过程
"""

import asyncio
from puppeteer import BrowserManager

async def debug_login_step_by_step():
    manager = BrowserManager()
    try:
        page = await manager.ensure_browser()
        print('🔧 逐步调试登录过程...')
        
        # 监听所有网络请求
        requests = []
        responses = []
        
        def handle_request(request):
            requests.append({
                'url': request.url,
                'method': request.method,
                'headers': dict(request.headers),
                'post_data': request.post_data
            })
            print(f'[REQUEST] {request.method} {request.url}')
        
        def handle_response(response):
            responses.append({
                'url': response.url,
                'status': response.status,
                'headers': dict(response.headers)
            })
            print(f'[RESPONSE] {response.status} {response.url}')
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        await page.goto('http://localhost:5173/login')
        await page.wait_for_timeout(2000)
        
        print('\n🎯 步骤1: 页面加载完成')
        
        # 填写登录表单
        await page.fill('input[type="text"]', 'admin')
        await page.fill('input[type="password"]', 'admin123')
        
        print('🎯 步骤2: 表单填写完成')
        
        # 点击登录按钮
        await page.click('button:has-text("登录")')
        
        print('🎯 步骤3: 点击登录按钮')
        
        # 等待登录完成
        await page.wait_for_timeout(3000)
        
        print('🎯 步骤4: 等待登录完成')
        
        # 检查最终状态
        final_state = await page.evaluate('''
            () => {
                try {
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                    
                    return {
                        currentUrl: window.location.href,
                        store: {
                            userInfo: userStore.userInfo,
                            permissions: userStore.permissions,
                            isLoggedIn: userStore.isLoggedIn,
                            token: userStore.token
                        },
                        localStorage: {
                            userInfo: localStorage.getItem('userInfo'),
                            token: localStorage.getItem('token')
                        }
                    };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f'\n📊 最终状态: {final_state}')
        
        # 显示所有登录相关的请求和响应
        print('\n📡 登录相关的网络请求:')
        for req in requests:
            if 'login' in req['url']:
                print(f'  请求: {req["method"]} {req["url"]}')
                if req['post_data']:
                    print(f'    数据: {req["post_data"]}')
        
        print('\n📡 登录相关的网络响应:')
        for resp in responses:
            if 'login' in resp['url']:
                print(f'  响应: {resp["status"]} {resp["url"]}')
        
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(debug_login_step_by_step())
