[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --cov=app --cov-report=html --cov-report=term --cov-report=xml
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    trading: 交易相关测试
    market: 行情相关测试
    strategy: 策略相关测试
    backtest: 回测相关测试
    auth: 认证相关测试
    api: API测试
    load: 负载测试
    stress: 压力测试
    security: 安全测试
    performance: 性能测试
    websocket: WebSocket测试
    error_handling: 错误处理测试
asyncio_mode = auto
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
