#!/usr/bin/env python3
"""
测试登录后跳转到滑块验证页面的修复
"""

import asyncio
import json
from datetime import datetime
from puppeteer import <PERSON>rowserManager

async def test_login_redirect_fix():
    """测试登录后跳转修复"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 测试登录后跳转到滑块验证页面...")
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 监听页面导航
        navigation_events = []
        
        def handle_navigation(frame):
            if frame == page.main_frame:
                navigation_events.append({
                    "url": frame.url,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"🔄 页面导航到: {frame.url}")
        
        page.on('framenavigated', handle_navigation)
        
        # 监听网络请求
        login_requests = []
        
        async def handle_request(request):
            if '/auth/login' in request.url:
                login_requests.append({
                    "url": request.url,
                    "method": request.method,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"📡 登录请求: {request.method} {request.url}")
        
        page.on('request', handle_request)
        
        # 查找并点击演示登录按钮
        print("🔍 查找演示登录按钮...")
        
        # 等待按钮出现
        await page.wait_for_selector('button:has-text("演示登录")', timeout=10000)
        
        # 点击演示登录按钮
        demo_button = await page.query_selector('button:has-text("演示登录")')
        if demo_button:
            print("✅ 找到演示登录按钮，准备点击...")
            await demo_button.click()
            print("🖱️ 已点击演示登录按钮")
            
            # 等待登录处理和页面跳转
            await page.wait_for_timeout(5000)
            
            # 检查当前URL
            current_url = page.url
            print(f"📍 当前页面URL: {current_url}")
            
            # 检查是否跳转到滑块验证页面
            is_on_slider_page = '/test-slider' in current_url
            
            # 如果还在登录页面，检查是否有错误
            if '/login' in current_url:
                print("⚠️ 仍在登录页面，检查错误信息...")
                
                # 检查页面上的错误消息
                error_messages = await page.evaluate('''
                    () => {
                        const errors = [];
                        // 检查Element Plus的消息
                        const messageElements = document.querySelectorAll('.el-message');
                        messageElements.forEach(el => {
                            errors.push(el.textContent);
                        });
                        
                        // 检查控制台错误
                        return errors;
                    }
                ''')
                
                if error_messages:
                    print(f"❌ 页面错误消息: {error_messages}")
            
            # 生成测试报告
            test_result = {
                "test_time": datetime.now().isoformat(),
                "initial_url": "http://localhost:5173/login",
                "final_url": current_url,
                "expected_url_pattern": "/test-slider",
                "redirect_successful": is_on_slider_page,
                "navigation_events": navigation_events,
                "login_requests": login_requests,
                "test_steps": [
                    "访问登录页面",
                    "点击演示登录按钮",
                    "等待登录处理",
                    "检查页面跳转"
                ]
            }
            
            # 截图记录
            screenshot_name = f"login_redirect_test_{datetime.now().strftime('%H%M%S')}.png"
            await page.screenshot(path=screenshot_name, full_page=True)
            print(f"📸 测试截图已保存: {screenshot_name}")
            
            # 保存测试报告
            with open('login_redirect_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(test_result, f, ensure_ascii=False, indent=2)
            
            print("📄 测试报告已保存: login_redirect_test_report.json")
            
            # 输出结果
            if is_on_slider_page:
                print("✅ 登录后跳转修复成功！")
                print(f"   - 成功跳转到滑块验证页面: {current_url}")
                print(f"   - 导航事件数: {len(navigation_events)}")
                print(f"   - 登录请求数: {len(login_requests)}")
            else:
                print("❌ 登录后跳转仍有问题")
                print(f"   - 当前URL: {current_url}")
                print(f"   - 期望URL包含: /test-slider")
                
                # 如果跳转到了其他页面，也算部分成功
                if current_url != "http://localhost:5173/login":
                    print("ℹ️ 登录成功但跳转到了其他页面")
                    
        else:
            print("❌ 未找到演示登录按钮")
            test_result = {
                "test_time": datetime.now().isoformat(),
                "error": "演示登录按钮未找到",
                "current_url": page.url
            }
            
    except Exception as e:
        print(f"💥 测试失败: {e}")
        test_result = {
            "test_time": datetime.now().isoformat(),
            "error": str(e),
            "current_url": page.url if 'page' in locals() else "unknown"
        }
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_login_redirect_fix())
