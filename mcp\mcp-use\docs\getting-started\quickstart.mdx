---
title: "Quickstart"
description: "Get started with mcp_use in minutes"
icon: "rocket"
---

# Quickstart Guide

<Info>
This guide will get you started with mcp_use in **under 5 minutes**. We'll cover installation, basic configuration, and running your first agent.
</Info>

## Installation

<CodeGroup>
  ```bash pip
  pip install mcp-use
  ```

  ```bash from source
  git clone https://github.com/mcp-use/mcp-use.git
  cd mcp-use
  pip install -e .
  ```
</CodeGroup>

<Tip>
Installing from source gives you access to the latest features and examples!
</Tip>

## Installing LangChain Providers

mcp_use works with various LLM providers through LangChain. You'll need to install the appropriate LangChain provider package for your chosen LLM:

<CodeGroup>
  ```bash OpenAI
  pip install langchain-openai
  ```

  ```bash Anthropic
  pip install langchain-anthropic
  ```

  ```bash Google
  pip install langchain-google-genai
  ```

  ```bash Groq
  pip install langchain-groq
  ```
</CodeGroup>

<Warning>
**Tool Calling Required**: Only models with tool calling capabilities can be used with mcp_use. Make sure your chosen model supports function calling or tool use.
</Warning>

<Tip>
For other providers, check the [LangChain chat models documentation](https://python.langchain.com/docs/integrations/chat/)
</Tip>

## Environment Setup

<Note>
Set up your environment variables in a `.env` file for secure API key management:
</Note>

```bash .env
OPENAI_API_KEY=your_api_key_here
ANTHROPIC_API_KEY=your_api_key_here
GROQ_API_KEY=your_api_key_here
GOOGLE_API_KEY=your_api_key_here
```

## Your First Agent

Here's a simple example to get you started:

```python
import asyncio
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from mcp_use import MCPAgent, MCPClient

async def main():
    # Load environment variables
    load_dotenv()

    # Create configuration dictionary
    config = {
      "mcpServers": {
        "playwright": {
          "command": "npx",
          "args": ["@playwright/mcp@latest"],
          "env": {
            "DISPLAY": ":1"
          }
        }
      }
    }

    # Create MCPClient from configuration dictionary
    client = MCPClient.from_dict(config)

    # Create LLM
    llm = ChatOpenAI(model="gpt-4o")

    # Create agent with the client
    agent = MCPAgent(llm=llm, client=client, max_steps=30)

    # Run the query
    result = await agent.run(
        "Find the best restaurant in San Francisco USING GOOGLE SEARCH",
    )
    print(f"\nResult: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration Options

You can also load servers configuration from a config file:

```python
client = MCPClient.from_config_file("browser_mcp.json")
```

Example configuration file (`browser_mcp.json`):

```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"],
      "env": {
        "DISPLAY": ":1"
      }
    }
  }
}
```

<Tip>
For multi-server setups, tool restrictions, and advanced configuration options, see the [Configuration Overview](/getting-started/configuration).
</Tip>


## Available MCP Servers

mcp_use supports **any MCP server**. Check out the [Awesome MCP Servers](https://github.com/punkpeye/awesome-mcp-servers) list for available options.

## Streaming Agent Output

Stream agent responses as they're generated:

```python
async for chunk in agent.astream("your query here"):
    print(chunk, end="", flush=True)
```

<Tip>
Uses LangChain's streaming API. See [streaming documentation](https://python.langchain.com/docs/how_to/streaming/) for more details.
</Tip>


## Next Steps

<CardGroup cols={3}>
  <Card title="Configuration" icon="gear" href="/getting-started/configuration">
    Complete configuration guide covering client setup and agent customization
  </Card>
  <Card title="LLM Integration" icon="brain" href="/agent/llm-integration">
    Discover all supported LLM providers and optimization tips
  </Card>
  <Card title="Examples" icon="code" href="https://github.com/mcp-use/mcp-use/tree/main/examples">
    Explore real-world examples and use cases
  </Card>
</CardGroup>

<Tip>
**Need Help?** Join our community discussions on GitHub or check out the comprehensive examples in our repository!
</Tip>
