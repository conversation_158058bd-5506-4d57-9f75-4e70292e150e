#!/usr/bin/env python3
"""
Tushare数据源服务启动脚本
用于快速启动和测试Tushare数据接入服务
"""
import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

import uvicorn
from app.main import create_app
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def check_tushare_config():
    """检查Tushare配置"""
    logger.info("🔍 检查Tushare配置...")

    if (
        not settings.TUSHARE_API_TOKEN
        or settings.TUSHARE_API_TOKEN == "your_tushare_token"
    ):
        logger.error("❌ TUSHARE_API_TOKEN未配置或使用默认值")
        return False

    logger.info(f"✅ Tushare Token: {settings.TUSHARE_API_TOKEN[:10]}...")
    logger.info(f"✅ 实时数据: {'启用' if settings.USE_REALTIME_DATA else '禁用'}")
    logger.info(f"✅ WebSocket: {'启用' if settings.WS_ENABLED else '禁用'}")

    return True


def check_dependencies():
    """检查依赖包"""
    logger.info("🔍 检查依赖包...")

    required_packages = ["tushare", "aioredis", "fastapi", "uvicorn", "pandas", "numpy"]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package}")

    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False

    return True


async def test_tushare_connection():
    """测试Tushare连接"""
    logger.info("🔍 测试Tushare API连接...")

    try:
        import tushare as ts

        # 初始化Tushare Pro API
        ts_pro = ts.pro_api(settings.TUSHARE_API_TOKEN)

        # 测试API调用
        df = ts_pro.trade_cal(
            exchange="SSE", start_date="20240101", end_date="20240110"
        )

        if not df.empty:
            logger.info(f"✅ Tushare API连接成功，获取到{len(df)}条数据")
            return True
        else:
            logger.error("❌ Tushare API返回空数据")
            return False

    except Exception as e:
        logger.error(f"❌ Tushare API连接失败: {e}")
        return False


def start_service():
    """启动服务"""
    logger.info("🚀 启动Tushare数据源服务...")
    logger.info("=" * 60)

    # 检查配置
    if not check_tushare_config():
        logger.error("配置检查失败，请检查环境变量")
        sys.exit(1)

    # 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败，请安装缺少的包")
        sys.exit(1)

    # 测试Tushare连接
    try:
        result = asyncio.run(test_tushare_connection())
        if not result:
            logger.error("Tushare连接测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Tushare连接测试异常: {e}")
        sys.exit(1)

    logger.info("✅ 所有检查通过，启动服务...")
    logger.info("=" * 60)

    # 创建应用
    app = create_app()

    # 启动服务
    logger.info("🌐 服务启动信息:")
    logger.info(f"   - 应用地址: http://localhost:8000")
    logger.info(f"   - API文档: http://localhost:8000/docs")
    logger.info(f"   - 健康检查: http://localhost:8000/health")
    logger.info(f"   - 指数数据: http://localhost:8000/api/v1/market/indices")
    logger.info(f"   - 股票列表: http://localhost:8000/api/v1/market/stocks")
    logger.info("=" * 60)

    # 配置uvicorn
    config = uvicorn.Config(
        app=app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False,  # 生产环境建议关闭
        access_log=True,
    )

    server = uvicorn.Server(config)

    try:
        server.run()
    except KeyboardInterrupt:
        logger.info("\n⏹️ 服务被用户停止")
    except Exception as e:
        logger.error(f"💥 服务运行异常: {e}")
        sys.exit(1)


# 脚本直接运行时执行
if __name__ == "__main__":
    # if not os.getenv("TUSHARE_API_TOKEN"):
    #     os.environ["TUSHARE_API_TOKEN"] = "f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400"

    # 运行数据服务
    asyncio.run(start_service())
