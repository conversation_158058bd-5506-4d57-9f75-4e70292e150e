/**
 * 量化投资平台深度用户测试
 * 模拟真实用户的完整使用流程
 */

const puppeteer = require('puppeteer');
const fs = require('fs');

class PlatformTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = [];
        this.errors = [];
        this.screenshots = [];
        this.startTime = Date.now();
    }

    async init() {
        console.log('🚀 启动量化投资平台深度测试...');
        
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: [
                '--start-maximized',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        });

        this.page = await this.browser.newPage();
        
        // 监听控制台消息
        this.page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                console.log(`❌ 控制台错误: ${text}`);
                this.errors.push({ type: 'console', message: text, timestamp: new Date() });
            } else if (type === 'warning') {
                console.log(`⚠️ 控制台警告: ${text}`);
            }
        });

        // 监听页面错误
        this.page.on('pageerror', error => {
            console.log(`❌ 页面错误: ${error.message}`);
            this.errors.push({ type: 'page', message: error.message, timestamp: new Date() });
        });

        // 监听网络请求失败
        this.page.on('requestfailed', request => {
            console.log(`❌ 请求失败: ${request.url()} - ${request.failure().errorText}`);
            this.errors.push({ 
                type: 'network', 
                url: request.url(), 
                error: request.failure().errorText, 
                timestamp: new Date() 
            });
        });
    }

    async takeScreenshot(name) {
        const timestamp = Date.now();
        const filename = `${name}_${timestamp}.png`;
        await this.page.screenshot({ 
            path: filename,
            fullPage: true 
        });
        this.screenshots.push(filename);
        console.log(`📸 截图保存: ${filename}`);
        return filename;
    }

    async addTestResult(testName, status, details = {}, duration = 0) {
        this.testResults.push({
            name: testName,
            status,
            details,
            duration,
            timestamp: new Date()
        });
        
        const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
        console.log(`${statusIcon} ${testName}: ${status} (${duration}ms)`);
    }

    async waitAndCheck(selector, timeout = 5000, testName = '') {
        try {
            await this.page.waitForSelector(selector, { timeout });
            return true;
        } catch (error) {
            if (testName) {
                await this.addTestResult(testName, 'FAIL', { 
                    error: `元素未找到: ${selector}`,
                    timeout 
                });
            }
            return false;
        }
    }

    // 测试1: 平台首页加载和导航
    async testHomePage() {
        console.log('\n📱 测试1: 平台首页加载和导航');
        const startTime = Date.now();

        try {
            await this.page.goto('http://localhost:5173', { 
                waitUntil: 'networkidle0',
                timeout: 30000 
            });

            await this.takeScreenshot('homepage_initial');

            // 检查页面基本元素
            const pageElements = await this.page.evaluate(() => {
                return {
                    title: document.title,
                    hasHeader: !!document.querySelector('header, .header, .navbar'),
                    hasNavigation: !!document.querySelector('nav, .nav, .navigation'),
                    hasMainContent: !!document.querySelector('main, .main, .content'),
                    hasFooter: !!document.querySelector('footer, .footer'),
                    linkCount: document.querySelectorAll('a').length,
                    buttonCount: document.querySelectorAll('button').length,
                    contentLength: document.body.innerText.length
                };
            });

            await this.addTestResult('首页基本结构', 
                pageElements.hasMainContent ? 'PASS' : 'FAIL', 
                pageElements, 
                Date.now() - startTime
            );

            // 测试导航链接
            const navLinks = await this.page.$$eval('a[href]', links => 
                links.map(link => ({
                    text: link.textContent.trim(),
                    href: link.href,
                    visible: link.offsetParent !== null
                })).filter(link => link.visible && link.text)
            );

            console.log(`🔗 发现 ${navLinks.length} 个导航链接`);
            
            for (let i = 0; i < Math.min(navLinks.length, 5); i++) {
                const link = navLinks[i];
                console.log(`  - ${link.text}: ${link.href}`);
            }

            await this.addTestResult('导航链接检测', 
                navLinks.length > 0 ? 'PASS' : 'FAIL', 
                { linkCount: navLinks.length, links: navLinks.slice(0, 5) }
            );

        } catch (error) {
            await this.addTestResult('首页加载', 'FAIL', { error: error.message });
        }
    }

    // 测试2: 模拟交易功能
    async testSimulatedTrading() {
        console.log('\n💰 测试2: 模拟交易功能');
        const startTime = Date.now();

        try {
            // 导航到模拟交易页面
            await this.page.goto('http://localhost:5173/trading/simulated', { 
                waitUntil: 'networkidle0',
                timeout: 30000 
            });

            await this.takeScreenshot('simulated_trading_page');

            // 检查页面结构
            const pageStructure = await this.page.evaluate(() => {
                return {
                    hasHeader: !!document.querySelector('.modern-header'),
                    hasSearchBox: !!document.querySelector('.modern-search'),
                    hasAccountDashboard: !!document.querySelector('.account-dashboard'),
                    hasTradingWorkspace: !!document.querySelector('.trading-workspace'),
                    hasLeftPanel: !!document.querySelector('.left-panel'),
                    hasRightPanel: !!document.querySelector('.right-panel'),
                    hasBottomPanel: !!document.querySelector('.bottom-data-panel'),
                    hasTradeForm: !!document.querySelector('.trade-form')
                };
            });

            await this.addTestResult('模拟交易页面结构', 
                pageStructure.hasHeader && pageStructure.hasTradingWorkspace ? 'PASS' : 'FAIL', 
                pageStructure
            );

            // 测试股票搜索功能
            await this.testStockSearch();

            // 测试交易表单
            await this.testTradingForm();

            // 测试账户信息显示
            await this.testAccountInfo();

        } catch (error) {
            await this.addTestResult('模拟交易页面访问', 'FAIL', { error: error.message });
        }
    }

    // 测试股票搜索
    async testStockSearch() {
        console.log('🔍 测试股票搜索功能...');
        
        try {
            const searchInput = await this.page.$('.modern-search input');
            if (!searchInput) {
                await this.addTestResult('搜索框存在性', 'FAIL', { error: '搜索框未找到' });
                return;
            }

            // 测试搜索输入
            await searchInput.click();
            await searchInput.type('000001', { delay: 100 });
            await this.page.waitForTimeout(1000);

            // 检查搜索建议
            const suggestions = await this.page.evaluate(() => {
                const suggestionList = document.querySelector('.el-autocomplete-suggestion__list');
                if (!suggestionList) return [];
                
                return Array.from(suggestionList.querySelectorAll('li')).map(li => ({
                    text: li.textContent.trim(),
                    visible: li.offsetParent !== null
                }));
            });

            await this.addTestResult('股票搜索建议', 
                suggestions.length > 0 ? 'PASS' : 'FAIL', 
                { suggestionCount: suggestions.length, suggestions: suggestions.slice(0, 3) }
            );

            // 选择第一个建议
            if (suggestions.length > 0) {
                await this.page.click('.el-autocomplete-suggestion__list li:first-child');
                await this.page.waitForTimeout(1000);
                
                // 检查股票是否被选中
                const stockSelected = await this.page.evaluate(() => {
                    const stockCard = document.querySelector('.stock-card');
                    const stockName = stockCard?.querySelector('.stock-name')?.textContent;
                    const stockPrice = stockCard?.querySelector('.main-price')?.textContent;
                    
                    return {
                        hasStockCard: !!stockCard,
                        stockName: stockName || '',
                        stockPrice: stockPrice || '',
                        hasDepthData: !!stockCard?.querySelector('.depth-book')
                    };
                });

                await this.addTestResult('股票选择', 
                    stockSelected.hasStockCard ? 'PASS' : 'FAIL', 
                    stockSelected
                );

                await this.takeScreenshot('stock_selected');
            }

        } catch (error) {
            await this.addTestResult('股票搜索测试', 'FAIL', { error: error.message });
        }
    }

    // 测试交易表单
    async testTradingForm() {
        console.log('📝 测试交易表单功能...');
        
        try {
            // 检查买入表单
            const buyFormExists = await this.waitAndCheck('.trade-form.buy-form', 3000);
            if (!buyFormExists) {
                await this.addTestResult('买入表单显示', 'FAIL', { error: '买入表单未找到' });
                return;
            }

            // 测试价格输入
            const priceInput = await this.page.$('.price-input input');
            if (priceInput) {
                await priceInput.click();
                await priceInput.type('13.50', { delay: 50 });
                await this.page.waitForTimeout(500);
            }

            // 测试数量输入
            const quantityInput = await this.page.$('.quantity-input input');
            if (quantityInput) {
                await quantityInput.click();
                await quantityInput.type('1000', { delay: 50 });
                await this.page.waitForTimeout(500);
            }

            // 检查交易摘要更新
            const tradeSummary = await this.page.evaluate(() => {
                const summaryElement = document.querySelector('.trade-summary');
                if (!summaryElement) return null;
                
                const summaryRows = Array.from(summaryElement.querySelectorAll('.summary-row'));
                const summary = {};
                summaryRows.forEach(row => {
                    const label = row.querySelector('.summary-label')?.textContent;
                    const value = row.querySelector('.summary-value')?.textContent;
                    if (label && value) {
                        summary[label] = value;
                    }
                });
                
                return summary;
            });

            await this.addTestResult('交易表单输入', 
                tradeSummary && Object.keys(tradeSummary).length > 0 ? 'PASS' : 'FAIL', 
                { tradeSummary }
            );

            // 测试快捷按钮
            const quickButtons = await this.page.$$('.quick-price-buttons .price-btn');
            if (quickButtons.length > 0) {
                await quickButtons[1].click(); // 点击现价按钮
                await this.page.waitForTimeout(500);
                
                await this.addTestResult('快捷价格按钮', 'PASS', { buttonCount: quickButtons.length });
            }

            await this.takeScreenshot('trading_form_filled');

        } catch (error) {
            await this.addTestResult('交易表单测试', 'FAIL', { error: error.message });
        }
    }

    // 测试账户信息
    async testAccountInfo() {
        console.log('💳 测试账户信息显示...');
        
        try {
            const accountInfo = await this.page.evaluate(() => {
                const dashboard = document.querySelector('.account-dashboard');
                if (!dashboard) return null;
                
                const items = Array.from(dashboard.querySelectorAll('.account-item'));
                const info = {};
                
                items.forEach(item => {
                    const label = item.querySelector('.account-label')?.textContent;
                    const value = item.querySelector('.account-value')?.textContent;
                    if (label && value) {
                        info[label] = value;
                    }
                });
                
                return info;
            });

            await this.addTestResult('账户信息显示', 
                accountInfo && Object.keys(accountInfo).length >= 3 ? 'PASS' : 'FAIL', 
                { accountInfo }
            );

        } catch (error) {
            await this.addTestResult('账户信息测试', 'FAIL', { error: error.message });
        }
    }

    // 测试3: 实盘交易功能
    async testLiveTrading() {
        console.log('\n🔴 测试3: 实盘交易功能');
        
        try {
            await this.page.goto('http://localhost:5173/trading/live', { 
                waitUntil: 'networkidle0',
                timeout: 30000 
            });

            await this.takeScreenshot('live_trading_page');

            const pageStructure = await this.page.evaluate(() => {
                return {
                    hasHeader: !!document.querySelector('.modern-header'),
                    hasSearchBox: !!document.querySelector('.modern-search'),
                    hasTradingTerminal: !!document.querySelector('.trading-terminal'),
                    hasConnectionStatus: !!document.querySelector('.connection-status'),
                    hasOrderPanel: !!document.querySelector('.order-panel'),
                    hasDepthPanel: !!document.querySelector('.depth-panel')
                };
            });

            await this.addTestResult('实盘交易页面结构', 
                pageStructure.hasHeader ? 'PASS' : 'FAIL', 
                pageStructure
            );

        } catch (error) {
            await this.addTestResult('实盘交易页面访问', 'FAIL', { error: error.message });
        }
    }

    // 测试4: 策略管理功能
    async testStrategyManagement() {
        console.log('\n🧠 测试4: 策略管理功能');
        
        try {
            // 尝试访问策略页面
            const strategyUrls = [
                'http://localhost:5173/strategy',
                'http://localhost:5173/strategies',
                'http://localhost:5173/strategy/list'
            ];

            for (const url of strategyUrls) {
                try {
                    await this.page.goto(url, { waitUntil: 'networkidle0', timeout: 10000 });
                    
                    const pageExists = await this.page.evaluate(() => {
                        return !document.body.textContent.includes('404') && 
                               !document.body.textContent.includes('Not Found');
                    });

                    if (pageExists) {
                        await this.takeScreenshot('strategy_page');
                        await this.addTestResult('策略页面访问', 'PASS', { url });
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }

        } catch (error) {
            await this.addTestResult('策略管理测试', 'FAIL', { error: error.message });
        }
    }

    // 测试5: 性能和响应性
    async testPerformance() {
        console.log('\n⚡ 测试5: 性能和响应性');
        
        try {
            // 测试页面加载性能
            const performanceMetrics = await this.page.evaluate(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalLoadTime: navigation.loadEventEnd - navigation.fetchStart,
                    resourceCount: performance.getEntriesByType('resource').length
                };
            });

            await this.addTestResult('页面加载性能', 
                performanceMetrics.totalLoadTime < 5000 ? 'PASS' : 'WARN', 
                performanceMetrics
            );

            // 测试内存使用
            const memoryInfo = await this.page.evaluate(() => {
                if (performance.memory) {
                    return {
                        usedJSHeapSize: performance.memory.usedJSHeapSize,
                        totalJSHeapSize: performance.memory.totalJSHeapSize,
                        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                    };
                }
                return null;
            });

            if (memoryInfo) {
                await this.addTestResult('内存使用情况', 
                    memoryInfo.usedJSHeapSize < 100 * 1024 * 1024 ? 'PASS' : 'WARN', 
                    memoryInfo
                );
            }

        } catch (error) {
            await this.addTestResult('性能测试', 'FAIL', { error: error.message });
        }
    }

    // 生成测试报告
    async generateReport() {
        const endTime = Date.now();
        const totalDuration = endTime - this.startTime;
        
        const passCount = this.testResults.filter(t => t.status === 'PASS').length;
        const failCount = this.testResults.filter(t => t.status === 'FAIL').length;
        const warnCount = this.testResults.filter(t => t.status === 'WARN').length;

        const report = {
            summary: {
                totalTests: this.testResults.length,
                passed: passCount,
                failed: failCount,
                warnings: warnCount,
                totalDuration,
                timestamp: new Date().toISOString()
            },
            testResults: this.testResults,
            errors: this.errors,
            screenshots: this.screenshots,
            recommendations: this.generateRecommendations()
        };

        const reportFile = `platform_test_report_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        console.log('\n📊 测试完成统计:');
        console.log(`✅ 通过: ${passCount}`);
        console.log(`❌ 失败: ${failCount}`);
        console.log(`⚠️ 警告: ${warnCount}`);
        console.log(`🕒 总耗时: ${(totalDuration / 1000).toFixed(2)}秒`);
        console.log(`📄 报告文件: ${reportFile}`);

        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.errors.length > 0) {
            recommendations.push('修复控制台错误和页面错误，提高稳定性');
        }
        
        const failedTests = this.testResults.filter(t => t.status === 'FAIL');
        if (failedTests.length > 0) {
            recommendations.push('修复失败的功能测试，确保核心功能正常');
        }
        
        const slowTests = this.testResults.filter(t => t.duration > 3000);
        if (slowTests.length > 0) {
            recommendations.push('优化页面加载速度，提升用户体验');
        }
        
        return recommendations;
    }

    async run() {
        try {
            await this.init();
            
            await this.testHomePage();
            await this.testSimulatedTrading();
            await this.testLiveTrading();
            await this.testStrategyManagement();
            await this.testPerformance();
            
            const report = await this.generateReport();
            
            console.log('\n🎉 深度测试完成！浏览器保持打开状态供手动检查...');
            
            // 保持浏览器打开
            await this.page.waitForTimeout(10000);
            
        } catch (error) {
            console.error('❌ 测试过程中发生严重错误:', error);
        }
    }
}

// 运行深度测试
const tester = new PlatformTester();
tester.run().catch(console.error);
