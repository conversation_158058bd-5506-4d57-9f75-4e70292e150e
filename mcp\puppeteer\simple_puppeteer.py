#!/usr/bin/env python3
"""
简化的Puppeteer类，用于真实用户测试
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path

try:
    from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeout
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright未安装，将使用模拟模式")

class SimplePuppeteer:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.page = None
        self.console_logs = []
        self.screenshots = {}
        self.mock_mode = not PLAYWRIGHT_AVAILABLE
        
    async def launch_browser(self):
        """启动浏览器"""
        if self.mock_mode:
            print("🎭 模拟模式: 浏览器启动")
            return {'success': True, 'message': '模拟浏览器启动成功'}
        
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                args=['--start-maximized', '--no-sandbox']
            )
            self.page = await self.browser.new_page(
                viewport={"width": 1280, "height": 720}
            )
            
            # 监听控制台消息
            async def handle_console(msg):
                log_entry = f"[{msg.type}] {msg.text}"
                self.console_logs.append(log_entry)
                print(f"🖥️ 浏览器控制台: {log_entry}")
            
            self.page.on("console", handle_console)
            
            return {'success': True, 'message': '浏览器启动成功'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def navigate_to(self, url):
        """导航到指定URL"""
        if self.mock_mode:
            print(f"🎭 模拟模式: 导航到 {url}")
            await asyncio.sleep(1)  # 模拟加载时间
            return {'success': True, 'url': url}
        
        try:
            if not self.page:
                await self.launch_browser()
            
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            return {'success': True, 'url': url}
        except PlaywrightTimeout:
            return {'success': False, 'error': f'导航到 {url} 超时'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def get_page_info(self):
        """获取页面信息"""
        if self.mock_mode:
            return {
                'success': True,
                'title': '量化投资平台 - 模拟',
                'url': 'http://localhost:5173',
                'content_length': 1000
            }
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            title = await self.page.title()
            url = self.page.url
            content = await self.page.content()
            
            return {
                'success': True,
                'title': title,
                'url': url,
                'content_length': len(content)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def query_selector_all(self, selector):
        """查询所有匹配的元素"""
        if self.mock_mode:
            # 模拟不同选择器的结果
            mock_results = {
                'nav': [{'tag': 'nav'}, {'tag': 'nav'}],
                '.nav': [{'class': 'nav'}],
                'button': [{'tag': 'button'}, {'tag': 'button'}, {'tag': 'button'}],
                'a': [{'tag': 'a'}, {'tag': 'a'}, {'tag': 'a'}, {'tag': 'a'}],
                '.menu': [],
                '.header': [{'class': 'header'}]
            }
            
            elements = mock_results.get(selector, [])
            return {'success': True, 'elements': elements, 'count': len(elements)}
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            elements = await self.page.query_selector_all(selector)
            element_data = []
            
            for element in elements:
                try:
                    tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                    element_data.append({'tag': tag_name})
                except:
                    element_data.append({'tag': 'unknown'})
            
            return {'success': True, 'elements': element_data, 'count': len(element_data)}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def take_screenshot(self, name):
        """截图"""
        if self.mock_mode:
            print(f"🎭 模拟模式: 截图 {name}")
            return {'success': True, 'filename': f'{name}_mock.png'}
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"{name}_{timestamp}.png"
            
            await self.page.screenshot(path=filename, full_page=True)
            self.screenshots[name] = filename
            
            return {'success': True, 'filename': filename}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def click_element(self, selector):
        """点击元素"""
        if self.mock_mode:
            print(f"🎭 模拟模式: 点击 {selector}")
            await asyncio.sleep(0.5)
            return {'success': True, 'selector': selector}
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            await self.page.click(selector, timeout=10000)
            return {'success': True, 'selector': selector}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def fill_input(self, selector, text):
        """填写输入框"""
        if self.mock_mode:
            print(f"🎭 模拟模式: 在 {selector} 中输入 {text}")
            return {'success': True, 'selector': selector, 'text': text}
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            await self.page.fill(selector, text)
            return {'success': True, 'selector': selector, 'text': text}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def wait_for_element(self, selector, timeout=10000):
        """等待元素出现"""
        if self.mock_mode:
            print(f"🎭 模拟模式: 等待元素 {selector}")
            await asyncio.sleep(1)
            return {'success': True, 'selector': selector}
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            await self.page.wait_for_selector(selector, timeout=timeout)
            return {'success': True, 'selector': selector}
        except PlaywrightTimeout:
            return {'success': False, 'error': f'等待元素 {selector} 超时'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def evaluate_script(self, script):
        """执行JavaScript脚本"""
        if self.mock_mode:
            print(f"🎭 模拟模式: 执行脚本")
            # 返回一些模拟数据
            return {'success': True, 'result': {'mockData': True}}
        
        try:
            if not self.page:
                return {'success': False, 'error': '页面未初始化'}
            
            result = await self.page.evaluate(script)
            return {'success': True, 'result': result}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def get_console_logs(self):
        """获取控制台日志"""
        return {'success': True, 'logs': self.console_logs}

    async def close_browser(self):
        """关闭浏览器"""
        if self.mock_mode:
            print("🎭 模拟模式: 关闭浏览器")
            return {'success': True}
        
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}

# 为了兼容性，创建一个Puppeteer别名
Puppeteer = SimplePuppeteer
