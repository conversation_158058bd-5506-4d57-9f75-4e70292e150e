{"timestamp": "2025-08-04T10:12:32.783926", "user_scenarios": [{"name": "新用户探索平台", "start_time": 1754273553.824426, "steps": ["访问首页"], "issues_found": ["未找到明显的导航菜单"], "user_experience_notes": ["缺少新用户引导或帮助信息"], "end_time": 1754273558.952693, "duration": 5.128267049789429}, {"name": "市场数据交互测试", "start_time": 1754273558.953061, "steps": ["导航到市场页面"], "issues_found": ["未发现数据表格", "未发现图表元素"], "user_experience_notes": ["未找到搜索功能"], "end_time": 1754273567.9782152, "duration": 9.025154113769531}, {"name": "策略工作流测试", "start_time": 1754273567.9787424, "steps": ["导航到策略页面", "发现4个策略卡片", "点击第一个策略卡片", "发现创建策略按钮", "点击创建策略按钮", "发现7个表单元素", "填写表单字段1", "填写表单字段2", "填写表单字段3"], "issues_found": [], "user_experience_notes": [], "end_time": 1754273576.246297, "duration": 8.267554521560669}, {"name": "响应式设计测试", "start_time": 1754273576.2467318, "steps": ["桌面端视口测试", "平板横屏视口测试", "平板竖屏视口测试", "手机大屏视口测试", "手机标准视口测试"], "issues_found": ["平板竖屏发现106个小文字元素", "手机大屏发现120个小文字元素", "手机标准发现120个小文字元素"], "user_experience_notes": [], "end_time": 1754273601.802326, "duration": 25.555594205856323}, {"name": "压力测试", "start_time": 1754273601.8028507, "steps": ["快速访问/market", "快速访问/strategy", "快速访问/trading", "快速访问/portfolio", "快速访问/settings", "快速访问/market", "快速访问/strategy", "快速访问/trading", "快速访问/portfolio", "快速访问/settings", "快速访问/market", "快速访问/strategy", "快速访问/trading", "快速访问/portfolio", "快速访问/settings", "内存使用: 197.37MB"], "issues_found": ["内存使用过高: 197.37MB"], "user_experience_notes": [], "end_time": 1754273613.0554094, "duration": 11.252558708190918}], "discovered_issues": [{"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:12:34.155159", "url": "http://localhost:5173/"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:34.155268", "url": "http://localhost:5173/"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:34.183076", "url": "http://localhost:5173/"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:12:37.974666", "url": "http://localhost:5173/"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:12:37.974757", "url": "http://localhost:5173/"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:12:38.976737", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:38.976937", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:38.983277", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:40.144655", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 1/10", "timestamp": "2025-08-04T10:12:40.144776", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:40.646046", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 2/10", "timestamp": "2025-08-04T10:12:40.646576", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:41.148978", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 3/10", "timestamp": "2025-08-04T10:12:41.149897", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:41.647819", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 4/10", "timestamp": "2025-08-04T10:12:41.647959", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:41.678200", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:41.678727", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:41.678821", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "WebSocket连接暂时不可用: Event", "timestamp": "2025-08-04T10:12:41.678992", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "WebSocket错误详情: Event", "timestamp": "2025-08-04T10:12:41.679147", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "timestamp": "2025-08-04T10:12:41.681533", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-08-04T10:12:41.681747", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:41.682344", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:41.695552", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:41.695814", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:42.148060", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 5/10", "timestamp": "2025-08-04T10:12:42.148329", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:42.660613", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 6/10", "timestamp": "2025-08-04T10:12:42.660743", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:43.163220", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 7/10", "timestamp": "2025-08-04T10:12:43.163577", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:43.662337", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 8/10", "timestamp": "2025-08-04T10:12:43.662528", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:44.163044", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 9/10", "timestamp": "2025-08-04T10:12:44.163344", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:12:44.678369", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 10/10", "timestamp": "2025-08-04T10:12:44.678509", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:12:44.696437", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:12:44.696539", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:44.996136", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:45.012548", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:45.029729", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:45.046066", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:12:45.047319", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "❌ 容器检查失败，超过最大重试次数", "timestamp": "2025-08-04T10:12:45.193591", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "❌ 容器准备失败，尝试fallback方案", "timestamp": "2025-08-04T10:12:45.193813", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "⚠️ Fallback: 图表容器仍未找到，放弃初始化", "timestamp": "2025-08-04T10:12:46.395219", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:12:48.008784", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:48.009029", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:48.020484", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:48.325799", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:48.334948", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:48.344812", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:48.352772", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "🚨 页面错误: TypeError: Cannot read properties of undefined (reading 'pointerdown')\n    at HTMLDocument.firstInputHandler (http://localhost:5173/src/services/performance-monitor.service.ts:70:47)", "timestamp": "2025-08-04T10:12:50.868901", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: TypeError, message: Cannot read properties of undefined (reading 'pointerdown'), details: undefined, context: Object}", "timestamp": "2025-08-04T10:12:50.869167", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "🚨 全局异常: {message: Uncaught TypeError: Cannot read properties of undefined (reading 'pointerdown'), filename: http://localhost:5173/src/services/performance-monitor.service.ts, lineno: 70, colno: 47, error: TypeError: Cannot read properties of undefined (reading 'pointerdown')\n    at HTMLDocument.firstInp…}", "timestamp": "2025-08-04T10:12:50.869354", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:12:51.431083", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:12:51.431184", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:53.108578", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:53.109078", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:53.111080", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:55.650149", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:55.656117", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:55.663453", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:55.669309", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:12:57.288825", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:57.288912", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:12:57.292376", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:57.515477", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:57.521455", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:57.528233", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:12:57.533905", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:00.594513", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:00.594617", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:02.678562", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:02.678671", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:02.684515", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:02.854043", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:02.860847", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:02.866150", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:02.871271", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:05.910911", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:05.911001", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:07.090105", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:07.090271", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:07.096920", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:07.298167", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:07.304998", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:07.312905", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:07.319807", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:10.389692", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:10.389790", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:12.756576", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:12.756694", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:12.759503", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:12.984368", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:12.992791", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:12.999939", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:13.007974", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:16.061876", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:16.061982", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:17.816877", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:17.817020", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:17.820003", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:18.013048", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:18.019326", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:18.025629", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:18.034463", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:21.073859", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:13:21.073954", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:21.818537", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:21.818682", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:21.820289", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:22.437117", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:22.437292", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:22.440913", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:22.614598", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:22.624576", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:22.630685", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:22.635859", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:23.023523", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:23.023695", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:23.028466", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:23.245796", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:23.246202", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:23.247839", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:23.248613", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:23.617145", "url": "http://localhost:5173/portfolio"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:23.617226", "url": "http://localhost:5173/portfolio"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:23.622309", "url": "http://localhost:5173/portfolio"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:24.213716", "url": "http://localhost:5173/settings"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:24.213844", "url": "http://localhost:5173/settings"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:24.219910", "url": "http://localhost:5173/settings"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:24.800280", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:24.800595", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:24.810284", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:25.461149", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:25.461334", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:25.479843", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:25.856828", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:25.873580", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:25.889456", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:25.904693", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:26.147947", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:26.148098", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:26.157798", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:26.687222", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:26.687488", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:26.687708", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:26.687904", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:27.273554", "url": "http://localhost:5173/portfolio"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:27.273684", "url": "http://localhost:5173/portfolio"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:27.281339", "url": "http://localhost:5173/portfolio"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:27.963132", "url": "http://localhost:5173/settings"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:27.963468", "url": "http://localhost:5173/settings"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:27.974633", "url": "http://localhost:5173/settings"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:28.680168", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:28.680593", "url": "http://localhost:5173/market"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:28.690868", "url": "http://localhost:5173/market"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:29.384241", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:29.384444", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:29.412686", "url": "http://localhost:5173/strategy"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.154072", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.154298", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.154514", "url": "http://localhost:5173/strategy/center"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.154767", "url": "http://localhost:5173/strategy/center"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:30.255750", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:30.266361", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:30.270468", "url": "http://localhost:5173/trading"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.985135", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.985439", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.985650", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "timestamp": "2025-08-04T10:13:30.985847", "url": "http://localhost:5173/trading/terminal"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:31.417772", "url": "http://localhost:5173/portfolio"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:31.418111", "url": "http://localhost:5173/portfolio"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:31.457828", "url": "http://localhost:5173/portfolio"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:13:32.230023", "url": "http://localhost:5173/settings"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:32.230207", "url": "http://localhost:5173/settings"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:13:32.234073", "url": "http://localhost:5173/settings"}], "performance_data": {}, "user_experience_problems": [], "functional_bugs": [{"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:12:41.678069"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:12:41.678471"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:12:41.682248"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:12:41.695426"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:12:41.695729"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:12:44.996036"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:12:45.012459"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:12:45.029638"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:12:45.045934"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:12:45.047237"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:12:48.001177"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:12:48.001517"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:12:48.001836"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:12:48.002115"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:12:48.002340"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'pointerdown')", "timestamp": "2025-08-04T10:12:50.869677", "url": "http://localhost:5173/strategy/center"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:13:22.431556"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:13:22.431760"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:22.431901"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:13:22.432043"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:22.432186"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/ctp/status", "method": "GET", "timestamp": "2025-08-04T10:13:23.609115"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:23.609389"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:23.609624"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:13:23.609919"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:13:23.610177"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:13:23.610388"}, {"type": "request_failed", "url": "http://localhost:5173/node_modules/.vite/deps/element-plus_es_components_cascader_style_index.js?v=a2acd714", "method": "GET", "timestamp": "2025-08-04T10:13:24.788849"}, {"type": "request_failed", "url": "http://localhost:5173/node_modules/element-plus/theme-chalk/src/divider.scss", "method": "GET", "timestamp": "2025-08-04T10:13:24.789115"}, {"type": "request_failed", "url": "http://localhost:5173/node_modules/.vite/deps/element-plus_es_components_statistic_style_index.js?v=b96b329f", "method": "GET", "timestamp": "2025-08-04T10:13:24.789392"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:25.450386"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:13:25.450692"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:25.450950"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:13:25.451287"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:13:25.451541"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:13:27.249500"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:27.250000"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:13:27.250265"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:27.250597"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/ctp/status", "method": "GET", "timestamp": "2025-08-04T10:13:27.250980"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:13:27.251292"}, {"type": "request_failed", "url": "http://localhost:5173/node_modules/.vite/deps/element-plus_es_components_statistic_style_index.js?v=b96b329f", "method": "GET", "timestamp": "2025-08-04T10:13:28.667848"}, {"type": "request_failed", "url": "http://localhost:5173/node_modules/.vite/deps/element-plus_es_components_cascader_style_index.js?v=a2acd714", "method": "GET", "timestamp": "2025-08-04T10:13:28.668075"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:29.363600"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:13:29.363958"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:13:29.364201"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:13:29.364452"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:29.364681"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:31.390123"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:13:31.390633"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:13:31.390921"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:13:31.391199"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:13:31.391406"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/ctp/status", "method": "GET", "timestamp": "2025-08-04T10:13:31.391590"}], "ui_inconsistencies": [], "analysis": {"summary": {"total_scenarios": 5, "successful_scenarios": 1, "success_rate": 0.2, "total_issues": 181, "functional_bugs": 54, "ux_issues": 2}, "critical_issues": [], "recommendations": ["发现多个功能性问题，建议进行代码审查和测试", "场景成功率较低，建议优化核心用户流程"]}}