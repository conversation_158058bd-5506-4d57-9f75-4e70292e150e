"""
测试数据夹具和生成器
提供各种测试场景所需的数据生成工具
"""

import random
import string
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
import pytest
import asyncio
from unittest.mock import Mock

from app.models.user import User
from app.models.trading import Order, Position, Trade
from app.schemas.market_data import TickData, KlineData, MarketDepth


@dataclass
class TestDataConfig:
    """测试数据配置"""
    random_seed: int = 42
    base_price: float = 100.0
    price_volatility: float = 0.1
    volume_range: tuple = (1000, 100000)
    symbols: List[str] = field(default_factory=lambda: ["000001", "000002", "000300", "600000", "600036"])


class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, config: TestDataConfig = None):
        self.config = config or TestDataConfig()
        random.seed(self.config.random_seed)
    
    def generate_random_string(self, length: int = 10, chars: str = None) -> str:
        """生成随机字符串"""
        if chars is None:
            chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def generate_random_email(self) -> str:
        """生成随机邮箱"""
        username = self.generate_random_string(8)
        domain = random.choice(["example.com", "test.com", "demo.org"])
        return f"{username}@{domain}"
    
    def generate_random_phone(self) -> str:
        """生成随机手机号"""
        return f"1{random.randint(3000000000, 8999999999)}"
    
    def generate_mock_user(self, **kwargs) -> Mock:
        """生成模拟用户对象"""
        default_data = {
            "id": self.generate_random_string(16),
            "username": f"testuser_{self.generate_random_string(6)}",
            "email": self.generate_random_email(),
            "phone": self.generate_random_phone(),
            "is_active": True,
            "is_verified": True,
            "created_at": datetime.now() - timedelta(days=random.randint(1, 365)),
            "updated_at": datetime.now(),
            "role": "user",
            "permissions": ["trading:read", "trading:write", "market_data:read"]
        }
        default_data.update(kwargs)
        
        user = Mock()
        for key, value in default_data.items():
            setattr(user, key, value)
        
        return user
    
    def generate_multiple_users(self, count: int = 10) -> List[Mock]:
        """生成多个用户"""
        return [self.generate_mock_user() for _ in range(count)]
    
    def generate_mock_order(self, user_id: str = None, **kwargs) -> Mock:
        """生成模拟订单对象"""
        default_data = {
            "id": f"order_{self.generate_random_string(12)}",
            "user_id": user_id or f"user_{self.generate_random_string(8)}",
            "symbol": random.choice(self.config.symbols),
            "side": random.choice(["buy", "sell"]),
            "order_type": random.choice(["limit", "market", "stop"]),
            "quantity": random.randint(100, 10000),
            "price": Decimal(str(round(self.config.base_price * (1 + random.uniform(-0.1, 0.1)), 2))),
            "filled_quantity": 0,
            "status": random.choice(["pending", "partial_filled", "filled", "cancelled"]),
            "time_in_force": "GTC",
            "created_at": datetime.now() - timedelta(minutes=random.randint(0, 1440)),
            "updated_at": datetime.now(),
            "commission": Decimal("0.00"),
            "notes": ""
        }
        default_data.update(kwargs)
        
        order = Mock()
        for key, value in default_data.items():
            setattr(order, key, value)
        
        # 为已成交订单设置成交数量
        if order.status in ["filled", "partial_filled"]:
            if order.status == "filled":
                order.filled_quantity = order.quantity
            else:
                order.filled_quantity = random.randint(1, order.quantity - 1)
            
            # 计算手续费
            order.commission = Decimal(str(float(order.price) * order.filled_quantity * 0.0005))
        
        return order
    
    def generate_order_sequence(self, user_id: str, count: int = 5) -> List[Mock]:
        """生成订单序列（模拟真实交易流程）"""
        orders = []
        current_time = datetime.now() - timedelta(hours=2)
        
        for i in range(count):
            # 生成有逻辑关联的订单
            if i == 0:
                # 第一个订单：买入
                order = self.generate_mock_order(
                    user_id=user_id,
                    side="buy",
                    status="filled",
                    created_at=current_time
                )
            elif i == count - 1:
                # 最后一个订单：卖出
                order = self.generate_mock_order(
                    user_id=user_id,
                    side="sell",
                    status="pending",
                    created_at=current_time
                )
            else:
                # 中间订单：随机
                order = self.generate_mock_order(
                    user_id=user_id,
                    created_at=current_time
                )
            
            orders.append(order)
            current_time += timedelta(minutes=random.randint(5, 60))
        
        return orders
    
    def generate_mock_position(self, user_id: str = None, **kwargs) -> Mock:
        """生成模拟持仓对象"""
        symbol = kwargs.get("symbol", random.choice(self.config.symbols))
        quantity = kwargs.get("quantity", random.randint(100, 5000))
        avg_price = kwargs.get("average_price", Decimal(str(round(self.config.base_price, 2))))
        current_price = Decimal(str(round(float(avg_price) * (1 + random.uniform(-0.05, 0.05)), 2)))
        
        default_data = {
            "id": f"pos_{self.generate_random_string(12)}",
            "user_id": user_id or f"user_{self.generate_random_string(8)}",
            "symbol": symbol,
            "quantity": quantity,
            "available_quantity": quantity,
            "average_price": avg_price,
            "market_price": current_price,
            "market_value": current_price * quantity,
            "unrealized_pnl": (current_price - avg_price) * quantity,
            "realized_pnl": Decimal("0.00"),
            "created_at": datetime.now() - timedelta(days=random.randint(1, 30)),
            "updated_at": datetime.now()
        }
        default_data.update(kwargs)
        
        position = Mock()
        for key, value in default_data.items():
            setattr(position, key, value)
        
        return position
    
    def generate_portfolio_positions(self, user_id: str, count: int = 5) -> List[Mock]:
        """生成投资组合持仓"""
        positions = []
        
        for _ in range(count):
            position = self.generate_mock_position(user_id=user_id)
            positions.append(position)
        
        return positions  
    
    def generate_mock_trade(self, order_id: str = None, user_id: str = None, **kwargs) -> Mock:
        """生成模拟成交记录"""
        default_data = {
            "id": f"trade_{self.generate_random_string(12)}",
            "order_id": order_id or f"order_{self.generate_random_string(12)}",
            "user_id": user_id or f"user_{self.generate_random_string(8)}",
            "symbol": random.choice(self.config.symbols),
            "side": random.choice(["buy", "sell"]),
            "quantity": random.randint(100, 1000),
            "price": Decimal(str(round(self.config.base_price * (1 + random.uniform(-0.05, 0.05)), 2))),
            "amount": Decimal("0.00"),  # 将在后面计算
            "commission": Decimal("0.00"),  # 将在后面计算
            "trade_time": datetime.now() - timedelta(minutes=random.randint(0, 1440)),
            "counter_party": f"counterparty_{self.generate_random_string(8)}"
        }
        default_data.update(kwargs)
        
        trade = Mock()
        for key, value in default_data.items():
            setattr(trade, key, value)
        
        # 计算金额和手续费
        trade.amount = trade.price * trade.quantity
        trade.commission = trade.amount * Decimal("0.0005")
        
        return trade
    
    def generate_trading_history(self, user_id: str, days: int = 30, trades_per_day: int = 3) -> List[Mock]:
        """生成交易历史"""
        trades = []
        
        for day in range(days):
            trade_date = datetime.now() - timedelta(days=day)
            daily_trades = random.randint(0, trades_per_day * 2)
            
            for _ in range(daily_trades):
                trade_time = trade_date.replace(
                    hour=random.randint(9, 15),
                    minute=random.randint(0, 59),
                    second=random.randint(0, 59)
                )
                
                trade = self.generate_mock_trade(
                    user_id=user_id,
                    trade_time=trade_time
                )
                trades.append(trade)
        
        return sorted(trades, key=lambda x: x.trade_time, reverse=True)


class MarketDataGenerator:
    """市场数据生成器"""
    
    def __init__(self, config: TestDataConfig = None):
        self.config = config or TestDataConfig()
        random.seed(self.config.random_seed)
    
    def generate_tick_data(self, symbol: str = None, **kwargs) -> TickData:
        """生成Tick数据"""
        symbol = symbol or random.choice(self.config.symbols)
        base_price = self.config.base_price
        
        # 生成价格波动
        price_change = random.uniform(-self.config.price_volatility, self.config.price_volatility)
        price = Decimal(str(round(base_price * (1 + price_change), 2)))
        
        default_data = {
            "symbol": symbol,
            "timestamp": datetime.now(),
            "price": price,
            "volume": random.randint(*self.config.volume_range),
            "bid_price": price - Decimal("0.01"),
            "ask_price": price + Decimal("0.01"),
            "bid_volume": random.randint(1000, 5000),
            "ask_volume": random.randint(1000, 5000),
            "open_price": price * Decimal("0.998"),
            "high_price": price * Decimal("1.005"),
            "low_price": price * Decimal("0.995"),
            "prev_close": price * Decimal("0.999")
        }
        default_data.update(kwargs)
        
        return TickData(**default_data)
    
    def generate_kline_data(self, symbol: str = None, interval: str = "1m", **kwargs) -> KlineData:
        """生成K线数据"""
        symbol = symbol or random.choice(self.config.symbols)
        base_price = self.config.base_price
        
        # 生成OHLC价格
        open_price = Decimal(str(round(base_price, 2)))
        close_change = random.uniform(-0.02, 0.02)
        close_price = open_price * Decimal(str(1 + close_change))
        
        high_price = max(open_price, close_price) * Decimal(str(1 + random.uniform(0, 0.01)))
        low_price = min(open_price, close_price) * Decimal(str(1 - random.uniform(0, 0.01)))
        
        volume = random.randint(*self.config.volume_range)
        amount = (open_price + close_price) / 2 * volume
        
        default_data = {
            "symbol": symbol,
            "timestamp": datetime.now(),
            "interval": interval,
            "open_price": open_price,
            "high_price": high_price,
            "low_price": low_price,
            "close_price": close_price,
            "volume": volume,
            "amount": amount
        }
        default_data.update(kwargs)
        
        return KlineData(**default_data)
    
    def generate_kline_series(
        self, 
        symbol: str = None, 
        interval: str = "1m", 
        count: int = 100,
        start_time: datetime = None
    ) -> List[KlineData]:
        """生成K线序列"""
        symbol = symbol or random.choice(self.config.symbols)
        start_time = start_time or (datetime.now() - timedelta(hours=count))
        
        klines = []
        current_price = self.config.base_price
        
        for i in range(count):
            # 计算时间间隔
            if interval == "1m":
                timestamp = start_time + timedelta(minutes=i)
            elif interval == "5m":
                timestamp = start_time + timedelta(minutes=i * 5)
            elif interval == "1h":
                timestamp = start_time + timedelta(hours=i)
            elif interval == "1d":
                timestamp = start_time + timedelta(days=i)
            else:
                timestamp = start_time + timedelta(minutes=i)
            
            # 价格随机游走
            price_change = random.uniform(-0.01, 0.01)
            current_price = current_price * (1 + price_change)
            
            kline = self.generate_kline_data(
                symbol=symbol,
                interval=interval,
                timestamp=timestamp,
                open_price=Decimal(str(round(current_price * (1 + random.uniform(-0.005, 0.005)), 2))),
                close_price=Decimal(str(round(current_price, 2)))
            )
            klines.append(kline)
        
        return klines
    
    def generate_market_depth(self, symbol: str = None, levels: int = 10, **kwargs) -> MarketDepth:
        """生成市场深度数据"""
        symbol = symbol or random.choice(self.config.symbols)
        base_price = self.config.base_price
        
        # 生成买卖盘数据
        bids = []
        asks = []
        
        for i in range(levels):
            # 买盘价格递减
            bid_price = Decimal(str(round(base_price - (i + 1) * 0.01, 2)))
            bid_volume = Decimal(str(random.randint(100, 5000)))
            bids.append([bid_price, bid_volume])
            
            # 卖盘价格递增
            ask_price = Decimal(str(round(base_price + (i + 1) * 0.01, 2)))
            ask_volume = Decimal(str(random.randint(100, 5000)))
            asks.append([ask_price, ask_volume])
        
        default_data = {
            "symbol": symbol,
            "timestamp": datetime.now(),
            "bids": bids,
            "asks": asks
        }
        default_data.update(kwargs)
        
        return MarketDepth(**default_data)
    
    def generate_market_statistics(self) -> Dict[str, Any]:
        """生成市场统计数据"""
        return {
            "total_symbols": random.randint(4000, 6000),
            "active_symbols": random.randint(3500, 5500),
            "up_count": random.randint(1500, 2500),
            "down_count": random.randint(1200, 2000),
            "unchanged_count": random.randint(200, 500),
            "limit_up_count": random.randint(50, 150),
            "limit_down_count": random.randint(30, 100),
            "suspension_count": random.randint(100, 300),
            "total_volume": str(random.randint(100000000000, 500000000000)),
            "total_amount": f"{random.randint(800000000000, 2000000000000)}.00",
            "update_time": datetime.now().isoformat()
        }


class PerformanceDataGenerator:
    """性能测试数据生成器"""
    
    def __init__(self, config: TestDataConfig = None):
        self.config = config or TestDataConfig()
    
    def generate_large_order_batch(self, user_id: str, count: int = 10000) -> List[Dict[str, Any]]:
        """生成大批量订单数据"""
        orders = []
        generator = TestDataGenerator(self.config)
        
        for _ in range(count):
            order_data = {
                "symbol": random.choice(self.config.symbols),
                "side": random.choice(["buy", "sell"]),
                "order_type": random.choice(["limit", "market"]),
                "quantity": random.randint(100, 1000),
                "price": f"{random.uniform(50.0, 150.0):.2f}",
                "time_in_force": "GTC"
            }
            orders.append(order_data)
        
        return orders
    
    def generate_concurrent_users_data(self, user_count: int = 1000) -> List[Dict[str, Any]]:
        """生成并发用户数据"""
        users = []
        generator = TestDataGenerator(self.config)
        
        for i in range(user_count):
            user_data = {
                "id": f"perf_user_{i:06d}",
                "username": f"perfuser{i:06d}",
                "email": f"perfuser{i:06d}@example.com",
                "password": "TestPass123!",
                "is_active": True
            }
            users.append(user_data)
        
        return users
    
    def generate_market_data_stream(self, symbols: List[str] = None, duration: int = 3600) -> List[TickData]:
        """生成市场数据流"""
        symbols = symbols or self.config.symbols
        market_generator = MarketDataGenerator(self.config)
        
        ticks = []
        start_time = datetime.now()
        
        # 每秒生成多个tick
        for second in range(duration):
            timestamp = start_time + timedelta(seconds=second)
            
            # 每个符号每秒生成1-5个tick
            for symbol in symbols:
                tick_count = random.randint(1, 5)
                for tick_num in range(tick_count):
                    tick_time = timestamp + timedelta(microseconds=tick_num * 200000)
                    tick = market_generator.generate_tick_data(
                        symbol=symbol,
                        timestamp=tick_time
                    )
                    ticks.append(tick)
        
        return ticks


# Pytest夹具定义

@pytest.fixture
def test_data_config():
    """测试数据配置夹具"""
    return TestDataConfig()


@pytest.fixture
def data_generator(test_data_config):
    """数据生成器夹具"""
    return TestDataGenerator(test_data_config)


@pytest.fixture
def market_data_generator(test_data_config):
    """市场数据生成器夹具"""
    return MarketDataGenerator(test_data_config)


@pytest.fixture
def performance_data_generator(test_data_config):
    """性能数据生成器夹具"""
    return PerformanceDataGenerator(test_data_config)


@pytest.fixture
def mock_user(data_generator):
    """单个模拟用户夹具"""
    return data_generator.generate_mock_user()


@pytest.fixture
def mock_users(data_generator):
    """多个模拟用户夹具"""
    return data_generator.generate_multiple_users(10)


@pytest.fixture
def mock_orders(data_generator, mock_user):
    """模拟订单列表夹具"""
    return data_generator.generate_order_sequence(mock_user.id, 5)


@pytest.fixture
def mock_positions(data_generator, mock_user):
    """模拟持仓列表夹具"""
    return data_generator.generate_portfolio_positions(mock_user.id, 3)


@pytest.fixture
def mock_trades(data_generator, mock_user):
    """模拟交易历史夹具"""
    return data_generator.generate_trading_history(mock_user.id, 7, 2)


@pytest.fixture
def sample_tick_data(market_data_generator):
    """示例Tick数据夹具"""
    return market_data_generator.generate_tick_data()


@pytest.fixture
def sample_kline_data(market_data_generator):
    """示例K线数据夹具"""
    return market_data_generator.generate_kline_data()


@pytest.fixture
def kline_series(market_data_generator):
    """K线序列夹具"""
    return market_data_generator.generate_kline_series(count=50)


@pytest.fixture
def market_depth_data(market_data_generator):
    """市场深度数据夹具"""
    return market_data_generator.generate_market_depth()


@pytest.fixture
def market_statistics(market_data_generator):
    """市场统计数据夹具"""
    return market_data_generator.generate_market_statistics()


# 清理夹具

@pytest.fixture
async def cleanup_test_data():
    """测试数据清理夹具"""
    # 测试前的准备工作
    yield
    
    # 测试后的清理工作
    # 这里可以添加清理数据库、缓存等的逻辑
    pass


# 性能测试夹具

@pytest.fixture
def large_order_batch(performance_data_generator):
    """大批量订单数据夹具"""
    return performance_data_generator.generate_large_order_batch("perf_user", 1000)


@pytest.fixture  
def concurrent_users_data(performance_data_generator):
    """并发用户数据夹具"""
    return performance_data_generator.generate_concurrent_users_data(100)


@pytest.fixture
def market_data_stream(performance_data_generator):
    """市场数据流夹具"""
    return performance_data_generator.generate_market_data_stream(duration=60)


# 辅助函数

def create_test_database_data(data_generator: TestDataGenerator, user_count: int = 50):
    """创建测试数据库数据"""
    # 这个函数可以用于在数据库中创建测试数据
    # 实际使用时需要根据具体的数据库模型来实现
    
    users = data_generator.generate_multiple_users(user_count)
    orders = []
    positions = []
    trades = []
    
    for user in users:
        user_orders = data_generator.generate_order_sequence(user.id, 10)
        user_positions = data_generator.generate_portfolio_positions(user.id, 5)
        user_trades = data_generator.generate_trading_history(user.id, 30, 2)
        
        orders.extend(user_orders)
        positions.extend(user_positions)
        trades.extend(user_trades)
    
    return {
        "users": users,
        "orders": orders,
        "positions": positions,
        "trades": trades
    }


def validate_test_data_integrity(data: Dict[str, List]) -> bool:
    """验证测试数据完整性"""
    # 检查数据关联性
    user_ids = {user.id for user in data["users"]}
    order_user_ids = {order.user_id for order in data["orders"]}
    position_user_ids = {position.user_id for position in data["positions"]}
    trade_user_ids = {trade.user_id for trade in data["trades"]}
    
    # 所有订单、持仓、交易都应该有对应的用户
    assert order_user_ids.issubset(user_ids), "存在无对应用户的订单"
    assert position_user_ids.issubset(user_ids), "存在无对应用户的持仓"
    assert trade_user_ids.issubset(user_ids), "存在无对应用户的交易"
    
    return True


if __name__ == "__main__":
    """测试数据生成器使用示例"""
    
    # 创建配置和生成器
    config = TestDataConfig()
    data_gen = TestDataGenerator(config)
    market_gen = MarketDataGenerator(config)
    perf_gen = PerformanceDataGenerator(config)
    
    print("=" * 50)
    print("测试数据生成器示例")
    print("=" * 50)
    
    # 生成用户数据
    print("\n1. 生成用户数据")
    users = data_gen.generate_multiple_users(5)
    for user in users:
        print(f"  用户: {user.username} ({user.email})")
    
    # 生成订单数据
    print("\n2. 生成订单数据")
    orders = data_gen.generate_order_sequence(users[0].id, 3)
    for order in orders:
        print(f"  订单: {order.id} - {order.symbol} {order.side} {order.quantity}@{order.price}")
    
    # 生成市场数据
    print("\n3. 生成市场数据")
    tick = market_gen.generate_tick_data("000001")
    print(f"  Tick: {tick.symbol} {tick.price} 成交量:{tick.volume}")
    
    klines = market_gen.generate_kline_series("000001", "1m", 3)
    print(f"  K线: 生成了{len(klines)}根1分钟K线")
    
    depth = market_gen.generate_market_depth("000001", 5)
    print(f"  深度: {depth.symbol} 买盘:{len(depth.bids)}档 卖盘:{len(depth.asks)}档")
    
    # 生成性能测试数据
    print("\n4. 生成性能测试数据")
    large_orders = perf_gen.generate_large_order_batch("perf_user", 1000)
    print(f"  大批量订单: {len(large_orders)}个")
    
    concurrent_users = perf_gen.generate_concurrent_users_data(100)
    print(f"  并发用户: {len(concurrent_users)}个")
    
    print("\n" + "=" * 50)
    print("数据生成完成!")