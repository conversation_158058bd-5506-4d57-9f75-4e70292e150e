"""
认证API集成测试
"""

import pytest
from httpx import AsyncClient
from unittest.mock import patch, Mock

from app.core.security import create_access_token


@pytest.mark.integration
@pytest.mark.auth
@pytest.mark.asyncio
class TestAuthAPI:
    """认证API测试类"""

    async def test_login_success(self, client: AsyncClient):
        """测试登录成功"""
        # 准备测试数据
        login_data = {"username": "testuser", "password": "testpassword123"}

        # 模拟用户认证成功
        with patch("app.core.auth.authenticate_user") as mock_auth:
            from datetime import datetime
            from app.schemas.user import UserRole

            mock_user = Mock()
            mock_user.id = 123
            mock_user.username = "testuser"
            mock_user.email = "<EMAIL>"
            mock_user.full_name = "Test User"
            mock_user.phone = "13800138000"
            mock_user.role = UserRole.ADMIN
            mock_user.is_active = True
            mock_user.created_at = datetime.now()
            mock_user.updated_at = datetime.now()
            mock_user.last_login = datetime.now()
            mock_user.login_count = 5
            mock_auth.return_value = mock_user

            # 发送请求
            response = await client.post("/api/v1/auth/login", json=login_data)

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["id"] == 123
        assert data["user"]["username"] == "testuser"

    async def test_login_invalid_credentials(self, client: AsyncClient):
        """测试登录凭据无效"""
        # 准备测试数据
        login_data = {"username": "testuser", "password": "wrongpassword"}

        # 模拟用户认证失败
        with patch("app.core.auth.authenticate_user", return_value=None):
            # 发送请求
            response = await client.post("/api/v1/auth/login", json=login_data)

        # 验证响应
        assert response.status_code == 401
        data = response.json()
        # 检查错误信息（可能在detail或message字段中）
        error_message = data.get("detail") or data.get("message", "")
        assert "用户名或密码错误" in error_message

    async def test_login_missing_fields(self, client: AsyncClient):
        """测试登录缺少必填字段"""
        # 准备不完整的测试数据
        login_data = {
            "username": "testuser"
            # 缺少password字段
        }

        # 发送请求
        response = await client.post("/api/v1/auth/login", json=login_data)

        # 验证响应
        assert response.status_code == 422  # Validation error

    async def test_register_success(self, client: AsyncClient):
        """测试注册成功"""
        # 准备测试数据
        register_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "Password123",
            "confirm_password": "Password123",
            "full_name": "New User",
        }

        # 模拟用户创建成功
        with patch(
            "app.services.auth_service.AuthService.create_user"
        ) as mock_create, patch(
            "app.services.auth_service.AuthService.get_user_by_username",
            return_value=None,
        ), patch(
            "app.services.auth_service.AuthService.get_user_by_email", return_value=None
        ), patch(
            "app.services.email_service.EmailService.send_welcome_email"
        ):
            from datetime import datetime
            from app.schemas.user import UserRole

            mock_user = Mock()
            mock_user.id = 456
            mock_user.username = "newuser"
            mock_user.email = "<EMAIL>"
            mock_user.full_name = "New User"
            mock_user.phone = None
            mock_user.role = UserRole.VIEWER
            mock_user.is_active = True
            mock_user.created_at = datetime.now()
            mock_user.updated_at = datetime.now()
            mock_user.last_login = None
            mock_user.login_count = 0
            mock_create.return_value = mock_user

            # 发送请求
            response = await client.post("/api/v1/auth/register", json=register_data)

        # 验证响应
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert "id" in data

    async def test_register_duplicate_username(self, client: AsyncClient):
        """测试注册重复用户名"""
        # 准备测试数据
        register_data = {
            "username": "existinguser",
            "email": "<EMAIL>",
            "password": "Password123",
            "confirm_password": "Password123",
            "full_name": "Existing User",
        }

        # 模拟用户名已存在
        existing_user = Mock()
        existing_user.username = "existinguser"
        with patch(
            "app.services.auth_service.AuthService.get_user_by_username",
            return_value=existing_user,
        ):
            # 发送请求
            response = await client.post("/api/v1/auth/register", json=register_data)

        # 验证响应
        assert response.status_code == 400
        data = response.json()
        # 检查错误信息（可能在detail或message字段中）
        error_message = data.get("detail") or data.get("message", "")
        assert "用户名已存在" in error_message

    async def test_register_invalid_email(self, client: AsyncClient):
        """测试注册无效邮箱"""
        # 准备测试数据
        register_data = {
            "username": "testuser",
            "email": "invalid-email",  # 无效邮箱格式
            "password": "Password123",
            "confirm_password": "Password123",
            "full_name": "Test User",
        }

        # 发送请求
        response = await client.post("/api/v1/auth/register", json=register_data)

        # 验证响应
        assert response.status_code == 422  # Validation error

    async def test_register_weak_password(self, client: AsyncClient):
        """测试注册弱密码"""
        # 准备测试数据
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "123",  # 弱密码
            "confirm_password": "123",
            "full_name": "Test User",
        }

        # 发送请求
        response = await client.post("/api/v1/auth/register", json=register_data)

        # 验证响应
        assert response.status_code == 422  # Validation error

    async def test_get_current_user_success(self, client: AsyncClient):
        """测试获取当前用户信息成功"""
        from datetime import datetime
        from app.schemas.user import UserRole
        from app.core.dependencies import get_current_active_user
        from app.main import app

        # 创建模拟用户
        mock_user = Mock()
        mock_user.id = 789
        mock_user.username = "testuser"
        mock_user.email = "<EMAIL>"
        mock_user.full_name = "Test User"
        mock_user.phone = "13800138000"
        mock_user.role = UserRole.ADMIN
        mock_user.is_active = True
        mock_user.created_at = datetime.now()
        mock_user.updated_at = datetime.now()
        mock_user.last_login = datetime.now()
        mock_user.login_count = 5

        # 覆盖依赖
        def override_get_current_user():
            return mock_user

        app.dependency_overrides[get_current_active_user] = override_get_current_user

        try:
            # 发送请求
            response = await client.get("/api/v1/auth/me")

            # 验证响应
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == 789
            assert data["username"] == "testuser"
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.pop(get_current_active_user, None)

    async def test_get_current_user_unauthorized(self, client: AsyncClient):
        """测试未授权获取当前用户信息"""
        # 发送请求（不带token）
        response = await client.get("/api/v1/auth/me")

        # 验证响应
        assert response.status_code == 403

    async def test_get_current_user_invalid_token(self, client: AsyncClient):
        """测试无效token获取当前用户信息"""
        # 使用无效token
        headers = {"Authorization": "Bearer invalid-token"}

        # 发送请求
        response = await client.get("/api/v1/auth/me", headers=headers)

        # 验证响应
        assert response.status_code == 401

    async def test_refresh_token_success(self, client: AsyncClient):
        """测试刷新token成功"""
        from datetime import timedelta, datetime
        from app.core.security import create_access_token
        from app.schemas.user import UserRole

        # 创建一个有效的刷新token
        refresh_token = create_access_token(
            subject=789, expires_delta=timedelta(days=7)
        )

        # 准备请求数据
        refresh_data = {"refresh_token": refresh_token}

        # Mock token验证和用户查询
        with patch("app.api.v1.auth.is_token_blacklisted") as mock_blacklist, patch(
            "app.core.security.SecurityManager.verify_token"
        ) as mock_verify_token, patch(
            "app.services.auth_service.AuthService.get_user_by_id"
        ) as mock_get_user:

            # Mock token不在黑名单中
            mock_blacklist.return_value = False

            # Mock token验证返回有效payload
            mock_verify_token.return_value = {"sub": "789"}

            # Mock用户查询
            mock_user = Mock()
            mock_user.id = 789
            mock_user.username = "testuser"
            mock_user.email = "<EMAIL>"
            mock_user.full_name = "Test User"
            mock_user.phone = "13800138000"
            mock_user.role = UserRole.ADMIN
            mock_user.is_active = True
            mock_user.created_at = datetime.now()
            mock_user.updated_at = datetime.now()
            mock_user.last_login = datetime.now()
            mock_user.login_count = 5
            mock_get_user.return_value = mock_user

            # 发送请求
            response = await client.post("/api/v1/auth/refresh", json=refresh_data)

            # 验证响应
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data
            assert data["token_type"] == "bearer"

    async def test_logout_success(self, client: AsyncClient):
        """测试登出成功"""
        from datetime import datetime
        from app.schemas.user import UserRole
        from app.core.dependencies import get_current_user
        from app.main import app

        # 创建模拟用户
        mock_user = Mock()
        mock_user.id = 789
        mock_user.username = "testuser"
        mock_user.email = "<EMAIL>"
        mock_user.full_name = "Test User"
        mock_user.phone = "13800138000"
        mock_user.role = UserRole.ADMIN
        mock_user.is_active = True
        mock_user.created_at = datetime.now()
        mock_user.updated_at = datetime.now()
        mock_user.last_login = datetime.now()
        mock_user.login_count = 5

        # 覆盖依赖
        def override_get_current_user():
            return mock_user

        app.dependency_overrides[get_current_user] = override_get_current_user

        try:
            # 发送请求
            response = await client.post("/api/v1/auth/logout")

            # 验证响应
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "登出成功"
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.pop(get_current_user, None)

    async def test_change_password_success(self, client: AsyncClient):
        """测试修改密码成功"""
        from datetime import datetime
        from app.schemas.user import UserRole
        from app.core.dependencies import get_current_active_user
        from app.main import app

        # 准备测试数据
        password_data = {
            "current_password": "oldpassword123",
            "new_password": "newpassword123",
            "confirm_new_password": "newpassword123",
        }

        # 创建模拟用户
        mock_user = Mock()
        mock_user.id = 789
        mock_user.username = "testuser"
        mock_user.email = "<EMAIL>"
        mock_user.full_name = "Test User"
        mock_user.phone = "13800138000"
        mock_user.role = UserRole.ADMIN
        mock_user.is_active = True
        mock_user.hashed_password = "hashed_old_password"
        mock_user.created_at = datetime.now()
        mock_user.updated_at = datetime.now()
        mock_user.last_login = datetime.now()
        mock_user.login_count = 5

        # 覆盖依赖
        def override_get_current_user():
            return mock_user

        app.dependency_overrides[get_current_active_user] = override_get_current_user

        try:
            # Mock密码验证和更新
            with patch("app.core.security.verify_password", return_value=True), patch(
                "app.services.auth_service.AuthService.change_password"
            ) as mock_change:

                mock_change.return_value = {"message": "密码修改成功"}

                # 发送请求
                response = await client.post(
                    "/api/v1/auth/change-password", json=password_data
                )

                # 验证响应
                assert response.status_code == 200
                data = response.json()
                assert data["message"] == "密码修改成功"
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.pop(get_current_active_user, None)

    async def test_change_password_wrong_current(self, client: AsyncClient):
        """测试修改密码当前密码错误"""
        from datetime import datetime
        from app.schemas.user import UserRole
        from app.core.dependencies import get_current_active_user
        from app.main import app

        # 准备测试数据
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "newpassword123",
            "confirm_new_password": "newpassword123",
        }

        # 创建模拟用户
        mock_user = Mock()
        mock_user.id = 789
        mock_user.username = "testuser"
        mock_user.email = "<EMAIL>"
        mock_user.full_name = "Test User"
        mock_user.phone = "13800138000"
        mock_user.role = UserRole.ADMIN
        mock_user.is_active = True
        mock_user.hashed_password = "hashed_old_password"
        mock_user.created_at = datetime.now()
        mock_user.updated_at = datetime.now()
        mock_user.last_login = datetime.now()
        mock_user.login_count = 5

        # 覆盖依赖
        def override_get_current_user():
            return mock_user

        app.dependency_overrides[get_current_active_user] = override_get_current_user

        try:
            # Mock密码验证失败
            with patch("app.core.security.verify_password", return_value=False):
                # 发送请求
                response = await client.post(
                    "/api/v1/auth/change-password", json=password_data
                )

                # 验证响应
                assert response.status_code == 400
                data = response.json()
                assert "当前密码错误" in data["detail"]
        finally:
            # 清理依赖覆盖
            app.dependency_overrides.pop(get_current_active_user, None)
