"""
参数优化API路由
提供策略参数优化、网格搜索、遗传算法等功能
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies_fixed import get_current_active_user
from app.db.models.user import User
from app.schemas.backtest import BacktestCreate
from app.services.backtest_service import BacktestService
from app.services.strategy_service import StrategyService

router = APIRouter(prefix="/optimization", tags=["参数优化"])


# 优化任务存储（实际应用中应该使用数据库）
optimization_tasks = {}


class OptimizationTask:
    """优化任务"""
    
    def __init__(self, task_id: str, strategy_id: str, config: dict):
        self.task_id = task_id
        self.strategy_id = strategy_id
        self.config = config
        self.status = "pending"
        self.progress = 0
        self.results = []
        self.best_params = None
        self.best_score = None
        self.created_at = datetime.now()
        self.completed_at = None
        self.error_message = None


@router.post("/grid-search", summary="网格搜索优化")
async def grid_search_optimization(
    strategy_id: UUID,
    param_ranges: Dict[str, List[Any]] = None,
    optimization_target: str = Query("sharpe_ratio", description="优化目标"),
    backtest_config: dict = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    启动网格搜索参数优化
    
    - **strategy_id**: 策略ID
    - **param_ranges**: 参数范围，例如：{"short_period": [10, 20, 30], "long_period": [40, 50, 60]}
    - **optimization_target**: 优化目标（sharpe_ratio/total_return/max_drawdown）
    - **backtest_config**: 回测配置（开始日期、结束日期、初始资金等）
    """
    strategy_service = StrategyService(db)
    
    # 验证策略存在且属于当前用户
    strategy = await strategy_service.get_strategy_by_id(strategy_id)
    if not strategy or strategy.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 默认参数范围
    if not param_ranges:
        param_ranges = {
            "short_period": [10, 15, 20, 25, 30],
            "long_period": [40, 45, 50, 55, 60],
            "stop_loss": [0.02, 0.03, 0.05, 0.08, 0.1]
        }
    
    # 计算总组合数
    total_combinations = 1
    for param_values in param_ranges.values():
        total_combinations *= len(param_values)
    
    if total_combinations > 1000:
        raise HTTPException(
            status_code=400, 
            detail=f"参数组合数量过多（{total_combinations}），请减少参数范围"
        )
    
    # 创建优化任务
    task_id = str(uuid4())
    task = OptimizationTask(task_id, str(strategy_id), {
        "method": "grid_search",
        "param_ranges": param_ranges,
        "optimization_target": optimization_target,
        "backtest_config": backtest_config or {},
        "total_combinations": total_combinations
    })
    
    optimization_tasks[task_id] = task
    
    # 异步执行优化任务
    asyncio.create_task(run_grid_search(task, strategy, db, current_user.id))
    
    return {
        "task_id": task_id,
        "message": "网格搜索优化任务已启动",
        "total_combinations": total_combinations,
        "estimated_time": f"{total_combinations * 2} 秒"  # 假设每次回测2秒
    }


@router.post("/genetic-algorithm", summary="遗传算法优化")
async def genetic_algorithm_optimization(
    strategy_id: UUID,
    param_bounds: Dict[str, Dict[str, float]] = None,
    population_size: int = Query(50, description="种群大小"),
    generations: int = Query(20, description="进化代数"),
    optimization_target: str = Query("sharpe_ratio", description="优化目标"),
    backtest_config: dict = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    启动遗传算法参数优化
    
    - **strategy_id**: 策略ID
    - **param_bounds**: 参数边界，例如：{"short_period": {"min": 5, "max": 50}, "long_period": {"min": 20, "max": 200}}
    - **population_size**: 种群大小
    - **generations**: 进化代数
    - **optimization_target**: 优化目标
    - **backtest_config**: 回测配置
    """
    strategy_service = StrategyService(db)
    
    # 验证策略
    strategy = await strategy_service.get_strategy_by_id(strategy_id)
    if not strategy or strategy.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 默认参数边界
    if not param_bounds:
        param_bounds = {
            "short_period": {"min": 5, "max": 50, "type": "int"},
            "long_period": {"min": 20, "max": 200, "type": "int"},
            "stop_loss": {"min": 0.01, "max": 0.2, "type": "float"},
            "take_profit": {"min": 0.02, "max": 0.5, "type": "float"}
        }
    
    # 创建优化任务
    task_id = str(uuid4())
    task = OptimizationTask(task_id, str(strategy_id), {
        "method": "genetic_algorithm",
        "param_bounds": param_bounds,
        "population_size": population_size,
        "generations": generations,
        "optimization_target": optimization_target,
        "backtest_config": backtest_config or {}
    })
    
    optimization_tasks[task_id] = task
    
    # 异步执行优化任务
    asyncio.create_task(run_genetic_algorithm(task, strategy, db, current_user.id))
    
    return {
        "task_id": task_id,
        "message": "遗传算法优化任务已启动",
        "population_size": population_size,
        "generations": generations,
        "estimated_time": f"{population_size * generations * 1.5} 秒"
    }


@router.post("/bayesian", summary="贝叶斯优化")
async def bayesian_optimization(
    strategy_id: UUID,
    param_bounds: Dict[str, Dict[str, float]] = None,
    n_initial_points: int = Query(10, description="初始采样点数"),
    n_iterations: int = Query(50, description="优化迭代次数"),
    optimization_target: str = Query("sharpe_ratio", description="优化目标"),
    backtest_config: dict = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    启动贝叶斯优化
    
    - **strategy_id**: 策略ID
    - **param_bounds**: 参数边界
    - **n_initial_points**: 初始随机采样点数
    - **n_iterations**: 优化迭代次数
    - **optimization_target**: 优化目标
    - **backtest_config**: 回测配置
    """
    strategy_service = StrategyService(db)
    
    # 验证策略
    strategy = await strategy_service.get_strategy_by_id(strategy_id)
    if not strategy or strategy.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 默认参数边界
    if not param_bounds:
        param_bounds = {
            "short_period": {"min": 5, "max": 50, "type": "int"},
            "long_period": {"min": 20, "max": 200, "type": "int"},
            "position_size": {"min": 0.1, "max": 1.0, "type": "float"}
        }
    
    # 创建优化任务
    task_id = str(uuid4())
    task = OptimizationTask(task_id, str(strategy_id), {
        "method": "bayesian",
        "param_bounds": param_bounds,
        "n_initial_points": n_initial_points,
        "n_iterations": n_iterations,
        "optimization_target": optimization_target,
        "backtest_config": backtest_config or {}
    })
    
    optimization_tasks[task_id] = task
    
    # 异步执行优化任务
    asyncio.create_task(run_bayesian_optimization(task, strategy, db, current_user.id))
    
    return {
        "task_id": task_id,
        "message": "贝叶斯优化任务已启动",
        "initial_points": n_initial_points,
        "iterations": n_iterations,
        "estimated_time": f"{(n_initial_points + n_iterations) * 2} 秒"
    }


@router.get("/tasks/{task_id}", summary="获取优化任务状态")
async def get_optimization_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    获取优化任务的状态和结果
    
    - **task_id**: 任务ID
    """
    task = optimization_tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="优化任务不存在")
    
    return {
        "task_id": task.task_id,
        "strategy_id": task.strategy_id,
        "method": task.config["method"],
        "status": task.status,
        "progress": task.progress,
        "created_at": task.created_at.isoformat(),
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "best_params": task.best_params,
        "best_score": task.best_score,
        "total_evaluations": len(task.results),
        "error_message": task.error_message
    }


@router.get("/tasks/{task_id}/results", summary="获取优化结果详情")
async def get_optimization_results(
    task_id: str,
    limit: int = Query(100, description="返回结果数量"),
    sort_by: str = Query("score", description="排序字段"),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取优化任务的详细结果
    
    - **task_id**: 任务ID
    - **limit**: 返回结果数量
    - **sort_by**: 排序字段（score/time）
    """
    task = optimization_tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="优化任务不存在")
    
    # 排序结果
    sorted_results = sorted(
        task.results, 
        key=lambda x: x.get(sort_by, 0), 
        reverse=True
    )[:limit]
    
    return {
        "task_id": task.task_id,
        "total_results": len(task.results),
        "results": sorted_results,
        "best_params": task.best_params,
        "best_score": task.best_score,
        "optimization_target": task.config.get("optimization_target")
    }


@router.get("/tasks", summary="获取用户的优化任务列表")
async def list_optimization_tasks(
    status: Optional[str] = Query(None, description="任务状态筛选"),
    limit: int = Query(20, description="返回数量"),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取用户的所有优化任务列表
    
    - **status**: 任务状态筛选（pending/running/completed/failed）
    - **limit**: 返回数量
    """
    user_tasks = []
    
    for task in optimization_tasks.values():
        # 这里应该检查任务是否属于当前用户
        if status and task.status != status:
            continue
            
        user_tasks.append({
            "task_id": task.task_id,
            "strategy_id": task.strategy_id,
            "method": task.config["method"],
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "best_score": task.best_score
        })
    
    # 按创建时间倒序排序
    user_tasks.sort(key=lambda x: x["created_at"], reverse=True)
    
    return {
        "tasks": user_tasks[:limit],
        "total": len(user_tasks)
    }


@router.delete("/tasks/{task_id}", summary="取消优化任务")
async def cancel_optimization_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    取消正在运行的优化任务
    
    - **task_id**: 任务ID
    """
    task = optimization_tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="优化任务不存在")
    
    if task.status == "completed":
        raise HTTPException(status_code=400, detail="任务已完成，无法取消")
    
    task.status = "cancelled"
    task.completed_at = datetime.now()
    
    return {
        "task_id": task_id,
        "message": "优化任务已取消"
    }


# ========== 优化算法实现 ==========

async def run_grid_search(task: OptimizationTask, strategy, db, user_id: int):
    """执行网格搜索优化"""
    task.status = "running"
    
    try:
        param_ranges = task.config["param_ranges"]
        backtest_config = task.config["backtest_config"]
        
        # 生成所有参数组合
        param_combinations = generate_param_combinations(param_ranges)
        total = len(param_combinations)
        
        for i, params in enumerate(param_combinations):
            if task.status == "cancelled":
                break
            
            # 更新进度
            task.progress = int((i + 1) / total * 100)
            
            # 执行回测
            score = await simulate_backtest(strategy, params, backtest_config, task.config["optimization_target"])
            
            # 保存结果
            result = {
                "params": params,
                "score": score,
                "timestamp": datetime.now().isoformat()
            }
            task.results.append(result)
            
            # 更新最佳参数
            if task.best_score is None or score > task.best_score:
                task.best_score = score
                task.best_params = params
            
            # 模拟延迟
            await asyncio.sleep(0.1)
        
        task.status = "completed"
        task.completed_at = datetime.now()
        
    except Exception as e:
        task.status = "failed"
        task.error_message = str(e)
        task.completed_at = datetime.now()


async def run_genetic_algorithm(task: OptimizationTask, strategy, db, user_id: int):
    """执行遗传算法优化"""
    task.status = "running"
    
    try:
        param_bounds = task.config["param_bounds"]
        population_size = task.config["population_size"]
        generations = task.config["generations"]
        
        # 初始化种群
        population = initialize_population(param_bounds, population_size)
        
        for generation in range(generations):
            if task.status == "cancelled":
                break
            
            # 更新进度
            task.progress = int((generation + 1) / generations * 100)
            
            # 评估种群
            fitness_scores = []
            for individual in population:
                score = await simulate_backtest(
                    strategy, individual, 
                    task.config["backtest_config"], 
                    task.config["optimization_target"]
                )
                fitness_scores.append(score)
                
                # 保存结果
                result = {
                    "params": individual,
                    "score": score,
                    "generation": generation,
                    "timestamp": datetime.now().isoformat()
                }
                task.results.append(result)
            
            # 选择最佳个体
            best_idx = fitness_scores.index(max(fitness_scores))
            best_individual = population[best_idx]
            best_score = fitness_scores[best_idx]
            
            if task.best_score is None or best_score > task.best_score:
                task.best_score = best_score
                task.best_params = best_individual
            
            # 进化操作：选择、交叉、变异
            population = evolve_population(population, fitness_scores, param_bounds)
            
            await asyncio.sleep(0.1)
        
        task.status = "completed"
        task.completed_at = datetime.now()
        
    except Exception as e:
        task.status = "failed"
        task.error_message = str(e)
        task.completed_at = datetime.now()


async def run_bayesian_optimization(task: OptimizationTask, strategy, db, user_id: int):
    """执行贝叶斯优化"""
    task.status = "running"
    
    try:
        param_bounds = task.config["param_bounds"]
        n_initial_points = task.config["n_initial_points"]
        n_iterations = task.config["n_iterations"]
        
        # 初始随机采样
        for i in range(n_initial_points):
            if task.status == "cancelled":
                break
            
            params = sample_random_params(param_bounds)
            score = await simulate_backtest(
                strategy, params,
                task.config["backtest_config"],
                task.config["optimization_target"]
            )
            
            result = {
                "params": params,
                "score": score,
                "iteration": i,
                "type": "initial",
                "timestamp": datetime.now().isoformat()
            }
            task.results.append(result)
            
            if task.best_score is None or score > task.best_score:
                task.best_score = score
                task.best_params = params
            
            task.progress = int((i + 1) / (n_initial_points + n_iterations) * 100)
        
        # 贝叶斯优化迭代
        for i in range(n_iterations):
            if task.status == "cancelled":
                break
            
            # 基于历史结果选择下一个采样点（简化实现）
            params = suggest_next_params(task.results, param_bounds)
            score = await simulate_backtest(
                strategy, params,
                task.config["backtest_config"],
                task.config["optimization_target"]
            )
            
            result = {
                "params": params,
                "score": score,
                "iteration": n_initial_points + i,
                "type": "bayesian",
                "timestamp": datetime.now().isoformat()
            }
            task.results.append(result)
            
            if score > task.best_score:
                task.best_score = score
                task.best_params = params
            
            task.progress = int((n_initial_points + i + 1) / (n_initial_points + n_iterations) * 100)
            await asyncio.sleep(0.1)
        
        task.status = "completed"
        task.completed_at = datetime.now()
        
    except Exception as e:
        task.status = "failed"
        task.error_message = str(e)
        task.completed_at = datetime.now()


# ========== 辅助函数 ==========

def generate_param_combinations(param_ranges: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
    """生成参数组合"""
    import itertools
    
    keys = list(param_ranges.keys())
    values = [param_ranges[k] for k in keys]
    
    combinations = []
    for combo in itertools.product(*values):
        combinations.append(dict(zip(keys, combo)))
    
    return combinations


def initialize_population(param_bounds: Dict[str, Dict[str, float]], size: int) -> List[Dict[str, Any]]:
    """初始化种群"""
    population = []
    for _ in range(size):
        individual = sample_random_params(param_bounds)
        population.append(individual)
    return population


def sample_random_params(param_bounds: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
    """随机采样参数"""
    import random
    
    params = {}
    for param, bounds in param_bounds.items():
        if bounds.get("type") == "int":
            params[param] = random.randint(int(bounds["min"]), int(bounds["max"]))
        else:
            params[param] = random.uniform(bounds["min"], bounds["max"])
    
    return params


def evolve_population(population: List[Dict], fitness_scores: List[float], param_bounds: Dict) -> List[Dict]:
    """种群进化（简化实现）"""
    import random
    
    # 选择优秀个体
    sorted_indices = sorted(range(len(fitness_scores)), key=lambda i: fitness_scores[i], reverse=True)
    elite_size = len(population) // 4
    new_population = [population[i] for i in sorted_indices[:elite_size]]
    
    # 交叉和变异产生新个体
    while len(new_population) < len(population):
        # 选择父母
        parent1 = random.choice(population[:len(population)//2])
        parent2 = random.choice(population[:len(population)//2])
        
        # 交叉
        child = {}
        for param in param_bounds:
            if random.random() < 0.5:
                child[param] = parent1[param]
            else:
                child[param] = parent2[param]
        
        # 变异
        if random.random() < 0.1:  # 10%变异概率
            param_to_mutate = random.choice(list(param_bounds.keys()))
            child[param_to_mutate] = sample_random_params({param_to_mutate: param_bounds[param_to_mutate]})[param_to_mutate]
        
        new_population.append(child)
    
    return new_population


def suggest_next_params(results: List[Dict], param_bounds: Dict) -> Dict[str, Any]:
    """基于历史结果建议下一个参数（简化实现）"""
    if not results:
        return sample_random_params(param_bounds)
    
    # 找到最佳结果附近进行探索
    best_result = max(results, key=lambda x: x["score"])
    best_params = best_result["params"]
    
    # 在最佳参数附近添加随机扰动
    import random
    new_params = {}
    for param, value in best_params.items():
        if param in param_bounds:
            bounds = param_bounds[param]
            if bounds.get("type") == "int":
                perturbation = random.randint(-2, 2)
                new_value = max(bounds["min"], min(bounds["max"], value + perturbation))
                new_params[param] = int(new_value)
            else:
                perturbation = random.uniform(-0.1, 0.1) * (bounds["max"] - bounds["min"])
                new_value = max(bounds["min"], min(bounds["max"], value + perturbation))
                new_params[param] = new_value
    
    return new_params


async def simulate_backtest(strategy, params: Dict[str, Any], backtest_config: dict, optimization_target: str) -> float:
    """模拟回测并返回评分（实际应该调用真实的回测引擎）"""
    import random
    import math
    
    # 模拟不同参数组合的表现
    # 实际应该调用回测服务
    base_score = random.uniform(0.5, 2.0)
    
    # 根据参数调整分数（模拟参数影响）
    if "short_period" in params and "long_period" in params:
        if params["short_period"] < params["long_period"]:
            base_score *= 1.2
        else:
            base_score *= 0.8
    
    if optimization_target == "sharpe_ratio":
        return base_score
    elif optimization_target == "total_return":
        return base_score * 100  # 转换为百分比
    elif optimization_target == "max_drawdown":
        return -abs(base_score * 0.1)  # 负数表示回撤
    else:
        return base_score


@router.get("/templates", summary="获取优化模板")
async def get_optimization_templates(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取预定义的优化模板
    """
    templates = [
        {
            "name": "均线策略优化",
            "description": "优化移动平均线交叉策略的周期参数",
            "method": "grid_search",
            "param_ranges": {
                "short_period": [5, 10, 15, 20, 25, 30],
                "long_period": [30, 40, 50, 60, 70, 80, 90, 100],
                "stop_loss": [0.02, 0.03, 0.05, 0.08]
            },
            "optimization_target": "sharpe_ratio"
        },
        {
            "name": "动量策略优化",
            "description": "优化动量策略的回看周期和阈值",
            "method": "genetic_algorithm",
            "param_bounds": {
                "lookback_period": {"min": 5, "max": 60, "type": "int"},
                "entry_threshold": {"min": 0.01, "max": 0.1, "type": "float"},
                "exit_threshold": {"min": 0.005, "max": 0.05, "type": "float"},
                "position_size": {"min": 0.1, "max": 1.0, "type": "float"}
            },
            "population_size": 50,
            "generations": 30,
            "optimization_target": "total_return"
        },
        {
            "name": "均值回归策略优化",
            "description": "优化均值回归策略的Z分数阈值",
            "method": "bayesian",
            "param_bounds": {
                "lookback": {"min": 10, "max": 100, "type": "int"},
                "entry_z_score": {"min": 1.0, "max": 3.0, "type": "float"},
                "exit_z_score": {"min": 0.0, "max": 1.0, "type": "float"},
                "max_position": {"min": 0.1, "max": 0.5, "type": "float"}
            },
            "n_initial_points": 20,
            "n_iterations": 80,
            "optimization_target": "sharpe_ratio"
        }
    ]
    
    return {
        "templates": templates,
        "total": len(templates)
    }


@router.get("/health", summary="优化服务健康检查")
async def health_check():
    """
    优化服务健康检查
    """
    active_tasks = sum(1 for t in optimization_tasks.values() if t.status == "running")
    completed_tasks = sum(1 for t in optimization_tasks.values() if t.status == "completed")
    
    return {
        "status": "healthy",
        "service": "optimization",
        "timestamp": datetime.now().isoformat(),
        "active_tasks": active_tasks,
        "completed_tasks": completed_tasks,
        "total_tasks": len(optimization_tasks)
    }