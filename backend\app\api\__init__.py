"""
API模块
提供RESTful API和WebSocket接口
"""

from fastapi import APIRouter

from .v1 import api_router as v1_router
from .models import (
    BacktestRequest, BacktestResponse, BacktestResult,
    StrategyConfigModel, BacktestConfigModel, RiskLimitsModel,
    PerformanceMetrics, WebSocketMessage, APIResponse
)

# 创建主API路由器
api_router = APIRouter()

# 注册v1版本API，统一加 /api/v1 前缀
api_router.include_router(v1_router, prefix="/api/v1")

__all__ = [
    "api_router",
    'BacktestRequest',
    'BacktestResponse', 
    'BacktestResult',
    'StrategyConfigModel',
    'BacktestConfigModel',
    'RiskLimitsModel',
    'PerformanceMetrics',
    'WebSocketMessage',
    'APIResponse'
]
