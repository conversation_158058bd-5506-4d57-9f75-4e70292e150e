"""
集成测试脚本
测试所有新创建的API模块
"""
import asyncio
import httpx
from datetime import datetime
import json
from typing import Dict, List

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

# 测试用户凭据
TEST_USER = {
    "username": "testuser",
    "password": "testpass123"
}

class APITester:
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=BASE_URL)
        self.access_token = None
        self.test_results = []
        
    async def setup(self):
        """初始化测试环境"""
        # 注册测试用户
        try:
            resp = await self.client.post("/auth/register", json={
                "username": TEST_USER["username"],
                "password": TEST_USER["password"],
                "email": f"{TEST_USER['username']}@test.com"
            })
            print(f"注册响应: {resp.status_code}")
        except:
            pass  # 用户可能已存在
            
        # 登录获取token
        resp = await self.client.post("/auth/login", data={
            "username": TEST_USER["username"],
            "password": TEST_USER["password"]
        })
        
        if resp.status_code == 200:
            self.access_token = resp.json()["access_token"]
            self.client.headers["Authorization"] = f"Bearer {self.access_token}"
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {resp.status_code}")
            
    async def test_market_module(self):
        """测试实时行情模块"""
        print("\n=== 测试实时行情模块 ===")
        
        tests = [
            ("获取股票列表", "GET", "/market-v2/stocks/list?limit=10"),
            ("获取实时行情", "GET", "/market-v2/quotes/realtime?symbols=000001,600036"),
            ("获取K线数据", "GET", "/market-v2/kline/000001?period=1d&limit=20"),
            ("获取市场深度", "GET", "/market-v2/depth/000001"),
            ("获取逐笔成交", "GET", "/market-v2/ticks/000001?limit=50"),
            ("获取市场概览", "GET", "/market-v2/overview"),
            ("搜索股票", "GET", "/market-v2/search?keyword=平安"),
            ("获取板块表现", "GET", "/market-v2/sectors/performance"),
        ]
        
        await self._run_tests(tests, "实时行情")
        
    async def test_trading_terminal(self):
        """测试交易终端模块"""
        print("\n=== 测试交易终端模块 ===")
        
        tests = [
            ("获取终端概览", "GET", "/terminal/terminal/overview"),
            ("获取订单簿", "GET", "/terminal/terminal/order-book/000001"),
            ("获取持仓明细", "GET", "/terminal/terminal/positions/detail"),
            ("获取今日订单", "GET", "/terminal/terminal/orders/today"),
            ("获取今日成交", "GET", "/terminal/terminal/trades/today"),
            ("获取热门股票", "GET", "/terminal/terminal/hot-stocks"),
            ("获取预设订单", "GET", "/terminal/terminal/preset-orders"),
            ("获取快捷键", "GET", "/terminal/terminal/keyboard-shortcuts"),
        ]
        
        # 测试快速下单
        order_resp = await self.client.post("/terminal/terminal/quick-order", json={
            "symbol": "000001",
            "side": "BUY",
            "quantity": 100,
            "order_type": "MARKET"
        })
        self._log_result("快速下单", order_resp.status_code == 200)
        
        await self._run_tests(tests, "交易终端")
        
    async def test_order_management(self):
        """测试订单管理模块"""
        print("\n=== 测试订单管理模块 ===")
        
        tests = [
            ("获取订单列表", "GET", "/orders/orders/list?page=1&page_size=20"),
            ("获取订单统计", "GET", "/orders/orders/statistics?period=today"),
            ("获取模板列表", "GET", "/orders/orders/template/list"),
            ("导出订单(JSON)", "GET", "/orders/orders/export?format=json"),
        ]
        
        await self._run_tests(tests, "订单管理")
        
    async def test_strategy_development(self):
        """测试策略开发模块"""
        print("\n=== 测试策略开发模块 ===")
        
        # 创建测试策略
        strategy_resp = await self.client.post("/strategy-dev/strategies/create", json={
            "name": "测试双均线策略",
            "description": "基于MA5和MA20的交叉策略",
            "code": """
def initialize(context):
    context.s1 = '000001.SZ'
    context.short_window = 5
    context.long_window = 20

def handle_data(context, data):
    short_ma = data.history(context.s1, 'close', context.short_window, '1d').mean()
    long_ma = data.history(context.s1, 'close', context.long_window, '1d').mean()
    
    if short_ma > long_ma:
        order_target_percent(context.s1, 1.0)
    else:
        order_target_percent(context.s1, 0.0)
""",
            "language": "python",
            "parameters": {"short_window": 5, "long_window": 20},
            "tags": ["均线", "趋势"]
        })
        
        strategy_id = None
        if strategy_resp.status_code == 200:
            strategy_id = strategy_resp.json()["data"]["strategy_id"]
            self._log_result("创建策略", True)
        else:
            self._log_result("创建策略", False)
            
        tests = [
            ("获取策略模板", "GET", "/strategy-dev/strategies/template/list"),
            ("获取策略市场", "GET", "/strategy-dev/strategies/market?page=1&page_size=10"),
        ]
        
        if strategy_id:
            # 测试回测
            backtest_resp = await self.client.post(f"/strategy-dev/strategies/{strategy_id}/backtest", json={
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "initial_capital": 1000000,
                "benchmark": "000300.SH",
                "symbols": ["000001", "000002", "600036"],
                "commission_rate": 0.0003,
                "slippage": 0.001
            })
            self._log_result("运行回测", backtest_resp.status_code == 200)
            
            # 测试其他策略功能
            tests.extend([
                ("获取策略信号", "GET", f"/strategy-dev/strategies/{strategy_id}/signals?days=7"),
                ("获取策略代码", "GET", f"/strategy-dev/strategies/{strategy_id}/code"),
                ("监控策略状态", "GET", f"/strategy-dev/strategies/{strategy_id}/monitor"),
            ])
            
        await self._run_tests(tests, "策略开发")
        
    async def _run_tests(self, tests: List[tuple], module_name: str):
        """运行测试列表"""
        for test_name, method, endpoint in tests:
            try:
                if method == "GET":
                    resp = await self.client.get(endpoint)
                elif method == "POST":
                    resp = await self.client.post(endpoint)
                    
                success = resp.status_code == 200
                self._log_result(test_name, success)
                
                if not success:
                    print(f"  错误详情: {resp.status_code} - {resp.text[:100]}")
                    
            except Exception as e:
                self._log_result(test_name, False)
                print(f"  异常: {str(e)}")
                
    def _log_result(self, test_name: str, success: bool):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "time": datetime.now().isoformat()
        })
        
    async def generate_report(self):
        """生成测试报告"""
        print("\n=== 测试报告 ===")
        
        total = len(self.test_results)
        passed = sum(1 for r in self.test_results if r["success"])
        failed = total - passed
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if failed > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}")
                    
        # 保存详细报告
        report = {
            "test_time": datetime.now().isoformat(),
            "summary": {
                "total": total,
                "passed": passed,
                "failed": failed,
                "success_rate": passed/total*100
            },
            "details": self.test_results
        }
        
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print("\n详细报告已保存到 test_report.json")
        
    async def cleanup(self):
        """清理资源"""
        await self.client.aclose()
        
    async def run_all_tests(self):
        """运行所有测试"""
        try:
            await self.setup()
            await self.test_market_module()
            await self.test_trading_terminal()
            await self.test_order_management()
            await self.test_strategy_development()
            await self.generate_report()
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("开始集成测试...")
    print(f"目标服务器: {BASE_URL}")
    print("=" * 50)
    
    tester = APITester()
    await tester.run_all_tests()
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())