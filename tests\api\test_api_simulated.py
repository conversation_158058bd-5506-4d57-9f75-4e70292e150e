#!/usr/bin/env python3
"""
简化的API测试脚本 - 测试模拟交易功能
"""

import requests
import json


def test_api():
    base_url = "http://localhost:8000/api/v1"
    
    # 1. 测试获取验证码
    print("1. 测试验证码生成...")
    resp = requests.get(f"{base_url}/captcha/generate")
    if resp.status_code == 200:
        captcha_data = resp.json()
        print(f"✓ 验证码Key: {captcha_data['data']['key']}")
        captcha_key = captcha_data['data']['key']
    else:
        print(f"✗ 验证码生成失败: {resp.status_code}")
        return
    
    # 2. 测试登录
    print("\n2. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123",
        "captcha_key": captcha_key,
        "captcha_code": "1234"
    }
    resp = requests.post(f"{base_url}/auth/login", json=login_data)
    if resp.status_code == 200:
        auth_data = resp.json()
        token = auth_data['data']['access_token']
        print("✓ 登录成功")
    else:
        print(f"✗ 登录失败: {resp.status_code} - {resp.text}")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 3. 获取账户列表
    print("\n3. 测试获取账户列表...")
    resp = requests.get(f"{base_url}/account-management/list", headers=headers)
    print(f"状态码: {resp.status_code}")
    if resp.status_code == 200:
        print(f"✓ 响应: {json.dumps(resp.json(), indent=2, ensure_ascii=False)}")
    else:
        print(f"✗ 错误: {resp.text}")
    
    # 4. 创建模拟账户
    print("\n4. 测试创建模拟账户...")
    account_data = {
        "account_type": "SIMULATED",
        "initial_capital": 1000000.0,
        "created_reason": "USER_CREATE"
    }
    resp = requests.post(f"{base_url}/account-management/create", headers=headers, json=account_data)
    print(f"状态码: {resp.status_code}")
    if resp.status_code == 200:
        result = resp.json()
        print(f"✓ 账户创建成功: {result['data']['account_id']}")
        account_id = result['data']['account_id']
    else:
        print(f"✗ 错误: {resp.text}")
        # 尝试获取已有账户
        resp = requests.get(f"{base_url}/account-management/list", headers=headers, params={"account_type": "SIMULATED"})
        if resp.status_code == 200 and resp.json()['data']['items']:
            account_id = resp.json()['data']['items'][0]['account_id']
            print(f"使用已有账户: {account_id}")
        else:
            return
    
    # 5. 获取行情
    print("\n5. 测试获取行情...")
    symbol = "600000"
    resp = requests.get(f"{base_url}/market-fixed/quote/{symbol}", headers=headers)
    print(f"状态码: {resp.status_code}")
    if resp.status_code == 200:
        quote = resp.json()['data']
        print(f"✓ {symbol} 当前价: {quote['current']}")
        current_price = quote['current']
    else:
        print(f"✗ 错误: {resp.text}")
        current_price = 10.0  # 默认价格
    
    # 6. 提交模拟订单
    print("\n6. 测试提交模拟订单...")
    order_data = {
        "account_id": account_id,
        "symbol": symbol,
        "direction": "BUY",
        "order_type": "LIMIT",
        "price": current_price,
        "volume": 100
    }
    resp = requests.post(f"{base_url}/simulated-trading/order/submit", headers=headers, json=order_data)
    print(f"状态码: {resp.status_code}")
    if resp.status_code == 200:
        order = resp.json()['data']
        print(f"✓ 订单提交成功: {order['order_id']}")
    else:
        print(f"✗ 错误: {resp.text}")
    
    # 7. 查询订单
    print("\n7. 测试查询订单...")
    resp = requests.get(f"{base_url}/simulated-trading/orders", headers=headers, params={"account_id": account_id})
    print(f"状态码: {resp.status_code}")
    if resp.status_code == 200:
        orders = resp.json()['data']['items']
        print(f"✓ 找到 {len(orders)} 个订单")
    else:
        print(f"✗ 错误: {resp.text}")
    
    # 8. 查询持仓
    print("\n8. 测试查询持仓...")
    resp = requests.get(f"{base_url}/simulated-trading/positions", headers=headers, params={"account_id": account_id})
    print(f"状态码: {resp.status_code}")
    if resp.status_code == 200:
        data = resp.json()['data']
        print(f"✓ 持仓数量: {len(data['positions'])}")
    else:
        print(f"✗ 错误: {resp.text}")
    
    print("\n测试完成！")


if __name__ == "__main__":
    test_api()