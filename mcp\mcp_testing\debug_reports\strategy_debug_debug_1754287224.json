{"session_id": "debug_1754287224", "timestamp": "2025-08-04T14:00:32.156095", "page_info": {"title": "策略详情 - 量化投资平台", "url": "http://localhost:5173/strategy/detail/mock-1", "readyState": "complete", "bodyContent": 47637, "hasVue": false, "hasVueApp": true, "vueAppContent": 42612, "totalElements": 609, "visibleElements": 486}, "elements_info": {"total": 609, "byTag": {"html": 1, "head": 1, "script": 3, "meta": 12, "link": 6, "title": 1, "style": 67, "body": 1, "div": 175, "aside": 1, "img": 1, "span": 40, "button": 9, "i": 45, "svg": 50, "path": 62, "nav": 1, "ul": 5, "li": 23, "header": 2, "sup": 1, "main": 1, "h1": 1, "p": 4, "h3": 8, "table": 4, "colgroup": 4, "col": 16, "thead": 2, "tr": 12, "th": 8, "tbody": 2, "td": 38, "textarea": 1, "vite-error-overlay": 1}, "withClass": 462, "withId": 16, "withHref": 6, "withOnclick": 0, "buttons": 9, "links": 0, "divs": 175, "spans": 40}, "clickable_info": {"total": 18, "elements": [{"selector": "button", "tagName": "button", "className": "el-button is-text", "id": "", "href": "", "onclick": "", "textContent": "", "position": {"x": 196, "y": 16, "width": 44, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "button", "tagName": "button", "className": "el-button is-text", "id": "", "href": "", "onclick": "", "textContent": "", "position": {"x": 1628, "y": 4, "width": 44, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "button", "tagName": "button", "className": "el-button is-text", "id": "", "href": "", "onclick": "", "textContent": "", "position": {"x": 1688, "y": 4, "width": 44, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "button", "tagName": "button", "className": "el-button is-text", "id": "", "href": "", "onclick": "", "textContent": "", "position": {"x": 1760, "y": 4, "width": 44, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "button", "tagName": "button", "className": "el-button el-button--primary", "id": "", "href": "", "onclick": "", "textContent": "订阅策略", "position": {"x": 1542, "y": 121, "width": 102, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "button", "tagName": "button", "className": "el-button", "id": "", "href": "", "onclick": "", "textContent": "回测", "position": {"x": 1668, "y": 121, "width": 74, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "button", "tagName": "button", "className": "el-button", "id": "", "href": "", "onclick": "", "textContent": "复制", "position": {"x": 1766, "y": 121, "width": 74, "height": 32}, "cursor": "pointer", "isVisible": true}, {"selector": "[role=\"button\"]", "tagName": "div", "className": "user-avatar el-tooltip__trigger el-tooltip__trigger", "id": "el-id-6322-0", "href": "", "onclick": "", "textContent": "", "position": {"x": 1820, "y": 0, "width": 76, "height": 40}, "cursor": "pointer", "isVisible": true}, {"selector": "div[class*=\"card\"]", "tagName": "div", "className": "stat-card", "id": "", "href": "", "onclick": "", "textContent": "28.45%总收益率", "position": {"x": 305, "y": 305, "width": 377, "height": 104}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"card\"]", "tagName": "div", "className": "stat-card", "id": "", "href": "", "onclick": "", "textContent": "18.92%年化收益", "position": {"x": 702, "y": 305, "width": 377, "height": 104}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"card\"]", "tagName": "div", "className": "stat-card", "id": "", "href": "", "onclick": "", "textContent": "-7.56%最大回撤", "position": {"x": 1099, "y": 305, "width": 377, "height": 104}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"card\"]", "tagName": "div", "className": "stat-card", "id": "", "href": "", "onclick": "", "textContent": "1.67夏普比率", "position": {"x": 1495, "y": 305, "width": 377, "height": 104}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"card\"]", "tagName": "div", "className": "el-tabs el-tabs--top el-tabs--card", "id": "", "href": "", "onclick": "", "textContent": "策略概览回测结果持仓分析评论讨论策略逻辑该策略采用多因子选股模型，结合技术指标和基本面分析，通过机器学习算法优化投资组合配置。主要包括：1. 基本面筛选 2. 技术指标确认 3. 风险控制 4. 动态", "position": {"x": 305, "y": 433, "width": 1567, "height": 392}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"item\"]", "tagName": "div", "className": "el-tabs__item is-top is-active", "id": "tab-overview", "href": "", "onclick": "", "textContent": "策略概览", "position": {"x": 306, "y": 434, "width": 96, "height": 40}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"item\"]", "tagName": "div", "className": "el-tabs__item is-top", "id": "tab-backtest", "href": "", "onclick": "", "textContent": "回测结果", "position": {"x": 402, "y": 434, "width": 97, "height": 40}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"item\"]", "tagName": "div", "className": "el-tabs__item is-top", "id": "tab-positions", "href": "", "onclick": "", "textContent": "持仓分析", "position": {"x": 499, "y": 434, "width": 97, "height": 40}, "cursor": "auto", "isVisible": true}, {"selector": "div[class*=\"item\"]", "tagName": "div", "className": "el-tabs__item is-top", "id": "tab-comments", "href": "", "onclick": "", "textContent": "评论讨论", "position": {"x": 596, "y": 434, "width": 97, "height": 40}, "cursor": "auto", "isVisible": true}, {"selector": "span[class*=\"link\"]", "tagName": "span", "className": "el-breadcrumb__inner is-link", "id": "", "href": "", "onclick": "", "textContent": "策略详情", "position": {"x": 281, "y": 13, "width": 56, "height": 14}, "cursor": "text", "isVisible": true}]}}