"""
回测功能完整流程测试
测试从创建策略到运行回测的完整流程
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal

import numpy as np
import pandas as pd
import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.database import Base, get_db
from app.db.models.backtest import BacktestResult as BacktestResultModel
from app.db.models.strategy import Strategy, StrategyStatus, StrategyType
from app.db.models.user import User
from app.schemas.backtest import BacktestCreate, BacktestStatus
from app.schemas.strategy import StrategyCreate
from app.services.backtest_engine import BacktestEngine
from app.services.backtest_service import BacktestService
from app.services.strategy_service import StrategyService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# ============ 测试辅助函数 ============


def create_test_user() -> User:
    """创建测试用户"""
    return User(
        id=1,
        username="test_user",
        email="<EMAIL>",
        is_active=True,
    )


def create_simple_ma_strategy() -> str:
    """创建简单移动平均策略代码"""
    return """
import pandas as pd
import numpy as np

def generate_signals(data: pd.DataFrame) -> pd.Series:
    '''简单双均线策略'''
    # 计算移动平均线
    ma_short = data['close'].rolling(window=5).mean()
    ma_long = data['close'].rolling(window=20).mean()
    
    # 生成信号
    signals = pd.Series(0, index=data.index)
    
    # 金叉买入
    signals[(ma_short > ma_long) & (ma_short.shift(1) <= ma_long.shift(1))] = 1
    
    # 死叉卖出
    signals[(ma_short < ma_long) & (ma_short.shift(1) >= ma_long.shift(1))] = -1
    
    # 保持持仓
    signals = signals.replace(0, np.nan).fillna(method='ffill').fillna(0)
    
    return signals
"""


def generate_mock_market_data(
    symbol: str, start: datetime, end: datetime, period: str = "1d"
) -> pd.DataFrame:
    """生成模拟市场数据"""
    # 生成日期索引
    date_range = pd.date_range(start=start, end=end, freq="B")  # B表示工作日

    # 生成模拟价格数据
    n_days = len(date_range)
    base_price = 100.0

    # 使用随机游走生成价格
    returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
    price_series = base_price * np.exp(np.cumsum(returns))

    # 生成OHLCV数据
    data = pd.DataFrame(index=date_range)
    data["close"] = price_series
    data["open"] = data["close"] * (1 + np.random.uniform(-0.01, 0.01, n_days))
    data["high"] = np.maximum(data["open"], data["close"]) * (
        1 + np.random.uniform(0, 0.02, n_days)
    )
    data["low"] = np.minimum(data["open"], data["close"]) * (
        1 - np.random.uniform(0, 0.02, n_days)
    )
    data["volume"] = np.random.randint(1000000, 5000000, n_days)

    return data


# ============ 测试类 ============


class TestBacktestFlow:
    """回测流程测试类"""

    @pytest.fixture
    async def db_session(self):
        """创建测试数据库会话"""
        # 使用内存数据库进行测试
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")

        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # 创建会话
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )

        async with async_session() as session:
            yield session

        await engine.dispose()

    async def test_complete_backtest_flow(self, db_session: AsyncSession):
        """测试完整的回测流程"""
        logger.info("开始测试完整回测流程")

        # 1. 创建测试用户
        user = create_test_user()
        db_session.add(user)
        await db_session.commit()
        logger.info("✓ 创建测试用户")

        # 2. 创建策略
        strategy_service = StrategyService(db_session)
        strategy_data = StrategyCreate(
            name="双均线策略",
            description="简单的双均线交叉策略",
            strategy_type=StrategyType.TREND_FOLLOWING,
            code=create_simple_ma_strategy(),
            symbols=["TEST001"],
            frequency="daily",
            parameters={
                "short_period": 5,
                "long_period": 20,
            },
            risk_limits={
                "max_position_size": 0.8,
                "stop_loss": 0.05,
            },
        )

        strategy = await strategy_service.create_strategy(user.id, strategy_data)
        logger.info(f"✓ 创建策略: {strategy.name}")

        # 3. 创建回测任务
        backtest_service = BacktestService(db_session)
        backtest_data = BacktestCreate(
            name="双均线策略回测",
            strategy_id=strategy.id,
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now() - timedelta(days=1),
            initial_capital=1000000.0,
            symbols=["TEST001"],
            commission_rate=0.001,
            slippage_rate=0.0005,
        )

        backtest = await backtest_service.create_backtest(user.id, backtest_data)
        logger.info(f"✓ 创建回测任务: {backtest.name}")

        # 4. 运行回测引擎
        logger.info("开始运行回测引擎...")

        # 创建回测引擎
        engine = BacktestEngine(
            data_loader=generate_mock_market_data,
            strategy_callable=self._execute_strategy,
            symbol="TEST001",
            start=backtest.start_date,
            end=backtest.end_date,
            initial_capital=backtest.initial_capital,
            commission_rate=float(backtest.commission_rate),
            slippage=float(backtest.slippage_rate),
        )

        # 运行回测
        result = engine.run()
        logger.info("✓ 回测引擎运行完成")

        # 5. 保存回测结果
        backtest_result = BacktestResultModel(
            backtest_id=backtest.id,
            # 收益指标
            total_return=Decimal(str(result.metrics["total_return"])),
            annual_return=Decimal(str(result.metrics["annual_return"])),
            max_drawdown=Decimal(str(result.metrics["max_drawdown"])),
            sharpe_ratio=Decimal(str(result.metrics["sharpe_ratio"])),
            sortino_ratio=Decimal(str(result.metrics.get("sortino_ratio", 0))),
            calmar_ratio=Decimal(str(result.metrics.get("calmar_ratio", 0))),
            # 交易统计
            total_trades=result.metrics["trades"],
            win_rate=Decimal(str(result.metrics.get("win_rate", 0))),
            profit_loss_ratio=Decimal(str(result.metrics.get("profit_loss_ratio", 0))),
            # 资金曲线
            equity_curve=result.equity_curve.to_dict(),
            # 其他结果
            trades=result.trades,
            positions=[],  # 暂时为空
            daily_returns=[],  # 暂时为空
        )

        db_session.add(backtest_result)

        # 更新回测状态
        backtest.status = BacktestStatus.COMPLETED
        backtest.end_time = datetime.now()

        await db_session.commit()
        logger.info("✓ 保存回测结果")

        # 6. 验证结果
        assert backtest.status == BacktestStatus.COMPLETED
        assert backtest_result.total_return is not None
        assert backtest_result.sharpe_ratio is not None
        assert len(result.trades) > 0

        logger.info("\n========== 回测结果 ==========")
        logger.info(f"总收益率: {result.metrics['total_return']:.2%}")
        logger.info(f"年化收益率: {result.metrics['annual_return']:.2%}")
        logger.info(f"最大回撤: {result.metrics['max_drawdown']:.2%}")
        logger.info(f"夏普比率: {result.metrics['sharpe_ratio']:.4f}")
        logger.info(f"交易次数: {result.metrics['trades']}")
        logger.info("==============================\n")

        logger.info("✅ 回测流程测试完成！")

    def _execute_strategy(self, data: pd.DataFrame) -> pd.Series:
        """执行策略，生成交易信号"""
        # 计算移动平均线
        ma_short = data["close"].rolling(window=5).mean()
        ma_long = data["close"].rolling(window=20).mean()

        # 生成信号
        signals = pd.Series(0, index=data.index)

        # 金叉买入
        buy_signals = (ma_short > ma_long) & (ma_short.shift(1) <= ma_long.shift(1))
        signals[buy_signals] = 1

        # 死叉卖出
        sell_signals = (ma_short < ma_long) & (ma_short.shift(1) >= ma_long.shift(1))
        signals[sell_signals] = -1

        # 前向填充信号（保持持仓）
        signals = signals.replace(0, np.nan).ffill().fillna(0)

        return signals

    async def test_backtest_with_different_parameters(self, db_session: AsyncSession):
        """测试不同参数的回测"""
        logger.info("测试不同参数的回测")

        # 创建用户和策略（复用上面的代码）
        user = create_test_user()
        db_session.add(user)
        await db_session.commit()

        strategy_service = StrategyService(db_session)
        strategy_data = StrategyCreate(
            name="参数化双均线策略",
            description="可调参数的双均线策略",
            strategy_type=StrategyType.TREND_FOLLOWING,
            code=create_simple_ma_strategy(),
            symbols=["TEST001"],
            frequency="daily",
            parameters={
                "short_period": 5,
                "long_period": 20,
            },
        )

        strategy = await strategy_service.create_strategy(user.id, strategy_data)

        # 测试不同的参数组合
        parameter_sets = [
            {"short_period": 5, "long_period": 20},
            {"short_period": 10, "long_period": 30},
            {"short_period": 20, "long_period": 60},
        ]

        results = []

        for params in parameter_sets:
            logger.info(f"测试参数: {params}")

            # 创建回测引擎
            engine = BacktestEngine(
                data_loader=generate_mock_market_data,
                strategy_callable=lambda data: self._execute_strategy_with_params(
                    data, params
                ),
                symbol="TEST001",
                start=datetime.now() - timedelta(days=365),
                end=datetime.now() - timedelta(days=1),
                initial_capital=1000000.0,
                commission_rate=0.001,
                slippage=0.0005,
            )

            # 运行回测
            result = engine.run()
            results.append(
                {
                    "params": params,
                    "total_return": result.metrics["total_return"],
                    "sharpe_ratio": result.metrics["sharpe_ratio"],
                    "max_drawdown": result.metrics["max_drawdown"],
                }
            )

        # 找出最佳参数
        best_result = max(results, key=lambda x: x["sharpe_ratio"])

        logger.info("\n========== 参数优化结果 ==========")
        for r in results:
            logger.info(
                f"参数 {r['params']}: "
                f"收益率={r['total_return']:.2%}, "
                f"夏普={r['sharpe_ratio']:.4f}, "
                f"回撤={r['max_drawdown']:.2%}"
            )
        logger.info(f"\n最佳参数: {best_result['params']}")
        logger.info("==================================\n")

    def _execute_strategy_with_params(
        self, data: pd.DataFrame, params: dict
    ) -> pd.Series:
        """使用指定参数执行策略"""
        short_period = params["short_period"]
        long_period = params["long_period"]

        # 计算移动平均线
        ma_short = data["close"].rolling(window=short_period).mean()
        ma_long = data["close"].rolling(window=long_period).mean()

        # 生成信号
        signals = pd.Series(0, index=data.index)

        # 金叉买入
        buy_signals = (ma_short > ma_long) & (ma_short.shift(1) <= ma_long.shift(1))
        signals[buy_signals] = 1

        # 死叉卖出
        sell_signals = (ma_short < ma_long) & (ma_short.shift(1) >= ma_long.shift(1))
        signals[sell_signals] = -1

        # 前向填充信号
        signals = signals.replace(0, np.nan).ffill().fillna(0)

        return signals


# ============ 主函数 ============


async def main():
    """主测试函数"""
    # 创建测试实例
    test = TestBacktestFlow()

    # 创建内存数据库
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")

    # 创建表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建会话
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        # 运行完整流程测试
        await test.test_complete_backtest_flow(session)

        # 运行参数优化测试
        await test.test_backtest_with_different_parameters(session)

    await engine.dispose()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
