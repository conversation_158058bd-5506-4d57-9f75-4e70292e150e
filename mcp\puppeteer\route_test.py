#!/usr/bin/env python3
"""
路由测试 - 检查不同路由是否正常工作
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_routes():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        routes_to_test = [
            'http://localhost:5173/',
            'http://localhost:5173/trading',
            'http://localhost:5173/trading/center',
            'http://localhost:5173/dashboard'
        ]
        
        for route in routes_to_test:
            try:
                logger.info(f"测试路由: {route}")
                
                await page.goto(route, wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(3000)
                
                title = await page.title()
                logger.info(f"  页面标题: {title}")
                
                # 检查页面内容
                content = await page.evaluate("() => document.body.innerText")
                if len(content) > 100:
                    logger.info(f"  页面内容长度: {len(content)} (有内容)")
                else:
                    logger.warning(f"  页面内容长度: {len(content)} (内容较少)")
                
                # 检查是否有Vue应用
                vue_app = await page.evaluate("() => document.querySelector('#app')")
                if vue_app:
                    logger.info("  ✅ Vue应用已加载")
                else:
                    logger.warning("  ❌ Vue应用未加载")
                
                # 检查路由内容
                router_view = await page.evaluate("() => document.querySelector('main')")
                if router_view:
                    logger.info("  ✅ 主要内容区域存在")
                else:
                    logger.warning("  ❌ 主要内容区域不存在")
                
                # 截图
                route_name = route.replace('http://localhost:5173/', '').replace('/', '_') or 'home'
                await page.screenshot(path=f'screenshots/route_{route_name}.png')
                logger.info(f"  📸 截图已保存: route_{route_name}.png")
                
                logger.info("  " + "="*50)
                
            except Exception as e:
                logger.error(f"  路由 {route} 测试失败: {e}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_routes())
