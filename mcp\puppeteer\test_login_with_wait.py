#!/usr/bin/env python3
"""
测试登录，等待loading状态结束
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

async def test_login_with_wait():
    """测试登录，等待loading状态结束"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 测试登录，等待loading状态结束...")
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 等待页面完全加载
        await page.wait_for_selector('button:has-text("演示登录")', timeout=10000)
        print("✅ 找到演示登录按钮")
        
        # 等待按钮变为可用状态（不在loading中）
        print("⏳ 等待按钮变为可用状态...")
        await page.wait_for_function('''
            () => {
                const buttons = document.querySelectorAll('button');
                const demoButton = Array.from(buttons).find(btn => btn.textContent.includes('演示登录'));
                return demoButton && !demoButton.disabled && !demoButton.classList.contains('is-loading');
            }
        ''', timeout=10000)
        
        print("✅ 按钮已变为可用状态")
        
        # 监听导航事件
        navigation_events = []
        
        def handle_navigation(frame):
            if frame == page.main_frame:
                navigation_events.append({
                    "url": frame.url,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"🔄 页面导航到: {frame.url}")
        
        page.on('framenavigated', handle_navigation)
        
        # 监听网络请求
        login_requests = []
        
        async def handle_response(response):
            if '/auth/login' in response.url:
                login_requests.append({
                    "url": response.url,
                    "status": response.status,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"📥 登录响应: {response.status} {response.url}")
        
        page.on('response', handle_response)
        
        # 记录点击前的URL
        url_before = page.url
        print(f"📍 点击前URL: {url_before}")
        
        # 点击演示登录按钮
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        print("🖱️ 已点击演示登录按钮")
        
        # 等待登录处理完成
        print("⏳ 等待登录处理...")
        await page.wait_for_timeout(5000)
        
        # 检查是否有导航发生
        if navigation_events:
            print("✅ 检测到页面导航事件")
            for event in navigation_events:
                print(f"   - {event['timestamp']}: {event['url']}")
        else:
            print("❌ 没有检测到页面导航事件")
            
            # 尝试手动检查URL变化
            url_after = page.url
            print(f"📍 点击后URL: {url_after}")
            
            if url_after != url_before:
                print("✅ URL已变化，但没有触发导航事件")
            else:
                print("❌ URL没有变化")
                
                # 检查是否有错误消息
                error_messages = await page.evaluate('''
                    () => {
                        const errors = [];
                        // 检查Element Plus的消息
                        const messageElements = document.querySelectorAll('.el-message');
                        messageElements.forEach(el => {
                            errors.push(el.textContent);
                        });
                        return errors;
                    }
                ''')
                
                if error_messages:
                    print(f"❌ 页面错误消息: {error_messages}")
                
                # 检查用户登录状态
                user_state = await page.evaluate('''
                    () => {
                        try {
                            const app = document.querySelector('#app').__vue_app__;
                            const pinia = app.config.globalProperties.$pinia;
                            if (pinia && pinia._s) {
                                const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                                if (userStore) {
                                    return {
                                        isLoggedIn: userStore.isLoggedIn,
                                        hasToken: !!userStore.token
                                    };
                                }
                            }
                            return { error: 'User store not found' };
                        } catch (e) {
                            return { error: e.message };
                        }
                    }
                ''')
                
                print(f"👤 用户登录状态: {user_state}")
                
                # 如果用户已登录但没有跳转，手动触发跳转
                if user_state.get('isLoggedIn'):
                    print("🔧 用户已登录，尝试手动跳转到滑块验证页面...")
                    await page.goto('http://localhost:5173/test-slider')
                    await page.wait_for_timeout(2000)
                    final_url = page.url
                    print(f"📍 手动跳转后URL: {final_url}")
        
        # 生成测试报告
        test_result = {
            "test_time": datetime.now().isoformat(),
            "url_before": url_before,
            "url_after": page.url,
            "navigation_events": navigation_events,
            "login_requests": login_requests
        }
        
        # 保存测试报告
        with open('login_wait_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_result, f, ensure_ascii=False, indent=2)
        
        print("📄 测试报告已保存: login_wait_test_report.json")
        
        # 截图
        screenshot_name = f"login_wait_test_{datetime.now().strftime('%H%M%S')}.png"
        await page.screenshot(path=screenshot_name, full_page=True)
        print(f"📸 测试截图已保存: {screenshot_name}")
        
    except Exception as e:
        print(f"💥 测试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_login_with_wait())
