#!/usr/bin/env python3
"""
量化投资平台深度用户体验测试
模拟真实用户行为，全面测试平台功能和用户体验
"""

import asyncio
import json
import time
import random
from datetime import datetime
from puppeteer import BrowserManager

class DeepUserExperienceTest:
    def __init__(self):
        self.manager = BrowserManager()
        self.page = None
        self.test_results = []
        self.screenshots = []
        self.user_actions = []
        self.performance_metrics = {}
        
    async def setup(self):
        """初始化浏览器和测试环境"""
        self.page = await self.manager.ensure_browser()
        print("🚀 开始深度用户体验测试...")
        print("📋 测试目标：模拟真实用户使用量化投资平台的完整流程")
        
        # 设置视口
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
    async def teardown(self):
        """清理资源并生成报告"""
        if self.manager.browser:
            await self.manager.browser.close()
        await self.generate_final_report()
        print("🔚 测试完成，报告已生成")
            
    async def take_screenshot(self, name, description=""):
        """截图并记录用户行为"""
        timestamp = datetime.now().strftime('%H%M%S')
        filename = f"deep_test_{name}_{timestamp}.png"
        await self.page.screenshot(path=filename, full_page=True)
        
        self.screenshots.append({
            "name": name,
            "filename": filename,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "url": self.page.url
        })
        print(f"📸 截图: {filename} - {description}")
        
    async def log_user_action(self, action, details="", success=True, timing=None):
        """记录用户行为和结果"""
        action_log = {
            "action": action,
            "details": details,
            "success": success,
            "timing": timing,
            "timestamp": datetime.now().isoformat(),
            "url": self.page.url
        }
        self.user_actions.append(action_log)
        
        status = "✅" if success else "❌"
        timing_info = f" ({timing:.2f}s)" if timing else ""
        print(f"{status} {action}: {details}{timing_info}")
        
    async def measure_performance(self, action_name):
        """测量页面性能指标"""
        start_time = time.time()
        
        # 等待网络空闲
        await self.page.wait_for_load_state('networkidle')
        
        # 获取性能指标
        metrics = await self.page.evaluate('''() => {
            const navigation = performance.getEntriesByType('navigation')[0];
            const paint = performance.getEntriesByType('paint');
            
            return {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                totalLoadTime: navigation.loadEventEnd - navigation.navigationStart
            };
        }''')
        
        end_time = time.time()
        metrics['actionDuration'] = end_time - start_time
        
        self.performance_metrics[action_name] = metrics
        return metrics
        
    async def simulate_human_behavior(self, delay_range=(0.5, 2.0)):
        """模拟人类操作延迟"""
        delay = random.uniform(*delay_range)
        await self.page.wait_for_timeout(int(delay * 1000))
        
    async def test_initial_page_load(self):
        """测试1: 初始页面加载体验"""
        print("\n🔍 测试1: 初始页面加载和首次印象")
        
        start_time = time.time()
        try:
            # 访问首页
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            load_time = time.time() - start_time
            
            await self.log_user_action("访问首页", f"加载时间: {load_time:.2f}秒", True, load_time)
            await self.take_screenshot("homepage_initial", "首页初始加载状态")
            
            # 检查页面基本元素
            title = await self.page.title()
            await self.log_user_action("获取页面标题", f"标题: {title}")
            
            # 检查是否有加载指示器
            loading_indicators = await self.page.query_selector_all('.loading, .spinner, .skeleton')
            if loading_indicators:
                await self.log_user_action("发现加载指示器", f"数量: {len(loading_indicators)}")
                await self.page.wait_for_timeout(3000)  # 等待加载完成
                
            # 测量性能
            metrics = await self.measure_performance("homepage_load")
            await self.log_user_action("性能测量", f"DOM加载: {metrics['domContentLoaded']:.0f}ms")
            
            # 检查控制台错误
            console_errors = await self.page.evaluate('''() => {
                return window.console.errors || [];
            }''')
            
            if console_errors:
                await self.log_user_action("发现控制台错误", f"错误数量: {len(console_errors)}", False)
            else:
                await self.log_user_action("控制台检查", "无错误")
                
        except Exception as e:
            await self.log_user_action("首页加载失败", str(e), False)
            
    async def test_navigation_exploration(self):
        """测试2: 导航探索 - 模拟用户浏览各个页面"""
        print("\n🧭 测试2: 导航探索和页面浏览")
        
        # 主要页面列表
        pages_to_test = [
            ('/dashboard', '仪表盘', '查看投资概览'),
            ('/market', '市场数据', '查看实时行情'),
            ('/trading', '交易终端', '进行交易操作'),
            ('/strategy', '策略中心', '管理投资策略'),
            ('/portfolio', '投资组合', '查看持仓情况'),
            ('/risk', '风险管理', '监控风险指标')
        ]
        
        for path, name, purpose in pages_to_test:
            try:
                await self.simulate_human_behavior()
                
                start_time = time.time()
                await self.page.goto(f'http://localhost:5173{path}', wait_until='networkidle')
                load_time = time.time() - start_time
                
                await self.log_user_action(f"访问{name}", purpose, True, load_time)
                await self.take_screenshot(f"page_{name.replace(' ', '_')}", f"{name}页面浏览")
                
                # 检查页面内容是否加载
                content_elements = await self.page.query_selector_all('div, section, article')
                await self.log_user_action(f"{name}内容检查", f"发现{len(content_elements)}个内容元素")
                
                # 模拟用户在页面上的停留和浏览
                await self.simulate_human_behavior((2, 5))
                
                # 尝试滚动页面
                await self.page.evaluate('window.scrollTo(0, document.body.scrollHeight / 2)')
                await self.simulate_human_behavior()
                await self.page.evaluate('window.scrollTo(0, 0)')
                
            except Exception as e:
                await self.log_user_action(f"{name}页面访问失败", str(e), False)
                
    async def test_login_user_flow(self):
        """测试3: 登录用户流程"""
        print("\n🔐 测试3: 用户登录流程体验")
        
        try:
            # 访问登录页面
            await self.page.goto('http://localhost:5173/login', wait_until='networkidle')
            await self.take_screenshot("login_page", "登录页面")
            await self.log_user_action("访问登录页面", "准备进行登录")
            
            # 查找登录表单元素
            username_input = await self.page.query_selector('input[type="text"], input[name="username"], input[placeholder*="用户名"]')
            password_input = await self.page.query_selector('input[type="password"], input[name="password"]')
            
            if username_input and password_input:
                await self.log_user_action("发现登录表单", "用户名和密码输入框存在")
                
                # 模拟用户输入
                await self.simulate_human_behavior()
                await username_input.fill("demo_user")
                await self.log_user_action("输入用户名", "demo_user")
                
                await self.simulate_human_behavior()
                await password_input.fill("demo_password")
                await self.log_user_action("输入密码", "已输入密码")
                
                # 查找并点击登录按钮
                login_button = await self.page.query_selector('button[type="submit"], .login-btn, .submit-btn, button:has-text("登录")')
                if login_button:
                    await self.simulate_human_behavior()
                    await login_button.click()
                    await self.log_user_action("点击登录按钮", "提交登录请求")
                    
                    # 等待登录响应
                    await self.page.wait_for_timeout(3000)
                    await self.take_screenshot("after_login", "登录后状态")
                    
                    # 检查是否跳转到主页面
                    current_url = self.page.url
                    if '/login' not in current_url:
                        await self.log_user_action("登录成功", f"跳转到: {current_url}")
                    else:
                        await self.log_user_action("登录后未跳转", "仍在登录页面", False)
                else:
                    await self.log_user_action("未找到登录按钮", "", False)
            else:
                await self.log_user_action("登录表单不完整", "缺少输入框", False)
                
        except Exception as e:
            await self.log_user_action("登录流程失败", str(e), False)
            
    async def test_interactive_elements(self):
        """测试4: 交互元素测试"""
        print("\n🎯 测试4: 交互元素和用户操作")
        
        # 回到首页进行交互测试
        await self.page.goto('http://localhost:5173', wait_until='networkidle')
        
        # 测试按钮交互
        buttons = await self.page.query_selector_all('button, .btn, [role="button"]')
        await self.log_user_action("发现按钮元素", f"总数: {len(buttons)}")
        
        # 随机点击几个按钮测试交互
        for i, button in enumerate(buttons[:5]):  # 只测试前5个按钮
            try:
                await self.simulate_human_behavior()
                button_text = await button.inner_text()
                await button.click()
                await self.log_user_action(f"点击按钮{i+1}", f"按钮文本: {button_text[:20]}")
                await self.page.wait_for_timeout(1000)
            except Exception as e:
                await self.log_user_action(f"按钮{i+1}点击失败", str(e), False)
                
        # 测试输入框
        inputs = await self.page.query_selector_all('input[type="text"], input[type="number"], textarea')
        await self.log_user_action("发现输入框", f"总数: {len(inputs)}")
        
        # 测试下拉菜单
        selects = await self.page.query_selector_all('select, .dropdown, .select')
        await self.log_user_action("发现下拉菜单", f"总数: {len(selects)}")

    async def test_data_loading(self):
        """测试5: 数据加载和API交互"""
        print("\n📊 测试5: 数据加载和API交互")

        # 监听网络请求
        requests = []
        responses = []

        async def handle_request(request):
            requests.append({
                "url": request.url,
                "method": request.method,
                "timestamp": datetime.now().isoformat()
            })

        async def handle_response(response):
            responses.append({
                "url": response.url,
                "status": response.status,
                "timestamp": datetime.now().isoformat()
            })

        self.page.on('request', handle_request)
        self.page.on('response', handle_response)

        # 访问数据密集型页面
        await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
        await self.page.wait_for_timeout(5000)  # 等待数据加载

        await self.log_user_action("监听网络请求", f"请求数: {len(requests)}, 响应数: {len(responses)}")
        await self.take_screenshot("market_data_loaded", "市场数据页面加载完成")

        # 检查API响应状态
        api_responses = [r for r in responses if '/api/' in r['url']]
        successful_apis = [r for r in api_responses if 200 <= r['status'] < 300]
        failed_apis = [r for r in api_responses if r['status'] >= 400]

        await self.log_user_action("API响应分析",
                                 f"总API请求: {len(api_responses)}, 成功: {len(successful_apis)}, 失败: {len(failed_apis)}")

        if failed_apis:
            for api in failed_apis:
                await self.log_user_action("API请求失败", f"{api['url']} - 状态码: {api['status']}", False)

        # 检查数据是否正确显示
        data_elements = await self.page.query_selector_all('.data-item, .stock-item, .market-data, table tr')
        await self.log_user_action("数据元素检查", f"发现 {len(data_elements)} 个数据项")

    async def test_responsive_design(self):
        """测试6: 响应式设计"""
        print("\n📱 测试6: 响应式设计和移动端适配")

        # 测试不同屏幕尺寸
        viewports = [
            (1920, 1080, "桌面端"),
            (1366, 768, "笔记本"),
            (768, 1024, "平板端"),
            (375, 667, "手机端")
        ]

        for width, height, device_type in viewports:
            try:
                await self.page.set_viewport_size({"width": width, "height": height})
                await self.page.wait_for_timeout(1000)

                await self.take_screenshot(f"responsive_{device_type}", f"{device_type}视图")
                await self.log_user_action(f"测试{device_type}适配", f"分辨率: {width}x{height}")

                # 检查是否有横向滚动条
                has_horizontal_scroll = await self.page.evaluate('''() => {
                    return document.body.scrollWidth > window.innerWidth;
                }''')

                if has_horizontal_scroll:
                    await self.log_user_action(f"{device_type}横向滚动", "存在横向滚动条", False)
                else:
                    await self.log_user_action(f"{device_type}布局检查", "无横向滚动，布局正常")

            except Exception as e:
                await self.log_user_action(f"{device_type}测试失败", str(e), False)

        # 恢复桌面端视图
        await self.page.set_viewport_size({"width": 1920, "height": 1080})

    async def test_error_handling(self):
        """测试7: 错误处理和边界情况"""
        print("\n⚠️ 测试7: 错误处理和边界情况")

        # 测试无效URL访问
        invalid_urls = [
            '/nonexistent-page',
            '/api/invalid-endpoint',
            '/dashboard/invalid-id'
        ]

        for url in invalid_urls:
            try:
                await self.page.goto(f'http://localhost:5173{url}', wait_until='networkidle')
                await self.page.wait_for_timeout(2000)

                # 检查是否显示错误页面
                error_indicators = await self.page.query_selector_all('.error, .not-found, .404, [class*="error"]')
                if error_indicators:
                    await self.log_user_action(f"错误页面处理", f"URL: {url}, 发现错误指示器")
                    await self.take_screenshot(f"error_page_{url.replace('/', '_')}", f"错误页面: {url}")
                else:
                    await self.log_user_action(f"错误页面缺失", f"URL: {url}, 未发现错误处理", False)

            except Exception as e:
                await self.log_user_action(f"错误页面测试异常", f"URL: {url}, 错误: {str(e)}", False)

        # 测试网络断开情况
        try:
            # 模拟网络断开
            await self.page.route('**/*', lambda route: route.abort())
            await self.page.goto('http://localhost:5173', wait_until='domcontentloaded')
            await self.page.wait_for_timeout(3000)

            await self.log_user_action("网络断开测试", "模拟网络不可用情况")
            await self.take_screenshot("network_offline", "网络断开状态")

            # 恢复网络
            await self.page.unroute('**/*')

        except Exception as e:
            await self.log_user_action("网络断开测试失败", str(e), False)

    async def test_accessibility(self):
        """测试8: 可访问性测试"""
        print("\n♿ 测试8: 可访问性和用户友好性")

        await self.page.goto('http://localhost:5173', wait_until='networkidle')

        # 检查页面标题
        title = await self.page.title()
        if title and len(title.strip()) > 0:
            await self.log_user_action("页面标题检查", f"标题: {title}")
        else:
            await self.log_user_action("页面标题缺失", "页面没有有效标题", False)

        # 检查alt属性
        images = await self.page.query_selector_all('img')
        images_without_alt = []
        for img in images:
            alt = await img.get_attribute('alt')
            if not alt:
                src = await img.get_attribute('src')
                images_without_alt.append(src)

        if images_without_alt:
            await self.log_user_action("图片alt属性检查", f"{len(images_without_alt)}张图片缺少alt属性", False)
        else:
            await self.log_user_action("图片alt属性检查", f"所有{len(images)}张图片都有alt属性")

        # 检查表单标签
        inputs = await self.page.query_selector_all('input, textarea, select')
        inputs_without_labels = 0
        for input_elem in inputs:
            # 检查是否有关联的label
            input_id = await input_elem.get_attribute('id')
            if input_id:
                label = await self.page.query_selector(f'label[for="{input_id}"]')
                if not label:
                    inputs_without_labels += 1
            else:
                inputs_without_labels += 1

        if inputs_without_labels > 0:
            await self.log_user_action("表单标签检查", f"{inputs_without_labels}个输入框缺少标签", False)
        else:
            await self.log_user_action("表单标签检查", f"所有{len(inputs)}个输入框都有适当标签")

        # 检查键盘导航
        try:
            # 测试Tab键导航
            await self.page.keyboard.press('Tab')
            await self.page.wait_for_timeout(500)
            focused_element = await self.page.evaluate('document.activeElement.tagName')
            await self.log_user_action("键盘导航测试", f"Tab键可用，焦点在: {focused_element}")
        except Exception as e:
            await self.log_user_action("键盘导航测试失败", str(e), False)
        
    async def generate_final_report(self):
        """生成最终测试报告"""
        report = {
            "test_summary": {
                "test_name": "深度用户体验测试",
                "test_time": datetime.now().isoformat(),
                "total_actions": len(self.user_actions),
                "successful_actions": len([a for a in self.user_actions if a['success']]),
                "failed_actions": len([a for a in self.user_actions if not a['success']]),
                "screenshots_taken": len(self.screenshots)
            },
            "performance_metrics": self.performance_metrics,
            "user_actions": self.user_actions,
            "screenshots": self.screenshots,
            "recommendations": self.generate_recommendations()
        }
        
        # 保存报告
        filename = f"deep_user_experience_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n📊 测试报告已保存: {filename}")
        self.print_summary()
        
    def generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        failed_actions = [a for a in self.user_actions if not a['success']]
        if failed_actions:
            recommendations.append({
                "priority": "高",
                "category": "功能问题",
                "description": f"发现{len(failed_actions)}个失败操作，需要修复",
                "details": [a['action'] for a in failed_actions]
            })
            
        # 性能建议
        if self.performance_metrics:
            avg_load_time = sum(m.get('actionDuration', 0) for m in self.performance_metrics.values()) / len(self.performance_metrics)
            if avg_load_time > 3:
                recommendations.append({
                    "priority": "中",
                    "category": "性能优化",
                    "description": f"平均加载时间{avg_load_time:.2f}秒，建议优化",
                    "details": ["考虑代码分割", "优化图片加载", "减少初始包大小"]
                })
                
        return recommendations
        
    def print_summary(self):
        """打印测试摘要"""
        total = len(self.user_actions)
        success = len([a for a in self.user_actions if a['success']])

        print(f"\n📈 测试摘要:")
        print(f"   总操作数: {total}")
        print(f"   成功操作: {success}")
        print(f"   失败操作: {total - success}")
        if total > 0:
            print(f"   成功率: {(success/total*100):.1f}%")
        else:
            print(f"   成功率: 0.0%")
        print(f"   截图数量: {len(self.screenshots)}")

async def main():
    """主测试函数"""
    tester = DeepUserExperienceTest()
    
    try:
        await tester.setup()
        
        # 执行测试序列
        await tester.test_initial_page_load()
        await tester.test_navigation_exploration()
        await tester.test_login_user_flow()
        await tester.test_interactive_elements()
        await tester.test_data_loading()
        await tester.test_responsive_design()
        await tester.test_error_handling()
        await tester.test_accessibility()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        await tester.teardown()

if __name__ == "__main__":
    asyncio.run(main())
