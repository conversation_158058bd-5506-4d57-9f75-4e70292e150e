<template>
  <div class="user-guide">
    <!-- 引导遮罩层 -->
    <div v-if="showGuide" class="guide-overlay" @click="closeGuide">
      <div class="guide-content" @click.stop>
        <div class="guide-header">
          <h2>{{ currentStep.title }}</h2>
          <el-button 
            type="text" 
            class="close-btn"
            @click="closeGuide"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        
        <div class="guide-body">
          <div class="step-indicator">
            <span class="current-step">{{ currentStepIndex + 1 }}</span>
            <span class="total-steps">/ {{ guideSteps.length }}</span>
          </div>
          
          <div class="step-content">
            <div class="step-icon">
              <el-icon :size="48" :color="currentStep.color">
                <component :is="currentStep.icon" />
              </el-icon>
            </div>
            
            <div class="step-description">
              <p>{{ currentStep.description }}</p>
              <ul v-if="currentStep.tips">
                <li v-for="tip in currentStep.tips" :key="tip">{{ tip }}</li>
              </ul>
            </div>
            
            <div v-if="currentStep.image" class="step-image">
              <img :src="currentStep.image" :alt="currentStep.title" />
            </div>
          </div>
        </div>
        
        <div class="guide-footer">
          <el-button 
            v-if="currentStepIndex > 0"
            @click="previousStep"
          >
            上一步
          </el-button>
          
          <el-button 
            v-if="currentStepIndex < guideSteps.length - 1"
            type="primary"
            @click="nextStep"
          >
            下一步
          </el-button>
          
          <el-button 
            v-if="currentStepIndex === guideSteps.length - 1"
            type="primary"
            @click="finishGuide"
          >
            开始使用
          </el-button>
          
          <el-button 
            type="text"
            @click="skipGuide"
          >
            跳过引导
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 引导触发按钮 -->
    <el-button 
      v-if="!showGuide"
      class="guide-trigger"
      type="primary"
      circle
      @click="startGuide"
    >
      <el-icon><QuestionFilled /></el-icon>
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Close, 
  QuestionFilled,
  TrendCharts,
  Monitor,
  Setting,
  DataAnalysis,
  Wallet,
  Warning
} from '@element-plus/icons-vue'

// 引导步骤定义
const guideSteps = ref([
  {
    title: '欢迎使用量化投资平台',
    description: '这是一个专业的量化投资平台，为您提供完整的投资分析和交易功能。',
    icon: TrendCharts,
    color: '#409EFF',
    tips: [
      '实时市场数据监控',
      '专业的技术分析工具',
      '智能策略回测系统',
      '全面的风险管理'
    ]
  },
  {
    title: '市场数据中心',
    description: '在这里您可以查看实时行情、K线图表、市场深度等数据。',
    icon: Monitor,
    color: '#67C23A',
    tips: [
      '点击左侧菜单"市场数据"进入',
      '支持多种时间周期的K线图',
      '实时更新的行情数据',
      '可自定义关注的股票列表'
    ]
  },
  {
    title: '交易终端',
    description: '专业的交易界面，支持快速下单、持仓管理、订单查询等功能。',
    icon: Wallet,
    color: '#E6A23C',
    tips: [
      '点击"交易终端"开始交易',
      '支持限价单、市价单等多种订单类型',
      '实时显示持仓和盈亏情况',
      '完整的交易历史记录'
    ]
  },
  {
    title: '策略中心',
    description: '开发、测试和部署您的量化策略，支持策略回测和实盘运行。',
    icon: DataAnalysis,
    color: '#9C27B0',
    tips: [
      '内置多种经典策略模板',
      '可视化的策略开发环境',
      '详细的回测报告分析',
      '策略性能监控和优化建议'
    ]
  },
  {
    title: '风险管理',
    description: '实时监控投资风险，设置风控规则，保护您的投资安全。',
    icon: Warning,
    color: '#F56C6C',
    tips: [
      '实时风险指标监控',
      '可设置止损止盈规则',
      '投资组合风险分析',
      '风险预警和提醒功能'
    ]
  },
  {
    title: '系统设置',
    description: '个性化配置您的交易环境，包括界面主题、通知设置等。',
    icon: Setting,
    color: '#606266',
    tips: [
      '点击右上角头像进入设置',
      '可切换明暗主题',
      '配置交易提醒和通知',
      '管理API密钥和安全设置'
    ]
  }
])

// 状态管理
const showGuide = ref(false)
const currentStepIndex = ref(0)

// 计算属性
const currentStep = computed(() => guideSteps.value[currentStepIndex.value])

// 方法
const startGuide = () => {
  showGuide.value = true
  currentStepIndex.value = 0
}

const closeGuide = () => {
  showGuide.value = false
}

const nextStep = () => {
  if (currentStepIndex.value < guideSteps.value.length - 1) {
    currentStepIndex.value++
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
  }
}

const skipGuide = () => {
  localStorage.setItem('userGuideCompleted', 'true')
  showGuide.value = false
  ElMessage.success('您可以随时点击右下角的帮助按钮重新查看引导')
}

const finishGuide = () => {
  localStorage.setItem('userGuideCompleted', 'true')
  showGuide.value = false
  ElMessage.success('欢迎使用量化投资平台！祝您投资顺利！')
}

// 生命周期
onMounted(() => {
  // 检查是否是首次使用
  const isCompleted = localStorage.getItem('userGuideCompleted')
  if (!isCompleted) {
    // 延迟显示引导，让页面先加载完成
    setTimeout(() => {
      startGuide()
    }, 1000)
  }
})
</script>

<style scoped>
.user-guide {
  position: relative;
}

.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.guide-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

.guide-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  padding: 8px;
  font-size: 18px;
}

.guide-body {
  padding: 24px;
}

.step-indicator {
  text-align: center;
  margin-bottom: 24px;
  font-size: 14px;
  color: #909399;
}

.current-step {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.step-content {
  text-align: center;
}

.step-icon {
  margin-bottom: 16px;
}

.step-description {
  margin-bottom: 24px;
}

.step-description p {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.step-description ul {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
  padding-left: 20px;
}

.step-description li {
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.5;
}

.step-image {
  margin-top: 16px;
}

.step-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.guide-footer {
  padding: 0 24px 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.guide-trigger {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  width: 56px;
  height: 56px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.guide-trigger:hover {
  transform: scale(1.1);
  transition: transform 0.2s;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .guide-content {
    width: 95%;
    margin: 20px;
  }
  
  .guide-header,
  .guide-body,
  .guide-footer {
    padding: 16px;
  }
  
  .guide-trigger {
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
  }
}

/* 暗色主题支持 */
.dark .guide-content {
  background: #1d1e1f;
  color: #e5eaf3;
}

.dark .guide-header {
  border-bottom-color: #414243;
}

.dark .guide-header h2 {
  color: #e5eaf3;
}

.dark .step-description p {
  color: #a3a6ad;
}

.dark .step-description li {
  color: #8b949e;
}
</style>
