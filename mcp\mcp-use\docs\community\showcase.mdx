---
title: 'Community Showcase'
description: 'High-quality projects and examples from the mcp-use community'
icon: 'users'
---

import { YouTubeEmbed } from '/snippets/youtube-embed.mdx'

## Featured Projects

Discover exemplary MCP implementations and projects created by our community. These projects demonstrate best practices and innovative uses of mcp-use.

### Community Projects

<div style={{width: '100%'}}>
<table style={{width: '100%'}}>
  <thead>
    <tr>
      <th style={{width: '70%'}}>Repository</th>
      <th style={{width: '30%'}}>Stars</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/170207473?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/tavily-ai/meeting-prep-agent"><strong>tavily-ai/meeting-prep-agent</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 112</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/20041231?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/krishnaik06/MCP-CRASH-Course"><strong>krishnaik06/MCP-CRASH-Course</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 37</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/892404?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/truemagic-coder/solana-agent-app"><strong>truemagic-coder/solana-agent-app</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 29</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/8344498?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/schogini/techietalksai"><strong>schogini/techietalksai</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 21</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/201161342?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/autometa-dev/whatsapp-mcp-voice-agent"><strong>autometa-dev/whatsapp-mcp-voice-agent</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 18</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/100749943?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/Deniscartin/mcp-cli"><strong>Deniscartin/mcp-cli</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 17</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/6764390?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/elastic/genai-workshops"><strong>elastic/genai-workshops</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 9</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/6688805?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/innovaccer/Healthcare-MCP"><strong>innovaccer/Healthcare-MCP</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 6</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/205593730?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/Qingyon-AI/Revornix"><strong>Qingyon-AI/Revornix</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 5</td>
    </tr>
    <tr>
      <td style={{verticalAlign: 'middle'}}>
        <div style={{display: 'flex', alignItems: 'center', gap: '2px'}}>
          <img src="https://avatars.githubusercontent.com/u/68845761?s=40&v=4" width="20" height="20" />
          <a href="https://github.com/entbappy/MCP-Tutorials"><strong>entbappy/MCP-Tutorials</strong></a>
        </div>
      </td>
      <td style={{verticalAlign: 'middle'}}>⭐ 5</td>
    </tr>
  </tbody>
</table>
</div>


#### Video Tutorials



<YouTubeEmbed
  videoId="4v_XmSsK960"
  title="LangChain Integration with mcp-use"
/>


<YouTubeEmbed
  videoId="khObn4yZJYE"
  title="Production Deployment Strategies for MCP Agents"
/>



<YouTubeEmbed
  videoId="9mVok-_McU4"
  title="Secure MCP Execution in Sandbox Environments"
/>



<YouTubeEmbed
  videoId="BG4F3b5QpjM"
  title="Building AI Agents with MCP & Python - Part 1"
/>


<YouTubeEmbed
  videoId="uM8zkJmyTrg"
  title="Advanced MCP Integration Techniques"
/>


<YouTubeEmbed
  videoId="7X9sQ-CyQVs"
  title="Multi-Server Agent Orchestration with mcp-use"
/>


<YouTubeEmbed
  videoId="jlFrhR7c5wg"
  title="Custom MCP Server Development"
/>

<Separator />

<YouTubeEmbed
  videoId="uM6w606_NVs"
  title="Browser Automation with MCP and mcp-use"
/>


<YouTubeEmbed
  videoId="jRk63S3v-UQ"
  title="File System Operations with MCP"
/>

Have you created video tutorials or demos using mcp-use? We'd love to feature them here! Share your content in our [Discord community](https://discord.gg/XkNkSkMz3V).

## Contributing

To get your project featured:

1. Ensure your project demonstrates best practices
2. Include comprehensive documentation
3. Add clear usage examples
4. Open a discussion in our [GitHub Discussions](https://github.com/mcp-use/mcp-use/discussions)

We review submissions regularly and feature projects that provide value to the mcp-use community.

## Further Reading

- [Building Custom Agents](/advanced/building-custom-agents) - Comprehensive guide to creating custom agents with mcp-use
- [Multi-Server Setup](/advanced/multi-server-setup) - Learn how to orchestrate multiple MCP servers
- [Examples Repository](https://github.com/mcp-use/mcp-use/tree/main/examples) - Official collection of usage examples
