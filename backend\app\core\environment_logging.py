"""
环境差异化日志配置
根据不同环境（开发、测试、生产）提供不同的日志配置
"""

import os
import logging
from enum import Enum
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path

from loguru import logger
from app.core.config import settings


class Environment(Enum):
    """环境枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class LoggerConfig:
    """日志器配置"""
    name: str
    level: str
    format: str
    handlers: List[str] = field(default_factory=list)
    propagate: bool = True
    disabled: bool = False


@dataclass
class HandlerConfig:
    """处理器配置"""
    name: str
    type: str  # console, file, rotating_file, timed_rotating_file, syslog
    level: str
    format: str
    filename: Optional[str] = None
    max_bytes: Optional[int] = None
    backup_count: Optional[int] = None
    when: Optional[str] = None  # for timed rotating
    interval: Optional[int] = None
    encoding: str = "utf-8"
    kwargs: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EnvironmentLoggingConfig:
    """环境日志配置"""
    environment: Environment
    root_level: str
    enable_console: bool
    enable_file: bool
    enable_json_format: bool
    enable_structured_logs: bool
    enable_performance_logs: bool
    enable_security_logs: bool
    enable_audit_logs: bool
    log_directory: str
    max_log_size: str
    backup_count: int
    retention_days: int
    handlers: List[HandlerConfig] = field(default_factory=list)
    loggers: List[LoggerConfig] = field(default_factory=list)
    third_party_levels: Dict[str, str] = field(default_factory=dict)
    sensitive_data_filter: bool = True
    include_trace_id: bool = True
    include_user_id: bool = False
    sampling_rate: float = 1.0


class EnvironmentLoggingManager:
    """环境日志管理器"""
    
    def __init__(self):
        self.current_env = self._detect_environment()
        self.config = self._get_environment_config()
        
    def _detect_environment(self) -> Environment:
        """检测当前环境"""
        env_str = getattr(settings, "ENVIRONMENT", "development").lower()
        
        try:
            return Environment(env_str)
        except ValueError:
            logger.warning(f"未知环境: {env_str}, 默认使用development")
            return Environment.DEVELOPMENT
    
    def _get_environment_config(self) -> EnvironmentLoggingConfig:
        """获取环境特定的日志配置"""
        if self.current_env == Environment.DEVELOPMENT:
            return self._get_development_config()
        elif self.current_env == Environment.TESTING:
            return self._get_testing_config()
        elif self.current_env == Environment.STAGING:
            return self._get_staging_config()
        elif self.current_env == Environment.PRODUCTION:
            return self._get_production_config()
        else:
            return self._get_development_config()
    
    def _get_development_config(self) -> EnvironmentLoggingConfig:
        """开发环境配置"""
        return EnvironmentLoggingConfig(
            environment=Environment.DEVELOPMENT,
            root_level="DEBUG",
            enable_console=True,
            enable_file=True,
            enable_json_format=False,  # 开发环境使用可读格式
            enable_structured_logs=True,
            enable_performance_logs=True,
            enable_security_logs=True,
            enable_audit_logs=False,  # 开发环境不需要审计日志
            log_directory="logs",
            max_log_size="50MB",
            backup_count=5,
            retention_days=7,
            handlers=[
                HandlerConfig(
                    name="console",
                    type="console",
                    level="DEBUG",
                    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                           "<level>{level: <8}</level> | "
                           "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                           "<level>{message}</level>"
                ),
                HandlerConfig(
                    name="file",
                    type="rotating_file",
                    level="DEBUG",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
                    filename="logs/app_dev.log",
                    max_bytes=50 * 1024 * 1024,  # 50MB
                    backup_count=5
                ),
                HandlerConfig(
                    name="error_file",
                    type="rotating_file",
                    level="ERROR",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message} | {extra}",
                    filename="logs/error_dev.log",
                    max_bytes=20 * 1024 * 1024,  # 20MB
                    backup_count=3
                )
            ],
            loggers=[
                LoggerConfig(name="uvicorn", level="INFO"),
                LoggerConfig(name="fastapi", level="INFO"),
                LoggerConfig(name="sqlalchemy.engine", level="INFO"),  # 显示SQL查询
                LoggerConfig(name="asyncio", level="WARNING"),
            ],
            third_party_levels={
                "urllib3": "WARNING",
                "requests": "WARNING",
                "boto3": "WARNING",
                "botocore": "WARNING",
            },
            sensitive_data_filter=False,  # 开发环境显示完整数据便于调试
            include_trace_id=True,
            include_user_id=True,
            sampling_rate=1.0
        )
    
    def _get_testing_config(self) -> EnvironmentLoggingConfig:
        """测试环境配置"""
        return EnvironmentLoggingConfig(
            environment=Environment.TESTING,
            root_level="INFO",
            enable_console=True,
            enable_file=True,
            enable_json_format=True,
            enable_structured_logs=True,
            enable_performance_logs=True,
            enable_security_logs=True,
            enable_audit_logs=True,
            log_directory="logs/test",
            max_log_size="20MB",
            backup_count=3,
            retention_days=3,
            handlers=[
                HandlerConfig(
                    name="console",
                    type="console",
                    level="INFO",
                    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
                ),
                HandlerConfig(
                    name="test_file",
                    type="rotating_file",
                    level="DEBUG",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message}",
                    filename="logs/test/app_test.log",
                    max_bytes=20 * 1024 * 1024,
                    backup_count=3
                )
            ],
            third_party_levels={
                "uvicorn": "WARNING",
                "fastapi": "WARNING", 
                "sqlalchemy.engine": "WARNING",
                "asyncio": "ERROR",
            },
            sensitive_data_filter=True,
            include_trace_id=True,
            include_user_id=False,
            sampling_rate=1.0
        )
    
    def _get_staging_config(self) -> EnvironmentLoggingConfig:
        """预发布环境配置"""
        return EnvironmentLoggingConfig(
            environment=Environment.STAGING,
            root_level="INFO",
            enable_console=False,  # 预发布环境不输出到控制台
            enable_file=True,
            enable_json_format=True,
            enable_structured_logs=True,
            enable_performance_logs=True,
            enable_security_logs=True,
            enable_audit_logs=True,
            log_directory="/var/log/quant-platform",
            max_log_size="100MB",
            backup_count=10,
            retention_days=14,
            handlers=[
                HandlerConfig(
                    name="app_file",
                    type="timed_rotating_file",
                    level="INFO",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name} | {message}",
                    filename="/var/log/quant-platform/app.log",
                    when="midnight",
                    interval=1,
                    backup_count=14
                ),
                HandlerConfig(
                    name="error_file",
                    type="rotating_file",
                    level="ERROR",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message} | {extra}",
                    filename="/var/log/quant-platform/error.log",
                    max_bytes=50 * 1024 * 1024,
                    backup_count=5
                ),
                HandlerConfig(
                    name="audit_file",
                    type="timed_rotating_file",
                    level="INFO",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {message}",
                    filename="/var/log/quant-platform/audit.log",
                    when="midnight",
                    interval=1,
                    backup_count=30  # 审计日志保留更久
                )
            ],
            third_party_levels={
                "uvicorn": "WARNING",
                "fastapi": "WARNING",
                "sqlalchemy.engine": "ERROR",  # 生产相关环境不记录SQL
                "asyncio": "ERROR",
            },
            sensitive_data_filter=True,
            include_trace_id=True,
            include_user_id=False,
            sampling_rate=0.5  # 采样以减少日志量
        )
    
    def _get_production_config(self) -> EnvironmentLoggingConfig:
        """生产环境配置"""
        return EnvironmentLoggingConfig(
            environment=Environment.PRODUCTION,
            root_level="WARNING",  # 生产环境只记录警告及以上级别
            enable_console=False,
            enable_file=True,
            enable_json_format=True,
            enable_structured_logs=True,
            enable_performance_logs=False,  # 生产环境关闭性能日志
            enable_security_logs=True,
            enable_audit_logs=True,
            log_directory="/var/log/quant-platform",
            max_log_size="200MB",
            backup_count=20,
            retention_days=90,  # 生产环境保留更久
            handlers=[
                HandlerConfig(
                    name="app_file",
                    type="timed_rotating_file",
                    level="WARNING",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name} | {message}",
                    filename="/var/log/quant-platform/app.log",
                    when="midnight",
                    interval=1,
                    backup_count=30
                ),
                HandlerConfig(
                    name="error_file",
                    type="rotating_file",
                    level="ERROR",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name} | {message}",
                    filename="/var/log/quant-platform/error.log",
                    max_bytes=100 * 1024 * 1024,
                    backup_count=10
                ),
                HandlerConfig(
                    name="critical_file",
                    type="rotating_file",
                    level="CRITICAL",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {message} | {extra}",
                    filename="/var/log/quant-platform/critical.log",
                    max_bytes=50 * 1024 * 1024,
                    backup_count=20
                ),
                HandlerConfig(
                    name="audit_file",
                    type="timed_rotating_file",
                    level="INFO",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | AUDIT | {message}",
                    filename="/var/log/quant-platform/audit.log",
                    when="midnight",
                    interval=1,
                    backup_count=180  # 审计日志保留6个月
                ),
                HandlerConfig(
                    name="security_file",
                    type="timed_rotating_file",
                    level="WARNING",
                    format="{time:YYYY-MM-DD HH:mm:ss.SSS} | SECURITY | {message}",
                    filename="/var/log/quant-platform/security.log",
                    when="midnight",
                    interval=1,
                    backup_count=365  # 安全日志保留1年
                )
            ],
            loggers=[
                LoggerConfig(name="audit", level="INFO", handlers=["audit_file"], propagate=False),
                LoggerConfig(name="security", level="WARNING", handlers=["security_file"], propagate=False),
            ],
            third_party_levels={
                "uvicorn": "ERROR",
                "fastapi": "ERROR",
                "sqlalchemy.engine": "CRITICAL",  # 生产环境基本不记录SQL
                "asyncio": "CRITICAL",
                "urllib3": "CRITICAL",
                "requests": "CRITICAL",
            },
            sensitive_data_filter=True,
            include_trace_id=True,
            include_user_id=False,  # 生产环境不记录用户ID保护隐私
            sampling_rate=0.1  # 大幅采样以减少日志量
        )
    
    def apply_configuration(self):
        """应用环境特定的日志配置"""
        try:
            # 创建日志目录
            log_dir = Path(self.config.log_directory)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 清除现有的loguru处理器
            logger.remove()
            
            # 应用处理器配置
            for handler_config in self.config.handlers:
                self._add_loguru_handler(handler_config)
            
            # 配置Python标准库日志
            self._configure_stdlib_logging()
            
            # 配置第三方库日志级别
            self._configure_third_party_logging()
            
            logger.info(
                f"日志配置已应用",
                environment=self.config.environment.value,
                root_level=self.config.root_level,
                handlers_count=len(self.config.handlers)
            )
            
        except Exception as e:
            print(f"应用日志配置失败: {e}")  # 使用print因为logger可能不可用
            raise
    
    def _add_loguru_handler(self, handler_config: HandlerConfig):
        """添加loguru处理器"""
        handler_kwargs = {
            "level": handler_config.level,
            "format": handler_config.format,
            "backtrace": self.config.environment == Environment.DEVELOPMENT,
            "diagnose": self.config.environment == Environment.DEVELOPMENT,
            "enqueue": True,  # 异步写入
        }
        
        if handler_config.type == "console":
            handler_kwargs["colorize"] = self.config.environment == Environment.DEVELOPMENT
            logger.add(
                sink=lambda msg: print(msg, end=""),
                **handler_kwargs
            )
            
        elif handler_config.type == "rotating_file":
            handler_kwargs.update({
                "rotation": handler_config.max_bytes,
                "retention": handler_config.backup_count,
                "compression": "zip" if self.config.environment != Environment.DEVELOPMENT else None,
                "encoding": handler_config.encoding,
            })
            logger.add(
                sink=handler_config.filename,
                **handler_kwargs
            )
            
        elif handler_config.type == "timed_rotating_file":
            rotation_time = f"{handler_config.when}" if handler_config.when else "1 day"
            handler_kwargs.update({
                "rotation": rotation_time,
                "retention": f"{handler_config.backup_count} days",
                "compression": "zip",
                "encoding": handler_config.encoding,
            })
            logger.add(
                sink=handler_config.filename,
                **handler_kwargs
            )
    
    def _configure_stdlib_logging(self):
        """配置Python标准库日志"""
        logging.basicConfig(
            level=getattr(logging, self.config.root_level),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[]  # 我们使用loguru，所以清空标准处理器
        )
        
        # 将标准库日志重定向到loguru
        import logging
        
        class InterceptHandler(logging.Handler):
            def emit(self, record):
                # 获取对应的loguru级别
                try:
                    level = logger.level(record.levelname).name
                except ValueError:
                    level = record.levelno
                
                # 找到调用者的frame
                frame, depth = logging.currentframe(), 2
                while frame.f_code.co_filename == logging.__file__:
                    frame = frame.f_back
                    depth += 1
                
                logger.opt(depth=depth, exception=record.exc_info).log(
                    level, record.getMessage()
                )
        
        # 替换根日志器的处理器
        logging.root.handlers = [InterceptHandler()]
        logging.root.setLevel(0)
        
        # 配置特定的日志器
        for logger_config in self.config.loggers:
            stdlib_logger = logging.getLogger(logger_config.name)
            stdlib_logger.setLevel(getattr(logging, logger_config.level))
            stdlib_logger.propagate = logger_config.propagate
            stdlib_logger.disabled = logger_config.disabled
    
    def _configure_third_party_logging(self):
        """配置第三方库日志级别"""
        for logger_name, level in self.config.third_party_levels.items():
            logging.getLogger(logger_name).setLevel(getattr(logging, level))
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "environment": self.config.environment.value,
            "root_level": self.config.root_level,
            "console_enabled": self.config.enable_console,
            "file_enabled": self.config.enable_file,
            "json_format": self.config.enable_json_format,
            "structured_logs": self.config.enable_structured_logs,
            "log_directory": self.config.log_directory,
            "handlers_count": len(self.config.handlers),
            "retention_days": self.config.retention_days,
            "sampling_rate": self.config.sampling_rate,
        }
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.current_env == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.current_env == Environment.PRODUCTION
    
    def should_log_sensitive_data(self) -> bool:
        """是否应该记录敏感数据"""
        return not self.config.sensitive_data_filter
    
    def should_sample_log(self) -> bool:
        """是否应该采样此日志"""
        import random
        return random.random() < self.config.sampling_rate


# 全局环境日志管理器
env_logging = EnvironmentLoggingManager()


def setup_environment_logging():
    """设置环境特定的日志配置"""
    env_logging.apply_configuration()


def get_environment_logging() -> EnvironmentLoggingManager:
    """获取环境日志管理器"""
    return env_logging