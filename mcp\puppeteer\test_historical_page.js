const puppeteer = require('puppeteer-core');

async function testHistoricalPage() {
  const browser = await puppeteer.launch({
    executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();
  
  // 监听所有控制台消息
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ [${type}]: ${text}`);
  });

  // 监听页面错误
  page.on('pageerror', error => {
    console.log(`🚨 页面错误: ${error.message}`);
  });

  // 监听网络请求
  page.on('request', request => {
    if (request.url().includes('/api/v1/historical')) {
      console.log(`📤 请求: ${request.method()} ${request.url()}`);
    }
  });

  page.on('response', response => {
    if (response.url().includes('/api/v1/historical')) {
      console.log(`📥 响应: ${response.status()} ${response.url()}`);
    }
  });

  try {
    console.log('🚀 访问历史数据页面...');
    await page.goto('http://localhost:5173/market/historical', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // 等待页面完全加载
    await new Promise(resolve => setTimeout(resolve, 8000));

    // 检查页面状态
    const pageInfo = await page.evaluate(() => {
      const getElementInfo = (selector) => {
        const element = document.querySelector(selector);
        if (!element) return { exists: false };
        
        const rect = element.getBoundingClientRect();
        return {
          exists: true,
          visible: rect.width > 0 && rect.height > 0,
          width: rect.width,
          height: rect.height,
          textContent: element.textContent.substring(0, 100)
        };
      };

      return {
        currentPath: window.location.pathname,
        hasHistoricalPage: getElementInfo('.historical-data-page'),
        hasPageHeader: getElementInfo('.page-header'),
        hasQuickActions: getElementInfo('.quick-actions'),
        hasStatsCards: getElementInfo('.stats-cards'),
        hasSearchCard: getElementInfo('.search-card'),
        hasStockTable: getElementInfo('.el-table'),
        hasLoadingContainer: getElementInfo('.loading-container'),
        hasErrorContainer: getElementInfo('.error-container'),
        
        // 检查统计数据
        statsNumbers: Array.from(document.querySelectorAll('.stat-number')).map(el => el.textContent),
        
        // 检查表格数据
        tableRows: document.querySelectorAll('.el-table__row').length,
        
        // 检查加载状态
        loadingElements: document.querySelectorAll('.el-loading-mask').length,
        
        // 检查错误信息
        errorMessages: Array.from(document.querySelectorAll('.el-message--error')).map(el => el.textContent),
        
        // 页面内容长度
        contentLength: document.body.textContent.length,
        
        // 检查Vue组件状态
        vueComponents: Array.from(document.querySelectorAll('[data-v-]')).length
      };
    });

    console.log('\n📋 历史数据页面状态:');
    console.log('='.repeat(60));
    console.log(`🎯 当前路径: ${pageInfo.currentPath}`);
    console.log(`📄 页面内容长度: ${pageInfo.contentLength} 字符`);
    console.log(`🔢 Vue组件数: ${pageInfo.vueComponents}`);
    console.log(`📊 表格行数: ${pageInfo.tableRows}`);
    console.log(`⏳ 加载元素: ${pageInfo.loadingElements}`);
    
    console.log('\n🏗️ 页面组件状态:');
    Object.entries(pageInfo).forEach(([key, value]) => {
      if (value && typeof value === 'object' && value.exists !== undefined) {
        const status = value.exists ? (value.visible ? '✅ 可见' : '⚠️ 隐藏') : '❌ 不存在';
        console.log(`  ${key}: ${status} (${value.width || 0}x${value.height || 0})`);
        if (value.textContent && value.textContent.length > 0) {
          console.log(`    内容: ${value.textContent}...`);
        }
      }
    });

    if (pageInfo.statsNumbers.length > 0) {
      console.log('\n📊 统计数据:');
      pageInfo.statsNumbers.forEach((stat, index) => {
        console.log(`  统计${index + 1}: ${stat}`);
      });
    }

    if (pageInfo.errorMessages.length > 0) {
      console.log('\n❌ 错误信息:');
      pageInfo.errorMessages.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    // 检查API调用结果
    const apiResults = await page.evaluate(() => {
      // 尝试获取Vue组件的数据状态
      try {
        const app = document.querySelector('#app').__vue__;
        if (app && app.$children) {
          // 查找HistoricalData组件
          const findComponent = (component) => {
            if (component.$options.name === 'HistoricalData' || 
                component.$el?.classList?.contains('historical-data-page')) {
              return component;
            }
            if (component.$children) {
              for (let child of component.$children) {
                const found = findComponent(child);
                if (found) return found;
              }
            }
            return null;
          };
          
          const historicalComponent = findComponent(app);
          if (historicalComponent) {
            return {
              hasComponent: true,
              loading: historicalComponent.loading,
              stockListLength: historicalComponent.stockList?.length || 0,
              statsData: historicalComponent.stats,
              error: historicalComponent.error
            };
          }
        }
        return { hasComponent: false, error: 'Component not found' };
      } catch (e) {
        return { hasComponent: false, error: e.message };
      }
    });

    console.log('\n🔍 Vue组件状态:');
    console.log(JSON.stringify(apiResults, null, 2));

    // 尝试手动触发数据加载
    console.log('\n🔄 尝试手动触发数据加载...');
    await page.evaluate(() => {
      // 尝试点击刷新按钮或重新加载
      const refreshButton = document.querySelector('button[icon="RefreshLeft"]');
      if (refreshButton) {
        refreshButton.click();
        return 'Refresh button clicked';
      }
      
      // 尝试点击搜索按钮
      const searchButton = document.querySelector('button[type="primary"]');
      if (searchButton && searchButton.textContent.includes('搜索')) {
        searchButton.click();
        return 'Search button clicked';
      }
      
      return 'No action buttons found';
    });

    await new Promise(resolve => setTimeout(resolve, 5000));

    // 再次检查页面状态
    const finalCheck = await page.evaluate(() => {
      return {
        tableRows: document.querySelectorAll('.el-table__row').length,
        contentLength: document.body.textContent.length,
        loadingElements: document.querySelectorAll('.el-loading-mask').length,
        hasData: document.querySelector('.el-table__body') !== null
      };
    });

    console.log('\n🔍 最终检查结果:');
    console.log(JSON.stringify(finalCheck, null, 2));

    // 保持浏览器打开
    console.log('\n✅ 测试完成，浏览器保持打开状态...');
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

testHistoricalPage();
