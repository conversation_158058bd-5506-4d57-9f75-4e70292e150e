repos:
-   repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.3.2
    hooks:
    -   id: ruff
        args: [--fix, --exit-non-zero-on-fix, --config=ruff.toml]
        types: [python]
    -   id: ruff-format
        args: [--config=ruff.toml]
        types: [python]

-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: debug-statements

# Define configuration for the Python checks
default_language_version:
    python: python3.11
