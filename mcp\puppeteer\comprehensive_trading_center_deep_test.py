#!/usr/bin/env python3
"""
全面交易中心深度测试 - 使用Puppeteer MCP作为真实用户
测试目标: http://localhost:5173
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from pathlib import Path
import traceback
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
except ImportError:
    print("请安装playwright: pip install playwright")
    print("然后运行: playwright install")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_trading_center_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveTradingCenterTester:
    def __init__(self):
        self.session_id = f"trading_center_test_{int(time.time())}"
        self.start_time = datetime.now()
        self.test_results = {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "test_type": "Comprehensive Trading Center Deep Test",
            "platform_url": "http://localhost:5173",
            "test_scenarios": [],
            "discovered_issues": [],
            "performance_metrics": [],
            "user_experience_feedback": [],
            "screenshots": [],
            "console_errors": [],
            "network_issues": [],
            "recommendations": [],
            "real_user_actions": [],
            "functional_coverage": {},
            "security_findings": [],
            "api_test_results": []
        }
        self.screenshot_counter = 1
        
    async def take_screenshot(self, page, description, step=None):
        """截图并记录"""
        try:
            timestamp = int(time.time())
            filename = f"{self.session_id}_{self.screenshot_counter:03d}_{description}_{timestamp}.png"
            filepath = Path(__file__).parent / filename
            
            await page.screenshot(path=str(filepath), full_page=True)
            
            screenshot_info = {
                "filename": filename,
                "description": description,
                "timestamp": datetime.now().isoformat(),
                "step": step or self.screenshot_counter
            }
            self.test_results["screenshots"].append(screenshot_info)
            self.screenshot_counter += 1
            
            logger.info(f"截图保存: {filename} - {description}")
            return filename
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    async def record_console_errors(self, page):
        """监听控制台错误"""
        def handle_console(msg):
            if msg.type in ['error', 'warning']:
                error_info = {
                    "type": msg.type,
                    "text": msg.text,
                    "timestamp": datetime.now().isoformat(),
                    "location": msg.location if hasattr(msg, 'location') else None
                }
                self.test_results["console_errors"].append(error_info)
                logger.warning(f"控制台{msg.type}: {msg.text}")
        
        page.on("console", handle_console)
        
        # 监听页面错误
        def handle_page_error(error):
            error_info = {
                "type": "page_error",
                "text": str(error),
                "timestamp": datetime.now().isoformat()
            }
            self.test_results["console_errors"].append(error_info)
            logger.error(f"页面错误: {error}")
        
        page.on("pageerror", handle_page_error)
    
    async def record_network_activity(self, page):
        """监听网络活动"""
        def handle_request_failed(request):
            network_issue = {
                "type": "request_failed",
                "url": request.url,
                "method": request.method,
                "timestamp": datetime.now().isoformat()
            }
            self.test_results["network_issues"].append(network_issue)
            logger.warning(f"网络请求失败: {request.method} {request.url}")
        
        def handle_response(response):
            if response.status >= 400:
                network_issue = {
                    "type": "http_error",
                    "url": response.url,
                    "status": response.status,
                    "timestamp": datetime.now().isoformat()
                }
                self.test_results["network_issues"].append(network_issue)
                logger.warning(f"HTTP错误: {response.status} {response.url}")
        
        page.on("requestfailed", handle_request_failed)
        page.on("response", handle_response)
    
    async def test_initial_access(self, page):
        """测试初始访问"""
        scenario = {
            "name": "初始访问测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": [],
            "real_user_actions": []
        }
        
        try:
            # 记录加载开始时间
            load_start = time.time()
            
            # 访问主页
            await page.goto("http://localhost:5173", wait_until="networkidle")
            load_time = time.time() - load_start
            
            scenario["steps"].append(f"成功访问平台，加载时间: {load_time:.2f}秒")
            scenario["real_user_actions"].append("用户打开浏览器并访问交易平台")
            
            # 记录性能指标
            self.test_results["performance_metrics"].append({
                "metric": "initial_load_time",
                "value": load_time,
                "timestamp": datetime.now().isoformat()
            })
            
            # 检查页面标题
            title = await page.title()
            scenario["steps"].append(f"页面标题: {title}")
            
            # 等待页面完全加载
            await page.wait_for_timeout(3000)
            
            # 截图
            await self.take_screenshot(page, "initial_access", 1)
            
            # 检查基本元素
            nav_elements = await page.query_selector_all("nav, .nav, [role='navigation'], .navbar, .menu")
            scenario["steps"].append(f"发现{len(nav_elements)}个导航元素")
            
            # 检查页面是否有内容
            body_text = await page.text_content("body")
            if len(body_text.strip()) < 100:
                scenario["issues"].append("页面内容过少，可能加载不完整")
            
            # 检查是否有加载错误
            error_elements = await page.query_selector_all(".error, .alert-danger, [class*='error']")
            if error_elements:
                scenario["issues"].append(f"页面显示{len(error_elements)}个错误信息")
            
            # 用户体验反馈
            if load_time < 2:
                scenario["user_feedback"].append("页面加载速度很快，用户体验良好")
            elif load_time < 5:
                scenario["user_feedback"].append("页面加载速度可接受")
            else:
                scenario["user_feedback"].append("页面加载速度较慢，影响用户体验")
                scenario["issues"].append("页面加载时间超过5秒")
            
        except Exception as e:
            scenario["issues"].append(f"初始访问失败: {str(e)}")
            logger.error(f"初始访问测试失败: {e}")
        
        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)

    async def test_navigation_system(self, page):
        """测试导航系统"""
        scenario = {
            "name": "导航系统测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": [],
            "real_user_actions": []
        }

        try:
            # 查找所有可能的导航元素
            nav_selectors = [
                "nav a", ".nav a", "[role='navigation'] a",
                ".navbar a", ".menu a", ".sidebar a",
                "button[role='tab']", ".tab", ".nav-item",
                "[data-testid*='nav']", "[data-testid*='menu']",
                ".router-link", "[href*='#']"
            ]

            all_nav_elements = []
            for selector in nav_selectors:
                elements = await page.query_selector_all(selector)
                all_nav_elements.extend(elements)

            # 去重
            unique_elements = []
            seen_texts = set()
            for element in all_nav_elements:
                try:
                    text = await element.text_content()
                    if text and text.strip() and text.strip() not in seen_texts:
                        unique_elements.append(element)
                        seen_texts.add(text.strip())
                except:
                    continue

            scenario["steps"].append(f"发现{len(unique_elements)}个可点击的导航元素")
            scenario["real_user_actions"].append("用户查看页面导航菜单")

            # 测试主要导航项
            main_nav_items = ["仪表盘", "市场数据", "交易终端", "投资组合", "策略中心", "风险管理", "首页", "登录", "注册"]

            for nav_item in main_nav_items:
                try:
                    # 尝试多种选择器
                    selectors_to_try = [
                        f"text=\"{nav_item}\"",
                        f"[title=\"{nav_item}\"]",
                        f"[aria-label=\"{nav_item}\"]",
                        f"a:has-text(\"{nav_item}\")",
                        f"button:has-text(\"{nav_item}\")",
                        f"[data-testid*='{nav_item.lower()}']"
                    ]

                    clicked = False
                    for selector in selectors_to_try:
                        try:
                            element = await page.wait_for_selector(selector, timeout=2000)
                            if element:
                                scenario["steps"].append(f"发现导航项: {nav_item}")
                                scenario["real_user_actions"].append(f"用户点击{nav_item}菜单")

                                # 记录点击前的URL
                                before_url = page.url
                                await element.click()
                                await page.wait_for_timeout(2000)

                                current_url = page.url
                                scenario["steps"].append(f"点击{nav_item}后URL: {current_url}")

                                # 检查URL是否发生变化或页面内容是否更新
                                if before_url != current_url:
                                    scenario["steps"].append(f"{nav_item}导航成功，URL已更新")
                                else:
                                    # 检查页面内容是否有变化
                                    await page.wait_for_timeout(1000)
                                    scenario["steps"].append(f"{nav_item}可能是单页应用路由")

                                # 截图
                                await self.take_screenshot(page, f"nav_{nav_item}", len(scenario["steps"]))

                                clicked = True
                                break
                        except:
                            continue

                    if not clicked:
                        scenario["issues"].append(f"无法找到或点击导航项: {nav_item}")

                except Exception as e:
                    scenario["issues"].append(f"测试导航项{nav_item}时出错: {str(e)}")

            # 测试面包屑导航
            breadcrumb_selectors = [".breadcrumb", ".breadcrumbs", "[aria-label='breadcrumb']"]
            breadcrumb_found = False
            for selector in breadcrumb_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    breadcrumb_found = True
                    scenario["steps"].append(f"发现面包屑导航: {len(elements)}个")
                    break

            if not breadcrumb_found:
                scenario["user_feedback"].append("建议添加面包屑导航提升用户体验")

            # 用户体验反馈
            if len(scenario["issues"]) == 0:
                scenario["user_feedback"].append("导航系统完整，用户可以轻松浏览各个功能")
            elif len(scenario["issues"]) < 3:
                scenario["user_feedback"].append("导航系统基本完整，有少量问题")
            else:
                scenario["user_feedback"].append("导航系统存在较多问题，影响用户使用")

        except Exception as e:
            scenario["issues"].append(f"导航系统测试失败: {str(e)}")
            logger.error(f"导航系统测试失败: {e}")

        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)

    async def test_trading_functionality(self, page):
        """测试交易功能"""
        scenario = {
            "name": "交易功能深度测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": [],
            "real_user_actions": []
        }

        try:
            # 进入交易页面
            trading_selectors = [
                "text=\"交易终端\"", "text=\"交易\"", "text=\"Trading\"",
                "[href*='trading']", "[href*='trade']",
                "a:has-text(\"交易\")", "button:has-text(\"交易\")"
            ]

            trading_page_accessed = False
            for selector in trading_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await element.click()
                        await page.wait_for_timeout(3000)
                        scenario["steps"].append(f"成功进入交易页面，使用选择器: {selector}")
                        scenario["real_user_actions"].append("用户进入交易终端页面")
                        trading_page_accessed = True
                        break
                except:
                    continue

            if not trading_page_accessed:
                scenario["issues"].append("无法访问交易页面")
                return

            # 截图交易页面
            await self.take_screenshot(page, "trading_page", len(scenario["steps"]))

            # 检查交易功能元素
            trading_elements = {
                "股票搜索": ["input[placeholder*='搜索']", "input[placeholder*='股票']", ".search-input", "[data-testid*='search']"],
                "买入按钮": ["button:has-text('买入')", "button:has-text('Buy')", ".buy-button", "[data-testid*='buy']"],
                "卖出按钮": ["button:has-text('卖出')", "button:has-text('Sell')", ".sell-button", "[data-testid*='sell']"],
                "价格输入": ["input[placeholder*='价格']", "input[type='number']", ".price-input", "[data-testid*='price']"],
                "数量输入": ["input[placeholder*='数量']", "input[placeholder*='quantity']", ".quantity-input", "[data-testid*='quantity']"],
                "订单列表": [".order-list", ".orders", "table", ".trade-history", "[data-testid*='order']"],
                "持仓信息": [".position", ".holdings", ".portfolio", "[data-testid*='position']"],
                "K线图": ["canvas", ".chart", "#chart", "[id*='chart']", ".kline"]
            }

            for element_name, selectors in trading_elements.items():
                found = False
                for selector in selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            found = True
                            scenario["steps"].append(f"发现{element_name}")
                            break
                    except:
                        continue

                if not found:
                    scenario["issues"].append(f"缺少{element_name}")

            # 测试交易表单交互
            scenario["real_user_actions"].append("用户尝试进行模拟交易操作")
            try:
                # 尝试输入股票代码
                search_selectors = ["input[placeholder*='搜索']", "input[placeholder*='股票']", ".search-input"]
                for selector in search_selectors:
                    search_input = await page.query_selector(selector)
                    if search_input:
                        await search_input.fill("000001")
                        await page.wait_for_timeout(1000)
                        scenario["steps"].append("测试股票搜索输入: 000001")
                        break

                # 尝试输入价格和数量
                price_selectors = ["input[placeholder*='价格']", "input[type='number']"]
                for selector in price_selectors:
                    price_input = await page.query_selector(selector)
                    if price_input:
                        await price_input.fill("10.50")
                        scenario["steps"].append("测试价格输入: 10.50")
                        break

                # 测试买入按钮
                buy_selectors = ["button:has-text('买入')", ".buy-button"]
                for selector in buy_selectors:
                    buy_button = await page.query_selector(selector)
                    if buy_button:
                        await buy_button.click()
                        await page.wait_for_timeout(2000)
                        scenario["steps"].append("测试买入按钮点击")
                        scenario["real_user_actions"].append("用户点击买入按钮")
                        break

            except Exception as e:
                scenario["issues"].append(f"交易表单交互测试失败: {str(e)}")

            # 用户体验反馈
            missing_elements = len([issue for issue in scenario["issues"] if "缺少" in issue])
            if missing_elements == 0:
                scenario["user_feedback"].append("交易功能完整，用户可以进行完整的交易操作")
            elif missing_elements < 3:
                scenario["user_feedback"].append("交易功能基本完整，有少量功能缺失")
            else:
                scenario["user_feedback"].append("交易功能不完整，影响用户交易体验")

        except Exception as e:
            scenario["issues"].append(f"交易功能测试失败: {str(e)}")
            logger.error(f"交易功能测试失败: {e}")

        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)

    async def test_market_data(self, page):
        """测试市场数据功能"""
        scenario = {
            "name": "市场数据深度测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": [],
            "real_user_actions": []
        }

        try:
            # 进入市场数据页面
            market_selectors = [
                "text=\"市场数据\"", "text=\"行情\"", "text=\"Market\"",
                "[href*='market']", "a:has-text(\"市场\")", "button:has-text(\"市场\")"
            ]

            market_accessed = False
            for selector in market_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await element.click()
                        await page.wait_for_timeout(3000)
                        scenario["steps"].append("成功进入市场数据页面")
                        scenario["real_user_actions"].append("用户查看市场数据和行情信息")
                        market_accessed = True
                        break
                except:
                    continue

            if not market_accessed:
                scenario["issues"].append("无法访问市场数据页面")
                return

            # 截图市场数据页面
            await self.take_screenshot(page, "market_data", len(scenario["steps"]))

            # 检查图表元素
            chart_selectors = [
                "canvas", ".chart", "#chart", "[id*='chart']",
                ".echarts", ".tradingview", ".kline", ".candlestick"
            ]

            chart_count = 0
            for selector in chart_selectors:
                elements = await page.query_selector_all(selector)
                chart_count += len(elements)

            scenario["steps"].append(f"发现{chart_count}个图表元素")

            if chart_count == 0:
                scenario["issues"].append("未发现图表元素")
                scenario["user_feedback"].append("市场数据页面缺少可视化图表")
            else:
                scenario["user_feedback"].append("市场数据页面包含图表，有助于用户分析")

            # 检查股票列表
            stock_list_selectors = [
                ".stock-list", ".market-list", "table", ".data-table",
                "[data-testid*='stock']", "[data-testid*='market']", ".ticker"
            ]

            stock_list_count = 0
            for selector in stock_list_selectors:
                elements = await page.query_selector_all(selector)
                stock_list_count += len(elements)

            scenario["steps"].append(f"发现{stock_list_count}个股票列表")

            # 检查实时数据更新
            scenario["steps"].append("等待实时数据更新")
            scenario["real_user_actions"].append("用户观察实时行情数据变化")
            await page.wait_for_timeout(5000)

            # 测试搜索功能
            search_selectors = ["input[placeholder*='搜索']", ".search-input", "[data-testid*='search']"]
            for selector in search_selectors:
                search_input = await page.query_selector(selector)
                if search_input:
                    await search_input.fill("平安银行")
                    await page.wait_for_timeout(2000)
                    scenario["steps"].append("测试股票搜索功能")
                    scenario["real_user_actions"].append("用户搜索特定股票")
                    break

        except Exception as e:
            scenario["issues"].append(f"市场数据测试失败: {str(e)}")
            logger.error(f"市场数据测试失败: {e}")

        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)

    async def test_user_interactions(self, page):
        """测试用户交互"""
        scenario = {
            "name": "用户交互深度测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": [],
            "real_user_actions": []
        }

        try:
            # 检查表单元素
            forms = await page.query_selector_all("form")
            scenario["steps"].append(f"发现{len(forms)}个表单")

            # 检查输入元素
            inputs = await page.query_selector_all("input, textarea, select")
            scenario["steps"].append(f"发现{len(inputs)}个输入元素")

            # 检查按钮
            buttons = await page.query_selector_all("button, input[type='button'], input[type='submit']")
            scenario["steps"].append(f"发现{len(buttons)}个按钮")

            scenario["real_user_actions"].append("用户测试页面交互元素")

            # 测试按钮响应
            for i, button in enumerate(buttons[:5]):  # 只测试前5个按钮
                try:
                    text = await button.text_content()
                    if text and text.strip():
                        start_time = time.time()
                        await button.click()
                        await page.wait_for_timeout(500)
                        response_time = time.time() - start_time
                        scenario["steps"].append(f"按钮'{text.strip()}'响应时间: {response_time:.3f}秒")

                        if response_time > 2:
                            scenario["issues"].append(f"按钮'{text.strip()}'响应时间过长")
                except:
                    continue

            # 测试键盘导航
            scenario["steps"].append("测试键盘导航")
            scenario["real_user_actions"].append("用户使用键盘导航")
            await page.keyboard.press("Tab")
            await page.wait_for_timeout(500)
            await page.keyboard.press("Tab")
            await page.wait_for_timeout(500)

            # 测试表单验证
            for form in forms[:3]:  # 只测试前3个表单
                try:
                    inputs_in_form = await form.query_selector_all("input")
                    for input_elem in inputs_in_form[:2]:
                        input_type = await input_elem.get_attribute("type")
                        if input_type == "email":
                            await input_elem.fill("invalid-email")
                            scenario["steps"].append("测试邮箱格式验证")
                        elif input_type == "number":
                            await input_elem.fill("abc")
                            scenario["steps"].append("测试数字格式验证")
                except:
                    continue

            # 用户体验反馈
            if len(buttons) > 0:
                scenario["user_feedback"].append("页面包含交互按钮，用户可以进行操作")
            if len(inputs) > 0:
                scenario["user_feedback"].append("页面包含输入元素，支持用户输入")

        except Exception as e:
            scenario["issues"].append(f"用户交互测试失败: {str(e)}")
            logger.error(f"用户交互测试失败: {e}")

        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)

async def main():
    """主函数"""
    tester = ComprehensiveTradingCenterTester()

    try:
        logger.info(f"开始全面交易中心测试 - 会话ID: {tester.session_id}")

        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器窗口以便观察
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )

            try:
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                )

                page = await context.new_page()

                # 设置控制台错误监听
                await tester.record_console_errors(page)
                await tester.record_network_activity(page)

                # 运行所有测试场景
                test_scenarios = [
                    tester.test_initial_access,
                    tester.test_navigation_system,
                    tester.test_trading_functionality,
                    tester.test_market_data,
                    tester.test_user_interactions
                ]

                for test_func in test_scenarios:
                    try:
                        logger.info(f"执行测试: {test_func.__name__}")
                        await test_func(page)
                        await page.wait_for_timeout(1000)  # 测试间隔
                    except Exception as e:
                        logger.error(f"测试{test_func.__name__}失败: {e}")
                        tester.test_results["discovered_issues"].append({
                            "test": test_func.__name__,
                            "error": str(e),
                            "timestamp": datetime.now().isoformat()
                        })

                # 生成测试报告
                await tester.generate_final_report()

            finally:
                await browser.close()

        print("测试完成！")

    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        traceback.print_exc()

    async def generate_final_report(self):
        """生成最终测试报告"""
        try:
            # 计算总体统计
            total_issues = sum(len(scenario.get("issues", [])) for scenario in self.test_results["test_scenarios"])
            total_scenarios = len(self.test_results["test_scenarios"])
            console_errors = len(self.test_results["console_errors"])
            network_issues = len(self.test_results["network_issues"])

            # 生成建议
            recommendations = []
            if total_issues == 0 and console_errors == 0:
                recommendations.append("平台功能完整，用户体验优秀")
                grade = "优秀"
            elif total_issues < 5 and console_errors < 3:
                recommendations.append("发现少量问题，建议优化")
                grade = "良好"
            elif total_issues < 10 and console_errors < 5:
                recommendations.append("发现一些问题，建议及时修复")
                grade = "一般"
            else:
                recommendations.append("发现较多功能性问题，建议优先修复")
                grade = "需要改进"

            # 性能建议
            load_times = [metric["value"] for metric in self.test_results["performance_metrics"] if metric["metric"] == "initial_load_time"]
            if load_times and max(load_times) > 3:
                recommendations.append("页面加载时间较长，建议优化性能")

            # 控制台错误建议
            if console_errors > 0:
                recommendations.append("发现控制台错误，建议修复JavaScript问题")

            # 网络问题建议
            if network_issues > 0:
                recommendations.append("发现网络请求问题，建议检查API接口")

            self.test_results["recommendations"] = recommendations

            # 完成测试
            self.test_results["end_time"] = datetime.now().isoformat()
            self.test_results["total_duration"] = (datetime.now() - self.start_time).total_seconds()

            # 总体评估
            self.test_results["overall_assessment"] = {
                "grade": grade,
                "total_scenarios": total_scenarios,
                "total_issues": total_issues,
                "console_errors": console_errors,
                "network_issues": network_issues,
                "issue_rate": total_issues / total_scenarios if total_scenarios > 0 else 0
            }

            # 保存测试报告
            report_filename = f"comprehensive_trading_center_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_path = Path(__file__).parent / report_filename

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)

            logger.info(f"测试完成！报告保存至: {report_filename}")
            logger.info(f"总体评估: {grade}")
            logger.info(f"发现问题: {total_issues}个")
            logger.info(f"控制台错误: {console_errors}个")

            # 打印详细报告
            print("\n" + "="*80)
            print("交易中心深度测试完成")
            print("="*80)
            print(f"总体评估: {grade}")
            print(f"测试场景: {total_scenarios}个")
            print(f"发现问题: {total_issues}个")
            print(f"控制台错误: {console_errors}个")
            print(f"网络问题: {network_issues}个")
            print(f"测试时长: {self.test_results['total_duration']:.2f}秒")
            print(f"截图数量: {len(self.test_results['screenshots'])}张")
            print("\n主要建议:")
            for rec in recommendations:
                print(f"- {rec}")
            print("\n详细问题:")
            for scenario in self.test_results["test_scenarios"]:
                if scenario.get("issues"):
                    print(f"\n{scenario['name']}:")
                    for issue in scenario["issues"]:
                        print(f"  - {issue}")
            print("="*80)

            return self.test_results

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return None

if __name__ == "__main__":
    asyncio.run(main())
