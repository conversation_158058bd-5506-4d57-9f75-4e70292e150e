"""
负载测试和压力测试套件
测试系统在高负载和极限条件下的表现
"""

import asyncio
import time
import json
import logging
import random
import statistics
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
import pytest
import httpx
import websockets
from dataclasses import dataclass, field

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class LoadTestConfig:
    """负载测试配置"""
    base_url: str = "http://localhost:8000"
    ws_url: str = "ws://localhost:8000/ws"
    concurrent_users: int = 50
    test_duration: int = 60  # 秒
    ramp_up_time: int = 10  # 秒
    think_time: float = 1.0  # 用户操作间隔时间
    timeout: float = 30.0
    

@dataclass
class LoadTestMetrics:
    """负载测试指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    response_times: List[float] = field(default_factory=list)
    error_messages: List[str] = field(default_factory=list)
    start_time: float = 0
    end_time: float = 0
    
    def add_request(self, success: bool, response_time: float, error_msg: str = None):
        """添加请求结果"""
        self.total_requests += 1
        self.response_times.append(response_time)
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
            if error_msg:
                self.error_messages.append(error_msg)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.response_times:
            return {}
            
        duration = self.end_time - self.start_time if self.end_time > 0 else 0
        
        sorted_times = sorted(self.response_times)
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0,
            "throughput": self.total_requests / duration if duration > 0 else 0,
            "avg_response_time": statistics.mean(self.response_times) * 1000,  # ms
            "min_response_time": min(self.response_times) * 1000,
            "max_response_time": max(self.response_times) * 1000,
            "p50_response_time": sorted_times[int(0.5 * len(sorted_times))] * 1000,
            "p95_response_time": sorted_times[int(0.95 * len(sorted_times))] * 1000,
            "p99_response_time": sorted_times[int(0.99 * len(sorted_times))] * 1000,
            "test_duration": duration,
            "error_rate": (self.failed_requests / self.total_requests * 100) if self.total_requests > 0 else 0,
            "unique_errors": len(set(self.error_messages))
        }


class LoadTestUser:
    """负载测试虚拟用户"""
    
    def __init__(self, user_id: str, config: LoadTestConfig):
        self.user_id = user_id
        self.config = config
        self.session = None
        self.auth_token = None
        self.metrics = LoadTestMetrics()
        
    async def setup(self):
        """用户初始化"""
        self.session = httpx.AsyncClient(timeout=self.config.timeout)
        await self.authenticate()
        
    async def teardown(self):
        """用户清理"""
        if self.session:
            await self.session.aclose()
            
    async def authenticate(self):
        """用户认证"""
        try:
            response = await self.session.post(
                f"{self.config.base_url}/api/v1/auth/login",
                json={
                    "username": f"testuser{self.user_id}",
                    "password": "testpass123"
                }
            )
            if response.status_code == 200:
                self.auth_token = response.json()["access_token"]
            else:
                # 如果认证失败，使用模拟token
                self.auth_token = f"test-token-{self.user_id}"
        except Exception as e:
            logger.warning(f"用户 {self.user_id} 认证失败: {str(e)}")
            self.auth_token = f"test-token-{self.user_id}"
    
    @property
    def auth_headers(self):
        """认证头"""
        return {"Authorization": f"Bearer {self.auth_token}"}
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> bool:
        """发送HTTP请求"""
        start_time = time.time()
        success = False
        error_msg = None
        
        try:
            response = await self.session.request(
                method,
                f"{self.config.base_url}{endpoint}",
                headers=self.auth_headers,
                **kwargs
            )
            success = response.status_code < 400
            if not success:
                error_msg = f"HTTP {response.status_code}: {response.text[:100]}"
                
        except Exception as e:
            error_msg = str(e)[:100]
            
        response_time = time.time() - start_time
        self.metrics.add_request(success, response_time, error_msg)
        return success


class TradingLoadTestScenario:
    """交易系统负载测试场景"""
    
    def __init__(self, config: LoadTestConfig):
        self.config = config
        self.users: List[LoadTestUser] = []
        self.overall_metrics = LoadTestMetrics()
        
    async def setup_users(self):
        """初始化虚拟用户"""
        self.users = []
        for i in range(self.config.concurrent_users):
            user = LoadTestUser(f"user-{i:04d}", self.config)
            await user.setup()
            self.users.append(user)
        logger.info(f"初始化了 {len(self.users)} 个虚拟用户")
    
    async def teardown_users(self):
        """清理虚拟用户"""
        for user in self.users:
            await user.teardown()
        self.users.clear()
    
    async def run_user_scenario(self, user: LoadTestUser):
        """运行用户场景"""
        end_time = time.time() + self.config.test_duration
        
        while time.time() < end_time:
            # 随机选择操作
            operation = random.choice([
                self.get_account_info,
                self.get_positions,
                self.get_orders,
                self.get_market_data,
                self.submit_order,
                self.cancel_order
            ])
            
            await operation(user)
            
            # 模拟用户思考时间
            await asyncio.sleep(random.uniform(0.5, self.config.think_time * 2))
    
    async def get_account_info(self, user: LoadTestUser):
        """获取账户信息"""
        await user.make_request("GET", "/api/v1/trading/account")
    
    async def get_positions(self, user: LoadTestUser):
        """获取持仓信息"""
        await user.make_request("GET", "/api/v1/trading/positions")
    
    async def get_orders(self, user: LoadTestUser):
        """获取订单列表"""
        params = {
            "limit": random.randint(10, 50),
            "status": random.choice(["pending", "filled", "cancelled", None])
        }
        await user.make_request("GET", "/api/v1/trading/orders", params=params)
    
    async def get_market_data(self, user: LoadTestUser):
        """获取市场数据"""
        symbols = ["000001", "000002", "000300", "600000", "600036"]
        symbol = random.choice(symbols)
        
        # 随机选择市场数据类型
        endpoint_type = random.choice(["tick", "kline", "depth"])
        
        if endpoint_type == "tick":
            await user.make_request("GET", f"/api/v1/market/tick/{symbol}")
        elif endpoint_type == "kline":
            params = {
                "interval": random.choice(["1m", "5m", "1h", "1d"]),
                "limit": random.randint(50, 200)
            }
            await user.make_request("GET", f"/api/v1/market/kline/{symbol}", params=params)
        else:  # depth
            params = {"level": random.randint(5, 20)}
            await user.make_request("GET", f"/api/v1/market/depth/{symbol}", params=params)
    
    async def submit_order(self, user: LoadTestUser):
        """提交订单"""
        symbols = ["000001", "000002", "000300"]
        order_data = {
            "symbol": random.choice(symbols),
            "side": random.choice(["buy", "sell"]),
            "order_type": random.choice(["limit", "market"]),
            "quantity": random.randint(100, 1000),
            "price": f"{random.uniform(10.0, 100.0):.2f}",
            "time_in_force": "GTC"
        }
        await user.make_request("POST", "/api/v1/trading/orders", json=order_data)
    
    async def cancel_order(self, user: LoadTestUser):
        """取消订单"""
        # 模拟取消订单（使用随机订单ID）
        order_id = f"mock-order-{random.randint(1000, 9999)}"
        await user.make_request("POST", f"/api/v1/trading/orders/{order_id}/cancel")
    
    async def run_load_test(self) -> Dict[str, Any]:
        """运行负载测试"""
        logger.info(f"开始负载测试 - {self.config.concurrent_users} 并发用户，持续 {self.config.test_duration} 秒")
        
        await self.setup_users()
        
        self.overall_metrics.start_time = time.time()
        
        # 分批启动用户（模拟逐步增加负载）
        batch_size = max(1, self.config.concurrent_users // self.config.ramp_up_time)
        
        tasks = []
        for i in range(0, len(self.users), batch_size):
            batch_users = self.users[i:i + batch_size]
            
            # 启动这批用户
            for user in batch_users:
                task = asyncio.create_task(self.run_user_scenario(user))
                tasks.append(task)
            
            # 等待一段时间再启动下一批
            if i + batch_size < len(self.users):
                await asyncio.sleep(1)
        
        # 等待所有用户完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        self.overall_metrics.end_time = time.time()
        
        # 合并所有用户的指标
        for user in self.users:
            self.overall_metrics.total_requests += user.metrics.total_requests
            self.overall_metrics.successful_requests += user.metrics.successful_requests
            self.overall_metrics.failed_requests += user.metrics.failed_requests
            self.overall_metrics.response_times.extend(user.metrics.response_times)
            self.overall_metrics.error_messages.extend(user.metrics.error_messages)
        
        await self.teardown_users()
        
        results = self.overall_metrics.get_statistics()
        logger.info(f"负载测试完成 - 总请求: {results['total_requests']}, 成功率: {results['success_rate']:.2f}%")
        
        return results


class WebSocketLoadTester:
    """WebSocket负载测试器"""
    
    def __init__(self, config: LoadTestConfig):
        self.config = config
        self.connections = []
        self.metrics = LoadTestMetrics()
    
    async def test_concurrent_connections(self, max_connections: int = 1000) -> Dict[str, Any]:
        """测试并发连接数"""
        logger.info(f"测试WebSocket并发连接 - 目标: {max_connections} 连接")
        
        successful_connections = 0
        failed_connections = 0
        connection_times = []
        
        async def establish_connection(conn_id: int):
            nonlocal successful_connections, failed_connections
            
            start_time = time.time()
            try:
                async with websockets.connect(
                    self.config.ws_url,
                    timeout=self.config.timeout
                ) as websocket:
                    connection_time = time.time() - start_time
                    connection_times.append(connection_time)
                    successful_connections += 1
                    
                    # 保持连接活跃
                    await asyncio.sleep(random.uniform(5, 15))
                    
            except Exception as e:
                failed_connections += 1
                logger.debug(f"连接 {conn_id} 失败: {str(e)}")
        
        # 分批建立连接
        batch_size = 50
        tasks = []
        
        for i in range(0, max_connections, batch_size):
            batch_end = min(i + batch_size, max_connections)
            
            # 启动这批连接
            for conn_id in range(i, batch_end):
                task = asyncio.create_task(establish_connection(conn_id))
                tasks.append(task)
            
            # 等待一小段时间避免过快建立连接
            await asyncio.sleep(0.1)
        
        # 等待所有连接完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            "target_connections": max_connections,
            "successful_connections": successful_connections,
            "failed_connections": failed_connections,
            "success_rate": (successful_connections / max_connections * 100) if max_connections > 0 else 0,
            "avg_connection_time": statistics.mean(connection_times) if connection_times else 0,
            "max_connection_time": max(connection_times) if connection_times else 0
        }
    
    async def test_message_load(self, connections: int = 100, messages_per_conn: int = 1000) -> Dict[str, Any]:
        """测试消息负载"""
        logger.info(f"测试WebSocket消息负载 - {connections} 连接，每连接 {messages_per_conn} 消息")
        
        messages_sent = 0
        messages_received = 0
        message_latencies = []
        
        async def connection_handler(conn_id: int):
            nonlocal messages_sent, messages_received
            
            try:
                async with websockets.connect(self.config.ws_url) as websocket:
                    # 发送消息任务
                    async def send_messages():
                        nonlocal messages_sent
                        for i in range(messages_per_conn):
                            message = {
                                "type": "test_message",
                                "conn_id": conn_id,
                                "msg_id": i,
                                "timestamp": time.time(),
                                "data": f"test_data_{i}"
                            }
                            await websocket.send(json.dumps(message))
                            messages_sent += 1
                            await asyncio.sleep(0.01)  # 控制发送频率
                    
                    # 接收消息任务
                    async def receive_messages():
                        nonlocal messages_received
                        try:
                            async for message in websocket:
                                receive_time = time.time()
                                data = json.loads(message)
                                
                                if "timestamp" in data:
                                    latency = receive_time - data["timestamp"]
                                    message_latencies.append(latency)
                                
                                messages_received += 1
                        except websockets.exceptions.ConnectionClosed:
                            pass
                    
                    # 并发执行发送和接收
                    await asyncio.gather(
                        send_messages(),
                        receive_messages(),
                        return_exceptions=True
                    )
                    
            except Exception as e:
                logger.debug(f"连接 {conn_id} 出错: {str(e)}")
        
        # 启动所有连接
        start_time = time.time()
        tasks = [connection_handler(i) for i in range(connections)]
        await asyncio.gather(*tasks, return_exceptions=True)
        duration = time.time() - start_time
        
        return {
            "connections": connections,
            "target_messages": connections * messages_per_conn,
            "messages_sent": messages_sent,
            "messages_received": messages_received,
            "message_loss_rate": ((messages_sent - messages_received) / messages_sent * 100) if messages_sent > 0 else 0,
            "avg_latency": statistics.mean(message_latencies) * 1000 if message_latencies else 0,  # ms
            "p95_latency": sorted(message_latencies)[int(0.95 * len(message_latencies))] * 1000 if message_latencies else 0,
            "throughput": messages_sent / duration if duration > 0 else 0,
            "test_duration": duration
        }


class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self):
        self.results = {}
    
    async def run_api_stress_test(self) -> Dict[str, Any]:
        """运行API压力测试"""
        logger.info("开始API压力测试")
        
        # 不同负载级别的测试
        test_configs = [
            LoadTestConfig(concurrent_users=10, test_duration=30),   # 轻载
            LoadTestConfig(concurrent_users=50, test_duration=60),   # 中载
            LoadTestConfig(concurrent_users=100, test_duration=90),  # 重载
            LoadTestConfig(concurrent_users=200, test_duration=120), # 极重载
        ]
        
        results = {}
        
        for i, config in enumerate(test_configs):
            logger.info(f"运行压力测试级别 {i+1}/4 - {config.concurrent_users} 并发用户")
            
            scenario = TradingLoadTestScenario(config)
            result = await scenario.run_load_test()
            results[f"level_{i+1}_{config.concurrent_users}_users"] = result
            
            # 在测试级别之间休息
            if i < len(test_configs) - 1:
                logger.info("等待系统恢复...")
                await asyncio.sleep(30)
        
        return results
    
    async def run_websocket_stress_test(self) -> Dict[str, Any]:
        """运行WebSocket压力测试"""
        logger.info("开始WebSocket压力测试")
        
        config = LoadTestConfig()
        ws_tester = WebSocketLoadTester(config)
        
        results = {}
        
        # 连接压力测试
        connection_tests = [100, 500, 1000, 2000]
        for conn_count in connection_tests:
            logger.info(f"测试 {conn_count} 并发连接")
            result = await ws_tester.test_concurrent_connections(conn_count)
            results[f"connections_{conn_count}"] = result
            
            await asyncio.sleep(10)  # 等待连接清理
        
        # 消息压力测试
        message_tests = [
            (50, 1000),   # 50连接，每连接1000消息
            (100, 500),   # 100连接，每连接500消息
            (200, 250),   # 200连接，每连接250消息
        ]
        
        for connections, messages in message_tests:
            logger.info(f"测试 {connections} 连接，每连接 {messages} 消息")
            result = await ws_tester.test_message_load(connections, messages)
            results[f"messages_{connections}x{messages}"] = result
            
            await asyncio.sleep(10)
        
        return results
    
    async def run_memory_stress_test(self) -> Dict[str, Any]:
        """运行内存压力测试"""
        logger.info("开始内存压力测试")
        
        import psutil
        import gc
        
        def get_memory_info():
            process = psutil.Process()
            return {
                "rss": process.memory_info().rss / 1024 / 1024,  # MB
                "vms": process.memory_info().vms / 1024 / 1024,  # MB
                "percent": process.memory_percent()
            }
        
        initial_memory = get_memory_info()
        logger.info(f"初始内存使用: {initial_memory}")
        
        # 创建大量对象模拟内存压力
        test_data = []
        memory_snapshots = []
        
        for i in range(10):
            # 创建大量数据
            batch_data = []
            for j in range(100000):
                batch_data.append({
                    "id": i * 100000 + j,
                    "timestamp": datetime.now(),
                    "data": f"test_data_{i}_{j}" * 10,
                    "values": list(range(100))
                })
            
            test_data.append(batch_data)
            
            # 记录内存使用
            memory_info = get_memory_info()
            memory_snapshots.append(memory_info)
            logger.info(f"批次 {i+1}: 内存使用 {memory_info['rss']:.1f}MB")
            
            await asyncio.sleep(1)
        
        peak_memory = max(memory_snapshots, key=lambda x: x['rss'])
        
        # 清理数据
        test_data.clear()
        gc.collect()
        
        final_memory = get_memory_info()
        
        return {
            "initial_memory": initial_memory,
            "peak_memory": peak_memory,
            "final_memory": final_memory,
            "memory_increase": peak_memory['rss'] - initial_memory['rss'],
            "memory_recovered": initial_memory['rss'] - final_memory['rss'],
            "snapshots": memory_snapshots
        }


# 测试用例

@pytest.mark.load
@pytest.mark.asyncio
async def test_basic_load():
    """基础负载测试"""
    config = LoadTestConfig(concurrent_users=20, test_duration=30)
    scenario = TradingLoadTestScenario(config)
    
    results = await scenario.run_load_test()
    
    # 验证基础负载下的性能
    assert results['success_rate'] > 95, f"成功率过低: {results['success_rate']}%"
    assert results['avg_response_time'] < 1000, f"平均响应时间过长: {results['avg_response_time']}ms"
    assert results['p95_response_time'] < 2000, f"P95响应时间过长: {results['p95_response_time']}ms"


@pytest.mark.load
@pytest.mark.asyncio
async def test_high_load():
    """高负载测试"""
    config = LoadTestConfig(concurrent_users=100, test_duration=60)
    scenario = TradingLoadTestScenario(config)
    
    results = await scenario.run_load_test()
    
    # 高负载下的最低要求
    assert results['success_rate'] > 90, f"高负载下成功率过低: {results['success_rate']}%"
    assert results['avg_response_time'] < 2000, f"高负载下平均响应时间过长: {results['avg_response_time']}ms"


@pytest.mark.stress
@pytest.mark.asyncio  
async def test_websocket_connection_stress():
    """WebSocket连接压力测试"""
    config = LoadTestConfig()
    ws_tester = WebSocketLoadTester(config)
    
    results = await ws_tester.test_concurrent_connections(500)
    
    # 验证连接能力
    assert results['success_rate'] > 80, f"WebSocket连接成功率过低: {results['success_rate']}%"
    assert results['avg_connection_time'] < 2.0, f"连接时间过长: {results['avg_connection_time']}s"


@pytest.mark.stress
@pytest.mark.asyncio
async def test_full_stress_suite():
    """完整压力测试套件"""
    runner = StressTestRunner()
    
    # 运行API压力测试
    api_results = await runner.run_api_stress_test()
    
    # 验证各级别压力测试结果
    for level, result in api_results.items():
        # 即使在高压力下，也应该有基本的成功率
        assert result['success_rate'] > 70, f"{level} 压力测试成功率过低: {result['success_rate']}%"
    
    # 运行WebSocket压力测试
    ws_results = await runner.run_websocket_stress_test()
    
    # 验证WebSocket压力测试
    for test_name, result in ws_results.items():
        if 'success_rate' in result:
            assert result['success_rate'] > 60, f"{test_name} WebSocket测试成功率过低: {result['success_rate']}%"


if __name__ == "__main__":
    async def main():
        """运行负载和压力测试"""
        print("=" * 60)
        print("量化交易系统负载和压力测试")
        print("=" * 60)
        
        runner = StressTestRunner()
        
        # 1. API压力测试
        print("\n1. API压力测试")
        print("-" * 40)
        api_results = await runner.run_api_stress_test()
        
        for level, result in api_results.items():
            print(f"\n{level}:")
            print(f"  总请求: {result['total_requests']}")
            print(f"  成功率: {result['success_rate']:.2f}%")
            print(f"  平均响应时间: {result['avg_response_time']:.2f}ms")
            print(f"  P95响应时间: {result['p95_response_time']:.2f}ms")
            print(f"  吞吐量: {result['throughput']:.2f} req/s")
        
        # 2. WebSocket压力测试
        print("\n\n2. WebSocket压力测试")
        print("-" * 40)
        ws_results = await runner.run_websocket_stress_test()
        
        for test_name, result in ws_results.items():
            print(f"\n{test_name}:")
            for key, value in result.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")
        
        # 3. 内存压力测试
        print("\n\n3. 内存压力测试")
        print("-" * 40)
        memory_results = await runner.run_memory_stress_test()
        
        print(f"初始内存: {memory_results['initial_memory']['rss']:.1f}MB")
        print(f"峰值内存: {memory_results['peak_memory']['rss']:.1f}MB")
        print(f"最终内存: {memory_results['final_memory']['rss']:.1f}MB")
        print(f"内存增长: {memory_results['memory_increase']:.1f}MB")
        
        print("\n" + "=" * 60)
        print("压力测试完成!")
        print("=" * 60)
    
    # 运行测试
    asyncio.run(main())