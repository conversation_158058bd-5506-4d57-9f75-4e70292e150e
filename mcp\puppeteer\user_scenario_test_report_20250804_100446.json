{"timestamp": "2025-08-04T10:04:18.790544", "scenarios": [{"name": "新用户入门流程", "start_time": 1754273059.9893403, "steps": ["访问首页成功", "页面加载时间: 865.20ms", "Vue应用根节点加载成功", "按钮组件加载成功", "桌面端响应式测试完成", "平板端响应式测试完成", "手机端响应式测试完成"], "success": false, "issues": ["导航菜单未找到", "手机端发现48个小于14px的文字元素", "发现5个缺少标签的表单元素"], "end_time": 1754273074.5970235, "duration": 14.607683181762695}, {"name": "市场数据浏览", "start_time": 1754273074.597661, "steps": ["导航到市场页面"], "success": true, "issues": ["未发现图表元素", "未发现数据表格"], "end_time": 1754273081.9293509, "duration": 7.331689834594727}, {"name": "策略管理", "start_time": 1754273081.929713, "steps": ["导航到策略页面", "发现4个策略卡片", "发现创建策略按钮"], "success": true, "issues": [], "end_time": 1754273086.2102349, "duration": 4.280521869659424}], "performance_metrics": {"homepage_load": {"domContentLoaded": 0.10000000009313226, "loadComplete": 0.5, "totalTime": 865.2000000001863}}, "accessibility_issues": [{"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:04:20.316123"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:04:34.938069"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:38.350542"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:38.365998"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:38.366099"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:38.366177"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:38.381603"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:38.384171"}, {"type": "console_error", "message": "WebSocket错误详情: Event", "timestamp": "2025-08-04T10:04:38.384614"}, {"type": "console_error", "message": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "timestamp": "2025-08-04T10:04:38.388418"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-08-04T10:04:38.388609"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:41.693080"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:41.725466"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:41.725572"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:41.725652"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:04:41.725726"}, {"type": "console_error", "message": "❌ 容器检查失败，超过最大重试次数", "timestamp": "2025-08-04T10:04:41.887005"}, {"type": "console_error", "message": "❌ 容器准备失败，尝试fallback方案", "timestamp": "2025-08-04T10:04:41.887131"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:04:41.954536"}], "user_experience_issues": [], "summary": {"total_scenarios": 3, "successful_scenarios": 2, "success_rate": 0.6666666666666666, "total_issues": 5, "total_accessibility_issues": 19}}