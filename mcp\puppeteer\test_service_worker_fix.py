#!/usr/bin/env python3
"""
测试Service Worker修复
验证sw.js文件是否正确加载和注册
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

async def test_service_worker_fix():
    """测试Service Worker修复"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 测试Service Worker修复...")
        
        # 访问首页
        await page.goto('http://localhost:5173/', wait_until='domcontentloaded')
        await page.wait_for_timeout(5000)  # 等待Service Worker注册
        
        print("📍 已访问首页，等待Service Worker注册...")
        
        # 检查Service Worker注册状态
        sw_status = await page.evaluate('''
            async () => {
                if ('serviceWorker' in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.getRegistration();
                        return {
                            supported: true,
                            registered: !!registration,
                            scope: registration ? registration.scope : null,
                            state: registration && registration.active ? registration.active.state : null,
                            scriptURL: registration && registration.active ? registration.active.scriptURL : null
                        };
                    } catch (error) {
                        return {
                            supported: true,
                            registered: false,
                            error: error.message
                        };
                    }
                } else {
                    return {
                        supported: false,
                        registered: false,
                        error: 'Service Worker not supported'
                    };
                }
            }
        ''')
        
        print(f"🔍 Service Worker状态: {json.dumps(sw_status, indent=2, ensure_ascii=False)}")
        
        # 检查控制台错误
        console_errors = []
        
        def handle_console(msg):
            if msg.type == 'error' and 'service worker' in msg.text.lower():
                console_errors.append({
                    "type": msg.type,
                    "text": msg.text,
                    "timestamp": datetime.now().isoformat()
                })
                print(f"❌ 控制台错误: {msg.text}")
        
        page.on('console', handle_console)
        
        # 等待一段时间观察错误
        await page.wait_for_timeout(3000)
        
        # 手动尝试注册Service Worker
        manual_registration = await page.evaluate('''
            async () => {
                if ('serviceWorker' in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.register('/sw.js');
                        return {
                            success: true,
                            scope: registration.scope,
                            state: registration.installing ? 'installing' : 
                                   registration.waiting ? 'waiting' : 
                                   registration.active ? 'active' : 'unknown'
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error: error.message
                        };
                    }
                } else {
                    return {
                        success: false,
                        error: 'Service Worker not supported'
                    };
                }
            }
        ''')
        
        print(f"🔧 手动注册结果: {json.dumps(manual_registration, indent=2, ensure_ascii=False)}")
        
        # 检查sw.js文件是否可访问
        sw_response = await page.evaluate('''
            async () => {
                try {
                    const response = await fetch('/sw.js');
                    return {
                        status: response.status,
                        statusText: response.statusText,
                        contentType: response.headers.get('content-type'),
                        ok: response.ok
                    };
                } catch (error) {
                    return {
                        error: error.message
                    };
                }
            }
        ''')
        
        print(f"📄 sw.js文件检查: {json.dumps(sw_response, indent=2, ensure_ascii=False)}")
        
        # 生成测试报告
        report = {
            "test_time": datetime.now().isoformat(),
            "service_worker_status": sw_status,
            "manual_registration": manual_registration,
            "sw_file_response": sw_response,
            "console_errors": console_errors,
            "fix_successful": (
                sw_response.get('ok', False) and 
                sw_response.get('contentType', '').startswith('text/javascript') and
                manual_registration.get('success', False)
            )
        }
        
        # 截图记录
        await page.screenshot(path='service_worker_fix_test.png', full_page=True)
        print("📸 测试截图已保存: service_worker_fix_test.png")
        
        # 保存报告
        with open('service_worker_fix_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print("📄 测试报告已保存: service_worker_fix_report.json")
        
        # 输出结果
        if report['fix_successful']:
            print("✅ Service Worker修复成功!")
            print(f"   - sw.js文件正确返回: {sw_response.get('contentType')}")
            print(f"   - 注册状态: {manual_registration.get('success')}")
            print(f"   - 控制台错误数: {len(console_errors)}")
        else:
            print("❌ Service Worker仍有问题")
            if console_errors:
                print("   控制台错误:")
                for error in console_errors:
                    print(f"     - {error['text']}")
                    
    except Exception as e:
        print(f"💥 测试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_service_worker_fix())
