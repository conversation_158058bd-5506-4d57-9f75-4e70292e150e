<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端登录功能测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
            max-width: 800px; 
            margin: 40px auto; 
            padding: 20px; 
            line-height: 1.6;
        }
        .test-section { 
            background: #f8f9fa; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px; 
            border-left: 4px solid #007bff;
        }
        .status { 
            display: inline-block; 
            padding: 4px 12px; 
            border-radius: 4px; 
            font-weight: bold; 
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .demo-button { background: #28a745; }
        .demo-button:hover { background: #1e7e34; }
        #result { 
            background: #fff; 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin-top: 15px; 
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .login-form {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>🧪 前端登录功能测试</h1>
    
    <div class="test-section">
        <h2>📋 测试信息</h2>
        <p><strong>前端地址:</strong> <a href="http://localhost:5173/login" target="_blank">http://localhost:5173/login</a></p>
        <p><strong>后端API:</strong> http://localhost:8000/api/v1/auth/login</p>
        <p><strong>演示账户:</strong> admin / admin123</p>
        <p><strong>测试时间:</strong> <span id="testTime"></span></p>
    </div>

    <div class="test-section">
        <h2>🔧 服务状态检查</h2>
        <p>前端服务 (5173): <span id="frontendStatus" class="status">检查中...</span></p>
        <p>后端服务 (8000): <span id="backendStatus" class="status">检查中...</span></p>
        <button onclick="checkServices()">重新检查服务</button>
    </div>

    <div class="test-section">
        <h2>🔐 登录API测试</h2>
        <div class="login-form">
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="admin123">
            </div>
            <button onclick="testLogin()">测试登录</button>
            <button class="demo-button" onclick="demoLogin()">演示登录 (admin/admin123)</button>
        </div>
        <div id="result"></div>
    </div>

    <div class="test-section">
        <h2>💡 使用说明</h2>
        <ol>
            <li>确保前端和后端服务都在运行</li>
            <li>打开新标签页访问: <a href="http://localhost:5173/login" target="_blank">http://localhost:5173/login</a></li>
            <li>点击绿色的"演示登录 (admin/admin123)"按钮</li>
            <li>或手动输入用户名admin和密码admin123</li>
            <li>如果成功，应该会跳转到主页面</li>
        </ol>
    </div>

    <script>
        document.getElementById('testTime').textContent = new Date().toLocaleString('zh-CN');

        async function checkServices() {
            // 检查前端服务
            try {
                const frontendResponse = await fetch('http://localhost:5173/', { 
                    method: 'HEAD', 
                    mode: 'no-cors' 
                });
                document.getElementById('frontendStatus').textContent = '✅ 运行中';
                document.getElementById('frontendStatus').className = 'status success';
            } catch (error) {
                document.getElementById('frontendStatus').textContent = '❌ 离线';
                document.getElementById('frontendStatus').className = 'status error';
            }

            // 检查后端服务
            try {
                const backendResponse = await fetch('http://localhost:8000/health');
                if (backendResponse.ok) {
                    document.getElementById('backendStatus').textContent = '✅ 运行中';
                    document.getElementById('backendStatus').className = 'status success';
                } else {
                    document.getElementById('backendStatus').textContent = '⚠️ 响应异常';
                    document.getElementById('backendStatus').className = 'status warning';
                }
            } catch (error) {
                document.getElementById('backendStatus').textContent = '❌ 离线';
                document.getElementById('backendStatus').className = 'status error';
            }
        }

        function demoLogin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            testLogin();
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.textContent = '正在测试登录...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                let result = `测试结果 (${new Date().toLocaleTimeString()}):\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应数据: ${JSON.stringify(data, null, 2)}`;
                
                if (data.success && response.status === 200) {
                    result += `\n\n✅ 登录测试成功！`;
                    result += `\n用户: ${data.data.user.username}`;
                    result += `\n邮箱: ${data.data.user.email}`;
                    result += `\nToken: ${data.data.token.substring(0, 50)}...`;
                } else {
                    result += `\n\n❌ 登录测试失败: ${data.message || '未知错误'}`;
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `❌ 网络错误: ${error.message}\n\n请检查：\n1. 后端服务是否在8000端口运行\n2. CORS配置是否正确\n3. 网络连接是否正常`;
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>