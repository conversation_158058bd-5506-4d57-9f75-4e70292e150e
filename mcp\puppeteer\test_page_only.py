#!/usr/bin/env python3
"""
仅测试页面，不重启服务
"""

import asyncio
import json
import os
from datetime import datetime
from playwright.async_api import async_playwright

async def test_page_issue():
    """测试页面问题"""
    print("🔍 使用Puppeteer测试页面问题...")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(
            headless=False,  # 显示浏览器窗口
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = await context.new_page()
        
        # 监听控制台消息
        console_messages = []
        def handle_console(msg):
            console_messages.append({
                'type': msg.type,
                'text': msg.text,
                'location': str(msg.location) if msg.location else None
            })
            print(f"   控制台 [{msg.type}]: {msg.text}")
        
        page.on('console', handle_console)
        
        # 监听网络请求
        network_requests = []
        network_responses = []
        
        def handle_request(req):
            network_requests.append({
                'url': req.url,
                'method': req.method
            })
        
        def handle_response(resp):
            network_responses.append({
                'url': resp.url,
                'status': resp.status,
                'ok': resp.ok
            })
            if not resp.ok:
                print(f"   网络错误 [{resp.status}]: {resp.url}")
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        try:
            print("   访问前端页面...")
            await page.goto("http://localhost:5173", wait_until='networkidle', timeout=30000)
            
            # 等待页面加载
            print("   等待页面加载...")
            await asyncio.sleep(5)
            
            # 获取页面信息
            title = await page.title()
            print(f"   📄 页面标题: {title}")
            
            # 获取页面HTML
            html_content = await page.content()
            print(f"   📄 HTML内容长度: {len(html_content)} 字符")
            
            # 检查Vue应用是否加载
            app_element = await page.query_selector('#app')
            if app_element:
                app_content = await app_element.inner_html()
                print(f"   📦 Vue应用内容长度: {len(app_content)} 字符")
                
                if len(app_content) < 1000:
                    print("   ⚠️ Vue应用内容过少，可能未正确加载")
                    print(f"   内容预览: {app_content[:500]}...")
                    
                    # 检查是否有加载错误
                    loading_element = await page.query_selector('.loading, .error, .el-loading')
                    if loading_element:
                        loading_text = await loading_element.inner_text()
                        print(f"   🔄 检测到加载状态: {loading_text}")
                else:
                    print("   ✅ Vue应用内容丰富")
                    
                    # 检查关键元素
                    elements_to_check = [
                        ('.layout-sidebar', '侧边栏'),
                        ('.dashboard-view', '仪表板'),
                        ('.el-menu', '菜单'),
                        ('.metric-card', '指标卡片'),
                        ('.chart-container', '图表容器')
                    ]
                    
                    for selector, name in elements_to_check:
                        element = await page.query_selector(selector)
                        if element:
                            print(f"   ✅ 检测到{name}")
                        else:
                            print(f"   ❌ 未检测到{name}")
            else:
                print("   ❌ 未找到Vue应用容器")
            
            # 检查是否有路由
            current_url = page.url
            print(f"   🌐 当前URL: {current_url}")
            
            # 尝试等待路由加载
            try:
                await page.wait_for_selector('.dashboard-view, .login-view, .error-view', timeout=10000)
                print("   ✅ 检测到页面视图")
            except:
                print("   ❌ 未检测到任何页面视图")
            
            # 截图
            timestamp = datetime.now().strftime("%H%M%S")
            screenshot_path = f"page_issue_test_{timestamp}.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"   📸 已保存截图: {screenshot_path}")
            
            # 分析控制台消息
            print(f"\n   📋 控制台消息分析 ({len(console_messages)} 条):")
            error_count = sum(1 for msg in console_messages if msg['type'] == 'error')
            warning_count = sum(1 for msg in console_messages if msg['type'] == 'warning')
            
            print(f"      错误: {error_count} 个")
            print(f"      警告: {warning_count} 个")
            
            # 显示重要错误
            for msg in console_messages:
                if msg['type'] == 'error':
                    print(f"      ❌ {msg['text']}")
            
            # 分析网络请求
            print(f"\n   🌐 网络请求分析:")
            failed_requests = [r for r in network_responses if not r['ok']]
            api_requests = [r for r in network_responses if '/api/' in r['url']]
            js_requests = [r for r in network_responses if r['url'].endswith('.js')]
            css_requests = [r for r in network_responses if r['url'].endswith('.css')]
            
            print(f"      总请求: {len(network_responses)} 个")
            print(f"      失败请求: {len(failed_requests)} 个")
            print(f"      API请求: {len(api_requests)} 个")
            print(f"      JS文件: {len(js_requests)} 个")
            print(f"      CSS文件: {len(css_requests)} 个")
            
            # 显示失败的请求
            for req in failed_requests:
                print(f"      ❌ {req['status']} - {req['url']}")
            
            # 生成报告
            report = {
                'timestamp': datetime.now().isoformat(),
                'page_title': title,
                'html_content_length': len(html_content),
                'app_content_length': len(app_content) if app_element else 0,
                'console_errors': error_count,
                'console_warnings': warning_count,
                'total_console_messages': len(console_messages),
                'failed_requests': len(failed_requests),
                'total_requests': len(network_responses),
                'api_requests': len(api_requests),
                'js_requests': len(js_requests),
                'css_requests': len(css_requests),
                'current_url': current_url,
                'screenshot': screenshot_path,
                'console_messages': console_messages,
                'failed_network_requests': failed_requests
            }
            
            # 保存报告
            report_path = f"page_issue_report_{timestamp}.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"   📄 已保存报告: {report_path}")
            
            # 分析结果
            print(f"\n{'='*60}")
            print("📋 问题分析结果:")
            
            if report['app_content_length'] < 1000:
                print("❌ 确认问题: 页面内容确实过于简单!")
                print("可能的原因:")
                if error_count > 0:
                    print("  1. JavaScript执行错误")
                if len(failed_requests) > 0:
                    print("  2. 关键资源加载失败")
                if len(js_requests) == 0:
                    print("  3. JavaScript文件未加载")
                if len(api_requests) == 0:
                    print("  4. API请求未发起")
                print("  5. Vue.js应用未正确初始化")
            else:
                print("✅ 页面内容正常，可能是显示或渲染问题")
            
            print(f"\n关键指标:")
            print(f"  页面标题: {title}")
            print(f"  应用内容: {report['app_content_length']} 字符")
            print(f"  JavaScript错误: {error_count} 个")
            print(f"  失败请求: {len(failed_requests)} 个")
            print(f"  API请求: {len(api_requests)} 个")
            
            return report
            
        except Exception as e:
            print(f"   ❌ 页面测试失败: {e}")
            return None
        
        finally:
            await browser.close()

async def main():
    await test_page_issue()

if __name__ == "__main__":
    asyncio.run(main())
