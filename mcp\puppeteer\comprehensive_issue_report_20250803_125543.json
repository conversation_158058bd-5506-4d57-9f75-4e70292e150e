{"test_summary": {"total_tests": 15, "total_issues": 115, "high_severity": 1, "medium_severity": 27, "low_severity": 87, "test_time": "2025-08-03T12:55:43.258664"}, "issues_by_category": {"性能": [{"category": "性能", "severity": "高", "title": "首页页面加载过慢", "description": "加载时间: 5.91秒，超过5秒阈值", "evidence": null, "timestamp": "2025-08-03T12:54:59.319399", "url": "http://localhost:5173/"}], "代码质量": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.080267", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.082889", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.089147", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.095551", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.101620", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:55:26.102519", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:55:26.108208", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.121050", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.127360", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.127989", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.128584", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.134306", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.134986", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.140404", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.140894", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.141275", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.141608", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.141831", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:55:26.142081", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:55:26.142407", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:55:26.142628", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.142827", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.143131", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.143481", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.143896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.144340", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.144646", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.144953", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.145151", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.145416", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.145673", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.146000", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.154742", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T12:55:12.019017, total: 8}", "evidence": null, "timestamp": "2025-08-03T12:55:26.155288", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.161307", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.167898", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.175257", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.181709", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.189506", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:55:26.195750", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.200507", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.202011", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.203613", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.215320", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.218395", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.220472", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.228309", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.230445", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.234945", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T12:55:14.978748, total: 8}", "evidence": null, "timestamp": "2025-08-03T12:55:26.235898", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.249523", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.249957", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.250902", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.251242", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.251605", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.252069", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:55:26.252503", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.253012", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.253471", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.253817", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.254210", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.255326", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.255559", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.256654", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.257015", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.262304", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.262601", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.264151", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.264518", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.265476", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.265671", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.266802", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.267255", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.268354", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.268572", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.269911", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.270342", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.271338", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.271535", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.272488", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.272758", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.273772", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.273970", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.275095", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.275417", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.276359", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.276540", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.277469", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.277730", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.278666", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.278840", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.279779", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.280053", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.280994", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.281181", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.282232", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.282603", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.284171", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.284405", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.285658", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.285951", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.286907", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.287114", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.288077", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.288359", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.289320", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.289612", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.290616", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.290905", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.291181", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.291466", "url": "http://localhost:5173/risk"}], "UI/UX": [{"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:55:36.151256", "url": "http://localhost:5173/"}], "可访问性": [{"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:55:36.814486", "url": "http://localhost:5173/"}], "安全": [{"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:55:43.258223", "url": "http://localhost:5173/"}]}, "detailed_issues": [{"category": "性能", "severity": "高", "title": "首页页面加载过慢", "description": "加载时间: 5.91秒，超过5秒阈值", "evidence": null, "timestamp": "2025-08-03T12:54:59.319399", "url": "http://localhost:5173/"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.080267", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.082889", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.089147", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.095551", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.101620", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:55:26.102519", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:55:26.108208", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.121050", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.127360", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.127989", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.128584", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.134306", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.134986", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.140404", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.140894", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:55:26.141275", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.141608", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:55:26.141831", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:55:26.142081", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:55:26.142407", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:55:26.142628", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.142827", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.143131", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:55:26.143481", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.143896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.144340", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.144646", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.144953", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.145151", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.145416", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.145673", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.146000", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.154742", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T12:55:12.019017, total: 8}", "evidence": null, "timestamp": "2025-08-03T12:55:26.155288", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.161307", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.167898", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.175257", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.181709", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.189506", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:55:26.195750", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.200507", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.202011", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.203613", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.215320", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.218395", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:55:26.220472", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.228309", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:55:26.230445", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.234945", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T12:55:14.978748, total: 8}", "evidence": null, "timestamp": "2025-08-03T12:55:26.235898", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.249523", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.249957", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.250902", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.251242", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:55:26.251605", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:55:26.252069", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:55:26.252503", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.253012", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.253471", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.253817", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.254210", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.255326", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.255559", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.256654", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.257015", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.262304", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.262601", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.264151", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.264518", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.265476", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.265671", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.266802", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.267255", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.268354", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.268572", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.269911", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.270342", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.271338", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.271535", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.272488", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.272758", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.273772", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.273970", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.275095", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.275417", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.276359", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.276540", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.277469", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.277730", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.278666", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.278840", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.279779", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.280053", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.280994", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.281181", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.282232", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.282603", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.284171", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.284405", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.285658", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.285951", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.286907", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.287114", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.288077", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.288359", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T12:55:26.289320", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T12:55:26.289612", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.290616", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.290905", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.291181", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:55:26.291466", "url": "http://localhost:5173/risk"}, {"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:55:36.151256", "url": "http://localhost:5173/"}, {"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:55:36.814486", "url": "http://localhost:5173/"}, {"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:55:43.258223", "url": "http://localhost:5173/"}], "test_results": [{"test": "首页性能测试", "status": "PASS", "details": "加载时间: 5.91秒", "timestamp": "2025-08-03T12:54:59.319649"}, {"test": "仪表盘性能测试", "status": "PASS", "details": "加载时间: 0.84秒", "timestamp": "2025-08-03T12:55:00.163367"}, {"test": "市场数据性能测试", "status": "PASS", "details": "加载时间: 1.85秒", "timestamp": "2025-08-03T12:55:02.014375"}, {"test": "交易终端性能测试", "status": "PASS", "details": "加载时间: 1.45秒", "timestamp": "2025-08-03T12:55:03.466249"}, {"test": "策略中心性能测试", "status": "PASS", "details": "加载时间: 1.12秒", "timestamp": "2025-08-03T12:55:04.591013"}, {"test": "投资组合性能测试", "status": "PASS", "details": "加载时间: 0.68秒", "timestamp": "2025-08-03T12:55:05.274970"}, {"test": "风险管理性能测试", "status": "PASS", "details": "加载时间: 0.87秒", "timestamp": "2025-08-03T12:55:06.147093"}, {"test": "控制台错误检查", "status": "PASS", "details": "发现 24 个错误, 87 个警告", "timestamp": "2025-08-03T12:55:26.291751"}, {"test": "网络请求分析", "status": "PASS", "details": "发现 0 个失败请求", "timestamp": "2025-08-03T12:55:33.965889"}, {"test": "桌面端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:55:34.755889"}, {"test": "平板端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:55:35.436260"}, {"test": "手机端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:55:36.151853"}, {"test": "可访问性检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:55:36.818993"}, {"test": "数据加载检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:55:42.547841"}, {"test": "安全头检查", "status": "PASS", "details": "缺少 5 个安全头", "timestamp": "2025-08-03T12:55:43.258477"}], "recommendations": [{"priority": "紧急", "title": "修复高优先级问题", "description": "发现 1 个高优先级问题需要立即处理", "actions": ["首页页面加载过慢"]}, {"priority": "高", "title": "性能优化", "description": "优化页面加载性能和响应速度", "actions": ["实施代码分割", "优化图片加载", "减少初始包大小", "添加缓存策略"]}, {"priority": "中", "title": "用户体验改进", "description": "提升界面响应性和用户体验", "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]}]}