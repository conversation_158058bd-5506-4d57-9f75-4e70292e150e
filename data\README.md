# 数据存储目录说明

本目录用于存储量化交易平台的各类数据文件。

## 目录结构

```
data/
├── historical/     # 历史数据
│   ├── stocks/    # 股票历史数据
│   ├── futures/   # 期货历史数据
│   └── indexes/   # 指数历史数据
├── realtime/      # 实时数据缓存
│   ├── ticks/     # Tick数据
│   ├── quotes/    # 行情快照
│   └── orderbook/ # 订单簿数据
├── reports/       # 生成的报告
│   ├── backtest/  # 回测报告
│   ├── trading/   # 交易报告
│   └── analysis/  # 分析报告
└── uploads/       # 用户上传文件
    ├── strategies/# 策略文件
    └── datasets/  # 数据集
```

## 文件格式

### 历史数据 (historical/)
- **CSV格式**: 标准的逗号分隔值文件
- **Parquet格式**: 高效的列式存储格式（推荐）
- **HDF5格式**: 适合存储大规模数组数据

### 实时数据 (realtime/)
- 使用内存映射文件提高性能
- 自动过期机制，避免占用过多磁盘空间
- 支持快速序列化/反序列化

### 报告文件 (reports/)
- **PDF**: 正式报告，包含图表和分析
- **Excel**: 详细数据表格
- **HTML**: 交互式报告，支持在线查看

### 上传文件 (uploads/)
- 支持的策略文件格式: .py, .ipynb
- 支持的数据格式: .csv, .xlsx, .parquet
- 文件大小限制: 默认50MB

## 数据管理

### 清理策略
- 实时数据: 保留最近24小时
- 历史数据: 永久保存
- 报告文件: 保留90天
- 上传文件: 保留30天（未使用的文件）

### 备份策略
- 每日增量备份
- 每周全量备份
- 备份保留30天

### 安全措施
- 文件访问权限控制
- 敏感数据加密存储
- 定期安全审计

## 使用示例

### 读取历史数据
```python
import pandas as pd

# 读取CSV格式
df = pd.read_csv('data/historical/stocks/000001.csv')

# 读取Parquet格式（推荐）
df = pd.read_parquet('data/historical/stocks/000001.parquet')
```

### 保存报告
```python
from app.utils.report import ReportGenerator

# 生成并保存回测报告
report = ReportGenerator()
report.generate_backtest_report(
    backtest_id='bt_001',
    output_path='data/reports/backtest/bt_001.pdf'
)
```

## 注意事项

1. **不要**直接修改historical目录中的文件
2. **定期**清理realtime目录避免磁盘空间不足
3. **确保**上传文件经过安全检查
4. **使用**相对路径访问数据文件
5. **遵循**命名规范：使用小写字母和下划线

## 故障排除

### 磁盘空间不足
```bash
# 清理过期的实时数据
python scripts/cleanup_realtime_data.py

# 清理旧报告
python scripts/cleanup_old_reports.py --days=30
```

### 权限问题
```bash
# 修复文件权限
chmod -R 755 data/
chown -R app:app data/
```

### 数据损坏
```bash
# 验证数据完整性
python scripts/verify_data_integrity.py

# 从备份恢复
python scripts/restore_from_backup.py --date=2024-01-01
```