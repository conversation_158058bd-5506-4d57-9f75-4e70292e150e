#!/usr/bin/env python3
"""
本地测试环境部署和集成测试脚本
不依赖Docker，使用本地Python环境
"""
import asyncio
import logging
import sys
import time
import subprocess
import os
import signal
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class LocalTestDeployment:
    """本地测试环境部署管理器"""

    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.backend_process = None
        self.test_db_file = "test_quant.db"

    def setup_test_environment(self) -> bool:
        """设置测试环境"""
        logger.info("🔧 Setting up test environment...")

        try:
            # 设置环境变量
            os.environ.update(
                {
                    "ENVIRONMENT": "test",
                    "DATABASE_URL": f"sqlite:///./{self.test_db_file}",
                    "REDIS_URL": "redis://localhost:6379/1",  # 使用不同的数据库
                    "SECRET_KEY": "test-secret-key-for-testing-only",
                    "LOG_LEVEL": "INFO",
                    "ENABLE_CORS": "true",
                    "ENABLE_DOCS": "true",
                }
            )

            logger.info("✅ Environment variables set")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to setup environment: {e}")
            return False

    def install_dependencies(self) -> bool:
        """安装测试依赖"""
        logger.info("📦 Installing test dependencies...")

        try:
            # 检查虚拟环境
            venv_python = "./venv/bin/python"
            if not os.path.exists(venv_python):
                logger.error(
                    "❌ Virtual environment not found. Please run: python -m venv venv"
                )
                return False

            # 安装requests用于测试
            result = subprocess.run(
                [venv_python, "-m", "pip", "install", "requests", "httpx"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                logger.info("✅ Test dependencies installed")
                return True
            else:
                logger.error(f"❌ Failed to install dependencies: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"❌ Error installing dependencies: {e}")
            return False

    def run_database_migrations(self) -> bool:
        """运行数据库迁移"""
        logger.info("🗄️ Setting up test database...")

        try:
            # 删除旧的测试数据库
            if os.path.exists(self.test_db_file):
                os.remove(self.test_db_file)

            # 这里应该运行数据库迁移，但由于我们使用SQLite，先跳过
            logger.info("✅ Test database ready")
            return True

        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return False

    def start_backend_service(self) -> bool:
        """启动后端服务"""
        logger.info("🚀 Starting backend service...")

        try:
            # 启动FastAPI服务
            cmd = [
                "./venv/bin/python",
                "-m",
                "uvicorn",
                "app.main:app",
                "--host",
                "0.0.0.0",
                "--port",
                "8000",
                "--reload",
            ]

            self.backend_process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
            )

            logger.info("✅ Backend service started")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to start backend: {e}")
            return False

    def wait_for_service(self, timeout: int = 60) -> bool:
        """等待服务启动"""
        logger.info("⏳ Waiting for backend service to be ready...")

        # 先等待一下让服务启动
        time.sleep(5)

        # 检查进程是否还在运行
        if self.backend_process and self.backend_process.poll() is not None:
            # 进程已经退出，读取错误信息
            stdout, stderr = self.backend_process.communicate()
            logger.error(f"❌ Backend process exited early:")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False

        logger.info("✅ Backend service appears to be running")
        return True

    def run_basic_tests(self) -> Dict[str, bool]:
        """运行基本测试"""
        logger.info("🧪 Running basic tests...")

        # 使用Python内置的urllib而不是requests
        import urllib.request
        import urllib.error

        results = {}
        test_endpoints = [
            ("/health", "Health Check"),
            ("/docs", "API Documentation"),
            ("/api/v1/openapi.json", "OpenAPI Schema"),
        ]

        for endpoint, name in test_endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                req = urllib.request.Request(url)

                with urllib.request.urlopen(req, timeout=10) as response:
                    success = response.status == 200
                    results[name] = success

                    if success:
                        logger.info(f"✅ {name}: OK")
                    else:
                        logger.error(f"❌ {name}: Failed ({response.status})")

            except urllib.error.HTTPError as e:
                results[name] = False
                logger.error(f"❌ {name}: HTTP Error {e.code}")
            except Exception as e:
                results[name] = False
                logger.error(f"❌ {name}: Error - {e}")

        return results

    def run_unit_tests(self) -> bool:
        """运行单元测试"""
        logger.info("🔬 Running unit tests...")

        try:
            result = subprocess.run(
                ["./venv/bin/python", "-m", "pytest", "tests/", "-v", "--tb=short"],
                capture_output=True,
                text=True,
                timeout=120,
            )

            success = result.returncode == 0

            if success:
                logger.info("✅ Unit tests passed")
                logger.info(f"Test output:\n{result.stdout}")
            else:
                logger.error("❌ Unit tests failed")
                logger.error(f"Test output:\n{result.stdout}")
                logger.error(f"Test errors:\n{result.stderr}")

            return success

        except subprocess.TimeoutExpired:
            logger.error("❌ Unit tests timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error running unit tests: {e}")
            return False

    def run_code_quality_checks(self) -> Dict[str, bool]:
        """运行代码质量检查"""
        logger.info("🔍 Running code quality checks...")

        results = {}

        # Black格式检查
        try:
            result = subprocess.run(
                ["./venv/bin/python", "-m", "black", "--check", "app/"],
                capture_output=True,
                text=True,
            )

            results["Black Format Check"] = result.returncode == 0
            if results["Black Format Check"]:
                logger.info("✅ Black format check: OK")
            else:
                logger.warning("⚠️ Black format check: Issues found")

        except Exception as e:
            results["Black Format Check"] = False
            logger.error(f"❌ Black check error: {e}")

        # Flake8代码风格检查
        try:
            result = subprocess.run(
                ["./venv/bin/python", "-m", "flake8", "app/", "--max-line-length=88"],
                capture_output=True,
                text=True,
            )

            results["Flake8 Style Check"] = result.returncode == 0
            if results["Flake8 Style Check"]:
                logger.info("✅ Flake8 style check: OK")
            else:
                logger.warning("⚠️ Flake8 style check: Issues found")

        except Exception as e:
            results["Flake8 Style Check"] = False
            logger.error(f"❌ Flake8 check error: {e}")

        return results

    def generate_report(
        self, basic_results: Dict, unit_test_result: bool, quality_results: Dict
    ):
        """生成测试报告"""
        logger.info("📊 Generating test report...")

        all_results = {
            **basic_results,
            "Unit Tests": unit_test_result,
            **quality_results,
        }
        total_tests = len(all_results)
        passed_tests = sum(all_results.values())
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        report = f"""
🎯 本地测试环境集成测试报告
{'='*60}

📈 总体结果:
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 成功率: {success_rate:.1f}%

📋 详细结果:
{self._format_results(all_results)}

💡 建议:
"""

        if success_rate >= 90:
            report += "- ✅ 项目状态优秀，可以进入生产环境部署阶段"
        elif success_rate >= 70:
            report += "- ⚠️ 项目基本可用，建议修复失败的测试后再部署"
        else:
            report += "- ❌ 项目存在较多问题，需要修复后再进行部署"

        report += f"\n{'='*60}\n"

        print(report)

        # 保存报告
        with open("local_test_report.txt", "w", encoding="utf-8") as f:
            f.write(report)

        return success_rate >= 70

    def _format_results(self, results: Dict[str, bool]) -> str:
        """格式化测试结果"""
        lines = []
        for test, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            lines.append(f"  {status} {test}")
        return "\n".join(lines)

    def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 Cleaning up test environment...")

        try:
            # 停止后端服务
            if self.backend_process:
                self.backend_process.terminate()
                try:
                    self.backend_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.backend_process.kill()
                logger.info("✅ Backend service stopped")

            # 清理测试数据库
            if os.path.exists(self.test_db_file):
                os.remove(self.test_db_file)
                logger.info("✅ Test database cleaned")

        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")


def main():
    """主函数"""
    deployment = LocalTestDeployment()

    try:
        # 设置测试环境
        if not deployment.setup_test_environment():
            return 1

        # 安装依赖
        if not deployment.install_dependencies():
            return 1

        # 设置数据库
        if not deployment.run_database_migrations():
            return 1

        # 启动服务
        if not deployment.start_backend_service():
            return 1

        # 等待服务就绪
        if not deployment.wait_for_service():
            deployment.cleanup()
            return 1

        # 运行测试
        basic_results = deployment.run_basic_tests()
        unit_test_result = deployment.run_unit_tests()
        quality_results = deployment.run_code_quality_checks()

        # 生成报告
        success = deployment.generate_report(
            basic_results, unit_test_result, quality_results
        )

        if success:
            logger.info("🎉 Local integration tests completed successfully!")
            return 0
        else:
            logger.error("❌ Local integration tests failed!")
            return 1

    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return 1
    finally:
        deployment.cleanup()


if __name__ == "__main__":
    sys.exit(main())
