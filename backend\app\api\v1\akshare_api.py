"""
AkShare数据API接口
提供基于AkShare的数据获取API端点
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
import logging

from ...services.akshare_data_source import get_akshare_client, AkShareDataSource
from ...core.dependencies import get_current_user
from ...db.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/akshare", tags=["AkShare数据"])


@router.get("/status")
async def get_akshare_status(
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """检查AkShare服务状态"""
    try:
        is_available = await akshare_client.is_available()
        
        return {
            "status": "available" if is_available else "unavailable",
            "message": "AkShare服务正常" if is_available else "AkShare服务不可用，将使用模拟数据",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"检查AkShare状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="检查服务状态失败")


@router.get("/stocks")
async def get_stock_list(
    market: str = Query("A股", description="市场类型：A股、港股、美股"),
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """获取股票列表"""
    try:
        stocks = await akshare_client.get_stock_list(market=market)
        
        return {
            "success": True,
            "data": stocks,
            "total": len(stocks),
            "market": market,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取股票列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")


@router.get("/stock/{symbol}/history")
async def get_stock_history(
    symbol: str,
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    period: str = Query("daily", description="数据周期：daily、weekly、monthly"),
    adjust: str = Query("qfq", description="复权类型：qfq前复权、hfq后复权、no不复权"),
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """获取股票历史数据"""
    try:
        # 默认获取最近一年的数据
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        
        stock_data = await akshare_client.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            period=period,
            adjust=adjust
        )
        
        if stock_data.empty:
            return {
                "success": False,
                "message": f"未找到股票 {symbol} 的数据",
                "data": [],
                "symbol": symbol
            }
        
        # 转换DataFrame为JSON格式
        data = []
        for date, row in stock_data.iterrows():
            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": float(row.get("open", 0)),
                "high": float(row.get("high", 0)),
                "low": float(row.get("low", 0)),
                "close": float(row.get("close", 0)),
                "volume": int(row.get("volume", 0)),
                "amount": float(row.get("amount", 0))
            })
        
        return {
            "success": True,
            "data": data,
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "period": period,
            "adjust": adjust,
            "total_records": len(data),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取股票历史数据失败 {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取股票 {symbol} 历史数据失败")


@router.get("/realtime")
async def get_realtime_data(
    symbols: str = Query(..., description="股票代码，多个用逗号分隔"),
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """获取实时股票数据"""
    try:
        symbol_list = [s.strip() for s in symbols.split(",")]
        
        realtime_data = await akshare_client.get_realtime_data(symbol_list)
        
        return {
            "success": True,
            "data": realtime_data,
            "symbols": symbol_list,
            "count": len(realtime_data),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取实时数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取实时数据失败")


@router.get("/index/{index_code}/history")
async def get_index_history(
    index_code: str = "000001",
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """获取指数历史数据"""
    try:
        # 默认获取最近一年的数据
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        
        index_data = await akshare_client.get_index_data(
            index_code=index_code,
            start_date=start_date,
            end_date=end_date
        )
        
        if index_data.empty:
            return {
                "success": False,
                "message": f"未找到指数 {index_code} 的数据",
                "data": [],
                "index_code": index_code
            }
        
        # 转换DataFrame为JSON格式
        data = []
        for date, row in index_data.iterrows():
            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": float(row.get("open", 0)),
                "high": float(row.get("high", 0)),
                "low": float(row.get("low", 0)),
                "close": float(row.get("close", 0)),
                "volume": int(row.get("volume", 0))
            })
        
        return {
            "success": True,
            "data": data,
            "index_code": index_code,
            "start_date": start_date,
            "end_date": end_date,
            "total_records": len(data),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取指数历史数据失败 {index_code}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取指数 {index_code} 历史数据失败")


@router.get("/stock/{symbol}/financial")
async def get_financial_indicators(
    symbol: str,
    year: Optional[int] = Query(None, description="年份"),
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """获取股票财务指标"""
    try:
        financial_data = await akshare_client.get_financial_indicators(
            symbol=symbol,
            year=year
        )
        
        if not financial_data:
            return {
                "success": False,
                "message": f"未找到股票 {symbol} 的财务数据",
                "data": {},
                "symbol": symbol
            }
        
        return {
            "success": True,
            "data": financial_data,
            "symbol": symbol,
            "year": year,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取财务指标失败 {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取股票 {symbol} 财务指标失败")


@router.get("/news")
async def get_news_data(
    symbol: Optional[str] = Query(None, description="股票代码（可选）"),
    limit: int = Query(10, ge=1, le=100, description="新闻条数限制"),
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """获取财经新闻"""
    try:
        news_data = await akshare_client.get_news_data(
            symbol=symbol,
            limit=limit
        )
        
        return {
            "success": True,
            "data": news_data,
            "symbol": symbol,
            "limit": limit,
            "count": len(news_data),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取新闻数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取新闻数据失败")


@router.post("/batch/history")
async def get_batch_stock_history(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    akshare_client: AkShareDataSource = Depends(get_akshare_client)
):
    """批量获取股票历史数据"""
    try:
        symbols = request.get("symbols", [])
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        period = request.get("period", "daily")
        adjust = request.get("adjust", "qfq")
        
        if not symbols:
            raise HTTPException(status_code=400, detail="股票代码列表不能为空")
        
        # 默认日期范围
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        
        batch_data = {}
        failed_symbols = []
        
        for symbol in symbols:
            try:
                stock_data = await akshare_client.get_stock_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    period=period,
                    adjust=adjust
                )
                
                if not stock_data.empty:
                    # 转换为JSON格式
                    data = []
                    for date, row in stock_data.iterrows():
                        data.append({
                            "date": date.strftime("%Y-%m-%d"),
                            "open": float(row.get("open", 0)),
                            "high": float(row.get("high", 0)),
                            "low": float(row.get("low", 0)),
                            "close": float(row.get("close", 0)),
                            "volume": int(row.get("volume", 0)),
                            "amount": float(row.get("amount", 0))
                        })
                    
                    batch_data[symbol] = {
                        "success": True,
                        "data": data,
                        "total_records": len(data)
                    }
                else:
                    batch_data[symbol] = {
                        "success": False,
                        "message": "无数据",
                        "data": []
                    }
                    failed_symbols.append(symbol)
                    
            except Exception as e:
                logger.error(f"获取 {symbol} 数据失败: {str(e)}")
                batch_data[symbol] = {
                    "success": False,
                    "message": str(e),
                    "data": []
                }
                failed_symbols.append(symbol)
        
        return {
            "success": True,
            "data": batch_data,
            "total_symbols": len(symbols),
            "successful_symbols": len(symbols) - len(failed_symbols),
            "failed_symbols": failed_symbols,
            "start_date": start_date,
            "end_date": end_date,
            "period": period,
            "adjust": adjust,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"批量获取股票历史数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="批量获取股票历史数据失败")