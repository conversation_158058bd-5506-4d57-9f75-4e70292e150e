#!/usr/bin/env python3
"""
消费者体验测试脚本 - 回测功能
模拟真实用户使用回测功能的完整流程
"""

import requests
import json
import time
import sys
from datetime import datetime, timedelta

class ConsumerBacktestTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.token = None
        self.user_id = None
        self.issues = []
        
    def log_issue(self, category, description, severity="medium"):
        """记录发现的问题"""
        issue = {
            "category": category,
            "description": description,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        }
        self.issues.append(issue)
        print(f"❌ [{severity.upper()}] {category}: {description}")
        
    def log_success(self, message):
        """记录成功的操作"""
        print(f"✅ {message}")
        
    def test_user_registration(self):
        """测试用户注册"""
        print("\n🔐 测试用户注册...")
        
        try:
            response = requests.post(f"{self.base_url}/api/auth/register", 
                json={
                    "username": "consumer_test",
                    "email": "<EMAIL>",
                    "password": "test123456",
                    "confirm_password": "test123456"
                })
            
            if response.status_code == 200:
                self.log_success("用户注册成功")
                return True
            elif response.status_code == 400 and "already exists" in response.text:
                self.log_success("用户已存在，跳过注册")
                return True
            else:
                self.log_issue("注册", f"注册失败: {response.status_code} - {response.text}", "high")
                return False
                
        except Exception as e:
            self.log_issue("注册", f"注册请求异常: {str(e)}", "high")
            return False
    
    def test_user_login(self):
        """测试用户登录"""
        print("\n🔑 测试用户登录...")
        
        try:
            response = requests.post(f"{self.base_url}/api/auth/login",
                json={
                    "username": "consumer_test",
                    "password": "test123456"
                })
            
            if response.status_code == 200:
                data = response.json()
                print(f"登录响应数据: {data}")  # 调试信息

                # 尝试不同的token字段
                self.token = data.get("access_token") or data.get("data", {}).get("token")
                if self.token:
                    self.log_success("用户登录成功，获取到token")
                    return True
                else:
                    self.log_issue("登录", f"登录成功但未获取到token，响应: {data}", "high")
                    return False
            else:
                self.log_issue("登录", f"登录失败: {response.status_code} - {response.text}", "high")
                return False
                
        except Exception as e:
            self.log_issue("登录", f"登录请求异常: {str(e)}", "high")
            return False
    
    def get_auth_headers(self):
        """获取认证头"""
        if not self.token:
            return {}
        return {"Authorization": f"Bearer {self.token}"}
    
    def test_backtest_creation(self):
        """测试创建回测"""
        print("\n📊 测试创建回测...")
        
        # 准备回测配置
        backtest_config = {
            "name": "消费者测试回测",
            "strategy_id": "simple_ma",
            "symbols": ["000001.SZ"],
            "start_date": "2024-01-01",
            "end_date": "2024-06-30",
            "initial_capital": 100000,
            "parameters": {
                "short_window": 5,
                "long_window": 20
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/api/backtest",
                json=backtest_config,
                headers=self.get_auth_headers())
            
            if response.status_code == 200:
                data = response.json()
                print(f"回测创建响应: {data}")  # 调试信息

                # 尝试不同的ID字段
                task_id = data.get("task_id") or data.get("data", {}).get("id")
                if task_id:
                    self.log_success(f"回测创建成功，任务ID: {task_id}")
                    return task_id
                else:
                    self.log_issue("回测创建", f"创建成功但未返回任务ID，响应: {data}", "medium")
                    return None
            else:
                self.log_issue("回测创建", f"创建失败: {response.status_code} - {response.text}", "high")
                return None
                
        except Exception as e:
            self.log_issue("回测创建", f"创建请求异常: {str(e)}", "high")
            return None
    
    def test_backtest_status_monitoring(self, task_id):
        """测试回测状态监控"""
        print(f"\n⏱️ 测试回测状态监控 (任务ID: {task_id})...")
        
        max_wait_time = 60  # 最大等待60秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                response = requests.get(f"{self.base_url}/api/backtest/status/{task_id}",
                    headers=self.get_auth_headers())
                
                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status")
                    progress = data.get("progress", 0)
                    
                    print(f"📈 回测状态: {status}, 进度: {progress}%")
                    
                    if status == "completed":
                        self.log_success("回测执行完成")
                        return True
                    elif status == "failed":
                        error = data.get("error", "未知错误")
                        self.log_issue("回测执行", f"回测失败: {error}", "high")
                        return False
                    elif status in ["running", "created"]:
                        time.sleep(2)  # 等待2秒后再次检查
                        continue
                    else:
                        self.log_issue("回测状态", f"未知状态: {status}", "medium")
                        
                elif response.status_code == 404:
                    self.log_issue("回测状态", "回测任务不存在", "high")
                    return False
                else:
                    self.log_issue("回测状态", f"状态查询失败: {response.status_code}", "medium")
                    
            except Exception as e:
                self.log_issue("回测状态", f"状态查询异常: {str(e)}", "medium")
                
            time.sleep(2)
        
        self.log_issue("回测执行", f"回测超时 (>{max_wait_time}秒)", "high")
        return False
    
    def test_backtest_results(self, task_id):
        """测试回测结果获取"""
        print(f"\n📋 测试回测结果获取 (任务ID: {task_id})...")
        
        try:
            response = requests.get(f"{self.base_url}/api/backtest/status/{task_id}",
                headers=self.get_auth_headers())
            
            if response.status_code == 200:
                data = response.json()
                results = data.get("results")
                
                if results:
                    # 检查关键指标
                    required_metrics = ["total_return", "sharpe_ratio", "max_drawdown"]
                    missing_metrics = []
                    
                    for metric in required_metrics:
                        if metric not in results:
                            missing_metrics.append(metric)
                    
                    if missing_metrics:
                        self.log_issue("回测结果", f"缺少关键指标: {missing_metrics}", "medium")
                    else:
                        self.log_success("回测结果包含所有关键指标")
                    
                    # 打印主要结果
                    print(f"📊 总收益率: {results.get('total_return', 'N/A')}")
                    print(f"📊 夏普比率: {results.get('sharpe_ratio', 'N/A')}")
                    print(f"📊 最大回撤: {results.get('max_drawdown', 'N/A')}")
                    
                    return True
                else:
                    self.log_issue("回测结果", "回测结果为空", "high")
                    return False
            else:
                self.log_issue("回测结果", f"结果获取失败: {response.status_code}", "high")
                return False
                
        except Exception as e:
            self.log_issue("回测结果", f"结果获取异常: {str(e)}", "high")
            return False
    
    def run_full_test(self):
        """运行完整的消费者体验测试"""
        print("🚀 开始消费者回测体验测试...")
        print("=" * 50)
        
        # 1. 用户注册
        if not self.test_user_registration():
            print("❌ 用户注册失败，终止测试")
            return False
        
        # 2. 用户登录
        if not self.test_user_login():
            print("❌ 用户登录失败，终止测试")
            return False
        
        # 3. 创建回测
        task_id = self.test_backtest_creation()
        if not task_id:
            print("❌ 回测创建失败，终止测试")
            return False
        
        # 4. 监控回测状态
        if not self.test_backtest_status_monitoring(task_id):
            print("❌ 回测执行失败，终止测试")
            return False
        
        # 5. 获取回测结果
        if not self.test_backtest_results(task_id):
            print("❌ 回测结果获取失败")
            return False
        
        return True
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📋 消费者体验测试报告")
        print("=" * 50)
        
        if not self.issues:
            print("🎉 恭喜！未发现任何问题，用户体验良好！")
        else:
            print(f"⚠️ 发现 {len(self.issues)} 个问题:")
            
            # 按严重程度分类
            high_issues = [i for i in self.issues if i["severity"] == "high"]
            medium_issues = [i for i in self.issues if i["severity"] == "medium"]
            low_issues = [i for i in self.issues if i["severity"] == "low"]
            
            if high_issues:
                print(f"\n🔴 高优先级问题 ({len(high_issues)}个):")
                for issue in high_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
            
            if medium_issues:
                print(f"\n🟡 中优先级问题 ({len(medium_issues)}个):")
                for issue in medium_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
            
            if low_issues:
                print(f"\n🟢 低优先级问题 ({len(low_issues)}个):")
                for issue in low_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    tester = ConsumerBacktestTester()
    
    try:
        success = tester.run_full_test()
        tester.generate_report()
        
        if success:
            print("✅ 测试完成：回测功能基本可用")
            sys.exit(0)
        else:
            print("❌ 测试完成：发现严重问题")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        tester.generate_report()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")
        tester.generate_report()
        sys.exit(1)
