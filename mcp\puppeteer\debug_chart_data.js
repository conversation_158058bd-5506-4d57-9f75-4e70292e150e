const puppeteer = require('puppeteer-core');

class ChartDataDebugger {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await puppeteer.launch({
      executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (text.includes('chart') || text.includes('Chart') || text.includes('stock') || text.includes('Stock') || text.includes('symbol')) {
        console.log(`🖥️ 数据相关 [${type}]: ${text}`);
      }
    });
  }

  async debugChartData() {
    try {
      console.log('🚀 启动浏览器...');
      await this.init();

      console.log('📊 访问交易页面...');
      await this.page.goto('http://localhost:5173/trading', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 检查Vue组件的数据状态
      const dataDebugInfo = await this.page.evaluate(() => {
        // 尝试获取Vue应用实例
        const app = document.querySelector('#app').__vue__;
        
        // 尝试获取TradingTerminal组件的数据
        const findTradingTerminal = (component) => {
          if (!component) return null;
          
          // 检查当前组件
          if (component.$options && component.$options.name === 'TradingTerminal') {
            return component;
          }
          
          // 递归检查子组件
          if (component.$children) {
            for (const child of component.$children) {
              const result = findTradingTerminal(child);
              if (result) return result;
            }
          }
          
          return null;
        };
        
        const tradingTerminal = findTradingTerminal(app);
        
        return {
          hasApp: !!app,
          hasTradingTerminal: !!tradingTerminal,
          currentStock: tradingTerminal?.currentStock || null,
          chartData: tradingTerminal?.chartData || null,
          selectedSymbol: tradingTerminal?.selectedSymbol || null,
          selectedPeriod: tradingTerminal?.selectedPeriod || null,
          marketStoreStocks: tradingTerminal?.marketStore?.stocks?.length || 0,
          
          // 检查DOM中的数据
          stockInfoText: document.querySelector('.stock-info')?.textContent || 'None',
          searchInputValue: document.querySelector('.el-autocomplete input')?.value || '',
          
          // 检查是否有错误显示
          hasEmptyPlaceholder: !!document.querySelector('.chart-placeholder .el-empty'),
          emptyPlaceholderText: document.querySelector('.chart-placeholder .el-empty')?.textContent || '',
          
          // 检查条件渲染的状态
          klineChartVisible: !!document.querySelector('.kline-chart'),
          chartPlaceholderVisible: !!document.querySelector('.chart-placeholder')
        };
      });

      console.log('📋 图表数据调试信息:');
      console.log('='.repeat(50));
      console.log(`🎯 Vue应用: ${dataDebugInfo.hasApp ? '✅' : '❌'}`);
      console.log(`💼 交易终端组件: ${dataDebugInfo.hasTradingTerminal ? '✅' : '❌'}`);
      console.log(`📈 当前股票: ${JSON.stringify(dataDebugInfo.currentStock)}`);
      console.log(`📊 图表数据: ${dataDebugInfo.chartData ? `${dataDebugInfo.chartData.length} 条` : '无'}`);
      console.log(`🔤 选中符号: ${dataDebugInfo.selectedSymbol || '无'}`);
      console.log(`⏰ 选中周期: ${dataDebugInfo.selectedPeriod || '无'}`);
      console.log(`📦 市场股票数量: ${dataDebugInfo.marketStoreStocks}`);
      console.log(`🔍 搜索输入值: ${dataDebugInfo.searchInputValue || '空'}`);
      console.log(`📊 K线图可见: ${dataDebugInfo.klineChartVisible ? '✅' : '❌'}`);
      console.log(`📋 占位符可见: ${dataDebugInfo.chartPlaceholderVisible ? '✅' : '❌'}`);
      console.log(`📝 占位符文本: ${dataDebugInfo.emptyPlaceholderText}`);
      console.log('='.repeat(50));

      // 尝试手动触发股票选择
      console.log('\n🔍 尝试手动选择第一只股票...');
      const stockSelected = await this.page.evaluate(() => {
        // 尝试直接调用handleSymbolSelect函数
        const app = document.querySelector('#app').__vue__;
        
        const findTradingTerminal = (component) => {
          if (!component) return null;
          if (component.$options && component.$options.name === 'TradingTerminal') {
            return component;
          }
          if (component.$children) {
            for (const child of component.$children) {
              const result = findTradingTerminal(child);
              if (result) return result;
            }
          }
          return null;
        };
        
        const tradingTerminal = findTradingTerminal(app);
        
        if (tradingTerminal && tradingTerminal.marketStore && tradingTerminal.marketStore.stocks.length > 0) {
          const firstStock = tradingTerminal.marketStore.stocks[0];
          console.log('找到第一只股票:', firstStock);
          
          // 调用handleSymbolSelect
          if (tradingTerminal.handleSymbolSelect) {
            tradingTerminal.handleSymbolSelect(firstStock);
            return { success: true, stock: firstStock };
          }
        }
        
        return { success: false, reason: '未找到交易终端组件或股票数据' };
      });

      console.log('股票选择结果:', stockSelected);

      if (stockSelected.success) {
        console.log('✅ 手动选择股票成功');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 再次检查数据状态
        const afterSelectInfo = await this.page.evaluate(() => {
          const app = document.querySelector('#app').__vue__;
          const findTradingTerminal = (component) => {
            if (!component) return null;
            if (component.$options && component.$options.name === 'TradingTerminal') {
              return component;
            }
            if (component.$children) {
              for (const child of component.$children) {
                const result = findTradingTerminal(child);
                if (result) return result;
              }
            }
            return null;
          };
          
          const tradingTerminal = findTradingTerminal(app);
          
          return {
            currentStock: tradingTerminal?.currentStock || null,
            chartData: tradingTerminal?.chartData?.length || 0,
            selectedSymbol: tradingTerminal?.selectedSymbol || null,
            klineChartVisible: !!document.querySelector('.kline-chart'),
            chartPlaceholderVisible: !!document.querySelector('.chart-placeholder'),
            periodButtons: document.querySelectorAll('.period-selector button').length
          };
        });
        
        console.log('\n📊 选择股票后的数据状态:');
        console.log(`📈 当前股票: ${JSON.stringify(afterSelectInfo.currentStock)}`);
        console.log(`📊 图表数据: ${afterSelectInfo.chartData} 条`);
        console.log(`🔤 选中符号: ${afterSelectInfo.selectedSymbol || '无'}`);
        console.log(`📊 K线图可见: ${afterSelectInfo.klineChartVisible ? '✅' : '❌'}`);
        console.log(`📋 占位符可见: ${afterSelectInfo.chartPlaceholderVisible ? '✅' : '❌'}`);
        console.log(`⏰ 周期按钮: ${afterSelectInfo.periodButtons} 个`);
      } else {
        console.log('❌ 手动选择股票失败:', stockSelected.reason);
      }

      return dataDebugInfo;

    } catch (error) {
      console.error('❌ 调试过程中出现错误:', error);
      throw error;
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

async function runDebug() {
  const chartDebugger = new ChartDataDebugger();
  
  try {
    const result = await chartDebugger.debugChartData();
    return result;
  } catch (error) {
    console.error('💥 调试失败:', error.message);
  } finally {
    await chartDebugger.close();
  }
}

// 运行调试
runDebug();
