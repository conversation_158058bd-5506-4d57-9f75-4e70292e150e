<template>
  <div class="data-center-module">
    <div class="module-header">
      <h2>数据中心</h2>
      <p class="module-description">查看和管理您的交易数据、订单记录和持仓信息</p>
    </div>

    <div class="data-content">
      <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
        <el-tab-pane label="订单管理" name="orders">
          <div class="tab-content">
            <div class="content-header">
              <div class="filters">
                <el-select v-model="orderFilters.status" placeholder="订单状态" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="已提交" value="submitted" />
                  <el-option label="部分成交" value="partial" />
                  <el-option label="已成交" value="filled" />
                  <el-option label="已撤销" value="cancelled" />
                </el-select>

                <el-date-picker
                  v-model="orderFilters.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px"
                />

                <el-input
                  v-model="orderFilters.symbol"
                  placeholder="股票代码"
                  style="width: 120px"
                />

                <el-button type="primary" @click="refreshOrders">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>

              <div class="actions">
                <el-button @click="exportOrders">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>

            <div class="table-container">
              <el-table :data="filteredOrders" stripe style="width: 100%">
                <el-table-column prop="orderId" label="订单号" width="120" />
                <el-table-column prop="symbol" label="股票代码" width="100" />
                <el-table-column prop="stockName" label="股票名称" width="120" />
                <el-table-column prop="side" label="买卖方向" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.side === 'buy' ? 'danger' : 'success'">
                      {{ row.side === 'buy' ? '买入' : '卖出' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="100" />
                <el-table-column prop="price" label="价格" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.price) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="timestamp" label="时间" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.timestamp) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
                      v-if="row.status === 'submitted' || row.status === 'partial'"
                      type="text"
                      size="small"
                      @click="cancelOrder(row)"
                    >
                      撤单
                    </el-button>
                    <el-button type="text" size="small" @click="viewOrderDetail(row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="持仓管理" name="positions">
          <div class="tab-content">
            <div class="content-header">
              <div class="summary-cards">
                <div class="summary-card">
                  <div class="card-title">总持仓市值</div>
                  <div class="card-value">¥{{ formatMoney(positionSummary.totalValue) }}</div>
                </div>
                <div class="summary-card">
                  <div class="card-title">今日盈亏</div>
                  <div class="card-value" :class="positionSummary.todayPnL >= 0 ? 'profit' : 'loss'">
                    {{ positionSummary.todayPnL >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(positionSummary.todayPnL)) }}
                  </div>
                </div>
                <div class="summary-card">
                  <div class="card-title">总盈亏</div>
                  <div class="card-value" :class="positionSummary.totalPnL >= 0 ? 'profit' : 'loss'">
                    {{ positionSummary.totalPnL >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(positionSummary.totalPnL)) }}
                  </div>
                </div>
              </div>

              <div class="actions">
                <el-button @click="refreshPositions">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>

            <div class="table-container">
              <el-table :data="positions" stripe style="width: 100%">
                <el-table-column prop="symbol" label="股票代码" width="100" />
                <el-table-column prop="stockName" label="股票名称" width="120" />
                <el-table-column prop="quantity" label="持仓数量" width="100" />
                <el-table-column prop="availableQuantity" label="可用数量" width="100" />
                <el-table-column prop="avgCost" label="成本价" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.avgCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="currentPrice" label="现价" width="100">
                  <template #default="{ row }">
                    <span :class="getPriceClass(row.changePercent)">
                      ¥{{ formatPrice(row.currentPrice) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="marketValue" label="市值" width="120">
                  <template #default="{ row }">
                    ¥{{ formatMoney(row.marketValue) }}
                  </template>
                </el-table-column>
                <el-table-column prop="pnl" label="盈亏" width="120">
                  <template #default="{ row }">
                    <span :class="row.pnl >= 0 ? 'profit' : 'loss'">
                      {{ row.pnl >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(row.pnl)) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="pnlPercent" label="盈亏比例" width="100">
                  <template #default="{ row }">
                    <span :class="row.pnlPercent >= 0 ? 'profit' : 'loss'">
                      {{ row.pnlPercent >= 0 ? '+' : '' }}{{ row.pnlPercent.toFixed(2) }}%
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
                      type="text"
                      size="small"
                      @click="quickSell(row)"
                      :disabled="row.availableQuantity <= 0"
                    >
                      快速卖出
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="成交记录" name="trades">
          <div class="tab-content">
            <div class="content-header">
              <div class="filters">
                <el-date-picker
                  v-model="tradeFilters.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px"
                />

                <el-input
                  v-model="tradeFilters.symbol"
                  placeholder="股票代码"
                  style="width: 120px"
                />

                <el-button type="primary" @click="refreshTrades">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>

              <div class="actions">
                <el-button @click="exportTrades">
                  <el-icon><Download /></el-icon>
                  导出交割单
                </el-button>
              </div>
            </div>

            <div class="table-container">
              <el-table :data="filteredTrades" stripe style="width: 100%">
                <el-table-column prop="tradeId" label="成交号" width="120" />
                <el-table-column prop="orderId" label="订单号" width="120" />
                <el-table-column prop="symbol" label="股票代码" width="100" />
                <el-table-column prop="stockName" label="股票名称" width="120" />
                <el-table-column prop="side" label="买卖方向" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.side === 'buy' ? 'danger' : 'success'">
                      {{ row.side === 'buy' ? '买入' : '卖出' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="成交数量" width="100" />
                <el-table-column prop="price" label="成交价格" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.price) }}
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="成交金额" width="120">
                  <template #default="{ row }">
                    ¥{{ formatMoney(row.amount) }}
                  </template>
                </el-table-column>
                <el-table-column prop="commission" label="手续费" width="100">
                  <template #default="{ row }">
                    ¥{{ formatMoney(row.commission) }}
                  </template>
                </el-table-column>
                <el-table-column prop="timestamp" label="成交时间" width="160">
                  <template #default="{ row }">
                    {{ formatDateTime(row.timestamp) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'

// Props
interface Props {
  account: {
    id: string
    type: string
    name: string
    availableFunds: number
  }
  activeTab: 'orders' | 'positions' | 'trades'
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  tabChange: [tab: 'orders' | 'positions' | 'trades']
}>()

// 响应式数据
const activeTab = ref(props.activeTab)

const orderFilters = reactive({
  status: '',
  dateRange: [],
  symbol: ''
})

const tradeFilters = reactive({
  dateRange: [],
  symbol: ''
})

// 模拟数据
const orders = ref([
  {
    orderId: 'ORD001',
    symbol: '000001',
    stockName: '平安银行',
    side: 'buy',
    quantity: 1000,
    price: 12.45,
    status: 'filled',
    timestamp: '2025-08-04T10:30:00'
  },
  {
    orderId: 'ORD002',
    symbol: '600036',
    stockName: '招商银行',
    side: 'sell',
    quantity: 500,
    price: 35.78,
    status: 'submitted',
    timestamp: '2025-08-04T11:15:00'
  }
])

const positions = ref([
  {
    symbol: '000001',
    stockName: '平安银行',
    quantity: 1000,
    availableQuantity: 1000,
    avgCost: 12.30,
    currentPrice: 12.45,
    marketValue: 12450,
    pnl: 150,
    pnlPercent: 1.22,
    changePercent: 1.88
  }
])

const trades = ref([
  {
    tradeId: 'TRD001',
    orderId: 'ORD001',
    symbol: '000001',
    stockName: '平安银行',
    side: 'buy',
    quantity: 1000,
    price: 12.45,
    amount: 12450,
    commission: 6.23,
    timestamp: '2025-08-04T10:30:15'
  }
])

const positionSummary = computed(() => ({
  totalValue: positions.value.reduce((sum, pos) => sum + pos.marketValue, 0),
  todayPnL: 234.56,
  totalPnL: positions.value.reduce((sum, pos) => sum + pos.pnl, 0)
}))

// 计算属性
const filteredOrders = computed(() => {
  return orders.value.filter(order => {
    if (orderFilters.status && order.status !== orderFilters.status) return false
    if (orderFilters.symbol && !order.symbol.includes(orderFilters.symbol)) return false
    return true
  })
})

const filteredTrades = computed(() => {
  return trades.value.filter(trade => {
    if (tradeFilters.symbol && !trade.symbol.includes(tradeFilters.symbol)) return false
    return true
  })
})

// 方法
const handleTabChange = (tab: string) => {
  emit('tabChange', tab as 'orders' | 'positions' | 'trades')
}

const formatPrice = (price: number) => price.toFixed(2)
const formatMoney = (amount: number) => amount.toFixed(2)
const formatDateTime = (timestamp: string) => new Date(timestamp).toLocaleString('zh-CN')

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    submitted: 'warning',
    partial: 'primary',
    filled: 'success',
    cancelled: 'info'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    submitted: '已提交',
    partial: '部分成交',
    filled: '已成交',
    cancelled: '已撤销'
  }
  return statusMap[status] || status
}

const refreshOrders = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('订单数据已刷新')
  } catch (error) {
    ElMessage.error('订单数据刷新失败，请重试')
  }
}

const refreshPositions = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('持仓数据已刷新')
  } catch (error) {
    ElMessage.error('持仓数据刷新失败，请重试')
  }
}

const refreshTrades = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('成交数据已刷新')
  } catch (error) {
    ElMessage.error('成交数据刷新失败，请重试')
  }
}

const exportOrders = () => {
  ElMessage.info('订单导出功能开发中')
}

const exportTrades = () => {
  ElMessage.info('交割单导出功能开发中')
}

const cancelOrder = (order: any) => {
  ElMessage.success(`订单 ${order.orderId} 撤销成功`)
}

const viewOrderDetail = (order: any) => {
  ElMessage.info(`查看订单 ${order.orderId} 详情`)
}

const quickSell = (position: any) => {
  ElMessage.info(`快速卖出 ${position.stockName}`)
}

onMounted(() => {
  console.log('数据中心模块初始化完成')
})
</script>

<style scoped lang="scss">
.data-center-module {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.module-header {
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #303133;
  }

  .module-description {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.data-content {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .el-tabs {
    height: 100%;

    :deep(.el-tabs__content) {
      height: calc(100% - 40px);
      padding: 0;
    }
  }
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;

  .filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }

  .actions {
    display: flex;
    gap: 12px;
  }
}

.summary-cards {
  display: flex;
  gap: 20px;

  .summary-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    min-width: 120px;
    text-align: center;

    .card-title {
      font-size: 12px;
      color: #909399;
      margin-bottom: 8px;
    }

    .card-value {
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      &.profit {
        color: #f56c6c;
      }

      &.loss {
        color: #67c23a;
      }
    }
  }
}

.table-container {
  flex: 1;
  overflow: auto;

  .price-up {
    color: #f56c6c;
  }

  .price-down {
    color: #67c23a;
  }

  .price-neutral {
    color: #909399;
  }

  .profit {
    color: #f56c6c;
  }

  .loss {
    color: #67c23a;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: stretch;

    .filters {
      justify-content: center;
    }

    .actions {
      justify-content: center;
    }
  }

  .summary-cards {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
