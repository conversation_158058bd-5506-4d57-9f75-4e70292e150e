import asyncio
import json
from unittest.mock import AsyncMock, patch

import pytest
from httpx import AsyncClient
from fastapi import WebSocketDisconnect

from app.main import app
from app.tasks.trading_tasks import process_order_async
from app.schemas.trading import OrderRequest

pytestmark = pytest.mark.integration


class DummyAsyncResult:
    """Minimal stand-in for Celery AsyncResult used in tests."""

    def __init__(self, result):
        self.id = "dummy-task-id"
        self._result = result

    @property
    def result(self):
        return self._result


@pytest.mark.asyncio
async def test_trading_order_e2e(client: AsyncClient, mock_user):
    """端到端：REST 下单 → Celery 任务 → WebSocket 推送。"""

    # --- 1️⃣ 建立 WebSocket 连接并订阅订单更新 --------------------------------
    ws = await client.websocket_connect("/api/v1/trading/ws")

    # 发送订阅消息（保持协议兼容）
    await ws.send_json({"type": "subscribe_orders"})
    # 接收订阅确认
    msg = await ws.receive_json()
    assert msg["type"] in {"subscription_success", "pong"}

    # --- 2️⃣ 模拟同步 Celery 任务 ----------------------------------------------
    async def _mock_process_order_async(user_id: str, order_payload: dict):
        # 直接返回伪造订单数据（应符合 OrderData 架构）
        order_data = {
            "order_id": "TEST_ORDER_001",
            "symbol": order_payload["symbol"],
            "direction": order_payload["direction"],
            "volume": order_payload["volume"],
            "price": order_payload["price"],
            "status": "ALL_FILLED",
        }
        # 广播订单更新（利用现有推送服务）
        from app.core.websocket import ws_service, WSMessage

        await ws_service.manager.broadcast_to_channel(
            WSMessage(type="order_update", data=order_data),
            channel=f"trading.orders.{user_id}",
        )
        return order_data

    with patch.object(
        process_order_async, "delay", new=lambda *args, **kwargs: DummyAsyncResult(None)
    ):
        with patch(
            "app.tasks.trading_tasks.process_order_async",
            new=AsyncMock(side_effect=_mock_process_order_async),
        ):
            # --- 3️⃣ 提交订单 -----------------------------------------------------
            order_req = OrderRequest(
                symbol="rb2405",
                direction="BUY",
                offset="OPEN",
                order_type="LIMIT",
                price=3800,
                volume=1,
            )
            response = await client.post(
                "/api/v1/trading/orders", json=order_req.model_dump()
            )
            assert response.status_code == 200

            # --- 4️⃣ 验证 WebSocket 收到更新 ------------------------------------
            try:
                pushed = await asyncio.wait_for(ws.receive_json(), timeout=3)
            except asyncio.TimeoutError:
                pytest.fail("未收到订单更新推送")

            assert pushed["type"] == "order_update"
            assert pushed["data"]["symbol"] == "rb2405"

    # 关闭 WebSocket
    await ws.close()
