@echo off
setlocal enabledelayedexpansion

:: Quantitative Investment Platform Status Check (Windows)
echo Checking Quantitative Investment Platform Status...
echo ================================================
echo.

:: 进入项目根目录
cd /d "%~dp0\.."

:: Check backend service status
echo Checking backend service status...
powershell -Command "try { Invoke-WebRequest -Uri http://localhost:8000/health -UseBasicParsing -TimeoutSec 3 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Backend service not responding
    echo    Checking port 8000 usage...
    netstat -an | findstr :8000 >nul 2>&1
    if errorlevel 1 (
        echo    [WARNING] Port 8000 not listening
    ) else (
        echo    [WARNING] Port 8000 occupied but service not responding
    )
) else (
    echo [SUCCESS] Backend service running normally
    echo    URL: http://localhost:8000
    echo    API Docs: http://localhost:8000/docs

    :: Check API endpoints
    echo    API endpoint check:
    powershell -Command "try { Invoke-WebRequest -Uri http://localhost:8000/api/v1/market/stocks -UseBasicParsing -TimeoutSec 2 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
    if errorlevel 1 (
        echo      [ERROR] Market data API
    ) else (
        echo      [SUCCESS] Market data API
    )
)

echo.

:: 检查前端服务状态
echo 🎨 检查前端服务状态...
curl -s --connect-timeout 3 http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    echo ❌ 前端服务未响应
    echo    检查端口5173占用情况...
    netstat -an | findstr :5173 >nul 2>&1
    if errorlevel 1 (
        echo    ⚠️ 端口5173未被监听
    ) else (
        echo    ⚠️ 端口5173被占用但服务无响应
    )
) else (
    echo ✅ 前端服务正常运行
    echo    🌐 地址: http://localhost:5173
)

echo.

:: 检查进程ID文件
echo 📋 检查进程ID文件...
if exist .backend.pid (
    echo ✅ 后端PID文件存在
) else (
    echo ⚠️ 后端PID文件不存在
)

if exist .frontend.pid (
    echo ✅ 前端PID文件存在
) else (
    echo ⚠️ 前端PID文件不存在
)

echo.

:: 检查日志文件
echo 📄 检查日志文件...
if exist "logs\backend.log" (
    echo ✅ 后端日志文件存在 (logs\backend.log)
    for %%A in ("logs\backend.log") do echo    文件大小: %%~zA bytes
) else (
    echo ⚠️ 后端日志文件不存在
)

if exist "logs\frontend.log" (
    echo ✅ 前端日志文件存在 (logs\frontend.log)
    for %%A in ("logs\frontend.log") do echo    文件大小: %%~zA bytes
) else (
    echo ⚠️ 前端日志文件不存在
)

echo.

:: 检查运行环境
echo 🔧 检查运行环境...

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    python3 --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python 未安装
    ) else (
        for /f "tokens=*" %%i in ('python3 --version 2^>^&1') do echo ✅ Python: %%i
    )
) else (
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ Python: %%i
)

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装
) else (
    for /f "tokens=*" %%i in ('node --version 2^>^&1') do echo ✅ Node.js: %%i
)

:: 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>^&1') do echo ✅ npm: %%i
)

echo.

:: 检查端口占用详情
echo 🔌 端口占用详情...
echo 端口 8000 (后端):
netstat -ano | findstr :8000
echo.
echo 端口 5173 (前端):
netstat -ano | findstr :5173

echo.

:: 系统资源信息
echo 💻 系统资源信息...
echo 操作系统: %OS%
echo 计算机名: %COMPUTERNAME%

:: 内存信息
for /f "skip=1 tokens=4" %%i in ('wmic OS get TotalVisibleMemorySize /value') do (
    if not "%%i"=="" set TotalMem=%%i
)
for /f "skip=1 tokens=4" %%i in ('wmic OS get FreePhysicalMemory /value') do (
    if not "%%i"=="" set FreeMem=%%i
)

if defined TotalMem (
    set /a TotalMemMB=!TotalMem!/1024
    set /a FreeMemMB=!FreeMem!/1024
    set /a UsedMemMB=!TotalMemMB!-!FreeMemMB!
    echo 内存使用: !UsedMemMB!MB / !TotalMemMB!MB
)

echo.
echo ==========================
echo ✅ 状态检查完成
echo.
echo 🔧 可用操作:
echo    启动服务: scripts\start.bat
echo    停止服务: scripts\stop.bat
echo    重启服务: scripts\restart.bat
echo    查看日志: type logs\backend.log
echo    查看日志: type logs\frontend.log
echo.
pause
