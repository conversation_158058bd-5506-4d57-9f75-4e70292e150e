#!/usr/bin/env python3
"""
简化版功能验证脚本
验证系统各个模块的基本结构和导入是否正常
"""

import sys
import json
import importlib.util
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

class SimpleValidator:
    """简化验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'validations': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
    
    def validate_module_structure(self, module_path: str, expected_classes: List[str]) -> Dict[str, Any]:
        """验证模块结构"""
        try:
            full_path = self.project_root / module_path
            if not full_path.exists():
                return {
                    'status': 'FAIL',
                    'error': f'模块文件不存在: {module_path}'
                }
            
            # 读取文件内容
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含预期的类
            found_classes = []
            missing_classes = []
            
            for class_name in expected_classes:
                if f'class {class_name}' in content:
                    found_classes.append(class_name)
                else:
                    missing_classes.append(class_name)
            
            return {
                'status': 'PASS' if not missing_classes else 'FAIL',
                'found_classes': found_classes,
                'missing_classes': missing_classes,
                'file_size': len(content),
                'line_count': len(content.split('\n'))
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def validate_backtest_engine(self) -> Dict[str, Any]:
        """验证回测引擎结构"""
        return self.validate_module_structure(
            'app/services/backtest_engine_enhanced.py',
            ['BacktestEngine', 'BacktestOrder', 'BacktestPosition', 'BacktestMetrics']
        )
    
    def validate_data_analyzer(self) -> Dict[str, Any]:
        """验证数据分析器结构"""
        return self.validate_module_structure(
            'app/services/backtest_data_analyzer.py',
            ['BacktestDataAnalyzer', 'RiskMetrics', 'PerformanceAttribution', 'SeasonalityAnalysis']
        )
    
    def validate_visualizer(self) -> Dict[str, Any]:
        """验证可视化器结构"""
        return self.validate_module_structure(
            'app/services/backtest_visualizer.py',
            ['BacktestVisualizer']
        )
    
    def validate_trading_models(self) -> Dict[str, Any]:
        """验证交易模型结构"""
        return self.validate_module_structure(
            'app/models/trading.py',
            ['Order', 'Trade', 'Position', 'Account', 'OrderDirection', 'OrderType', 'OrderStatus']
        )
    
    def validate_api_endpoints(self) -> Dict[str, Any]:
        """验证API端点结构"""
        try:
            api_files = [
                'app/api/v1/backtest_enhanced.py',
                'app/api/v1/auth_fixed.py',
                'app/api/v1/strategy_fixed.py',
                'app/api/v1/risk_fixed.py'
            ]
            
            results = {}
            for api_file in api_files:
                full_path = self.project_root / api_file
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查路由定义
                    router_count = content.count('@router.')
                    async_def_count = content.count('async def ')
                    
                    results[api_file] = {
                        'exists': True,
                        'router_endpoints': router_count,
                        'async_functions': async_def_count,
                        'file_size': len(content)
                    }
                else:
                    results[api_file] = {
                        'exists': False
                    }
            
            all_exist = all(r['exists'] for r in results.values())
            total_endpoints = sum(r.get('router_endpoints', 0) for r in results.values())
            
            return {
                'status': 'PASS' if all_exist and total_endpoints > 0 else 'FAIL',
                'files': results,
                'total_endpoints': total_endpoints,
                'all_files_exist': all_exist
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def validate_core_system(self) -> Dict[str, Any]:
        """验证核心系统"""
        return self.validate_module_structure(
            'app/core/fix_api_system.py',
            ['APISystemFixer']
        )
    
    def run_all_validations(self) -> Dict[str, Any]:
        """运行所有验证"""
        validations = [
            ('backtest_engine', self.validate_backtest_engine),
            ('data_analyzer', self.validate_data_analyzer),
            ('visualizer', self.validate_visualizer),
            ('trading_models', self.validate_trading_models),
            ('api_endpoints', self.validate_api_endpoints),
            ('core_system', self.validate_core_system)
        ]
        
        for validation_name, validation_func in validations:
            print(f"运行验证: {validation_name}")
            try:
                result = validation_func()
                self.results['validations'][validation_name] = result
                self.results['summary']['total_tests'] += 1
                
                if result['status'] == 'PASS':
                    self.results['summary']['passed'] += 1
                    print(f"  ✓ {validation_name} - 通过")
                else:
                    self.results['summary']['failed'] += 1
                    print(f"  ✗ {validation_name} - 失败")
                    if 'error' in result:
                        print(f"    错误: {result['error']}")
                        self.results['summary']['errors'].append({
                            'validation': validation_name,
                            'error': result['error']
                        })
                        
            except Exception as e:
                self.results['validations'][validation_name] = {
                    'status': 'FAIL',
                    'error': str(e),
                    'error_type': type(e).__name__
                }
                self.results['summary']['failed'] += 1
                self.results['summary']['total_tests'] += 1
                self.results['summary']['errors'].append({
                    'validation': validation_name,
                    'error': str(e)
                })
                print(f"  ✗ {validation_name} - 失败: {str(e)}")
        
        return self.results
    
    def generate_report(self) -> str:
        """生成验证报告"""
        report = []
        report.append("=" * 60)
        report.append("系统功能验证报告")
        report.append("=" * 60)
        report.append(f"验证时间: {self.results['timestamp']}")
        report.append(f"总验证项: {self.results['summary']['total_tests']}")
        report.append(f"通过: {self.results['summary']['passed']}")
        report.append(f"失败: {self.results['summary']['failed']}")
        
        # 计算通过率
        if self.results['summary']['total_tests'] > 0:
            pass_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
            report.append(f"通过率: {pass_rate:.1f}%")
        
        report.append("")
        
        for validation_name, validation_result in self.results['validations'].items():
            report.append(f"验证项: {validation_name}")
            report.append(f"状态: {validation_result['status']}")
            
            if validation_result['status'] == 'PASS':
                # 显示成功的关键信息
                if 'found_classes' in validation_result:
                    report.append(f"  发现的类: {', '.join(validation_result['found_classes'])}")
                    report.append(f"  文件大小: {validation_result.get('file_size', 0)} 字节")
                    report.append(f"  代码行数: {validation_result.get('line_count', 0)}")
                elif validation_name == 'api_endpoints':
                    report.append(f"  API端点总数: {validation_result.get('total_endpoints', 0)}")
                    report.append(f"  文件检查通过: {validation_result.get('all_files_exist', False)}")
            else:
                # 显示错误信息
                report.append(f"  错误: {validation_result.get('error', 'Unknown error')}")
                if 'missing_classes' in validation_result:
                    report.append(f"  缺失的类: {', '.join(validation_result['missing_classes'])}")
            
            report.append("-" * 40)
        
        # 显示系统状态总结
        report.append("系统状态总结:")
        if self.results['summary']['failed'] == 0:
            report.append("✓ 所有组件验证通过，系统完整性良好")
        else:
            report.append(f"⚠ 发现 {self.results['summary']['failed']} 个问题需要处理")
        
        # 推荐的后续步骤
        report.append("")
        report.append("推荐的后续步骤:")
        if self.results['summary']['failed'] == 0:
            report.append("1. 运行完整的功能测试")
            report.append("2. 启动系统进行集成测试")
            report.append("3. 检查API文档和接口可用性")
        else:
            report.append("1. 修复上述发现的问题")
            report.append("2. 重新运行验证脚本")
            report.append("3. 确保所有依赖项正确安装")
        
        return '\n'.join(report)

def main():
    """主函数"""
    validator = SimpleValidator()
    
    print("开始系统功能验证...")
    print("=" * 60)
    
    results = validator.run_all_validations()
    
    print("\n" + "=" * 60)
    
    # 生成报告
    report = validator.generate_report()
    print(report)
    
    # 保存结果到文件
    project_root = Path(__file__).parent
    results_file = project_root / 'simple_validation_results.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    report_file = project_root / 'simple_validation_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n验证结果已保存到: {results_file}")
    print(f"验证报告已保存到: {report_file}")
    
    # 返回验证状态
    return 0 if results['summary']['failed'] == 0 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)