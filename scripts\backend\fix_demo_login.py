#!/usr/bin/env python3
"""
修复演示登录问题 - 确保admin用户存在且密码正确
"""
import sqlite3
import bcrypt
import os
from datetime import datetime

def hash_password(password: str) -> str:
    """生成bcrypt密码哈希"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def ensure_data_directory():
    """确保data目录存在"""
    if not os.path.exists('data'):
        os.makedirs('data')
        print("✓ 创建了data目录")

def init_database():
    """初始化数据库和admin用户"""
    ensure_data_directory()
    
    db_path = 'data/quantplatform.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建users表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                full_name VARCHAR(100),
                hashed_password VARCHAR(255) NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                is_verified BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP
            )
        ''')
        
        # 检查admin用户是否存在
        cursor.execute('SELECT id, username, hashed_password FROM users WHERE username = ?', ('admin',))
        admin_user = cursor.fetchone()
        
        admin_password = 'admin123456'
        admin_hash = hash_password(admin_password)
        
        if admin_user:
            user_id, username, current_hash = admin_user
            # 验证当前密码是否正确
            if verify_password(admin_password, current_hash):
                print(f"✓ admin用户已存在且密码正确")
                print(f"  - 用户ID: {user_id}")
                print(f"  - 用户名: {username}")
                print(f"  - 密码: {admin_password}")
            else:
                # 更新密码
                cursor.execute('''
                    UPDATE users 
                    SET hashed_password = ?, is_active = 1, is_superuser = 1, is_verified = 1
                    WHERE username = ?
                ''', (admin_hash, 'admin'))
                print(f"✓ 更新了admin用户密码")
                print(f"  - 用户名: admin")
                print(f"  - 新密码: {admin_password}")
        else:
            # 创建admin用户
            cursor.execute('''
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
                VALUES (?, ?, ?, ?, 1, 1, 1)
            ''', ('admin', '<EMAIL>', '系统管理员', admin_hash))
            print(f"✓ 创建了admin用户")
            print(f"  - 用户名: admin")
            print(f"  - 密码: {admin_password}")
            print(f"  - 邮箱: <EMAIL>")
        
        # 创建demo用户（可选）
        cursor.execute('SELECT id FROM users WHERE username = ?', ('demo',))
        demo_user = cursor.fetchone()
        
        if not demo_user:
            demo_password = 'demo123'
            demo_hash = hash_password(demo_password)
            cursor.execute('''
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
                VALUES (?, ?, ?, ?, 1, 0, 1)
            ''', ('demo', '<EMAIL>', '演示用户', demo_hash))
            print(f"✓ 创建了demo用户")
            print(f"  - 用户名: demo")
            print(f"  - 密码: {demo_password}")
        
        conn.commit()
        
        # 显示所有用户
        cursor.execute('SELECT id, username, email, is_active, is_superuser FROM users')
        users = cursor.fetchall()
        
        print(f"\n📋 数据库用户列表:")
        for user in users:
            user_id, username, email, is_active, is_superuser = user
            status = "✅ 活跃" if is_active else "❌ 未激活"
            role = "管理员" if is_superuser else "普通用户"
            print(f"  - {username} ({email}) - {status} - {role}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False

def test_login():
    """测试登录功能"""
    import requests
    import json
    
    print(f"\n🧪 测试登录功能...")
    
    test_cases = [
        ("admin", "admin123456"),
        ("admin", "admin123"),  # 旧密码，应该失败
        ("demo", "demo123")
    ]
    
    for username, password in test_cases:
        try:
            response = requests.post(
                'http://localhost:8001/api/v1/auth/login',
                json={'username': username, 'password': password},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {username}/{password} - 登录成功")
            else:
                print(f"❌ {username}/{password} - 登录失败 ({response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {username}/{password} - 连接失败: {e}")

if __name__ == "__main__":
    print("🔧 修复演示登录问题...")
    print("=" * 50)
    
    if init_database():
        print("\n✅ 数据库初始化完成！")
        
        print(f"\n📱 前端演示登录账户:")
        print(f"   - admin / admin123456 (管理员)")
        print(f"   - demo / demo123 (演示用户)")
        
        print(f"\n🌐 登录地址:")
        print(f"   - http://localhost:5173/login")
        
        # 测试登录
        test_login()
        
    else:
        print("\n❌ 数据库初始化失败")