"""
滑点模型实现
支持多种滑点计算方式，适应A股市场特点
"""
import numpy as np
import pandas as pd
from typing import Dict, Optional
from enum import Enum

class SlippageType(Enum):
    """滑点类型"""
    PERCENTAGE = "percentage"  # 百分比滑点
    FIXED = "fixed"           # 固定滑点
    VOLUME_BASED = "volume_based"  # 基于成交量的滑点
    MARKET_IMPACT = "market_impact"  # 市场冲击滑点

class SlippageModel:
    """滑点模型"""
    
    def __init__(self, config):
        self.config = config
        self.slippage_type = SlippageType(config.slippage_type)
        self.base_slippage = config.slippage_value
        
        # 基于成交量的滑点参数
        self.volume_impact_factor = config.get('volume_impact_factor', 0.1)
        self.volume_threshold = config.get('volume_threshold', 0.01)  # 占日成交量1%
        
        # 市场冲击滑点参数
        self.market_impact_coeff = config.get('market_impact_coeff', 0.5)
        
    def calculate_slippage(
        self,
        order_size: float,
        price: float,
        market_data: pd.Series,
        side: str
    ) -> float:
        """
        计算滑点
        
        Args:
            order_size: 订单大小（股数）
            price: 基准价格
            market_data: 市场数据（包含volume, high, low等）
            side: 交易方向 ('buy' or 'sell')
            
        Returns:
            滑点金额
        """
        if self.slippage_type == SlippageType.PERCENTAGE:
            return self._calculate_percentage_slippage(order_size, price, side)
            
        elif self.slippage_type == SlippageType.FIXED:
            return self._calculate_fixed_slippage(order_size, side)
            
        elif self.slippage_type == SlippageType.VOLUME_BASED:
            return self._calculate_volume_based_slippage(
                order_size, price, market_data, side
            )
            
        elif self.slippage_type == SlippageType.MARKET_IMPACT:
            return self._calculate_market_impact_slippage(
                order_size, price, market_data, side
            )
            
        else:
            return 0
            
    def _calculate_percentage_slippage(
        self,
        order_size: float,
        price: float,
        side: str
    ) -> float:
        """计算百分比滑点"""
        slippage_rate = self.base_slippage
        
        if side == 'buy':
            # 买入时价格上涨
            return order_size * price * slippage_rate
        else:
            # 卖出时价格下跌
            return order_size * price * slippage_rate
            
    def _calculate_fixed_slippage(
        self,
        order_size: float,
        side: str
    ) -> float:
        """计算固定滑点"""
        return self.base_slippage
        
    def _calculate_volume_based_slippage(
        self,
        order_size: float,
        price: float,
        market_data: pd.Series,
        side: str
    ) -> float:
        """计算基于成交量的滑点"""
        daily_volume = market_data.get('volume', 0)
        
        if daily_volume == 0:
            return self._calculate_percentage_slippage(order_size, price, side)
            
        # 计算订单占日成交量的比例
        volume_ratio = order_size / daily_volume
        
        # 基础滑点
        base_slippage = self.base_slippage
        
        # 成交量冲击
        if volume_ratio > self.volume_threshold:
            volume_impact = self.volume_impact_factor * np.log(1 + volume_ratio * 10)
            slippage_rate = base_slippage + volume_impact
        else:
            slippage_rate = base_slippage
            
        return order_size * price * slippage_rate
        
    def _calculate_market_impact_slippage(
        self,
        order_size: float,
        price: float,
        market_data: pd.Series,
        side: str
    ) -> float:
        """计算市场冲击滑点"""
        daily_volume = market_data.get('volume', 0)
        high_price = market_data.get('high', price)
        low_price = market_data.get('low', price)
        
        if daily_volume == 0:
            return self._calculate_percentage_slippage(order_size, price, side)
            
        # 计算日内波动率
        if high_price != low_price:
            intraday_volatility = (high_price - low_price) / price
        else:
            intraday_volatility = 0.01  # 默认1%
            
        # 计算订单规模影响
        order_value = order_size * price
        avg_trade_value = market_data.get('amount', 0) / max(daily_volume, 1)
        
        if avg_trade_value > 0:
            size_impact = np.sqrt(order_value / avg_trade_value)
        else:
            size_impact = 1
            
        # 计算市场冲击滑点
        market_impact = (
            self.market_impact_coeff * 
            intraday_volatility * 
            size_impact * 
            price
        )
        
        return order_size * market_impact
        
    def get_execution_price(
        self,
        order_size: float,
        benchmark_price: float,
        market_data: pd.Series,
        side: str
    ) -> float:
        """
        获取执行价格
        
        Args:
            order_size: 订单大小
            benchmark_price: 基准价格
            market_data: 市场数据
            side: 交易方向
            
        Returns:
            执行价格
        """
        slippage_amount = self.calculate_slippage(
            order_size, benchmark_price, market_data, side
        )
        
        slippage_per_share = slippage_amount / order_size if order_size > 0 else 0
        
        if side == 'buy':
            return benchmark_price + slippage_per_share
        else:
            return benchmark_price - slippage_per_share
            
    def simulate_partial_fill(
        self,
        order_size: float,
        benchmark_price: float,
        market_data: pd.Series,
        side: str
    ) -> Dict[str, float]:
        """
        模拟部分成交
        
        Returns:
            包含filled_size和avg_price的字典
        """
        daily_volume = market_data.get('volume', 0)
        
        if daily_volume == 0:
            # 无成交量时无法成交
            return {'filled_size': 0, 'avg_price': benchmark_price}
            
        # 计算最大可成交量（不超过日成交量的10%）
        max_fill_ratio = 0.1
        max_fill_size = daily_volume * max_fill_ratio
        
        if order_size <= max_fill_size:
            # 完全成交
            execution_price = self.get_execution_price(
                order_size, benchmark_price, market_data, side
            )
            return {'filled_size': order_size, 'avg_price': execution_price}
        else:
            # 部分成交
            filled_size = max_fill_size
            execution_price = self.get_execution_price(
                filled_size, benchmark_price, market_data, side
            )
            return {'filled_size': filled_size, 'avg_price': execution_price}
            
    def calculate_liquidity_cost(
        self,
        order_size: float,
        price: float,
        market_data: pd.Series
    ) -> float:
        """计算流动性成本"""
        daily_volume = market_data.get('volume', 0)
        daily_amount = market_data.get('amount', 0)
        
        if daily_volume == 0 or daily_amount == 0:
            return 0.01  # 默认1%流动性成本
            
        # 计算平均交易金额
        avg_trade_size = daily_amount / daily_volume
        order_value = order_size * price
        
        # 订单越大，流动性成本越高
        liquidity_ratio = order_value / daily_amount
        liquidity_cost = 0.001 * np.log(1 + liquidity_ratio * 100)  # 基础0.1%
        
        return min(liquidity_cost, 0.05)  # 最大5%流动性成本