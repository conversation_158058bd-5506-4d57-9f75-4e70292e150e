#!/usr/bin/env python3
"""
市场服务测试脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_market_service():
    """测试市场服务"""
    try:
        # 导入市场服务
        from app.services.market_service import MarketService
        print("✓ 成功导入MarketService")
        
        # 创建服务实例
        service = MarketService()
        print("✓ 成功创建MarketService实例")
        
        # 测试获取股票行情
        quote = await service.get_quote("600519")
        if quote:
            print(f"✓ 成功获取股票行情: {quote.symbol} - {quote.name}")
            print(f"  当前价格: {quote.currentPrice}")
            print(f"  涨跌幅: {quote.changePercent}%")
        else:
            print("✗ 获取股票行情失败")
        
        # 测试批量获取行情
        quotes = await service.get_quotes(["600519", "000858"])
        print(f"✓ 成功批量获取行情，数量: {len(quotes)}")
        
        # 测试获取市场概览
        overview = await service.get_market_overview()
        if overview:
            print("✓ 成功获取市场概览")
            print(f"  市场统计数量: {len(overview.get('marketStats', []))}")
        
        # 测试获取指数
        indices = await service.get_indices()
        print(f"✓ 成功获取指数，数量: {len(indices)}")
        
        # 测试获取板块
        sectors = await service.get_sectors()
        print(f"✓ 成功获取板块，数量: {len(sectors)}")
        
        # 测试搜索股票
        search_results = await service.search_stocks("茅台")
        print(f"✓ 成功搜索股票，结果数量: {len(search_results)}")
        
        # 测试获取排行榜
        rankings = await service.get_rankings("CHANGE_PERCENT", 10)
        print(f"✓ 成功获取排行榜，数量: {len(rankings)}")
        
        # 测试获取订单簿
        orderbook = await service.get_orderbook("600519")
        if orderbook:
            print("✓ 成功获取订单簿")
            print(f"  买盘档位: {len(orderbook.get('bids', []))}")
            print(f"  卖盘档位: {len(orderbook.get('asks', []))}")
        
        # 测试获取自选股列表（不需要数据库）
        watchlist = await service.get_watchlist(1)
        print(f"✓ 成功获取自选股列表，数量: {len(watchlist)}")
        
        print("\n✅ 所有测试通过！市场服务功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_market_service())
    if success:
        print("\n🎉 市场服务修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 市场服务仍有问题需要修复")
        sys.exit(1)