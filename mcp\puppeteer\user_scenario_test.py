#!/usr/bin/env python3
"""
用户场景测试脚本
模拟真实用户使用量化投资平台的完整流程
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UserScenarioTest:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'scenarios': [],
            'performance_metrics': {},
            'accessibility_issues': [],
            'user_experience_issues': []
        }
        
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器以便观察
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        # 创建新页面
        self.page = await self.browser.new_page()
        
        # 设置视口大小
        await self.page.set_viewport_size({'width': 1920, 'height': 1080})
        
        # 监听控制台消息
        self.page.on('console', self.handle_console_message)
        
        logger.info("🚀 测试环境初始化完成")

    async def handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type == 'error':
            self.test_results['accessibility_issues'].append({
                'type': 'console_error',
                'message': msg.text,
                'timestamp': datetime.now().isoformat()
            })

    async def scenario_1_new_user_onboarding(self):
        """场景1: 新用户入门流程"""
        logger.info("📋 开始场景1: 新用户入门流程")
        scenario_result = {
            'name': '新用户入门流程',
            'start_time': time.time(),
            'steps': [],
            'success': True,
            'issues': []
        }
        
        try:
            # 步骤1: 访问首页
            await self.page.goto('http://localhost:5173')
            await self.page.wait_for_load_state('networkidle')
            scenario_result['steps'].append('访问首页成功')
            
            # 步骤2: 检查页面加载性能
            performance = await self.page.evaluate('''() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalTime: navigation.loadEventEnd - navigation.fetchStart
                };
            }''')
            
            self.test_results['performance_metrics']['homepage_load'] = performance
            scenario_result['steps'].append(f'页面加载时间: {performance["totalTime"]:.2f}ms')
            
            # 步骤3: 检查关键元素是否存在
            key_elements = [
                {'selector': '#app', 'name': 'Vue应用根节点'},
                {'selector': '.nav, .navigation, .menu', 'name': '导航菜单'},
                {'selector': 'button, .el-button', 'name': '按钮组件'},
            ]
            
            for element in key_elements:
                try:
                    await self.page.wait_for_selector(element['selector'], timeout=5000)
                    scenario_result['steps'].append(f'{element["name"]}加载成功')
                except:
                    scenario_result['issues'].append(f'{element["name"]}未找到')
                    scenario_result['success'] = False
            
            # 步骤4: 测试响应式设计
            await self.test_responsive_design(scenario_result)
            
            # 步骤5: 测试可访问性
            await self.test_accessibility(scenario_result)
            
        except Exception as e:
            scenario_result['success'] = False
            scenario_result['issues'].append(f'场景执行失败: {str(e)}')
            logger.error(f"场景1执行失败: {e}")
        
        scenario_result['end_time'] = time.time()
        scenario_result['duration'] = scenario_result['end_time'] - scenario_result['start_time']
        self.test_results['scenarios'].append(scenario_result)
        
        logger.info(f"✅ 场景1完成，耗时: {scenario_result['duration']:.2f}秒")

    async def scenario_2_market_data_browsing(self):
        """场景2: 市场数据浏览"""
        logger.info("📊 开始场景2: 市场数据浏览")
        scenario_result = {
            'name': '市场数据浏览',
            'start_time': time.time(),
            'steps': [],
            'success': True,
            'issues': []
        }
        
        try:
            # 导航到市场页面
            await self.page.goto('http://localhost:5173/market')
            await self.page.wait_for_load_state('networkidle')
            scenario_result['steps'].append('导航到市场页面')
            
            # 等待数据加载
            await asyncio.sleep(3)
            
            # 检查图表是否加载
            chart_elements = await self.page.query_selector_all('canvas, .chart-container')
            if chart_elements:
                scenario_result['steps'].append(f'发现{len(chart_elements)}个图表元素')
            else:
                scenario_result['issues'].append('未发现图表元素')
            
            # 检查数据表格
            table_elements = await self.page.query_selector_all('.el-table, table')
            if table_elements:
                scenario_result['steps'].append(f'发现{len(table_elements)}个数据表格')
            else:
                scenario_result['issues'].append('未发现数据表格')
            
            # 测试搜索功能
            search_input = await self.page.query_selector('input[placeholder*="搜索"], .search-input')
            if search_input:
                await search_input.fill('000001')
                await self.page.keyboard.press('Enter')
                await asyncio.sleep(2)
                scenario_result['steps'].append('测试搜索功能')
            
        except Exception as e:
            scenario_result['success'] = False
            scenario_result['issues'].append(f'场景执行失败: {str(e)}')
            logger.error(f"场景2执行失败: {e}")
        
        scenario_result['end_time'] = time.time()
        scenario_result['duration'] = scenario_result['end_time'] - scenario_result['start_time']
        self.test_results['scenarios'].append(scenario_result)
        
        logger.info(f"✅ 场景2完成，耗时: {scenario_result['duration']:.2f}秒")

    async def scenario_3_strategy_management(self):
        """场景3: 策略管理"""
        logger.info("🧠 开始场景3: 策略管理")
        scenario_result = {
            'name': '策略管理',
            'start_time': time.time(),
            'steps': [],
            'success': True,
            'issues': []
        }
        
        try:
            # 导航到策略页面
            await self.page.goto('http://localhost:5173/strategy')
            await self.page.wait_for_load_state('networkidle')
            scenario_result['steps'].append('导航到策略页面')
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 检查策略卡片
            strategy_cards = await self.page.query_selector_all('.strategy-card, .el-card')
            if strategy_cards:
                scenario_result['steps'].append(f'发现{len(strategy_cards)}个策略卡片')
            
            # 测试创建策略按钮
            create_button = await self.page.query_selector('button:has-text("创建"), button:has-text("新建")')
            if create_button:
                scenario_result['steps'].append('发现创建策略按钮')
            
        except Exception as e:
            scenario_result['success'] = False
            scenario_result['issues'].append(f'场景执行失败: {str(e)}')
            logger.error(f"场景3执行失败: {e}")
        
        scenario_result['end_time'] = time.time()
        scenario_result['duration'] = scenario_result['end_time'] - scenario_result['start_time']
        self.test_results['scenarios'].append(scenario_result)
        
        logger.info(f"✅ 场景3完成，耗时: {scenario_result['duration']:.2f}秒")

    async def test_responsive_design(self, scenario_result):
        """测试响应式设计"""
        viewports = [
            {'width': 1920, 'height': 1080, 'name': '桌面端'},
            {'width': 768, 'height': 1024, 'name': '平板端'},
            {'width': 375, 'height': 667, 'name': '手机端'}
        ]
        
        for viewport in viewports:
            await self.page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
            await asyncio.sleep(1)
            
            # 检查文字大小
            small_text_count = await self.page.evaluate('''() => {
                const elements = document.querySelectorAll('*');
                let count = 0;
                elements.forEach(el => {
                    const style = window.getComputedStyle(el);
                    const fontSize = parseFloat(style.fontSize);
                    if (fontSize < 14 && el.textContent.trim()) {
                        count++;
                    }
                });
                return count;
            }''')
            
            if viewport['name'] == '手机端' and small_text_count > 0:
                scenario_result['issues'].append(f'{viewport["name"]}发现{small_text_count}个小于14px的文字元素')
            
            scenario_result['steps'].append(f'{viewport["name"]}响应式测试完成')

    async def test_accessibility(self, scenario_result):
        """测试可访问性"""
        # 检查表单标签
        unlabeled_inputs = await self.page.evaluate('''() => {
            const inputs = document.querySelectorAll('input, textarea, select');
            let count = 0;
            inputs.forEach(input => {
                const id = input.id;
                const hasLabel = id && document.querySelector(`label[for="${id}"]`);
                const hasAriaLabel = input.getAttribute('aria-label');
                const hasPlaceholder = input.placeholder;
                
                if (!hasLabel && !hasAriaLabel && !hasPlaceholder) {
                    count++;
                }
            });
            return count;
        }''')
        
        if unlabeled_inputs > 0:
            scenario_result['issues'].append(f'发现{unlabeled_inputs}个缺少标签的表单元素')
        
        # 检查图片alt属性
        images_without_alt = await self.page.evaluate('''() => {
            const images = document.querySelectorAll('img');
            let count = 0;
            images.forEach(img => {
                if (!img.alt && !img.getAttribute('aria-label')) {
                    count++;
                }
            });
            return count;
        }''')
        
        if images_without_alt > 0:
            scenario_result['issues'].append(f'发现{images_without_alt}个缺少alt属性的图片')

    async def generate_report(self):
        """生成测试报告"""
        report_file = Path(f'user_scenario_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 计算总体统计
        total_scenarios = len(self.test_results['scenarios'])
        successful_scenarios = sum(1 for s in self.test_results['scenarios'] if s['success'])
        total_issues = sum(len(s['issues']) for s in self.test_results['scenarios'])
        
        self.test_results['summary'] = {
            'total_scenarios': total_scenarios,
            'successful_scenarios': successful_scenarios,
            'success_rate': successful_scenarios / total_scenarios if total_scenarios > 0 else 0,
            'total_issues': total_issues,
            'total_accessibility_issues': len(self.test_results['accessibility_issues'])
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📋 测试报告已保存: {report_file}")
        
        # 输出摘要
        print("\n" + "="*60)
        print("📊 用户场景测试摘要")
        print("="*60)
        print(f"总场景数: {total_scenarios}")
        print(f"成功场景: {successful_scenarios}")
        print(f"成功率: {self.test_results['summary']['success_rate']:.1%}")
        print(f"发现问题: {total_issues}")
        print(f"可访问性问题: {len(self.test_results['accessibility_issues'])}")
        
        for scenario in self.test_results['scenarios']:
            status = "✅" if scenario['success'] else "❌"
            print(f"{status} {scenario['name']}: {scenario['duration']:.2f}秒")
            if scenario['issues']:
                for issue in scenario['issues']:
                    print(f"   ⚠️ {issue}")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    test = UserScenarioTest()
    
    try:
        await test.setup()
        
        # 执行用户场景测试
        await test.scenario_1_new_user_onboarding()
        await test.scenario_2_market_data_browsing()
        await test.scenario_3_strategy_management()
        
        # 生成报告
        await test.generate_report()
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
