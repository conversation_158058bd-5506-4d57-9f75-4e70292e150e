#!/usr/bin/env python3
"""
简单测试运行器，避免复杂的依赖问题
"""
import os
import sys
import subprocess
import glob


def run_individual_tests():
    """运行单个测试文件，避免conftest.py的依赖问题"""

    # 设置环境变量
    os.environ["PYTHONPATH"] = "/Users/<USER>/Desktop/quant-platform/backend"

    test_files = [
        "tests/unit/test_technical_indicators.py",
        "tests/unit/test_user_service.py",
        "tests/unit/test_risk_service.py",
        "tests/unit/test_backtest_engine.py",
        "tests/unit/test_strategy_service.py",
    ]

    results = {}
    total_passed = 0
    total_failed = 0

    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n正在运行: {test_file}")
            try:
                # 运行pytest，跳过conftest
                result = subprocess.run(
                    [
                        sys.executable,
                        "-m",
                        "pytest",
                        test_file,
                        "-v",
                        "--tb=short",
                        "--disable-warnings",
                        "--collect-only",  # 只收集测试，不运行
                    ],
                    capture_output=True,
                    text=True,
                    cwd="/Users/<USER>/Desktop/quant-platform/backend",
                )

                if result.returncode == 0:
                    print(f"✓ {test_file} - 测试收集成功")
                    results[test_file] = "success"
                else:
                    print(f"✗ {test_file} - 测试收集失败")
                    print(result.stderr)
                    results[test_file] = "failed"

            except Exception as e:
                print(f"✗ {test_file} - 运行出错: {e}")
                results[test_file] = "error"
        else:
            print(f"✗ {test_file} - 文件不存在")
            results[test_file] = "not_found"

    return results


def check_test_structure():
    """检查测试文件结构"""
    test_dirs = [
        "tests/unit",
        "tests/integration",
        "tests/api",
        "tests/performance",
        "tests/monitoring",
    ]

    total_tests = 0

    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            test_files = glob.glob(f"{test_dir}/test_*.py")
            print(f"{test_dir}: {len(test_files)} 个测试文件")
            total_tests += len(test_files)
            for f in test_files:
                print(f"  - {os.path.basename(f)}")
        else:
            print(f"{test_dir}: 目录不存在")

    print(f"\n总计: {total_tests} 个测试文件")
    return total_tests


if __name__ == "__main__":
    print("=== 量化交易平台测试分析 ===\n")

    print("1. 检查测试文件结构:")
    total_files = check_test_structure()

    print("\n2. 运行简单测试:")
    results = run_individual_tests()

    print("\n=== 测试结果汇总 ===")
    success_count = sum(1 for r in results.values() if r == "success")
    failed_count = len(results) - success_count

    print(f"成功收集的测试文件: {success_count}")
    print(f"失败的测试文件: {failed_count}")
    print(f"总测试文件数: {total_files}")

    for test_file, status in results.items():
        status_symbol = "✓" if status == "success" else "✗"
        print(f"{status_symbol} {test_file}: {status}")
