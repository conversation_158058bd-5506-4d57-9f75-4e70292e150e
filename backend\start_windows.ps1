# Windows PowerShell启动脚本
Write-Host "启动量化交易平台后端服务..." -ForegroundColor Green
Write-Host ""

# 激活虚拟环境
try {
    & "backend\venv\Scripts\Activate.ps1"
    Write-Host "虚拟环境已激活" -ForegroundColor Green
} catch {
    Write-Host "错误：无法激活虚拟环境" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# 切换到backend目录
Set-Location backend

# 启动FastAPI服务器
Write-Host "启动FastAPI服务器..." -ForegroundColor Yellow
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

Read-Host "按任意键退出"
