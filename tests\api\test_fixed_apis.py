"""
测试修复后的API
"""
import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000/api/v1"

async def test_apis():
    """测试所有修复的API"""
    async with aiohttp.ClientSession() as session:
        print("=== 测试API修复 ===\n")
        
        # 1. 测试认证API
        print("1. 测试认证API")
        
        # 注册
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "test123456"
        }
        
        try:
            async with session.post(f"{BASE_URL}/auth/register", json=register_data) as resp:
                print(f"   注册: {resp.status}")
                if resp.status == 201:
                    print("   ✓ 注册成功")
        except Exception as e:
            print(f"   ✗ 注册失败: {e}")
        
        # 登录
        login_data = {
            "username": "testuser",
            "password": "test123456"
        }
        
        token = None
        try:
            async with session.post(f"{BASE_URL}/auth/login", data=login_data) as resp:
                print(f"   登录: {resp.status}")
                if resp.status == 200:
                    result = await resp.json()
                    token = result.get("access_token")
                    print("   ✓ 登录成功")
        except Exception as e:
            print(f"   ✗ 登录失败: {e}")
        
        if token:
            headers = {"Authorization": f"Bearer {token}"}
            
            # 2. 测试交易API
            print("\n2. 测试交易API")
            
            # 获取账户信息
            try:
                async with session.get(f"{BASE_URL}/trading/account", headers=headers) as resp:
                    print(f"   获取账户: {resp.status}")
                    if resp.status == 200:
                        print("   ✓ 账户信息获取成功")
            except Exception as e:
                print(f"   ✗ 获取账户失败: {e}")
            
            # 3. 测试策略API
            print("\n3. 测试策略API")
            
            # 获取策略列表
            try:
                async with session.get(f"{BASE_URL}/strategy/strategies", headers=headers) as resp:
                    print(f"   策略列表: {resp.status}")
                    if resp.status == 200:
                        print("   ✓ 策略列表获取成功")
            except Exception as e:
                print(f"   ✗ 获取策略失败: {e}")
            
            # 4. 测试风控API
            print("\n4. 测试风控API")
            
            # 获取风险指标
            try:
                async with session.get(f"{BASE_URL}/risk/metrics", headers=headers) as resp:
                    print(f"   风险指标: {resp.status}")
                    if resp.status == 200:
                        print("   ✓ 风险指标获取成功")
            except Exception as e:
                print(f"   ✗ 获取风险指标失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_apis())
