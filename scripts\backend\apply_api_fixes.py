"""
应用API修复脚本
将修复后的代码集成到现有系统中
"""
import os
import shutil
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def apply_fixes():
    """应用所有修复"""
    backend_dir = Path(__file__).parent
    
    fixes_applied = []
    
    # 1. 备份原文件
    logger.info("备份原始文件...")
    backup_dir = backend_dir / "backup_before_fix"
    backup_dir.mkdir(exist_ok=True)
    
    # 2. 应用交易服务修复
    logger.info("应用交易服务修复...")
    
    # 复制数据库模型
    trading_model_src = backend_dir / "app/models/trading.py"
    if trading_model_src.exists():
        # 确保models目录存在
        models_dir = backend_dir / "app/models"
        models_dir.mkdir(exist_ok=True)
        
        # 创建__init__.py
        init_file = models_dir / "__init__.py"
        if not init_file.exists():
            init_file.write_text('"""数据库模型"""\n')
        
        fixes_applied.append("交易数据库模型")
    
    # 复制交易服务实现
    trading_service_src = backend_dir / "app/services/trading_service_impl.py"
    if trading_service_src.exists():
        trading_service_dst = backend_dir / "app/services/trading_service.py"
        if trading_service_dst.exists():
            shutil.copy(trading_service_dst, backup_dir / "trading_service.py.bak")
        shutil.copy(trading_service_src, trading_service_dst)
        fixes_applied.append("交易服务实现")
    
    # 3. 更新API路由
    logger.info("更新API路由...")
    
    # 更新v1 __init__.py以包含修复的路由
    v1_init_path = backend_dir / "app/api/v1/__init__.py"
    if v1_init_path.exists():
        content = v1_init_path.read_text()
        
        # 添加修复路由的导入
        additions = []
        
        if "auth_fixed" not in content:
            additions.append("from .auth_fixed import router as auth_fixed_router")
        if "strategy_fixed" not in content:
            additions.append("from .strategy_fixed import router as strategy_fixed_router")
        if "risk_fixed" not in content:
            additions.append("from .risk_fixed import router as risk_fixed_router")
        
        if additions:
            # 在导入部分添加
            lines = content.split("\n")
            import_end = 0
            for i, line in enumerate(lines):
                if line.strip() and not line.startswith("from") and not line.startswith("import"):
                    import_end = i
                    break
            
            for addition in additions:
                lines.insert(import_end, addition)
                import_end += 1
            
            # 在路由注册部分添加
            router_section_start = content.find("# 注册所有路由")
            if router_section_start > 0:
                lines.append("\n# 注册修复后的路由（临时覆盖）")
                lines.append("api_router.include_router(auth_fixed_router, prefix='/auth', tags=['认证-修复版'])")
                lines.append("api_router.include_router(strategy_fixed_router, prefix='/strategy', tags=['策略-修复版'])")
                lines.append("api_router.include_router(risk_fixed_router, prefix='/risk', tags=['风控-修复版'])")
            
            v1_init_path.write_text("\n".join(lines))
            fixes_applied.append("API路由注册")
    
    # 4. 创建数据库迁移脚本
    logger.info("创建数据库迁移脚本...")
    
    migration_script = backend_dir / "create_trading_tables.py"
    migration_content = '''"""
创建交易相关数据表
"""
import asyncio
from sqlalchemy import create_engine
from app.core.config import get_settings
from app.core.database import Base
from app.models.trading import Order, Trade, Position, Account
from app.db.models.user import User

settings = get_settings()

def create_tables():
    """创建数据表"""
    # 创建同步引擎用于建表
    engine = create_engine(
        settings.DATABASE_URL.replace("postgresql+asyncpg", "postgresql"),
        echo=True
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("数据表创建完成！")

if __name__ == "__main__":
    create_tables()
'''
    
    migration_script.write_text(migration_content)
    fixes_applied.append("数据库迁移脚本")
    
    # 5. 创建前端API更新脚本
    logger.info("创建前端API更新指南...")
    
    frontend_update_guide = backend_dir.parent / "frontend_api_update_guide.md"
    guide_content = '''# 前端API更新指南

## 1. 更新API端点配置

将以下端点更新到前端的API配置文件中：

### 认证API
- POST `/api/v1/auth/register` - 用户注册
- POST `/api/v1/auth/login` - 用户登录
- GET `/api/v1/auth/me` - 获取当前用户
- POST `/api/v1/auth/logout` - 用户登出
- POST `/api/v1/auth/change-password` - 修改密码

### 交易API
- POST `/api/v1/trading/orders` - 创建订单
- GET `/api/v1/trading/orders` - 获取订单列表
- DELETE `/api/v1/trading/orders/{order_id}` - 取消订单
- GET `/api/v1/trading/positions` - 获取持仓
- GET `/api/v1/trading/account` - 获取账户信息

### 策略API
- POST `/api/v1/strategy/strategies` - 创建策略
- GET `/api/v1/strategy/strategies` - 获取策略列表
- PUT `/api/v1/strategy/strategies/{strategy_id}` - 更新策略
- POST `/api/v1/strategy/strategies/{strategy_id}/backtest` - 运行回测
- POST `/api/v1/strategy/strategies/import` - 导入策略

### 风控API
- GET `/api/v1/risk/metrics` - 获取风险指标
- GET `/api/v1/risk/limits` - 获取风控限制
- PUT `/api/v1/risk/limits` - 更新风控限制
- GET `/api/v1/risk/alerts` - 获取风险告警
- POST `/api/v1/risk/check-order` - 检查订单风险

## 2. 更新请求格式

确保请求体格式与后端API匹配：

### 登录请求
```json
{
    "username": "string",
    "password": "string"
}
```

### 创建订单请求
```json
{
    "symbol": "string",
    "direction": "BUY" | "SELL",
    "order_type": "MARKET" | "LIMIT",
    "price": number,
    "volume": number
}
```

## 3. 处理响应格式

所有API响应都遵循统一格式：

```json
{
    "success": boolean,
    "message": "string",
    "data": object | array
}
```

## 4. 错误处理

- 401: 未授权，需要重新登录
- 403: 无权限
- 404: 资源不存在
- 405: 方法不允许（检查HTTP方法）
- 422: 请求参数验证失败
'''
    
    frontend_update_guide.write_text(guide_content)
    fixes_applied.append("前端更新指南")
    
    # 6. 创建测试脚本
    logger.info("创建API测试脚本...")
    
    test_script = backend_dir / "test_fixed_apis.py"
    test_content = '''"""
测试修复后的API
"""
import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000/api/v1"

async def test_apis():
    """测试所有修复的API"""
    async with aiohttp.ClientSession() as session:
        print("=== 测试API修复 ===\\n")
        
        # 1. 测试认证API
        print("1. 测试认证API")
        
        # 注册
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "test123456"
        }
        
        try:
            async with session.post(f"{BASE_URL}/auth/register", json=register_data) as resp:
                print(f"   注册: {resp.status}")
                if resp.status == 201:
                    print("   ✓ 注册成功")
        except Exception as e:
            print(f"   ✗ 注册失败: {e}")
        
        # 登录
        login_data = {
            "username": "testuser",
            "password": "test123456"
        }
        
        token = None
        try:
            async with session.post(f"{BASE_URL}/auth/login", data=login_data) as resp:
                print(f"   登录: {resp.status}")
                if resp.status == 200:
                    result = await resp.json()
                    token = result.get("access_token")
                    print("   ✓ 登录成功")
        except Exception as e:
            print(f"   ✗ 登录失败: {e}")
        
        if token:
            headers = {"Authorization": f"Bearer {token}"}
            
            # 2. 测试交易API
            print("\\n2. 测试交易API")
            
            # 获取账户信息
            try:
                async with session.get(f"{BASE_URL}/trading/account", headers=headers) as resp:
                    print(f"   获取账户: {resp.status}")
                    if resp.status == 200:
                        print("   ✓ 账户信息获取成功")
            except Exception as e:
                print(f"   ✗ 获取账户失败: {e}")
            
            # 3. 测试策略API
            print("\\n3. 测试策略API")
            
            # 获取策略列表
            try:
                async with session.get(f"{BASE_URL}/strategy/strategies", headers=headers) as resp:
                    print(f"   策略列表: {resp.status}")
                    if resp.status == 200:
                        print("   ✓ 策略列表获取成功")
            except Exception as e:
                print(f"   ✗ 获取策略失败: {e}")
            
            # 4. 测试风控API
            print("\\n4. 测试风控API")
            
            # 获取风险指标
            try:
                async with session.get(f"{BASE_URL}/risk/metrics", headers=headers) as resp:
                    print(f"   风险指标: {resp.status}")
                    if resp.status == 200:
                        print("   ✓ 风险指标获取成功")
            except Exception as e:
                print(f"   ✗ 获取风险指标失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_apis())
'''
    
    test_script.write_text(test_content)
    fixes_applied.append("API测试脚本")
    
    # 打印总结
    print("\n=== API修复应用完成 ===\n")
    print("已应用的修复：")
    for fix in fixes_applied:
        print(f"  ✓ {fix}")
    
    print("\n下一步操作：")
    print("1. 运行数据库迁移: python create_trading_tables.py")
    print("2. 重启后端服务: uvicorn app.main:app --reload")
    print("3. 运行API测试: python test_fixed_apis.py")
    print("4. 查看前端更新指南: ../frontend_api_update_guide.md")
    
    return True


if __name__ == "__main__":
    apply_fixes()