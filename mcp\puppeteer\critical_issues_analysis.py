#!/usr/bin/env python3
"""
关键问题分析脚本
基于深度用户测试结果，针对发现的关键问题进行深入分析
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CriticalIssuesAnalysis:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'critical_issues': [],
            'ui_problems': [],
            'navigation_issues': [],
            'data_loading_problems': [],
            'responsive_issues': [],
            'performance_problems': [],
            'recommendations': []
        }
        
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
        logger.info("🔍 关键问题分析环境初始化完成")

    async def analyze_navigation_issues(self):
        """分析导航问题"""
        logger.info("🧭 分析导航问题...")
        
        await self.page.goto('http://localhost:5173', wait_until='networkidle')
        await asyncio.sleep(2)
        
        # 检查导航元素
        nav_selectors = [
            'nav', '.nav', '.navigation', '.navbar', 
            '.menu', '.sidebar', '.header-nav',
            '[role="navigation"]', '.main-nav'
        ]
        
        found_nav = False
        for selector in nav_selectors:
            nav_elements = await self.page.query_selector_all(selector)
            if nav_elements:
                found_nav = True
                logger.info(f"✅ 找到导航元素: {selector} ({len(nav_elements)}个)")
                break
        
        if not found_nav:
            self.analysis_results['navigation_issues'].append({
                'issue': '缺少明显的导航菜单',
                'severity': 'high',
                'description': '用户无法轻松找到主要导航元素',
                'recommendation': '添加明显的导航栏或菜单'
            })
        
        # 检查面包屑导航
        breadcrumb_selectors = ['.breadcrumb', '.breadcrumbs', '[aria-label="breadcrumb"]']
        has_breadcrumb = False
        for selector in breadcrumb_selectors:
            if await self.page.query_selector(selector):
                has_breadcrumb = True
                break
        
        if not has_breadcrumb:
            self.analysis_results['navigation_issues'].append({
                'issue': '缺少面包屑导航',
                'severity': 'medium',
                'description': '用户难以了解当前位置',
                'recommendation': '添加面包屑导航帮助用户定位'
            })

    async def analyze_data_loading_issues(self):
        """分析数据加载问题"""
        logger.info("📊 分析数据加载问题...")
        
        await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
        await asyncio.sleep(5)  # 等待数据加载
        
        # 检查数据表格
        table_selectors = [
            'table', '.el-table', '.data-table', 
            '[role="table"]', '.table-container'
        ]
        
        tables_found = 0
        for selector in table_selectors:
            tables = await self.page.query_selector_all(selector)
            tables_found += len(tables)
        
        if tables_found == 0:
            self.analysis_results['data_loading_problems'].append({
                'issue': '市场页面缺少数据表格',
                'severity': 'high',
                'description': '用户无法查看市场数据',
                'recommendation': '确保数据表格正确渲染和显示'
            })
        
        # 检查图表
        chart_selectors = [
            'canvas', '.chart-container', '[id*="chart"]',
            '.echarts', '.trading-view', '.chart-wrapper'
        ]
        
        charts_found = 0
        for selector in chart_selectors:
            charts = await self.page.query_selector_all(selector)
            charts_found += len(charts)
        
        if charts_found == 0:
            self.analysis_results['data_loading_problems'].append({
                'issue': '市场页面缺少图表',
                'severity': 'high',
                'description': '用户无法查看图形化的市场数据',
                'recommendation': '确保图表组件正确初始化和渲染'
            })
        
        # 检查加载状态
        loading_indicators = await self.page.query_selector_all(
            '.loading, .spinner, .el-loading, [aria-label*="loading"]'
        )
        
        if len(loading_indicators) > 0:
            self.analysis_results['data_loading_problems'].append({
                'issue': '数据加载状态持续显示',
                'severity': 'medium',
                'description': '加载指示器可能卡住，影响用户体验',
                'recommendation': '检查数据加载逻辑和错误处理'
            })

    async def analyze_responsive_issues(self):
        """分析响应式设计问题"""
        logger.info("📱 分析响应式设计问题...")
        
        viewports = [
            {'width': 768, 'height': 1024, 'name': '平板'},
            {'width': 414, 'height': 896, 'name': '手机大屏'},
            {'width': 375, 'height': 667, 'name': '手机标准'}
        ]
        
        for viewport in viewports:
            await self.page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
            await self.page.reload(wait_until='networkidle')
            await asyncio.sleep(2)
            
            # 检查文字大小
            small_text_count = await self.page.evaluate('''() => {
                const elements = document.querySelectorAll('*');
                let count = 0;
                const details = [];
                elements.forEach(el => {
                    const style = window.getComputedStyle(el);
                    const fontSize = parseFloat(style.fontSize);
                    if (fontSize < 14 && el.textContent.trim() && el.textContent.trim().length > 2) {
                        count++;
                        if (count <= 5) {  // 只记录前5个示例
                            details.push({
                                tag: el.tagName,
                                fontSize: fontSize,
                                text: el.textContent.trim().substring(0, 50)
                            });
                        }
                    }
                });
                return { count, details };
            }''')
            
            if small_text_count['count'] > 20:
                self.analysis_results['responsive_issues'].append({
                    'issue': f'{viewport["name"]}端文字过小',
                    'severity': 'high',
                    'description': f'发现{small_text_count["count"]}个小于14px的文字元素',
                    'examples': small_text_count['details'],
                    'recommendation': f'为{viewport["name"]}端优化文字大小，确保可读性'
                })
            
            # 检查水平滚动
            has_horizontal_scroll = await self.page.evaluate('''() => {
                return document.documentElement.scrollWidth > window.innerWidth;
            }''')
            
            if has_horizontal_scroll:
                self.analysis_results['responsive_issues'].append({
                    'issue': f'{viewport["name"]}端出现水平滚动',
                    'severity': 'medium',
                    'description': '页面内容超出视口宽度',
                    'recommendation': '调整布局确保内容适应屏幕宽度'
                })

    async def analyze_performance_issues(self):
        """分析性能问题"""
        logger.info("⚡ 分析性能问题...")
        
        await self.page.goto('http://localhost:5173', wait_until='networkidle')
        
        # 检查页面加载性能
        performance_metrics = await self.page.evaluate('''() => {
            const navigation = performance.getEntriesByType('navigation')[0];
            const paint = performance.getEntriesByType('paint');
            
            let fcp = 0;
            paint.forEach(entry => {
                if (entry.name === 'first-contentful-paint') {
                    fcp = entry.startTime;
                }
            });
            
            return {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalLoadTime: navigation.loadEventEnd - navigation.fetchStart,
                firstContentfulPaint: fcp
            };
        }''')
        
        if performance_metrics['totalLoadTime'] > 3000:
            self.analysis_results['performance_problems'].append({
                'issue': '页面加载时间过长',
                'severity': 'high',
                'description': f'总加载时间: {performance_metrics["totalLoadTime"]:.0f}ms',
                'recommendation': '优化资源加载，使用代码分割和懒加载'
            })
        
        if performance_metrics['firstContentfulPaint'] > 2000:
            self.analysis_results['performance_problems'].append({
                'issue': '首次内容绘制时间过长',
                'severity': 'medium',
                'description': f'FCP: {performance_metrics["firstContentfulPaint"]:.0f}ms',
                'recommendation': '优化关键渲染路径，减少阻塞资源'
            })
        
        # 检查内存使用
        memory_info = await self.page.evaluate('''() => {
            if (performance.memory) {
                return {
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                };
            }
            return null;
        }''')
        
        if memory_info:
            memory_usage_mb = memory_info['usedJSHeapSize'] / 1024 / 1024
            if memory_usage_mb > 100:
                self.analysis_results['performance_problems'].append({
                    'issue': '内存使用过高',
                    'severity': 'medium',
                    'description': f'JS堆内存使用: {memory_usage_mb:.2f}MB',
                    'recommendation': '检查内存泄漏，优化组件生命周期管理'
                })

    async def analyze_ui_consistency(self):
        """分析UI一致性问题"""
        logger.info("🎨 分析UI一致性问题...")
        
        pages = ['/market', '/strategy', '/trading', '/portfolio']
        
        for page_url in pages:
            try:
                await self.page.goto(f'http://localhost:5173{page_url}', wait_until='networkidle')
                await asyncio.sleep(2)
                
                # 检查页面标题
                title = await self.page.text_content('h1, .page-title, .title')
                if not title or title.strip() == '':
                    self.analysis_results['ui_problems'].append({
                        'issue': f'{page_url}页面缺少标题',
                        'severity': 'medium',
                        'description': '页面没有明确的标题',
                        'recommendation': '为每个页面添加清晰的标题'
                    })
                
                # 检查错误状态
                error_elements = await self.page.query_selector_all(
                    '.error, .el-alert--error, [role="alert"]'
                )
                
                if len(error_elements) > 0:
                    error_text = await error_elements[0].text_content()
                    self.analysis_results['ui_problems'].append({
                        'issue': f'{page_url}页面显示错误',
                        'severity': 'high',
                        'description': f'错误信息: {error_text[:100]}',
                        'recommendation': '修复页面错误或改善错误处理'
                    })
                
            except Exception as e:
                self.analysis_results['ui_problems'].append({
                    'issue': f'{page_url}页面访问失败',
                    'severity': 'high',
                    'description': f'页面无法正常访问: {str(e)}',
                    'recommendation': '检查路由配置和页面组件'
                })

    async def generate_recommendations(self):
        """生成改进建议"""
        logger.info("💡 生成改进建议...")
        
        # 基于发现的问题生成建议
        all_issues = (
            self.analysis_results['navigation_issues'] +
            self.analysis_results['data_loading_problems'] +
            self.analysis_results['responsive_issues'] +
            self.analysis_results['performance_problems'] +
            self.analysis_results['ui_problems']
        )
        
        high_severity_count = sum(1 for issue in all_issues if issue.get('severity') == 'high')
        medium_severity_count = sum(1 for issue in all_issues if issue.get('severity') == 'medium')
        
        recommendations = []
        
        if high_severity_count > 0:
            recommendations.append({
                'priority': 'urgent',
                'title': '立即修复高优先级问题',
                'description': f'发现{high_severity_count}个高优先级问题，需要立即处理',
                'actions': [
                    '修复数据加载和显示问题',
                    '改善导航和用户引导',
                    '解决页面访问错误'
                ]
            })
        
        if len(self.analysis_results['responsive_issues']) > 0:
            recommendations.append({
                'priority': 'high',
                'title': '优化移动端体验',
                'description': '移动端存在多个可用性问题',
                'actions': [
                    '调整移动端文字大小至14px以上',
                    '优化触摸目标大小',
                    '确保内容适应屏幕宽度'
                ]
            })
        
        if len(self.analysis_results['performance_problems']) > 0:
            recommendations.append({
                'priority': 'medium',
                'title': '提升性能表现',
                'description': '页面性能需要优化',
                'actions': [
                    '实施代码分割和懒加载',
                    '优化图片和资源加载',
                    '减少内存使用'
                ]
            })
        
        self.analysis_results['recommendations'] = recommendations

    async def generate_report(self):
        """生成分析报告"""
        await self.generate_recommendations()
        
        report_file = Path(f'critical_issues_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 保存详细报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📋 关键问题分析报告已保存: {report_file}")
        
        # 输出摘要
        print("\n" + "="*80)
        print("🔍 关键问题分析报告")
        print("="*80)
        print(f"分析时间: {self.analysis_results['timestamp']}")
        
        categories = [
            ('导航问题', self.analysis_results['navigation_issues']),
            ('数据加载问题', self.analysis_results['data_loading_problems']),
            ('响应式问题', self.analysis_results['responsive_issues']),
            ('性能问题', self.analysis_results['performance_problems']),
            ('UI问题', self.analysis_results['ui_problems'])
        ]
        
        total_issues = 0
        for category_name, issues in categories:
            if issues:
                print(f"\n🔸 {category_name} ({len(issues)}个):")
                total_issues += len(issues)
                for issue in issues:
                    severity_icon = "🚨" if issue.get('severity') == 'high' else "⚠️"
                    print(f"   {severity_icon} {issue['issue']}")
                    print(f"      {issue['description']}")
                    print(f"      💡 {issue['recommendation']}")
        
        print(f"\n📊 总计发现 {total_issues} 个问题")
        
        if self.analysis_results['recommendations']:
            print("\n💡 改进建议:")
            for rec in self.analysis_results['recommendations']:
                priority_icon = "🔥" if rec['priority'] == 'urgent' else "⭐" if rec['priority'] == 'high' else "📝"
                print(f"\n{priority_icon} {rec['title']}")
                print(f"   {rec['description']}")
                for action in rec['actions']:
                    print(f"   • {action}")
        
        return report_file

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    analysis = CriticalIssuesAnalysis()
    
    try:
        await analysis.setup()
        
        # 执行各项分析
        await analysis.analyze_navigation_issues()
        await analysis.analyze_data_loading_issues()
        await analysis.analyze_responsive_issues()
        await analysis.analyze_performance_issues()
        await analysis.analyze_ui_consistency()
        
        # 生成报告
        report_file = await analysis.generate_report()
        
        print(f"\n🎉 关键问题分析完成！详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"分析执行失败: {e}")
    finally:
        await analysis.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
