#!/usr/bin/env python3
"""
测试前端登录页面功能
"""
import requests
import time
import json
from datetime import datetime

def test_frontend_login():
    """测试前端登录页面的后端API集成"""
    
    print("🧪 测试前端登录页面功能")
    print("=" * 50)
    
    # 测试后端服务连通性
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        print(f"✅ 后端服务运行正常 (端口8000)")
    except requests.exceptions.RequestException:
        print(f"⚠️ 后端服务可能未在8000端口运行")
    
    # 测试前端服务连通性
    try:
        response = requests.get('http://localhost:5173', timeout=5)
        print(f"✅ 前端服务运行正常 (端口5173)")
    except requests.exceptions.RequestException:
        print(f"❌ 前端服务未在5173端口运行")
        return False
    
    print(f"\n🔐 测试登录API...")
    
    # 测试演示登录
    test_cases = [
        {
            "name": "演示管理员登录",
            "username": "admin", 
            "password": "admin123",
            "should_succeed": True
        },
        {
            "name": "演示用户登录", 
            "username": "demo",
            "password": "demo123", 
            "should_succeed": True
        },
        {
            "name": "错误密码",
            "username": "admin",
            "password": "admin123456",
            "should_succeed": False
        },
        {
            "name": "不存在用户",
            "username": "nonexistent", 
            "password": "password",
            "should_succeed": False
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  测试: {test_case['name']}")
        print(f"  账户: {test_case['username']}/{test_case['password']}")
        
        try:
            response = requests.post(
                'http://localhost:8000/api/v1/auth/login',
                json={
                    'username': test_case['username'],
                    'password': test_case['password']
                },
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            result = response.json()
            
            if test_case['should_succeed']:
                if result.get('success') and response.status_code == 200:
                    print(f"  ✅ 登录成功")
                    print(f"     用户: {result['data']['user']['username']}")
                    print(f"     邮箱: {result['data']['user']['email']}")
                    if 'token' in result['data']:
                        print(f"     Token: {result['data']['token'][:50]}...")
                else:
                    print(f"  ❌ 预期成功但失败: {result.get('message', '未知错误')}")
            else:
                if not result.get('success'):
                    print(f"  ✅ 预期失败且确实失败: {result.get('message', '未知错误')}")
                else:
                    print(f"  ❌ 预期失败但成功了")
                    
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 连接失败: {e}")
    
    print(f"\n📱 前端页面检查...")
    
    # 检查前端登录页面是否可访问
    try:
        response = requests.get('http://localhost:5173/login', timeout=5)
        if response.status_code == 200:
            print(f"✅ 登录页面可访问: http://localhost:5173/login")
            
            # 检查页面内容
            content = response.text
            if 'admin/admin123' in content:
                print(f"✅ 页面包含正确的演示账户信息")
            else:
                print(f"⚠️ 页面可能未包含演示账户信息")
                
        else:
            print(f"❌ 登录页面访问失败 (状态码: {response.status_code})")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法访问登录页面: {e}")
    
    print(f"\n💡 使用说明:")
    print(f"   1. 打开浏览器访问: http://localhost:5173/login")
    print(f"   2. 点击 '演示登录 (admin/admin123)' 绿色按钮")
    print(f"   3. 或手动输入:")
    print(f"      - 用户名: admin")
    print(f"      - 密码: admin123")
    print(f"   4. 点击登录按钮")
    
    return True

def test_api_cors():
    """测试API的CORS配置"""
    print(f"\n🌐 测试CORS配置...")
    
    try:
        # 模拟前端请求
        headers = {
            'Origin': 'http://localhost:5173',
            'Content-Type': 'application/json'
        }
        
        response = requests.options(
            'http://localhost:8000/api/v1/auth/login',
            headers=headers,
            timeout=5
        )
        
        if 'Access-Control-Allow-Origin' in response.headers:
            print(f"✅ CORS配置正常")
        else:
            print(f"⚠️ CORS可能需要配置")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ CORS测试失败: {e}")

if __name__ == "__main__":
    if test_frontend_login():
        test_api_cors()
        
        print(f"\n✅ 前端登录功能测试完成")
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"\n❌ 前端登录功能测试失败")