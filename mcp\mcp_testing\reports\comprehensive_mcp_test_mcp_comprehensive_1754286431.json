{"session_id": "mcp_comprehensive_1754286431", "start_time": "2025-08-04T13:47:11.489243", "test_scenarios": [{"name": "真实用户完整使用流程", "start_time": 1754286432.572025, "steps": ["成功导航到交易中心", "页面标题元素存在", "导航按钮存在", "成功切换到交易终端", "成功切换到账户管理", "成功切换到数据中心", "股票搜索输入成功", "买入按钮存在", "测试数据保存成功"], "issues": [], "performance": [], "end_time": 1754286446.845032, "duration": 14.273006916046143}], "discovered_issues": [], "performance_metrics": {"browser_operations": {"total": 16, "successful": 16, "avg_duration": 0.14031560719013214, "success_rate": 1.0}, "file_operations": {"total": 1, "successful": 1, "avg_duration": 0.0005352497100830078, "success_rate": 1.0}}, "mcp_operations": [{"service": "browser_tools_mcp", "operation": "navigate", "duration": 1.5934233665466309, "success": true, "timestamp": "2025-08-04T13:47:14.165888"}, {"service": "browser_tools_mcp", "operation": "screenshot", "duration": 0.13176894187927246, "success": true, "timestamp": "2025-08-04T13:47:14.297672"}, {"service": "browser_tools_mcp", "operation": "check_element", "duration": 0.009578227996826172, "success": true, "timestamp": "2025-08-04T13:47:17.310210"}, {"service": "browser_tools_mcp", "operation": "check_element", "duration": 0.008063077926635742, "success": true, "timestamp": "2025-08-04T13:47:17.318281"}, {"service": "browser_tools_mcp", "operation": "click", "duration": 0.022365331649780273, "success": true, "timestamp": "2025-08-04T13:47:17.340872"}, {"service": "browser_tools_mcp", "operation": "screenshot", "duration": 0.103485107421875, "success": true, "timestamp": "2025-08-04T13:47:19.454086"}, {"service": "browser_tools_mcp", "operation": "check_element", "duration": 0.0045909881591796875, "success": true, "timestamp": "2025-08-04T13:47:19.458686"}, {"service": "browser_tools_mcp", "operation": "click", "duration": 0.046187639236450195, "success": true, "timestamp": "2025-08-04T13:47:19.504881"}, {"service": "browser_tools_mcp", "operation": "screenshot", "duration": 0.12425637245178223, "success": true, "timestamp": "2025-08-04T13:47:21.633002"}, {"service": "browser_tools_mcp", "operation": "check_element", "duration": 0.005266904830932617, "success": true, "timestamp": "2025-08-04T13:47:21.638282"}, {"service": "browser_tools_mcp", "operation": "click", "duration": 0.03308391571044922, "success": true, "timestamp": "2025-08-04T13:47:21.671375"}, {"service": "browser_tools_mcp", "operation": "screenshot", "duration": 0.09957170486450195, "success": true, "timestamp": "2025-08-04T13:47:23.774926"}, {"service": "browser_tools_mcp", "operation": "check_element", "duration": 0.006627321243286133, "success": true, "timestamp": "2025-08-04T13:47:23.781569"}, {"service": "browser_tools_mcp", "operation": "click", "duration": 0.03936147689819336, "success": true, "timestamp": "2025-08-04T13:47:23.821315"}, {"service": "browser_tools_mcp", "operation": "type", "duration": 0.012509584426879883, "success": true, "timestamp": "2025-08-04T13:47:25.838778"}, {"service": "browser_tools_mcp", "operation": "check_element", "duration": 0.0049097537994384766, "success": true, "timestamp": "2025-08-04T13:47:26.844254"}, {"service": "filesystem_mcp", "operation": "write_file", "duration": 0.0005352497100830078, "success": true, "timestamp": "2025-08-04T13:47:26.845019"}], "end_time": "2025-08-04T13:47:26.845343"}