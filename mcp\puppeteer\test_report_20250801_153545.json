{"test_summary": {"total_tests": 12, "passed": 6, "failed": 6, "warnings": 0, "timestamp": "2025-08-01T15:35:45.889242"}, "test_results": [{"test_name": "首页访问", "status": "FAIL", "details": "首页访问失败", "error": "Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"http://localhost:5173/\", waiting until \"networkidle\"\n", "timestamp": "2025-08-01T15:33:13.358783"}, {"test_name": "导航菜单检测", "status": "PASS", "details": "发现 0 个导航项", "error": null, "timestamp": "2025-08-01T15:33:13.400953"}, {"test_name": "仪表盘页面访问", "status": "FAIL", "details": "访问失败: /dashboard", "error": "Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"http://localhost:5173/dashboard\", waiting until \"networkidle\"\n", "timestamp": "2025-08-01T15:33:43.402315"}, {"test_name": "市场数据页面访问", "status": "FAIL", "details": "访问失败: /market", "error": "Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"http://localhost:5173/market\", waiting until \"networkidle\"\n", "timestamp": "2025-08-01T15:34:13.404084"}, {"test_name": "交易终端页面访问", "status": "FAIL", "details": "访问失败: /trading", "error": "Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"http://localhost:5173/trading\", waiting until \"networkidle\"\n", "timestamp": "2025-08-01T15:34:43.407379"}, {"test_name": "策略中心页面访问", "status": "PASS", "details": "成功访问，标题: 策略中心 - 量化投资平台", "error": null, "timestamp": "2025-08-01T15:34:56.774642"}, {"test_name": "投资组合页面访问", "status": "PASS", "details": "成功访问，标题: 投资组合 - 量化投资平台", "error": null, "timestamp": "2025-08-01T15:34:59.530095"}, {"test_name": "风险管理页面访问", "status": "PASS", "details": "成功访问，标题: 风险监控 - 量化投资平台", "error": null, "timestamp": "2025-08-01T15:35:10.210390"}, {"test_name": "登录表单检测", "status": "PASS", "details": "登录表单元素完整", "error": null, "timestamp": "2025-08-01T15:35:13.553475"}, {"test_name": "演示登录", "status": "PASS", "details": "演示登录功能正常", "error": null, "timestamp": "2025-08-01T15:35:15.886638"}, {"test_name": "交互元素测试", "status": "FAIL", "details": "交互元素测试失败", "error": "Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"http://localhost:5173/\", waiting until \"networkidle\"\n", "timestamp": "2025-08-01T15:35:45.889137"}, {"test_name": "响应式设计测试", "status": "FAIL", "details": "响应式测试失败", "error": "Page.set_viewport_size() takes 2 positional arguments but 3 were given", "timestamp": "2025-08-01T15:35:45.889217"}], "screenshots": [{"name": "page_策略中心", "filename": "test_page_策略中心_153456.png", "description": "策略中心页面", "timestamp": "2025-08-01T15:34:56.774602"}, {"name": "page_投资组合", "filename": "test_page_投资组合_153459.png", "description": "投资组合页面", "timestamp": "2025-08-01T15:34:59.530053"}, {"name": "page_风险管理", "filename": "test_page_风险管理_153509.png", "description": "风险管理页面", "timestamp": "2025-08-01T15:35:10.210346"}, {"name": "login_page", "filename": "test_login_page_153513.png", "description": "登录页面", "timestamp": "2025-08-01T15:35:13.522366"}, {"name": "after_demo_login", "filename": "test_after_demo_login_153515.png", "description": "演示登录后", "timestamp": "2025-08-01T15:35:15.886599"}]}