#!/usr/bin/env python3
"""
简化的交易后端服务 - 专门用于测试模拟账户功能
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uvicorn
from datetime import datetime
import random
import hashlib

app = FastAPI(title="量化交易平台 - 简化版", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 用户认证
demo_users = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "password_hash": hashlib.sha256("admin123".encode()).hexdigest()
    },
    "demo": {
        "id": 2,
        "username": "demo",
        "email": "<EMAIL>",
        "password_hash": hashlib.sha256("demo123".encode()).hexdigest()
    }
}

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return hashlib.sha256(plain_password.encode()).hexdigest() == hashed_password

@app.post("/api/v1/auth/login")
async def login(credentials: dict):
    """用户登录"""
    username = credentials.get("username")
    password = credentials.get("password")
    
    user = demo_users.get(username)
    if not user or not verify_password(password, user["password_hash"]):
        return {
            "success": False,
            "data": None,
            "message": "用户名或密码错误"
        }
    
    # 创建简单token
    token = f"token_{username}_{datetime.now().timestamp()}"
    
    return {
        "success": True,
        "data": {
            "user": {
                "id": user["id"],
                "username": user["username"],
                "email": user["email"]
            },
            "token": token
        },
        "message": "登录成功"
    }

@app.get("/api/v1/trading/account")
async def get_trading_account():
    """获取交易账户信息 - 模拟账户"""
    account_data = {
        "accountId": "SIMULATED-001",
        "accountType": "SIMULATED",  # 明确标识为模拟账户
        "totalAssets": 1000000,      # 总资产 100万
        "availableCash": 450000,     # 可用资金 45万
        "frozenCash": 50000,         # 冻结资金 5万
        "marketValue": 500000,       # 市值 50万
        "totalProfit": 50000,        # 总盈亏 5万
        "totalProfitPercent": 5.26,  # 总盈亏率 5.26%
        "dailyProfit": 2500,         # 日盈亏 2500
        "dailyProfitPercent": 0.25,  # 日盈亏率 0.25%
        "positionRatio": 50.0,       # 仓位比例 50%
        "lastUpdateTime": datetime.now().isoformat()
    }
    return {"success": True, "data": account_data, "message": "获取模拟账户信息成功"}

@app.get("/api/v1/trading/positions")
async def get_trading_positions():
    """获取持仓信息 - 模拟持仓"""
    positions = [
        {
            "symbol": "000001",
            "name": "平安银行",
            "quantity": 10000,
            "availableQuantity": 10000,
            "avgCost": 12.50,
            "currentPrice": 13.20,
            "marketValue": 132000,
            "cost": 125000,
            "unrealizedPnl": 7000,
            "unrealizedPnlPercent": 5.6,
            "positionRatio": 13.2
        },
        {
            "symbol": "000002",
            "name": "万科A",
            "quantity": 5000,
            "availableQuantity": 5000,
            "avgCost": 25.80,
            "currentPrice": 24.60,
            "marketValue": 123000,
            "cost": 129000,
            "unrealizedPnl": -6000,
            "unrealizedPnlPercent": -4.65,
            "positionRatio": 12.3
        }
    ]
    return {"success": True, "data": positions, "message": "获取模拟持仓成功"}

@app.get("/api/v1/trading/orders")
async def get_trading_orders():
    """获取订单列表 - 模拟订单"""
    orders = [
        {
            "id": "ORDER_001",
            "symbol": "000001",
            "name": "平安银行",
            "side": "buy",
            "type": "limit",
            "price": 13.00,
            "quantity": 1000,
            "filledQuantity": 0,
            "status": "pending",
            "createTime": datetime.now().isoformat(),
            "updateTime": datetime.now().isoformat()
        }
    ]
    return {"success": True, "data": orders, "message": "获取模拟订单成功"}

@app.post("/api/v1/trading/orders")
async def create_order(order_data: dict):
    """创建模拟订单"""
    order_id = f"ORDER_{random.randint(1000, 9999)}"
    
    order = {
        "id": order_id,
        "symbol": order_data.get("symbol"),
        "name": order_data.get("symbolName", ""),
        "side": order_data.get("side"),
        "type": order_data.get("orderType", "limit"),
        "price": order_data.get("price", 0),
        "quantity": order_data.get("quantity", 0),
        "filledQuantity": 0,
        "status": "pending",
        "createTime": datetime.now().isoformat(),
        "updateTime": datetime.now().isoformat()
    }
    
    return {
        "success": True,
        "data": order,
        "message": "模拟订单创建成功"
    }

@app.get("/api/v1/market/stocks")
async def get_market_stocks():
    """获取股票列表"""
    stocks = [
        {"symbol": "000001", "name": "平安银行", "price": 13.20, "change": 0.15, "changePercent": 1.15},
        {"symbol": "000002", "name": "万科A", "price": 24.60, "change": -0.35, "changePercent": -1.40},
        {"symbol": "000858", "name": "五粮液", "price": 168.50, "change": 2.30, "changePercent": 1.38},
        {"symbol": "600036", "name": "招商银行", "price": 42.80, "change": -0.20, "changePercent": -0.47}
    ]
    return {"success": True, "data": stocks, "message": "获取股票列表成功"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "message": "服务运行正常", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    print("🚀 启动简化版量化交易后端服务...")
    print("📊 模拟账户信息:")
    print("   - 账户类型: 模拟账户")
    print("   - 总资产: ¥1,000,000")
    print("   - 可用资金: ¥450,000")
    print("   - 持仓市值: ¥500,000")
    print("   - 总盈亏: +¥50,000 (+5.26%)")
    print("   - 日盈亏: +¥2,500 (+0.25%)")
    print("🔐 测试账户: admin/admin123")
    print("🌐 API地址: http://localhost:8001")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")