#!/usr/bin/env python3
"""
最简单的WebSocket测试服务器
用于隔离WebSocket问题
"""

import asyncio
import json
import logging
from datetime import datetime

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建最简单的FastAPI应用
app = FastAPI(title="Simple WebSocket Test Server")


@app.get("/")
async def root():
    """根路径"""
    return {"message": "Simple WebSocket Test Server", "status": "running"}


@app.get("/health")
async def health():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.websocket("/api/v1/ws")
async def websocket_endpoint(websocket: WebSocket):
    """最简单的WebSocket端点"""
    logger.info("WebSocket connection attempt")
    
    try:
        await websocket.accept()
        logger.info("WebSocket connection accepted")
        
        # 发送欢迎消息
        welcome_message = {
            "type": "welcome",
            "message": "WebSocket连接成功！",
            "timestamp": datetime.now().isoformat(),
            "server": "simple-test-server"
        }
        await websocket.send_json(welcome_message)
        logger.info(f"Sent welcome message: {welcome_message}")
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                logger.info(f"Received message: {data}")
                
                # 解析消息
                try:
                    message = json.loads(data)
                except json.JSONDecodeError:
                    message = {"raw": data}
                
                # 回显消息
                echo_response = {
                    "type": "echo",
                    "original": message,
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                }
                
                await websocket.send_json(echo_response)
                logger.info(f"Sent echo response: {echo_response}")
                
            except WebSocketDisconnect:
                logger.info("WebSocket disconnected by client")
                break
            except Exception as e:
                logger.error(f"Error in message loop: {e}")
                error_response = {
                    "type": "error",
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                try:
                    await websocket.send_json(error_response)
                except:
                    pass
                break
                
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    finally:
        logger.info("WebSocket connection closed")


@app.websocket("/ws")
async def simple_websocket_endpoint(websocket: WebSocket):
    """备用WebSocket端点"""
    logger.info("Simple WebSocket connection attempt")
    
    try:
        await websocket.accept()
        logger.info("Simple WebSocket connection accepted")
        
        await websocket.send_text("Hello from simple WebSocket!")
        
        while True:
            data = await websocket.receive_text()
            await websocket.send_text(f"Echo: {data}")
            
    except WebSocketDisconnect:
        logger.info("Simple WebSocket disconnected")
    except Exception as e:
        logger.error(f"Simple WebSocket error: {e}")


if __name__ == "__main__":
    logger.info("Starting Simple WebSocket Test Server...")
    logger.info("WebSocket endpoints:")
    logger.info("  - ws://localhost:8000/api/v1/ws")
    logger.info("  - ws://localhost:8000/ws")
    logger.info("HTTP endpoints:")
    logger.info("  - http://localhost:8000/")
    logger.info("  - http://localhost:8000/health")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        log_level="info",
        access_log=True
    )
