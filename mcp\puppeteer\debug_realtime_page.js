const puppeteer = require('puppeteer-core');

class RealtimePageDebugger {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await puppeteer.launch({
      executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听所有控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ [${type}]: ${text}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.log(`🚨 页面错误: ${error.message}`);
    });

    // 监听网络请求失败
    this.page.on('requestfailed', request => {
      console.log(`🌐 请求失败: ${request.url()} - ${request.failure().errorText}`);
    });
  }

  async debugPage() {
    try {
      console.log('🚀 启动浏览器调试...');
      await this.init();

      console.log('📊 访问实时行情页面...');
      await this.page.goto('http://localhost:5173/market/realtime', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // 等待页面完全加载
      await new Promise(resolve => setTimeout(resolve, 8000));

      // 检查页面DOM结构
      const domStructure = await this.page.evaluate(() => {
        const getElementInfo = (selector) => {
          const element = document.querySelector(selector);
          if (!element) return null;
          
          const rect = element.getBoundingClientRect();
          const style = window.getComputedStyle(element);
          
          return {
            exists: true,
            visible: rect.width > 0 && rect.height > 0,
            display: style.display,
            visibility: style.visibility,
            width: rect.width,
            height: rect.height,
            textContent: element.textContent.substring(0, 100),
            innerHTML: element.innerHTML.length
          };
        };

        return {
          app: getElementInfo('#app'),
          sidebar: getElementInfo('.sidebar'),
          mainContent: getElementInfo('.main-content'),
          marketView: getElementInfo('.market-view-optimized'),
          marketHeader: getElementInfo('.market-header'),
          marketIndices: getElementInfo('.market-indices'),
          stockTable: getElementInfo('.el-table'),
          chartSection: getElementInfo('.market-chart-section'),
          chartContainer: getElementInfo('.market-chart'),
          
          // 检查路由状态
          currentPath: window.location.pathname,
          routerView: getElementInfo('.router-view'),
          
          // 检查Vue应用状态
          hasVueApp: !!window.Vue || !!document.querySelector('#app').__vue__,
          
          // 检查错误元素
          errorElements: Array.from(document.querySelectorAll('.error, .el-alert--error')).map(el => ({
            text: el.textContent,
            visible: el.offsetWidth > 0 && el.offsetHeight > 0
          })),
          
          // 检查加载状态
          loadingElements: Array.from(document.querySelectorAll('.loading, .el-loading-mask')).length,
          
          // 检查页面整体内容
          bodyContent: document.body.textContent.length,
          totalElements: document.querySelectorAll('*').length
        };
      });

      console.log('\n📋 DOM结构分析:');
      console.log('='.repeat(60));
      console.log(`🎯 当前路径: ${domStructure.currentPath}`);
      console.log(`📱 Vue应用: ${domStructure.hasVueApp ? '✅' : '❌'}`);
      console.log(`📄 页面内容长度: ${domStructure.bodyContent} 字符`);
      console.log(`🔢 总元素数: ${domStructure.totalElements}`);
      console.log(`⏳ 加载元素: ${domStructure.loadingElements} 个`);
      
      console.log('\n🏗️ 关键组件状态:');
      Object.entries(domStructure).forEach(([key, value]) => {
        if (value && typeof value === 'object' && value.exists !== undefined) {
          const status = value.exists ? (value.visible ? '✅ 可见' : '⚠️ 隐藏') : '❌ 不存在';
          console.log(`  ${key}: ${status} (${value.width}x${value.height})`);
          if (value.textContent && value.textContent.length > 0) {
            console.log(`    内容: ${value.textContent.substring(0, 50)}...`);
          }
        }
      });

      if (domStructure.errorElements.length > 0) {
        console.log('\n❌ 错误信息:');
        domStructure.errorElements.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error.text} (可见: ${error.visible})`);
        });
      }

      // 检查Vue Router状态
      const routerInfo = await this.page.evaluate(() => {
        try {
          const app = document.querySelector('#app').__vue__;
          if (app && app.$router) {
            return {
              currentRoute: app.$router.currentRoute.value || app.$router.currentRoute,
              routes: app.$router.getRoutes ? app.$router.getRoutes().length : 'unknown'
            };
          }
          return { error: 'Router not found' };
        } catch (e) {
          return { error: e.message };
        }
      });

      console.log('\n🧭 路由信息:');
      console.log(JSON.stringify(routerInfo, null, 2));

      // 检查Store状态
      const storeInfo = await this.page.evaluate(() => {
        try {
          const app = document.querySelector('#app').__vue__;
          if (app && app.$store) {
            return {
              hasMarketStore: !!app.$store.state.market,
              stockCount: app.$store.state.market?.stocks?.length || 0,
              indicesCount: Object.keys(app.$store.state.market?.indices || {}).length
            };
          }
          return { error: 'Store not found' };
        } catch (e) {
          return { error: e.message };
        }
      });

      console.log('\n📦 Store状态:');
      console.log(JSON.stringify(storeInfo, null, 2));

      // 尝试手动触发路由导航
      console.log('\n🔄 尝试手动导航到实时行情页面...');
      await this.page.evaluate(() => {
        try {
          const app = document.querySelector('#app').__vue__;
          if (app && app.$router) {
            app.$router.push('/market/realtime');
            return 'Navigation triggered';
          }
          return 'Router not available';
        } catch (e) {
          return `Error: ${e.message}`;
        }
      });

      await new Promise(resolve => setTimeout(resolve, 3000));

      // 再次检查页面状态
      const finalCheck = await this.page.evaluate(() => {
        return {
          currentPath: window.location.pathname,
          marketViewExists: !!document.querySelector('.market-view-optimized'),
          contentLength: document.body.textContent.length,
          hasMainContent: !!document.querySelector('.main-content'),
          sidebarVisible: !!document.querySelector('.sidebar') && 
                         document.querySelector('.sidebar').offsetWidth > 0
        };
      });

      console.log('\n🔍 最终检查结果:');
      console.log(JSON.stringify(finalCheck, null, 2));

      return domStructure;

    } catch (error) {
      console.error('❌ 调试过程中出现错误:', error);
      throw error;
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

async function runDebug() {
  const pageDebugger = new RealtimePageDebugger();

  try {
    const result = await pageDebugger.debugPage();
    return result;
  } catch (error) {
    console.error('💥 调试失败:', error.message);
  } finally {
    await pageDebugger.close();
  }
}

// 运行调试
runDebug();
