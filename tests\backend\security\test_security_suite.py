"""
安全测试套件
包含身份验证、授权、注入攻击、数据泄露等安全测试
"""

import pytest
import asyncio
import hashlib
import base64
import json
import time
from typing import Dict, List, Any
from httpx import AsyncClient
from unittest.mock import patch, Mock
import jwt

from app.main import app
from app.core.security import create_access_token, verify_password


@pytest.mark.security
@pytest.mark.asyncio
class TestAuthenticationSecurity:
    """身份验证安全测试"""

    async def test_login_brute_force_protection(self, client: AsyncClient):
        """测试登录暴力破解防护"""
        username = "<EMAIL>"
        
        # 尝试多次错误登录
        failed_attempts = 0
        for i in range(10):
            response = await client.post(
                "/api/v1/auth/login",
                json={"username": username, "password": f"wrong_password_{i}"}
            )
            
            if response.status_code == 401:
                failed_attempts += 1
            elif response.status_code == 429:  # Too Many Requests
                # 检测到频率限制，说明防护生效
                assert failed_attempts >= 3, "暴力破解防护触发过晚"
                break
        else:
            # 如果没有触发频率限制，至少应该记录失败尝试
            assert failed_attempts > 0, "应该有失败的登录尝试"

    async def test_password_strength_requirements(self, client: AsyncClient):
        """测试密码强度要求"""
        weak_passwords = [
            "123",           # 太短
            "password",      # 常见密码
            "12345678",      # 纯数字
            "abcdefgh",      # 纯字母
            "PASSWORD",      # 纯大写
        ]
        
        for weak_password in weak_passwords:
            response = await client.post(
                "/api/v1/auth/register",
                json={
                    "username": "testuser",
                    "email": "<EMAIL>", 
                    "password": weak_password
                }
            )
            
            # 应该拒绝弱密码
            assert response.status_code in [400, 422], f"弱密码 '{weak_password}' 被接受"

    async def test_jwt_token_security(self, client: AsyncClient):
        """测试JWT令牌安全性"""
        # 测试无效令牌
        invalid_tokens = [
            "invalid.token.here",
            "Bearer invalid_token",
            "",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature",  # 无效签名
        ]
        
        for token in invalid_tokens:
            headers = {"Authorization": f"Bearer {token}"}
            response = await client.get("/api/v1/trading/account", headers=headers)
            assert response.status_code == 401, f"无效令牌 '{token}' 被接受"

    async def test_token_expiration(self, client: AsyncClient):
        """测试令牌过期"""
        # 创建一个已过期的令牌
        expired_payload = {
            "sub": "testuser",
            "exp": int(time.time()) - 3600,  # 1小时前过期
            "iat": int(time.time()) - 7200   # 2小时前签发
        }
        
        # 这里需要使用与应用相同的密钥，在实际测试中需要配置
        try:
            expired_token = jwt.encode(expired_payload, "test-secret-key", algorithm="HS256")
            headers = {"Authorization": f"Bearer {expired_token}"}
            
            response = await client.get("/api/v1/trading/account", headers=headers)
            assert response.status_code == 401, "过期令牌被接受"
        except Exception:
            # 如果无法创建令牌，跳过此测试
            pytest.skip("无法创建测试令牌")

    async def test_session_hijacking_protection(self, client: AsyncClient):
        """测试会话劫持防护"""
        # 模拟不同IP地址的请求
        headers_ip1 = {
            "Authorization": "Bearer valid-token",
            "X-Forwarded-For": "*************"
        }
        
        headers_ip2 = {
            "Authorization": "Bearer valid-token", 
            "X-Forwarded-For": "*********"
        }
        
        # 如果实现了IP绑定，应该检测到异常
        response1 = await client.get("/api/v1/trading/account", headers=headers_ip1)
        response2 = await client.get("/api/v1/trading/account", headers=headers_ip2)
        
        # 根据实际实现调整断言
        # 这里主要测试系统是否有相关的安全机制


@pytest.mark.security
@pytest.mark.asyncio
class TestAuthorizationSecurity:
    """授权安全测试"""

    @pytest.fixture
    def auth_headers(self):
        return {"Authorization": "Bearer test-token"}

    async def test_vertical_privilege_escalation(self, client: AsyncClient, auth_headers):
        """测试垂直权限提升"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            # 模拟普通用户
            mock_user = Mock()
            mock_user.id = "normal-user"
            mock_user.role = "user"
            mock_user.permissions = ["trading:read"]
            mock_auth.return_value = mock_user
            
            # 尝试访问管理员功能
            admin_endpoints = [
                "/api/v1/admin/users",
                "/api/v1/admin/system/status",
                "/api/v1/admin/audit/logs",
            ]
            
            for endpoint in admin_endpoints:
                response = await client.get(endpoint, headers=auth_headers)
                assert response.status_code in [403, 404], f"普通用户可以访问管理员端点: {endpoint}"

    async def test_horizontal_privilege_escalation(self, client: AsyncClient, auth_headers):
        """测试水平权限提升"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "user-001"
            mock_auth.return_value = mock_user
            
            # 尝试访问其他用户的资源
            other_user_resources = [
                "/api/v1/trading/users/user-002/orders",
                "/api/v1/trading/users/user-003/positions",
            ]
            
            for endpoint in other_user_resources:
                response = await client.get(endpoint, headers=auth_headers)
                assert response.status_code in [403, 404], f"用户可以访问其他用户资源: {endpoint}"

    async def test_resource_access_control(self, client: AsyncClient, auth_headers):
        """测试资源访问控制"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_order_by_id") as mock_get:
                mock_user = Mock()
                mock_user.id = "user-001"
                mock_auth.return_value = mock_user
                
                # 模拟其他用户的订单
                mock_order = Mock()
                mock_order.id = "order-123"
                mock_order.user_id = "user-002"  # 不同用户
                mock_get.return_value = mock_order
                
                response = await client.get("/api/v1/trading/orders/order-123", headers=auth_headers)
                assert response.status_code == 403, "用户可以访问其他用户的订单"


@pytest.mark.security
@pytest.mark.asyncio  
class TestInjectionAttacks:
    """注入攻击测试"""

    @pytest.fixture
    def auth_headers(self):
        return {"Authorization": "Bearer test-token"}

    async def test_sql_injection_attempts(self, client: AsyncClient, auth_headers):
        """测试SQL注入攻击"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            sql_injection_payloads = [
                "'; DROP TABLE orders; --",
                "' OR '1'='1",
                "1'; UPDATE users SET password='hacked' WHERE '1'='1'; --",
                "' UNION SELECT * FROM users --",
                "admin'--",
                "' OR 1=1 #",
            ]
            
            for payload in sql_injection_payloads:
                # 尝试在不同参数中注入
                test_cases = [
                    # 查询参数
                    {"params": {"symbol": payload}},
                    {"params": {"status": payload}},
                    # JSON载荷
                    {"json": {"symbol": payload, "side": "buy", "order_type": "limit", "quantity": 100, "price": "10.0"}},
                ]
                
                for case in test_cases:
                    try:
                        response = await client.get("/api/v1/trading/orders", headers=auth_headers, **case)
                        # 不应该返回SQL错误信息
                        if response.status_code == 500:
                            error_text = response.text.lower()
                            sql_error_indicators = ["sql", "syntax", "database", "table", "column"]
                            for indicator in sql_error_indicators:
                                assert indicator not in error_text, f"SQL注入可能成功，返回了数据库错误信息: {payload}"
                    except Exception:
                        # 请求异常是可以接受的，说明被正确拒绝
                        pass

    async def test_xss_attempts(self, client: AsyncClient, auth_headers):
        """测试XSS攻击"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            xss_payloads = [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src='x' onerror='alert(1)'>",
                "<svg onload='alert(1)'>",
                "';alert(String.fromCharCode(88,83,83))//",
            ]
            
            for payload in xss_payloads:
                order_data = {
                    "symbol": payload,
                    "side": "buy",
                    "order_type": "limit", 
                    "quantity": 100,
                    "price": "10.0",
                    "notes": payload  # 假设有备注字段
                }
                
                response = await client.post("/api/v1/trading/orders", json=order_data, headers=auth_headers)
                
                # 检查响应是否包含未转义的脚本
                if response.status_code == 200:
                    response_text = response.text
                    dangerous_patterns = ["<script", "javascript:", "onerror=", "onload="]
                    for pattern in dangerous_patterns:
                        assert pattern not in response_text, f"可能存在XSS漏洞: {payload}"

    async def test_command_injection_attempts(self, client: AsyncClient, auth_headers):
        """测试命令注入攻击"""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& whoami",
            "`id`",
            "$(uname -a)",
        ]
        
        for payload in command_injection_payloads:
            # 尝试在文件上传或其他可能执行命令的地方注入
            test_data = {
                "filename": f"test{payload}.txt",
                "content": "test content"
            }
            
            response = await client.post("/api/v1/files/upload", json=test_data, headers=auth_headers)
            
            # 不应该执行系统命令
            if response.status_code == 500:
                error_text = response.text.lower()
                system_info = ["root", "uid=", "gid=", "linux", "windows"]
                for info in system_info:
                    assert info not in error_text, f"可能存在命令注入: {payload}"


@pytest.mark.security
@pytest.mark.asyncio
class TestDataSecurity:
    """数据安全测试"""

    @pytest.fixture
    def auth_headers(self):
        return {"Authorization": "Bearer test-token"}

    async def test_sensitive_data_exposure(self, client: AsyncClient, auth_headers):
        """测试敏感数据暴露"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            # 测试各种端点是否泄露敏感信息
            endpoints = [
                "/api/v1/trading/account",
                "/api/v1/trading/positions", 
                "/api/v1/users/profile",
            ]
            
            for endpoint in endpoints:
                response = await client.get(endpoint, headers=auth_headers)
                
                if response.status_code == 200:
                    response_text = response.text.lower()
                    
                    # 检查是否泄露敏感信息
                    sensitive_patterns = [
                        "password",
                        "secret",
                        "private_key",
                        "api_key",
                        "access_token",
                        "database",
                        "connection_string"
                    ]
                    
                    for pattern in sensitive_patterns:
                        assert pattern not in response_text, f"端点 {endpoint} 可能泄露敏感信息: {pattern}"

    async def test_data_validation_bypass(self, client: AsyncClient, auth_headers):
        """测试数据验证绕过"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            # 尝试绕过数据验证的各种payload
            bypass_attempts = [
                # 超长字符串
                {"symbol": "A" * 10000},
                # 特殊字符
                {"symbol": "\x00\x01\x02"},
                # Unicode绕过
                {"symbol": "ᴀᴅᴍɪɴ"},
                # 负数
                {"quantity": -999999},
                # 极大数
                {"price": "999999999999999999999"},
                # Null字节
                {"symbol": "test\x00admin"},
            ]
            
            for bypass_data in bypass_attempts:
                order_data = {
                    "symbol": "000001",
                    "side": "buy",
                    "order_type": "limit",
                    "quantity": 100,
                    "price": "10.0",
                    **bypass_data
                }
                
                response = await client.post("/api/v1/trading/orders", json=order_data, headers=auth_headers)
                
                # 应该被验证拒绝，而不是引起服务器错误
                if response.status_code == 500:
                    # 不应该返回详细的服务器错误信息
                    error_text = response.text.lower()
                    internal_error_patterns = ["traceback", "exception", "stack trace", "internal server error"]
                    for pattern in internal_error_patterns:
                        assert pattern not in error_text, f"数据验证绕过可能导致内部错误暴露: {bypass_data}"

    async def test_mass_assignment_vulnerability(self, client: AsyncClient, auth_headers):
        """测试批量赋值漏洞"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            # 尝试设置不应该被用户修改的字段
            malicious_data = {
                "symbol": "000001",
                "side": "buy",
                "order_type": "limit",
                "quantity": 100,
                "price": "10.0",
                # 恶意字段
                "user_id": "admin",
                "is_admin": True,
                "role": "admin",
                "permissions": ["admin:all"],
                "status": "filled",  # 用户不应该能直接设置订单状态
                "created_by": "system",
                "id": "custom-id"
            }
            
            response = await client.post("/api/v1/trading/orders", json=malicious_data, headers=auth_headers)
            
            if response.status_code in [200, 201]:
                response_data = response.json()
                
                # 检查恶意字段是否被设置
                dangerous_fields = ["user_id", "is_admin", "role", "permissions"]
                for field in dangerous_fields:
                    if field in response_data:
                        assert response_data[field] != malicious_data[field], f"批量赋值漏洞: {field} 字段被非法修改"


@pytest.mark.security
@pytest.mark.asyncio
class TestBusinessLogicSecurity:
    """业务逻辑安全测试"""

    @pytest.fixture
    def auth_headers(self):
        return {"Authorization": "Bearer test-token"}

    async def test_race_condition_vulnerabilities(self, client: AsyncClient, auth_headers):
        """测试竞态条件漏洞"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            # 模拟并发修改同一资源
            order_data = {
                "symbol": "000001",
                "side": "buy", 
                "order_type": "limit",
                "quantity": 100,
                "price": "10.0"
            }
            
            # 并发创建多个订单，可能导致资金重复使用
            tasks = []
            for _ in range(10):
                task = client.post("/api/v1/trading/orders", json=order_data, headers=auth_headers)
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = 0
            for response in responses:
                if hasattr(response, 'status_code') and response.status_code in [200, 201]:
                    success_count += 1
            
            # 如果有竞态条件漏洞，可能会创建过多订单
            # 这个测试需要根据具体业务逻辑调整
            assert success_count <= 5, f"可能存在竞态条件漏洞，创建了 {success_count} 个订单"

    async def test_business_flow_bypass(self, client: AsyncClient, auth_headers):
        """测试业务流程绕过"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            # 尝试绕过正常的业务流程
            # 例如：直接取消一个不存在的订单
            fake_order_id = "non-existent-order"
            response = await client.post(f"/api/v1/trading/orders/{fake_order_id}/cancel", headers=auth_headers)
            
            # 应该返回404而不是500
            assert response.status_code in [404, 400], "业务流程验证不足"
            
            # 尝试修改已成交的订单
            filled_order_id = "filled-order-123"
            update_data = {"price": "999.99"}
            response = await client.patch(f"/api/v1/trading/orders/{filled_order_id}", json=update_data, headers=auth_headers)
            
            # 应该被业务逻辑拒绝
            assert response.status_code in [400, 409], "可以修改已成交的订单"

    async def test_parameter_pollution(self, client: AsyncClient, auth_headers):
        """测试参数污染攻击"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user"
            mock_auth.return_value = mock_user
            
            # HTTP参数污染
            polluted_params = "symbol=000001&symbol='; DROP TABLE orders; --&limit=10&limit=999999"
            
            # 手动构造请求以测试参数污染
            response = await client.get(f"/api/v1/trading/orders?{polluted_params}", headers=auth_headers)
            
            # 系统应该正确处理重复参数
            assert response.status_code in [200, 400, 422], "参数污染可能导致异常"


@pytest.mark.security
@pytest.mark.asyncio
class TestCryptographicSecurity:
    """加密安全测试"""

    async def test_password_hashing_security(self):
        """测试密码哈希安全性"""
        test_password = "TestPassword123!"
        
        # 测试密码哈希
        hashed = hashlib.pbkdf2_hmac('sha256', test_password.encode(), b'salt', 100000)
        
        # 哈希应该是不可逆的
        assert hashed != test_password.encode(), "密码未被正确哈希"
        assert len(hashed) >= 32, "哈希长度不足"
        
        # 相同密码应该产生相同哈希（使用相同盐值）
        hashed2 = hashlib.pbkdf2_hmac('sha256', test_password.encode(), b'salt', 100000)
        assert hashed == hashed2, "相同密码的哈希不一致"
        
        # 不同盐值应该产生不同哈希
        hashed3 = hashlib.pbkdf2_hmac('sha256', test_password.encode(), b'different_salt', 100000)
        assert hashed != hashed3, "不同盐值产生了相同哈希"

    async def test_token_randomness(self):
        """测试令牌随机性"""
        tokens = set()
        
        # 生成多个令牌
        for _ in range(100):
            # 模拟令牌生成
            token = base64.b64encode(f"user_123_{time.time()}_{hash(time.time())}".encode()).decode()
            tokens.add(token)
        
        # 所有令牌应该都不相同
        assert len(tokens) == 100, "令牌生成缺乏随机性"

    async def test_cryptographic_timing_attacks(self, client: AsyncClient):
        """测试加密计时攻击"""
        # 测试登录时间是否一致（防止用户名枚举）
        valid_username = "<EMAIL>"
        invalid_username = "<EMAIL>"
        wrong_password = "wrong_password"
        
        # 测量有效用户名的响应时间
        start_time = time.time()
        response1 = await client.post("/api/v1/auth/login", json={"username": valid_username, "password": wrong_password})
        valid_user_time = time.time() - start_time
        
        # 测量无效用户名的响应时间
        start_time = time.time()
        response2 = await client.post("/api/v1/auth/login", json={"username": invalid_username, "password": wrong_password})
        invalid_user_time = time.time() - start_time
        
        # 响应时间差异不应该太大（防止用户名枚举）
        time_difference = abs(valid_user_time - invalid_user_time)
        assert time_difference < 0.5, f"登录响应时间差异过大: {time_difference}s，可能存在用户名枚举漏洞"


if __name__ == "__main__":
    async def run_security_tests():
        """运行安全测试套件"""
        print("=" * 60)
        print("量化交易系统安全测试套件")
        print("=" * 60)
        
        test_classes = [
            TestAuthenticationSecurity,
            TestAuthorizationSecurity, 
            TestInjectionAttacks,
            TestDataSecurity,
            TestBusinessLogicSecurity,
            TestCryptographicSecurity
        ]
        
        for test_class in test_classes:
            print(f"\n执行 {test_class.__name__}")
            print("-" * 40)
            
            # 这里应该集成实际的测试执行逻辑
            # 由于是示例，我们只打印测试类名
            methods = [method for method in dir(test_class) if method.startswith('test_')]
            for method in methods:
                print(f"  ✓ {method}")
        
        print("\n" + "=" * 60)
        print("安全测试套件执行完成")
        print("=" * 60)
    
    # 运行安全测试
    asyncio.run(run_security_tests())