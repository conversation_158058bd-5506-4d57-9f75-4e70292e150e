"""
WebSocket路由模块
"""

from fastapi import APIRouter, WebSocket, Query
from typing import Optional
from app.api.websocket.handlers import (
    handle_general_websocket,
    handle_trading_websocket,
    handle_market_data_websocket,
    handle_strategy_websocket,
)

router = APIRouter()


@router.websocket("")
async def general_websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None)
):
    """通用WebSocket端点 - 支持多种消息类型"""
    await handle_general_websocket(websocket, token)


@router.websocket("/trading")
async def trading_websocket_endpoint(websocket: WebSocket):
    """交易WebSocket端点"""
    await handle_trading_websocket(websocket)


@router.websocket("/market")
async def market_websocket_endpoint(websocket: WebSocket):
    """行情WebSocket端点"""
    await handle_market_data_websocket(websocket)


@router.websocket("/strategy")
async def strategy_websocket_endpoint(websocket: WebSocket):
    """策略WebSocket端点"""
    await handle_strategy_websocket(websocket)
