#!/usr/bin/env python3
"""
调试响应数据
"""

import asyncio
from puppeteer import BrowserManager

async def debug_response_data():
    manager = BrowserManager()
    try:
        page = await manager.ensure_browser()
        print('🔧 调试响应数据...')
        
        # 拦截响应并获取数据
        response_data = None
        
        def handle_response(response):
            if 'auth/login' in response.url and response.status == 200:
                print(f'[RESPONSE] 拦截到登录响应: {response.url}')
                # 这里我们需要在页面中获取响应数据
        
        page.on('response', handle_response)
        
        await page.goto('http://localhost:5173/login')
        await page.wait_for_timeout(2000)
        
        # 注入代码来拦截fetch响应
        await page.evaluate('''
            () => {
                const originalFetch = window.fetch;
                window.fetch = async (...args) => {
                    const response = await originalFetch(...args);
                    
                    if (args[0].includes('auth/login')) {
                        const clonedResponse = response.clone();
                        const data = await clonedResponse.json();
                        window.__LOGIN_RESPONSE__ = data;
                        console.log('[INTERCEPTED] Login response:', data);
                    }
                    
                    return response;
                };
            }
        ''')
        
        # 填写登录表单并提交
        await page.fill('input[type="text"]', 'admin')
        await page.fill('input[type="password"]', 'admin123')
        await page.click('button:has-text("登录")')
        
        # 等待响应
        await page.wait_for_timeout(3000)
        
        # 获取拦截到的响应数据
        intercepted_data = await page.evaluate('() => window.__LOGIN_RESPONSE__')
        print(f'📊 拦截到的登录响应: {intercepted_data}')
        
        # 检查authService处理后的数据
        auth_result = await page.evaluate('''
            async () => {
                try {
                    // 模拟authService.login的处理逻辑
                    const response = window.__LOGIN_RESPONSE__;
                    if (!response) return { error: 'No response data' };
                    
                    const tokenResponse = response.data || response;
                    
                    return {
                        original: response,
                        processed: {
                            user: tokenResponse.user,
                            token: tokenResponse.access_token || tokenResponse.token,
                            refreshToken: tokenResponse.refresh_token
                        }
                    };
                } catch (error) {
                    return { error: error.message };
                }
            }
        ''')
        
        print(f'📊 AuthService处理结果: {auth_result}')
        
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(debug_response_data())
