const puppeteer = require('puppeteer-core');

class KLineChartDebugger {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await puppeteer.launch({
      executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (text.includes('chart') || text.includes('Chart') || text.includes('echarts') || text.includes('ECharts')) {
        console.log(`🖥️ 图表相关 [${type}]: ${text}`);
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      if (error.message.includes('chart') || error.message.includes('Chart') || error.message.includes('echarts')) {
        console.log(`🚨 图表错误: ${error.message}`);
      }
    });
  }

  async debugKLineChart() {
    try {
      console.log('🚀 启动浏览器...');
      await this.init();

      console.log('📊 访问交易页面...');
      await this.page.goto('http://localhost:5173/trading', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 检查K线图组件的详细状态
      const chartDebugInfo = await this.page.evaluate(() => {
        return {
          // 检查K线图容器
          hasKLineContainer: !!document.querySelector('.kline-chart'),
          hasChartContainer: !!document.querySelector('.chart-container'),
          hasEChartsContainer: !!document.querySelector('[_echarts_instance_]'),
          
          // 检查周期选择器
          periodButtons: document.querySelectorAll('.period-selector button').length,
          periodSelector: !!document.querySelector('.period-selector'),
          
          // 检查图表工具栏
          hasToolbar: !!document.querySelector('.chart-toolbar'),
          hasIndicatorDropdown: !!document.querySelector('.indicator-dropdown'),
          
          // 检查图表相关的DOM元素
          chartElements: Array.from(document.querySelectorAll('[class*="chart"]')).map(el => ({
            className: el.className,
            tagName: el.tagName,
            hasContent: el.textContent.length > 0
          })),
          
          // 检查ECharts实例
          echartsInstances: Array.from(document.querySelectorAll('[_echarts_instance_]')).length,
          
          // 检查Vue组件状态
          hasVueApp: !!document.querySelector('#app').__vue__,
          
          // 检查错误信息
          errorElements: Array.from(document.querySelectorAll('.el-empty, .error-message')).map(el => el.textContent),
          
          // 检查当前股票信息
          currentStock: document.querySelector('.stock-info .stock-name')?.textContent || 'None',
          
          // 检查图表数据相关
          hasChartData: !!document.querySelector('[data-chart-data]'),
          
          // 检查加载状态
          isLoading: !!document.querySelector('.el-loading-mask'),
          
          // 获取所有可能的错误信息
          allErrors: Array.from(document.querySelectorAll('.error, .el-alert--error')).map(el => el.textContent)
        };
      });

      console.log('📋 K线图调试信息:');
      console.log('='.repeat(50));
      console.log(`📊 K线图容器: ${chartDebugInfo.hasKLineContainer ? '✅' : '❌'}`);
      console.log(`📈 图表容器: ${chartDebugInfo.hasChartContainer ? '✅' : '❌'}`);
      console.log(`🎯 ECharts容器: ${chartDebugInfo.hasEChartsContainer ? '✅' : '❌'}`);
      console.log(`⏰ 周期选择器: ${chartDebugInfo.periodButtons} 个按钮 ${chartDebugInfo.periodSelector ? '✅' : '❌'}`);
      console.log(`🔧 图表工具栏: ${chartDebugInfo.hasToolbar ? '✅' : '❌'}`);
      console.log(`📊 ECharts实例: ${chartDebugInfo.echartsInstances} 个`);
      console.log(`📈 当前股票: ${chartDebugInfo.currentStock}`);
      console.log(`⏳ 加载状态: ${chartDebugInfo.isLoading ? '加载中' : '已完成'}`);
      
      if (chartDebugInfo.chartElements.length > 0) {
        console.log('\n📊 图表相关元素:');
        chartDebugInfo.chartElements.forEach((el, index) => {
          console.log(`  ${index + 1}. ${el.tagName}.${el.className} ${el.hasContent ? '(有内容)' : '(空)'}`);
        });
      }
      
      if (chartDebugInfo.errorElements.length > 0) {
        console.log('\n❌ 错误信息:');
        chartDebugInfo.errorElements.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }
      
      if (chartDebugInfo.allErrors.length > 0) {
        console.log('\n🚨 所有错误:');
        chartDebugInfo.allErrors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }

      console.log('='.repeat(50));

      // 尝试手动触发股票选择
      console.log('\n🔍 尝试选择股票...');
      const stockSelected = await this.page.evaluate(() => {
        const searchInput = document.querySelector('.el-autocomplete input');
        if (searchInput) {
          // 模拟输入
          searchInput.value = '000001';
          searchInput.dispatchEvent(new Event('input', { bubbles: true }));
          return true;
        }
        return false;
      });

      if (stockSelected) {
        console.log('✅ 股票搜索输入成功');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 再次检查图表状态
        const afterSelectInfo = await this.page.evaluate(() => {
          return {
            hasKLineContainer: !!document.querySelector('.kline-chart'),
            periodButtons: document.querySelectorAll('.period-selector button').length,
            echartsInstances: Array.from(document.querySelectorAll('[_echarts_instance_]')).length,
            currentStock: document.querySelector('.stock-info .stock-name')?.textContent || 'None'
          };
        });
        
        console.log('\n📊 选择股票后的状态:');
        console.log(`📊 K线图容器: ${afterSelectInfo.hasKLineContainer ? '✅' : '❌'}`);
        console.log(`⏰ 周期选择器: ${afterSelectInfo.periodButtons} 个按钮`);
        console.log(`📊 ECharts实例: ${afterSelectInfo.echartsInstances} 个`);
        console.log(`📈 当前股票: ${afterSelectInfo.currentStock}`);
      } else {
        console.log('❌ 股票搜索输入失败');
      }

      return chartDebugInfo;

    } catch (error) {
      console.error('❌ 调试过程中出现错误:', error);
      throw error;
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

async function runDebug() {
  const chartDebugger = new KLineChartDebugger();

  try {
    const result = await chartDebugger.debugKLineChart();
    return result;
  } catch (error) {
    console.error('💥 调试失败:', error.message);
  } finally {
    await chartDebugger.close();
  }
}

// 运行调试
runDebug();
