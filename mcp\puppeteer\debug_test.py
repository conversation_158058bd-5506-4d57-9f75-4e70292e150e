#!/usr/bin/env python3
"""
调试测试 - 详细检查页面问题
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_test():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 监听控制台消息
        def handle_console(msg):
            logger.info(f"Console {msg.type}: {msg.text}")
        
        def handle_page_error(error):
            logger.error(f"Page error: {error}")
        
        page.on('console', handle_console)
        page.on('pageerror', handle_page_error)
        
        try:
            logger.info("访问交易中心页面...")
            await page.goto('http://localhost:5173/trading/center', wait_until='networkidle', timeout=60000)
            
            # 等待更长时间
            await page.wait_for_timeout(5000)
            
            # 检查页面HTML结构
            html = await page.content()
            logger.info(f"页面HTML长度: {len(html)}")
            
            # 检查是否有Vue应用
            vue_app = await page.evaluate("""
                () => {
                    const app = document.querySelector('#app');
                    return app ? app.innerHTML.length : 0;
                }
            """)
            logger.info(f"Vue应用内容长度: {vue_app}")
            
            # 检查是否有错误信息
            error_info = await page.evaluate("""
                () => {
                    const errors = [];
                    
                    // 检查控制台错误
                    if (window.console && window.console.error) {
                        errors.push('Console available');
                    }
                    
                    // 检查Vue错误
                    if (window.Vue) {
                        errors.push('Vue available');
                    }
                    
                    // 检查Element Plus
                    if (window.ElementPlus) {
                        errors.push('ElementPlus available');
                    }
                    
                    // 检查页面内容
                    const tradingCenter = document.querySelector('.trading-center');
                    if (tradingCenter) {
                        errors.push('trading-center found');
                    } else {
                        errors.push('trading-center NOT found');
                    }
                    
                    const buttons = document.querySelectorAll('button');
                    errors.push(`${buttons.length} buttons found`);
                    
                    return errors;
                }
            """)
            
            for info in error_info:
                logger.info(f"检查结果: {info}")
            
            # 检查网络请求
            network_errors = await page.evaluate("""
                () => {
                    const errors = [];
                    const scripts = document.querySelectorAll('script[src]');
                    scripts.forEach(script => {
                        if (script.src) {
                            errors.push(`Script: ${script.src}`);
                        }
                    });
                    return errors;
                }
            """)
            
            logger.info("加载的脚本:")
            for script in network_errors[:5]:  # 只显示前5个
                logger.info(f"  {script}")
            
            # 尝试手动触发Vue渲染
            await page.evaluate("""
                () => {
                    if (window.Vue && window.Vue.nextTick) {
                        window.Vue.nextTick(() => {
                            console.log('Vue nextTick triggered');
                        });
                    }
                }
            """)
            
            await page.wait_for_timeout(2000)
            
            # 最终检查
            final_buttons = await page.query_selector_all('button')
            logger.info(f"最终发现 {len(final_buttons)} 个按钮")
            
            # 截图
            await page.screenshot(path='screenshots/debug_test.png', full_page=True)
            logger.info("📸 完整页面截图已保存")
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            await page.screenshot(path='screenshots/debug_error.png')
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_test())
