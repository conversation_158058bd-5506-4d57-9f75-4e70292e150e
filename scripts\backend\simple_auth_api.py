#!/usr/bin/env python3
"""
简化的认证API服务
绕过ORM问题，直接使用SQLite进行认证
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import sqlite3
import bcrypt
import secrets
import uvicorn
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# 创建FastAPI应用
app = FastAPI(title="简化认证API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全认证
security = HTTPBearer(auto_error=False)

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: Dict[str, Any]

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool
    is_superuser: bool

# 数据库操作
def get_db_connection():
    """获取数据库连接"""
    return sqlite3.connect('test.db')

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def get_user_by_username(username: str) -> Optional[Dict[str, Any]]:
    """根据用户名获取用户"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, hashed_password, full_name, is_active, is_superuser
            FROM users WHERE username = ? OR email = ?
        ''', (username, username))
        
        user_data = cursor.fetchone()
        conn.close()
        
        if user_data:
            return {
                "id": user_data[0],
                "username": user_data[1],
                "email": user_data[2],
                "hashed_password": user_data[3],
                "full_name": user_data[4],
                "is_active": bool(user_data[5]),
                "is_superuser": bool(user_data[6])
            }
        
        return None
        
    except Exception as e:
        print(f"数据库查询失败: {e}")
        return None

def get_user_by_id(user_id: int) -> Optional[Dict[str, Any]]:
    """根据ID获取用户"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, full_name, is_active, is_superuser
            FROM users WHERE id = ?
        ''', (user_id,))
        
        user_data = cursor.fetchone()
        conn.close()
        
        if user_data:
            return {
                "id": user_data[0],
                "username": user_data[1],
                "email": user_data[2],
                "full_name": user_data[3],
                "is_active": bool(user_data[4]),
                "is_superuser": bool(user_data[5])
            }
        
        return None
        
    except Exception as e:
        print(f"获取用户失败: {e}")
        return None

def create_access_token(user_data: Dict[str, Any]) -> str:
    """创建访问令牌"""
    return f"token_{secrets.token_hex(16)}_{user_data['id']}"

def parse_token(token: str) -> Optional[int]:
    """解析token获取用户ID"""
    try:
        if token.startswith("token_"):
            parts = token.split("_")
            if len(parts) >= 3:
                return int(parts[-1])
    except:
        pass
    return None

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/v1/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    print(f"🔐 登录请求: {request.username}")
    
    # 获取用户
    user = get_user_by_username(request.username)
    if not user:
        print(f"❌ 用户不存在: {request.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 检查用户状态
    if not user["is_active"]:
        print(f"❌ 用户未激活: {request.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用"
        )
    
    # 验证密码
    if not verify_password(request.password, user["hashed_password"]):
        print(f"❌ 密码错误: {request.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 创建令牌
    access_token = create_access_token(user)
    
    print(f"✅ 登录成功: {request.username}")
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user={
            "id": user["id"],
            "username": user["username"],
            "email": user["email"],
            "full_name": user["full_name"],
            "is_active": user["is_active"],
            "is_superuser": user["is_superuser"]
        }
    )

@app.get("/api/v1/auth/me", response_model=UserResponse)
async def get_current_user_info(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """获取当前用户信息"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    user_id = parse_token(credentials.credentials)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    user = get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    return UserResponse(**user)

# 添加一些基本的API端点来测试认证
@app.get("/api/v1/market/overview")
async def get_market_overview(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """获取市场概览（需要认证）"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    user_id = parse_token(credentials.credentials)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    # 返回模拟数据
    return {
        "success": True,
        "data": {
            "market_status": "open",
            "total_stocks": 4500,
            "rising_stocks": 2100,
            "falling_stocks": 1800,
            "unchanged_stocks": 600,
            "total_volume": ************,
            "total_turnover": ************,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/v1/trading/account")
async def get_trading_account(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """获取交易账户信息（需要认证）"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    user_id = parse_token(credentials.credentials)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    # 返回模拟账户数据
    return {
        "success": True,
        "data": {
            "account_id": f"ACC_{user_id}",
            "balance": {
                "total_equity": 1500000.0,
                "cash": 300000.0,
                "market_value": 1200000.0,
                "available_cash": 250000.0
            },
            "updated_at": datetime.now().isoformat()
        }
    }

if __name__ == "__main__":
    print("🚀 启动简化认证API服务...")
    print("📋 可用端点:")
    print("   - POST /api/v1/auth/login")
    print("   - GET /api/v1/auth/me")
    print("   - GET /api/v1/market/overview")
    print("   - GET /api/v1/trading/account")
    print("📋 测试账户:")
    print("   - admin / admin123")
    print("   - demo / demo123")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
