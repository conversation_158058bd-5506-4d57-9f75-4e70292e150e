#!/usr/bin/env python3
"""
简化的Tushare连接测试
直接测试Tushare API连接，不依赖复杂的服务层
"""

import os
import sys
import tushare as ts
import requests

# 你的Tushare token
TUSHARE_TOKEN = "f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400"

def test_direct_connection():
    """直接测试Tushare连接"""
    print("🔧 直接测试Tushare API连接")
    print("=" * 50)
    
    try:
        # 1. 设置Token
        print(f"📋 Token长度: {len(TUSHARE_TOKEN)}")
        ts.set_token(TUSHARE_TOKEN)
        
        # 2. 创建pro_api实例
        print("🔄 创建Pro API实例...")
        pro = ts.pro_api()
        
        # 3. 测试简单的API调用
        print("🔄 测试交易日历API...")
        df = pro.trade_cal(exchange='SSE', start_date='20241201', end_date='20241210')
        
        if not df.empty:
            print(f"✅ 成功获取交易日历数据: {len(df)} 条记录")
            print("📋 前3条记录:")
            for _, row in df.head(3).iterrows():
                print(f"   {row['cal_date']} - {'交易日' if row['is_open'] == 1 else '非交易日'}")
        else:
            print("❌ 获取交易日历数据为空")
            
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_stock_basic():
    """测试股票基本信息"""
    print("\n📊 测试股票基本信息")
    print("=" * 50)
    
    try:
        pro = ts.pro_api()
        
        print("🔄 获取股票基本信息...")
        df = pro.stock_basic(exchange='', list_status='L', 
                           fields='ts_code,symbol,name,area,industry,market')
        
        if not df.empty:
            print(f"✅ 成功获取 {len(df)} 只股票信息")
            
            # 显示市场分布
            market_counts = df['market'].value_counts()
            print("📊 市场分布:")
            for market, count in market_counts.items():
                print(f"   {market}: {count} 只")
                
            # 显示前5只股票
            print("\n📋 前5只股票:")
            for _, row in df.head().iterrows():
                print(f"   {row['ts_code']} - {row['name']} ({row['market']})")
                
        return True
        
    except Exception as e:
        print(f"❌ 获取股票基本信息失败: {e}")
        return False

def test_daily_data():
    """测试日线数据"""
    print("\n📈 测试日线数据")
    print("=" * 50)
    
    try:
        pro = ts.pro_api()
        
        # 测试几只知名股票
        test_stocks = ["000001.SZ", "600000.SH"]
        
        for stock in test_stocks:
            print(f"🔄 获取 {stock} 最新日线数据...")
            df = pro.daily(ts_code=stock, limit=1)
            
            if not df.empty:
                row = df.iloc[0]
                print(f"✅ {stock} 最新数据:")
                print(f"   日期: {row['trade_date']}")
                print(f"   收盘: {row['close']:.2f}")
                print(f"   涨跌: {row['change']:.2f} ({row['pct_chg']:.2f}%)")
                print(f"   成交量: {row['vol']:.0f}万手")
            else:
                print(f"❌ {stock} 无数据")
            print()
            
        return True
        
    except Exception as e:
        print(f"❌ 获取日线数据失败: {e}")
        return False

def check_network():
    """检查网络连接"""
    print("🌐 检查网络连接")
    print("=" * 50)
    
    try:
        # 检查是否能访问Tushare服务器
        response = requests.get("https://tushare.pro", timeout=10)
        print(f"✅ Tushare网站可访问，状态码: {response.status_code}")
        
        # 检查代理设置
        proxies = requests.Session().proxies
        if proxies:
            print(f"⚠️ 检测到代理设置: {proxies}")
        else:
            print("✅ 无代理设置")
            
        return True
        
    except Exception as e:
        print(f"❌ 网络检查失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 Tushare简化连接测试")
    print(f"⏰ 测试开始")
    print()
    
    # 检查网络
    network_ok = check_network()
    
    if not network_ok:
        print("🚨 网络连接有问题，请检查网络设置")
        return
    
    # 测试基本连接
    if test_direct_connection():
        print("\n🎉 基本连接成功！继续测试其他功能...")
        test_stock_basic()
        test_daily_data()
        print("\n✅ 所有测试完成")
    else:
        print("\n❌ 基本连接失败，请检查Token或网络")

if __name__ == "__main__":
    main()