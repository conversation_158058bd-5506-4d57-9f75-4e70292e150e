"""
交易API端点测试
"""

import pytest
from httpx import AsyncClient
from unittest.mock import AsyncMock, Mo<PERSON>, patch
from datetime import datetime
from decimal import Decimal

from app.main import app
from app.schemas.trading import OrderCreate, OrderUpdate
from app.models.trading import Order, Position


@pytest.mark.api
@pytest.mark.trading
@pytest.mark.asyncio
class TestTradingAPI:
    """交易API测试类"""

    @pytest.fixture
    def auth_headers(self):
        """认证头"""
        return {"Authorization": "Bearer test-token"}

    @pytest.fixture
    def sample_order_data(self):
        """示例订单数据"""
        return {
            "symbol": "000001",
            "side": "buy",
            "order_type": "limit",
            "quantity": 100,
            "price": "10.50",
            "time_in_force": "GTC"
        }

    async def test_create_order_success(self, client: AsyncClient, auth_headers, sample_order_data):
        """测试创建订单成功"""
        # Mock认证和服务
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.create_order") as mock_create:
                # Mock用户
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                # Mock订单创建
                mock_order = Mock()
                mock_order.id = "test-order-id"
                mock_order.symbol = "000001"
                mock_order.status = "pending"
                mock_create.return_value = mock_order

                # 发送请求
                response = await client.post(
                    "/api/v1/trading/orders",
                    json=sample_order_data,
                    headers=auth_headers
                )

                # 验证响应
                assert response.status_code == 201
                data = response.json()
                assert data["id"] == "test-order-id"
                assert data["symbol"] == "000001"
                assert data["status"] == "pending"

    async def test_create_order_unauthorized(self, client: AsyncClient, sample_order_data):
        """测试未认证创建订单"""
        response = await client.post(
            "/api/v1/trading/orders",
            json=sample_order_data
        )
        assert response.status_code == 401

    async def test_create_order_invalid_data(self, client: AsyncClient, auth_headers):
        """测试创建订单时数据无效"""
        invalid_data = {
            "symbol": "",  # 空符号
            "side": "invalid_side",  # 无效方向
            "order_type": "limit",
            "quantity": -100,  # 负数量
            "price": "0"  # 零价格
        }

        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user-id"
            mock_auth.return_value = mock_user

            response = await client.post(
                "/api/v1/trading/orders",
                json=invalid_data,
                headers=auth_headers
            )
            assert response.status_code == 422

    async def test_get_order_success(self, client: AsyncClient, auth_headers):
        """测试获取订单成功"""
        order_id = "test-order-id"
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_order_by_id") as mock_get:
                # Mock用户
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                # Mock订单
                mock_order = Mock()
                mock_order.id = order_id
                mock_order.user_id = "test-user-id"
                mock_order.symbol = "000001"
                mock_order.status = "pending"
                mock_get.return_value = mock_order

                response = await client.get(
                    f"/api/v1/trading/orders/{order_id}",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert data["id"] == order_id
                assert data["symbol"] == "000001"

    async def test_get_order_not_found(self, client: AsyncClient, auth_headers):
        """测试获取不存在的订单"""
        order_id = "nonexistent-order"
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_order_by_id") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user
                mock_get.return_value = None

                response = await client.get(
                    f"/api/v1/trading/orders/{order_id}",
                    headers=auth_headers
                )

                assert response.status_code == 404

    async def test_get_order_access_denied(self, client: AsyncClient, auth_headers):
        """测试访问他人订单被拒绝"""
        order_id = "other-user-order"
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_order_by_id") as mock_get:
                # 当前用户
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                # 他人的订单
                mock_order = Mock()
                mock_order.id = order_id
                mock_order.user_id = "other-user-id"
                mock_get.return_value = mock_order

                response = await client.get(
                    f"/api/v1/trading/orders/{order_id}",
                    headers=auth_headers
                )

                assert response.status_code == 403

    async def test_cancel_order_success(self, client: AsyncClient, auth_headers):
        """测试取消订单成功"""
        order_id = "test-order-id"
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.cancel_order") as mock_cancel:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user
                mock_cancel.return_value = True

                response = await client.post(
                    f"/api/v1/trading/orders/{order_id}/cancel",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert data["message"] == "订单已取消"

    async def test_cancel_order_failed(self, client: AsyncClient, auth_headers):
        """测试取消订单失败"""
        order_id = "test-order-id"
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.cancel_order") as mock_cancel:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user
                mock_cancel.side_effect = ValueError("订单已成交，无法取消")

                response = await client.post(
                    f"/api/v1/trading/orders/{order_id}/cancel",
                    headers=auth_headers
                )

                assert response.status_code == 400
                data = response.json()
                assert "订单已成交" in data["detail"]

    async def test_get_user_orders(self, client: AsyncClient, auth_headers):
        """测试获取用户订单列表"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_user_orders") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                # Mock订单列表
                mock_orders = [
                    Mock(id="order1", symbol="000001", status="pending"),
                    Mock(id="order2", symbol="000002", status="filled"),
                ]
                mock_get.return_value = mock_orders

                response = await client.get(
                    "/api/v1/trading/orders",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert len(data) == 2
                assert data[0]["id"] == "order1"
                assert data[1]["id"] == "order2"

    async def test_get_user_orders_with_filters(self, client: AsyncClient, auth_headers):
        """测试带过滤条件获取用户订单"""
        params = {
            "symbol": "000001",
            "status": "pending",
            "limit": 10
        }
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_user_orders") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_orders = [Mock(id="order1", symbol="000001", status="pending")]
                mock_get.return_value = mock_orders

                response = await client.get(
                    "/api/v1/trading/orders",
                    params=params,
                    headers=auth_headers
                )

                assert response.status_code == 200
                # 验证过滤参数被传递
                mock_get.assert_called_once()

    async def test_get_user_positions(self, client: AsyncClient, auth_headers):
        """测试获取用户持仓"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_user_positions") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_positions = [
                    Mock(
                        id="pos1",
                        symbol="000001",
                        quantity=100,
                        average_price=Decimal("10.50"),
                        market_value=Decimal("1050.00")
                    ),
                    Mock(
                        id="pos2",
                        symbol="000002",
                        quantity=200,
                        average_price=Decimal("20.00"),
                        market_value=Decimal("4000.00")
                    ),
                ]
                mock_get.return_value = mock_positions

                response = await client.get(
                    "/api/v1/trading/positions",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert len(data) == 2
                assert data[0]["symbol"] == "000001"
                assert data[1]["symbol"] == "000002"

    async def test_get_portfolio_summary(self, client: AsyncClient, auth_headers):
        """测试获取投资组合摘要"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_portfolio_summary") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_summary = {
                    "total_market_value": Decimal("50000.00"),
                    "total_unrealized_pnl": Decimal("2500.00"),
                    "daily_pnl": Decimal("500.00"),
                    "position_count": 5,
                    "cash_balance": Decimal("25000.00"),
                    "buying_power": Decimal("75000.00")
                }
                mock_get.return_value = mock_summary

                response = await client.get(
                    "/api/v1/trading/portfolio/summary",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert data["position_count"] == 5
                assert "total_market_value" in data
                assert "total_unrealized_pnl" in data

    async def test_update_order_success(self, client: AsyncClient, auth_headers):
        """测试更新订单成功"""
        order_id = "test-order-id"
        update_data = {
            "price": "11.00",
            "quantity": 150
        }
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.update_order") as mock_update:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_updated_order = Mock()
                mock_updated_order.id = order_id
                mock_updated_order.price = Decimal("11.00")
                mock_updated_order.quantity = 150
                mock_update.return_value = mock_updated_order

                response = await client.patch(
                    f"/api/v1/trading/orders/{order_id}",
                    json=update_data,
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert data["id"] == order_id
                assert float(data["price"]) == 11.00
                assert data["quantity"] == 150

    async def test_batch_cancel_orders(self, client: AsyncClient, auth_headers):
        """测试批量取消订单"""
        order_ids = ["order1", "order2", "order3"]
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.batch_cancel_orders") as mock_batch:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_result = {
                    "cancelled": 2,
                    "failed": 1,
                    "details": [
                        {"order_id": "order1", "status": "cancelled"},
                        {"order_id": "order2", "status": "cancelled"},
                        {"order_id": "order3", "status": "failed", "reason": "已成交"}
                    ]
                }
                mock_batch.return_value = mock_result

                response = await client.post(
                    "/api/v1/trading/orders/batch-cancel",
                    json={"order_ids": order_ids},
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert data["cancelled"] == 2
                assert data["failed"] == 1
                assert len(data["details"]) == 3

    async def test_get_trade_history(self, client: AsyncClient, auth_headers):
        """测试获取交易历史"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_user_trades") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_trades = [
                    Mock(
                        id="trade1",
                        order_id="order1",
                        symbol="000001",
                        side="buy",
                        quantity=100,
                        price=Decimal("10.50"),
                        amount=Decimal("1050.00"),
                        commission=Decimal("2.10"),
                        trade_time=datetime.now()
                    )
                ]
                mock_get.return_value = mock_trades

                response = await client.get(
                    "/api/v1/trading/trades",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert len(data) == 1
                assert data[0]["symbol"] == "000001"
                assert data[0]["side"] == "buy"

    async def test_risk_check_before_order(self, client: AsyncClient, auth_headers, sample_order_data):
        """测试下单前风险检查"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.risk_service.RiskService.check_order_risk") as mock_risk:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                # Mock风险检查失败
                from app.schemas.trading import RiskCheckResult
                mock_risk.return_value = RiskCheckResult(
                    passed=False,
                    message="资金不足",
                    details={"required": "1050.00", "available": "500.00"}
                )

                response = await client.post(
                    "/api/v1/trading/orders",
                    json=sample_order_data,
                    headers=auth_headers
                )

                assert response.status_code == 400
                data = response.json()
                assert "资金不足" in data["detail"]

    async def test_order_status_updates(self, client: AsyncClient, auth_headers):
        """测试订单状态更新"""
        order_id = "test-order-id"
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.get_order_status_history") as mock_get:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                mock_history = [
                    {
                        "status": "pending",
                        "timestamp": datetime.now().isoformat(),
                        "reason": "订单已提交"
                    },
                    {
                        "status": "partial_filled",
                        "timestamp": datetime.now().isoformat(),
                        "reason": "部分成交 50/100"
                    }
                ]
                mock_get.return_value = mock_history

                response = await client.get(
                    f"/api/v1/trading/orders/{order_id}/status-history",
                    headers=auth_headers
                )

                assert response.status_code == 200
                data = response.json()
                assert len(data) == 2
                assert data[0]["status"] == "pending"
                assert data[1]["status"] == "partial_filled"

    async def test_concurrent_order_creation(self, client: AsyncClient, auth_headers, sample_order_data):
        """测试并发创建订单"""
        import asyncio
        
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            with patch("app.services.trading_service.TradingService.create_order") as mock_create:
                mock_user = Mock()
                mock_user.id = "test-user-id"
                mock_auth.return_value = mock_user

                # Mock订单创建成功
                mock_order = Mock()
                mock_order.id = "concurrent-order"
                mock_create.return_value = mock_order

                # 并发发送多个订单
                tasks = []
                for i in range(5):
                    task = client.post(
                        "/api/v1/trading/orders",
                        json={**sample_order_data, "quantity": 100 + i * 10},
                        headers=auth_headers
                    )
                    tasks.append(task)

                responses = await asyncio.gather(*tasks)
                
                # 验证所有订单都成功创建
                for response in responses:
                    assert response.status_code == 201

    async def test_order_validation_edge_cases(self, client: AsyncClient, auth_headers):
        """测试订单验证边界情况"""
        test_cases = [
            {
                "name": "极小数量",
                "data": {"symbol": "000001", "side": "buy", "order_type": "limit", "quantity": 1, "price": "0.01"},
                "expected_status": 201
            },
            {
                "name": "极大数量",
                "data": {"symbol": "000001", "side": "buy", "order_type": "limit", "quantity": 999999999, "price": "1.00"},
                "expected_status": 422  # 数量超限
            },
            {
                "name": "零价格",
                "data": {"symbol": "000001", "side": "buy", "order_type": "limit", "quantity": 100, "price": "0"},
                "expected_status": 422
            },
            {
                "name": "负价格",
                "data": {"symbol": "000001", "side": "buy", "order_type": "limit", "quantity": 100, "price": "-1.00"},
                "expected_status": 422
            }
        ]

        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user-id"
            mock_auth.return_value = mock_user

            for case in test_cases:
                if case["expected_status"] == 201:
                    with patch("app.services.trading_service.TradingService.create_order") as mock_create:
                        mock_order = Mock()
                        mock_order.id = "edge-case-order"
                        mock_create.return_value = mock_order

                        response = await client.post(
                            "/api/v1/trading/orders",
                            json=case["data"],
                            headers=auth_headers
                        )
                else:
                    response = await client.post(
                        "/api/v1/trading/orders",
                        json=case["data"],
                        headers=auth_headers
                    )

                assert response.status_code == case["expected_status"], f"测试用例 '{case['name']}' 失败"

    async def test_api_rate_limiting(self, client: AsyncClient, auth_headers, sample_order_data):
        """测试API频率限制"""
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = "test-user-id"
            mock_auth.return_value = mock_user

            # 快速发送大量请求
            responses = []
            for i in range(100):
                try:
                    response = await client.post(
                        "/api/v1/trading/orders",
                        json=sample_order_data,
                        headers=auth_headers,
                        timeout=1.0
                    )
                    responses.append(response)
                except Exception:
                    # 忽略超时等异常
                    pass

            # 检查是否有请求被限制
            status_codes = [r.status_code for r in responses]
            if 429 in status_codes:  # Too Many Requests
                assert True  # 频率限制生效
            else:
                # 如果没有频率限制，至少验证请求都被处理了
                assert len(responses) > 0