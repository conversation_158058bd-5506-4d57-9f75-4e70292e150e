# 错误处理和日志系统使用指南

本文档详细介绍了量化投资平台的错误处理和日志系统的使用方法。

## 目录

1. [系统概述](#系统概述)
2. [异常类型](#异常类型)
3. [使用方法](#使用方法)
4. [最佳实践](#最佳实践)
5. [监控和告警](#监控和告警)
6. [故障排除](#故障排除)

## 系统概述

我们的错误处理系统包含以下核心组件：

- **增强的异常类**：提供丰富的错误信息和上下文
- **异常处理中间件**：统一捕获和处理所有异常
- **请求日志中间件**：记录详细的请求和响应信息
- **服务基类**：为业务服务提供一致的错误处理
- **错误监控系统**：实时监控错误趋势和告警

### 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API端点       │    │   中间件层        │    │   监控系统       │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ • 路由处理      │───▶│ • 异常处理中间件  │───▶│ • 错误统计       │
│ • 参数验证      │    │ • 请求日志中间件  │    │ • 趋势分析       │
│ • 业务逻辑      │    │ • 性能监控中间件  │    │ • 告警规则       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   服务层        │    │   日志系统        │    │   告警处理       │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ • 服务基类      │    │ • 结构化日志     │    │ • 邮件告警       │
│ • 装饰器       │    │ • 上下文日志     │    │ • 短信告警       │
│ • 重试机制      │    │ • 性能日志       │    │ • 钉钉告警       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 异常类型

### 基础异常类

所有自定义异常都继承自 `BaseCustomException`：

```python
from app.core.exceptions import BaseCustomException, ErrorSeverity, ErrorCategory

class CustomError(BaseCustomException):
    def __init__(self, message: str = "自定义错误", **kwargs):
        super().__init__(
            message=message,
            error_code="CUSTOM_ERROR",
            category=ErrorCategory.BUSINESS_LOGIC,
            severity=ErrorSeverity.MEDIUM,
            user_message="用户友好的错误信息",
            recovery_hint="恢复建议",
            should_retry=False,
            **kwargs
        )
```

### 内置异常类型

#### 1. 验证错误
```python
from app.core.exceptions import ValidationError

raise ValidationError(
    "数据验证失败",
    details={"field": "email", "value": "invalid-email"},
    recovery_hint="请提供有效的邮箱地址"
)
```

#### 2. 数据库错误
```python
from app.core.exceptions import DatabaseError

raise DatabaseError(
    "数据库连接失败",
    should_retry=True,
    retry_after=30
)
```

#### 3. 网络错误
```python
from app.core.exceptions import NetworkError

raise NetworkError(
    "网络连接超时",
    details={"timeout": 10, "endpoint": "https://api.example.com"},
    should_retry=True,
    retry_after=60
)
```

#### 4. 业务逻辑错误
```python
from app.core.exceptions import BusinessLogicError

raise BusinessLogicError(
    "余额不足",
    details={"required": 1000, "available": 500},
    recovery_hint="请充值或减少交易金额"
)
```

#### 5. 量化平台特定错误

- **MarketDataError**: 行情数据相关错误
- **TradingError**: 交易相关错误
- **StrategyError**: 策略相关错误
- **RiskManagementError**: 风控相关错误
- **CTPError**: CTP接口相关错误

### 错误严重级别

- `LOW`: 低级别错误，不影响核心功能
- `MEDIUM`: 中等级别错误，可能影响部分功能
- `HIGH`: 高级别错误，影响重要功能
- `CRITICAL`: 关键错误，影响系统稳定性

### 错误类别

- `AUTHENTICATION`: 身份验证
- `AUTHORIZATION`: 权限验证
- `VALIDATION`: 数据验证
- `BUSINESS_LOGIC`: 业务逻辑
- `DATA_ACCESS`: 数据访问
- `NETWORK`: 网络通信
- `SYSTEM`: 系统级错误
- `EXTERNAL_SERVICE`: 外部服务
- `MARKET_DATA`: 行情数据
- `TRADING`: 交易相关
- `STRATEGY`: 策略相关
- `RISK_MANAGEMENT`: 风险管理
- `CTP`: CTP接口

## 使用方法

### 1. 在API端点中使用

```python
from fastapi import APIRouter, HTTPException
from app.core.exceptions import ValidationError, BusinessLogicError

router = APIRouter()

@router.post("/users")
async def create_user(user_data: UserCreateRequest):
    # 参数验证
    if not user_data.email:
        raise ValidationError(
            "邮箱不能为空",
            details={"field": "email"},
            recovery_hint="请提供有效的邮箱地址"
        )
    
    # 业务逻辑验证
    if await user_exists(user_data.email):
        raise BusinessLogicError(
            "用户已存在",
            details={"email": user_data.email},
            recovery_hint="请使用不同的邮箱地址"
        )
    
    return await create_user_in_db(user_data)
```

### 2. 在服务类中使用

```python
from app.core.service_base import ServiceBase, database_operation, external_api_call

class UserService(ServiceBase):
    def __init__(self):
        super().__init__("user_service")
    
    async def _initialize_service(self):
        # 服务初始化逻辑
        pass
    
    @database_operation(timeout=30.0, retry_count=2)
    async def create_user(self, user_data: dict) -> dict:
        try:
            # 数据库操作
            result = await self.db.create_user(user_data)
            return result
        except Exception as e:
            # 异常会被装饰器自动包装为DatabaseError
            raise
    
    @external_api_call(timeout=10.0, retry_count=3)
    async def send_verification_email(self, email: str) -> bool:
        # 外部API调用
        # 异常会被自动包装为ExternalServiceError
        pass
```

### 3. 使用装饰器

```python
from app.core.service_base import service_method

class TradingService(ServiceBase):
    @service_method(
        timeout=5.0,
        retry_count=0,  # 交易操作不重试
        log_performance=True
    )
    async def place_order(self, order_data: dict) -> dict:
        # 交易逻辑
        pass
    
    @service_method(
        timeout=30.0,
        retry_count=2,
        retry_delay=1.0
    )
    async def get_market_data(self, symbol: str) -> dict:
        # 获取行情数据
        pass
```

## 最佳实践

### 1. 异常处理原则

#### ✅ 好的做法

```python
# 提供详细的错误信息
raise ValidationError(
    "用户名格式不正确",
    details={
        "field": "username",
        "value": username,
        "requirements": "3-20个字符，只能包含字母、数字和下划线"
    },
    recovery_hint="请使用3-20个字符，只能包含字母、数字和下划线"
)

# 区分不同类型的错误
if not user_exists:
    raise ResourceNotFoundError("用户不存在")
elif not user_active:
    raise BusinessLogicError("用户账户已被禁用")
elif password_wrong:
    raise AuthenticationError("密码错误")
```

#### ❌ 不好的做法

```python
# 信息不够详细
raise Exception("错误")

# 静默忽略错误
try:
    risky_operation()
except:
    pass

# 使用通用异常
raise ValueError("Something went wrong")
```

### 2. 日志记录

```python
from app.core.logging_config import get_contextual_logger

logger = get_contextual_logger("service_name")

# 记录详细的上下文信息
logger.info("User operation started", 
           user_id=user_id, 
           operation="create_account",
           request_id=request_id)

# 记录错误时包含足够的调试信息
try:
    result = await complex_operation()
except Exception as e:
    logger.error("Complex operation failed",
                error=str(e),
                operation_params=operation_params,
                exc_info=True)  # 包含堆栈跟踪
    raise
```

### 3. 服务设计

```python
class WellDesignedService(ServiceBase):
    def __init__(self):
        super().__init__("well_designed_service")
        self.external_client = None
    
    async def _initialize_service(self):
        """服务初始化"""
        self.external_client = ExternalClient()
        await self.external_client.connect()
    
    async def _cleanup_service(self):
        """资源清理"""
        if self.external_client:
            await self.external_client.close()
    
    @database_operation()
    async def save_data(self, data: dict) -> dict:
        """保存数据 - 自动处理数据库错误和重试"""
        return await self.db.save(data)
    
    @external_api_call()
    async def fetch_external_data(self, query: str) -> dict:
        """获取外部数据 - 自动处理网络错误和重试"""
        return await self.external_client.query(query)
```

### 4. 错误响应格式

所有API错误响应都遵循统一格式：

```json
{
  "error": {
    "type": "validation_error",
    "code": "VALIDATION_FAILED",
    "message": "用户友好的错误信息",
    "details": {
      "field": "email",
      "value": "invalid-email"
    },
    "request_id": "req_12345",
    "timestamp": "2024-01-01T12:00:00Z",
    "severity": "low",
    "recovery_hint": "请提供有效的邮箱地址",
    "should_retry": false,
    "retry_after": null
  }
}
```

## 监控和告警

### 1. 错误监控

系统自动监控以下指标：

- 错误总数和错误率
- 按类型分类的错误统计
- 错误严重级别分布
- 错误趋势分析
- 最常见的错误类型

### 2. 告警规则

内置的告警规则包括：

- **高频错误告警**: 5分钟内超过50个错误
- **关键错误告警**: 出现CRITICAL级别错误
- **错误率飙升**: 错误率比前期增长3倍
- **数据库错误**: 5分钟内超过10个数据库错误

### 3. 自定义告警规则

```python
from app.core.error_monitoring import get_error_monitor, AlertRule, AlertLevel

monitor = get_error_monitor()

# 添加自定义告警规则
def trading_errors_rule(events):
    """交易错误告警"""
    recent_trading_errors = [
        e for e in events 
        if e.category == ErrorCategory.TRADING 
        and (datetime.utcnow() - e.timestamp).seconds < 300
    ]
    return len(recent_trading_errors) > 5

monitor.add_alert_rule(AlertRule(
    name="trading_errors",
    description="5分钟内交易错误超过5个",
    condition=trading_errors_rule,
    alert_level=AlertLevel.HIGH,
    cooldown_minutes=10
))
```

### 4. 告警处理器

```python
from app.core.error_monitoring import EmailAlertHandler

# 邮件告警
email_handler = EmailAlertHandler(
    smtp_server="smtp.example.com",
    smtp_port=587,
    username="<EMAIL>",
    password="password",
    recipients=["<EMAIL>", "<EMAIL>"]
)

monitor.add_alert_handler(email_handler)

# 自定义告警处理器
async def custom_alert_handler(alert):
    """自定义告警处理逻辑"""
    if alert.level == AlertLevel.CRITICAL:
        # 发送紧急通知
        await send_emergency_notification(alert)
    
    # 记录到告警系统
    await log_to_alert_system(alert)

monitor.add_alert_handler(custom_alert_handler)
```

## 故障排除

### 1. 常见问题

#### 问题：异常没有被中间件捕获
**解决方案**：
- 确保异常处理中间件已正确注册
- 检查异常是否在中间件之前被处理
- 验证异常类型是否被中间件识别

#### 问题：日志信息不够详细
**解决方案**：
- 使用结构化日志记录
- 添加更多上下文信息
- 启用详细日志模式

#### 问题：告警过于频繁
**解决方案**：
- 调整告警规则的阈值
- 增加冷却时间
- 使用更智能的告警条件

### 2. 调试技巧

#### 启用详细日志
```python
# 在配置中启用详细日志
ENABLE_DETAILED_ERROR_LOGGING = True
LOG_REQUEST_BODY = True
LOG_RESPONSE_BODY = True
```

#### 查看错误统计
```python
from app.core.error_monitoring import get_error_monitor

monitor = get_error_monitor()

# 获取错误统计
stats = monitor.get_error_statistics()
print(f"总错误数: {stats['total_events']}")
print(f"最常见错误: {stats['most_common_errors']}")

# 获取错误趋势
trends = monitor.get_error_trends(hours=24)
print(f"24小时内错误趋势: {trends['trend']}")
```

#### 查看中间件统计
```python
# 在异常处理中间件中获取统计信息
exception_middleware = app.user_middleware[0]  # 假设是第一个中间件
stats = exception_middleware.get_error_statistics()
print(f"错误统计: {stats}")
```

### 3. 性能优化

- 合理设置日志级别，避免过度日志记录
- 使用异步日志写入
- 定期清理旧的错误事件
- 优化告警规则，避免重复告警

### 4. 监控端点

系统提供了以下监控端点：

- `GET /health`: 健康检查
- `GET /metrics`: 性能指标
- `GET /api/v1/monitoring/errors`: 错误统计
- `GET /api/v1/monitoring/logs`: 日志查询

## 示例代码

完整的示例代码请参考：
- `app/examples/error_handling_examples.py`: 错误处理示例
- `app/core/service_base.py`: 服务基类
- `app/middleware/exception_middleware.py`: 异常处理中间件
- `app/core/error_monitoring.py`: 错误监控系统

## 总结

良好的错误处理系统是生产环境应用程序的关键组成部分。通过使用我们提供的错误处理框架，您可以：

1. **提供一致的错误响应格式**
2. **记录详细的调试信息**
3. **实现智能的重试机制**
4. **监控系统健康状况**
5. **及时发现和解决问题**

记住，错误处理不仅仅是技术问题，也是用户体验问题。良好的错误处理能够：

- 提供清晰的错误信息
- 给出有用的恢复建议
- 保护敏感信息不泄露
- 帮助开发人员快速定位问题

遵循本指南中的最佳实践，您的应用程序将更加健壮和可靠。