#!/usr/bin/env python3
"""
基本访问测试 - 检查页面是否可以正常访问
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def basic_access_test():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            logger.info("访问交易中心页面...")
            
            # 设置更长的超时时间
            await page.goto('http://localhost:5173/trading/center', wait_until='networkidle', timeout=60000)
            
            # 等待页面完全加载
            await page.wait_for_timeout(5000)
            
            # 检查页面内容
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 检查是否有Vue应用
            vue_app = await page.evaluate("() => document.querySelector('#app')")
            if vue_app:
                logger.info("✅ Vue应用已加载")
            else:
                logger.warning("❌ Vue应用未加载")
            
            # 检查页面标题元素
            try:
                page_title = await page.wait_for_selector('.page-title', timeout=10000)
                if page_title:
                    title_text = await page_title.text_content()
                    logger.info(f"✅ 页面标题元素找到: {title_text}")
                else:
                    logger.warning("❌ 页面标题元素未找到")
            except Exception as e:
                logger.warning(f"❌ 页面标题元素查找失败: {e}")
            
            # 检查导航按钮
            try:
                nav_buttons = await page.query_selector_all('.nav-right button')
                logger.info(f"✅ 发现 {len(nav_buttons)} 个导航按钮")
                
                for i, button in enumerate(nav_buttons):
                    button_text = await button.text_content()
                    logger.info(f"  按钮 {i+1}: {button_text.strip()}")
                    
            except Exception as e:
                logger.warning(f"❌ 导航按钮查找失败: {e}")
            
            # 检查是否有错误
            errors = await page.evaluate("""
                () => {
                    const errors = [];
                    
                    // 检查控制台错误
                    if (window.console && window.console.error) {
                        errors.push('Console available');
                    }
                    
                    // 检查页面内容
                    const tradingCenter = document.querySelector('.trading-center');
                    if (tradingCenter) {
                        errors.push('trading-center found');
                    } else {
                        errors.push('trading-center NOT found');
                    }
                    
                    return errors;
                }
            """)
            
            for error in errors:
                logger.info(f"检查结果: {error}")
            
            # 截图
            await page.screenshot(path='screenshots/basic_access_test.png', full_page=True)
            logger.info("📸 截图已保存")
            
            # 尝试点击第一个按钮
            try:
                first_button = await page.query_selector('.nav-right button')
                if first_button:
                    await first_button.click()
                    await page.wait_for_timeout(2000)
                    logger.info("✅ 第一个按钮点击成功")
                    
                    # 再次截图
                    await page.screenshot(path='screenshots/after_click.png')
                    logger.info("📸 点击后截图已保存")
                else:
                    logger.warning("❌ 未找到可点击的按钮")
            except Exception as e:
                logger.warning(f"❌ 按钮点击失败: {e}")
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            await page.screenshot(path='screenshots/error.png')
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(basic_access_test())
