"""
市场数据API端点测试
"""

import pytest
from httpx import AsyncClient
from unittest.mock import AsyncM<PERSON>, <PERSON><PERSON>, patch
from datetime import datetime, timedelta
from decimal import Decimal

from app.main import app
from app.schemas.market_data import TickData, KlineData, MarketDepth


@pytest.mark.api
@pytest.mark.market
@pytest.mark.asyncio
class TestMarketAPI:
    """市场数据API测试类"""

    @pytest.fixture
    def sample_tick_data(self):
        """示例Tick数据"""
        return {
            "symbol": "000001",
            "timestamp": datetime.now().isoformat(),
            "price": "10.50",
            "volume": 1000,
            "bid_price": "10.49",
            "ask_price": "10.51",
            "bid_volume": 500,
            "ask_volume": 600
        }

    @pytest.fixture
    def sample_kline_data(self):
        """示例K线数据"""
        return {
            "symbol": "000001",
            "timestamp": datetime.now().isoformat(),
            "interval": "1m",
            "open_price": "10.45",
            "high_price": "10.55",
            "low_price": "10.40",
            "close_price": "10.50",
            "volume": 50000,
            "amount": "525000.00"
        }

    async def test_get_tick_data_success(self, client: AsyncClient):
        """测试获取Tick数据成功"""
        symbol = "000001"
        
        with patch("app.services.market_data_service.MarketDataService.get_latest_tick") as mock_get:
            mock_tick = TickData(
                symbol=symbol,
                timestamp=datetime.now(),
                price=Decimal("10.50"),
                volume=1000,
                bid_price=Decimal("10.49"),
                ask_price=Decimal("10.51"),
                bid_volume=500,
                ask_volume=600
            )
            mock_get.return_value = mock_tick

            response = await client.get(f"/api/v1/market/tick/{symbol}")

            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == symbol
            assert float(data["price"]) == 10.50
            assert data["volume"] == 1000

    async def test_get_tick_data_not_found(self, client: AsyncClient):
        """测试获取不存在的Tick数据"""
        symbol = "NONEXISTENT"
        
        with patch("app.services.market_data_service.MarketDataService.get_latest_tick") as mock_get:
            mock_get.return_value = None

            response = await client.get(f"/api/v1/market/tick/{symbol}")

            assert response.status_code == 404
            data = response.json()
            assert "未找到" in data["detail"]

    async def test_get_kline_data_success(self, client: AsyncClient):
        """测试获取K线数据成功"""
        symbol = "000001"
        interval = "1m"
        
        with patch("app.services.market_data_service.MarketDataService.get_kline_history") as mock_get:
            mock_klines = [
                KlineData(
                    symbol=symbol,
                    timestamp=datetime.now() - timedelta(minutes=i),
                    interval=interval,
                    open_price=Decimal("10.00") + Decimal(str(i * 0.01)),
                    high_price=Decimal("10.10") + Decimal(str(i * 0.01)),
                    low_price=Decimal("9.90") + Decimal(str(i * 0.01)),
                    close_price=Decimal("10.05") + Decimal(str(i * 0.01)),
                    volume=1000 + i * 100,
                    amount=Decimal("10050.00") + Decimal(str(i * 100))
                ) for i in range(10)
            ]
            mock_get.return_value = mock_klines

            response = await client.get(f"/api/v1/market/kline/{symbol}", params={"interval": interval})

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 10
            assert data[0]["symbol"] == symbol
            assert data[0]["interval"] == interval

    async def test_get_kline_data_with_date_range(self, client: AsyncClient):
        """测试获取指定时间范围的K线数据"""
        symbol = "000001"
        interval = "1d"
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        
        with patch("app.services.market_data_service.MarketDataService.get_kline_history") as mock_get:
            mock_klines = [
                KlineData(
                    symbol=symbol,
                    timestamp=datetime(2024, 1, i+1),
                    interval=interval,
                    open_price=Decimal("10.00"),
                    high_price=Decimal("10.50"),
                    low_price=Decimal("9.80"),
                    close_price=Decimal("10.20"),
                    volume=100000,
                    amount=Decimal("1020000.00")
                ) for i in range(31)
            ]
            mock_get.return_value = mock_klines

            response = await client.get(
                f"/api/v1/market/kline/{symbol}",
                params={
                    "interval": interval,
                    "start_time": start_date,
                    "end_time": end_date
                }
            )

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 31
            # 验证时间范围参数被传递
            mock_get.assert_called_once()

    async def test_get_depth_data_success(self, client: AsyncClient):
        """测试获取深度数据成功"""
        symbol = "000001"
        
        with patch("app.services.market_data_service.MarketDataService.get_latest_depth") as mock_get:
            mock_depth = MarketDepth(
                symbol=symbol,
                timestamp=datetime.now(),
                bids=[
                    [Decimal("10.49"), Decimal("500")],
                    [Decimal("10.48"), Decimal("800")],
                    [Decimal("10.47"), Decimal("600")]
                ],
                asks=[
                    [Decimal("10.51"), Decimal("600")],
                    [Decimal("10.52"), Decimal("700")],
                    [Decimal("10.53"), Decimal("400")]
                ]
            )
            mock_get.return_value = mock_depth

            response = await client.get(f"/api/v1/market/depth/{symbol}")

            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == symbol
            assert len(data["bids"]) == 3
            assert len(data["asks"]) == 3
            assert float(data["bids"][0][0]) == 10.49  # 最优买价
            assert float(data["asks"][0][0]) == 10.51  # 最优卖价

    async def test_get_depth_data_with_level(self, client: AsyncClient):
        """测试获取指定档位的深度数据"""
        symbol = "000001"
        level = 10
        
        with patch("app.services.market_data_service.MarketDataService.get_latest_depth") as mock_get:
            # Mock更多档位的数据
            mock_depth = MarketDepth(
                symbol=symbol,
                timestamp=datetime.now(),
                bids=[[Decimal(f"10.{49-i:02d}"), Decimal(str(500+i*50))] for i in range(10)],
                asks=[[Decimal(f"10.{51+i:02d}"), Decimal(str(600+i*50))] for i in range(10)]
            )
            mock_get.return_value = mock_depth

            response = await client.get(f"/api/v1/market/depth/{symbol}", params={"level": level})

            assert response.status_code == 200
            data = response.json()
            assert len(data["bids"]) == 10
            assert len(data["asks"]) == 10

    async def test_get_symbol_info_success(self, client: AsyncClient):
        """测试获取合约信息成功"""
        symbol = "000001"
        
        with patch("app.services.market_data_service.MarketDataService.get_symbol_info") as mock_get:
            mock_info = {
                "symbol": symbol,
                "name": "平安银行",
                "exchange": "SZSE",
                "market": "stock",
                "lot_size": 100,
                "tick_size": "0.01",
                "min_quantity": 100,
                "max_quantity": 1000000,
                "trading_hours": "09:30-15:00",
                "status": "trading"
            }
            mock_get.return_value = mock_info

            response = await client.get(f"/api/v1/market/symbol/{symbol}")

            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == symbol
            assert data["name"] == "平安银行"
            assert data["exchange"] == "SZSE"

    async def test_search_symbols_success(self, client: AsyncClient):
        """测试搜索合约成功"""
        keyword = "平安"
        
        with patch("app.services.market_data_service.MarketDataService.search_symbols") as mock_search:
            mock_results = [
                {
                    "symbol": "000001",
                    "name": "平安银行",
                    "exchange": "SZSE",
                    "market": "stock"
                },
                {
                    "symbol": "601318",
                    "name": "中国平安",
                    "exchange": "SSE",
                    "market": "stock"
                }
            ]
            mock_search.return_value = mock_results

            response = await client.get("/api/v1/market/symbols/search", params={"keyword": keyword})

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert all("平安" in item["name"] for item in data)

    async def test_search_symbols_empty_result(self, client: AsyncClient):
        """测试搜索合约无结果"""
        keyword = "NONEXISTENT"
        
        with patch("app.services.market_data_service.MarketDataService.search_symbols") as mock_search:
            mock_search.return_value = []

            response = await client.get("/api/v1/market/symbols/search", params={"keyword": keyword})

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 0

    async def test_get_market_status(self, client: AsyncClient):
        """测试获取市场状态"""
        with patch("app.services.market_data_service.MarketDataService.get_market_status") as mock_get:
            mock_status = {
                "market": "CN_STOCK",
                "status": "trading",
                "open_time": "09:30:00",
                "close_time": "15:00:00",
                "current_time": datetime.now().strftime("%H:%M:%S"),
                "is_trading_day": True,
                "next_trading_day": "2024-01-16"
            }
            mock_get.return_value = mock_status

            response = await client.get("/api/v1/market/status")

            assert response.status_code == 200
            data = response.json()
            assert data["market"] == "CN_STOCK"
            assert data["status"] in ["pre_trading", "trading", "post_trading", "closed"]

    async def test_get_trading_calendar(self, client: AsyncClient):
        """测试获取交易日历"""
        year = 2024
        
        with patch("app.services.market_data_service.MarketDataService.get_trading_calendar") as mock_get:
            mock_calendar = {
                "year": year,
                "trading_days": [
                    "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05",
                    # ... 更多交易日
                ],
                "holidays": [
                    {"date": "2024-01-01", "name": "元旦"},
                    {"date": "2024-02-10", "name": "春节"},
                    # ... 更多节假日
                ],
                "total_trading_days": 245
            }
            mock_get.return_value = mock_calendar

            response = await client.get(f"/api/v1/market/calendar/{year}")

            assert response.status_code == 200
            data = response.json()
            assert data["year"] == year
            assert "trading_days" in data
            assert "holidays" in data

    async def test_batch_get_tick_data(self, client: AsyncClient):
        """测试批量获取Tick数据"""
        symbols = ["000001", "000002", "000300"]
        
        with patch("app.services.market_data_service.MarketDataService.batch_get_latest_tick") as mock_batch:
            mock_ticks = {
                "000001": TickData(
                    symbol="000001",
                    timestamp=datetime.now(),
                    price=Decimal("10.50"),
                    volume=1000,
                    bid_price=Decimal("10.49"),
                    ask_price=Decimal("10.51"),
                    bid_volume=500,
                    ask_volume=600
                ),
                "000002": TickData(
                    symbol="000002",
                    timestamp=datetime.now(),
                    price=Decimal("20.30"),
                    volume=2000,
                    bid_price=Decimal("20.29"),
                    ask_price=Decimal("20.31"),
                    bid_volume=800,
                    ask_volume=900
                )
            }
            mock_batch.return_value = mock_ticks

            response = await client.post(
                "/api/v1/market/tick/batch",
                json={"symbols": symbols}
            )

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2  # 只返回有数据的合约
            assert "000001" in data
            assert "000002" in data

    async def test_get_market_statistics(self, client: AsyncClient):
        """测试获取市场统计信息"""
        with patch("app.services.market_data_service.MarketDataService.get_market_statistics") as mock_get:
            mock_stats = {
                "total_symbols": 5000,
                "active_symbols": 4800,
                "total_volume": "123456789000",
                "total_amount": "9876543210000.00",
                "up_count": 2400,
                "down_count": 1800,
                "unchanged_count": 600,
                "limit_up_count": 120,
                "limit_down_count": 80,
                "suspension_count": 200,
                "update_time": datetime.now().isoformat()
            }
            mock_get.return_value = mock_stats

            response = await client.get("/api/v1/market/statistics")

            assert response.status_code == 200
            data = response.json()
            assert data["total_symbols"] == 5000
            assert data["active_symbols"] == 4800
            assert "up_count" in data
            assert "down_count" in data

    async def test_get_top_gainers_losers(self, client: AsyncClient):
        """测试获取涨跌幅排行"""
        ranking_type = "gainers"
        limit = 10
        
        with patch("app.services.market_data_service.MarketDataService.get_ranking") as mock_get:
            mock_ranking = [
                {
                    "symbol": f"00000{i}",
                    "name": f"股票{i}",
                    "price": f"{10 + i}.50",
                    "change": f"{i * 0.5}",
                    "change_percent": f"{i * 5}.00%",
                    "volume": 1000000 + i * 100000,
                    "amount": f"{(10 + i) * (1000000 + i * 100000)}.00"
                } for i in range(1, 11)
            ]
            mock_get.return_value = mock_ranking

            response = await client.get(
                f"/api/v1/market/ranking/{ranking_type}",
                params={"limit": limit}
            )

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 10
            assert all("change_percent" in item for item in data)

    async def test_get_sector_performance(self, client: AsyncClient):
        """测试获取板块表现"""
        with patch("app.services.market_data_service.MarketDataService.get_sector_performance") as mock_get:
            mock_sectors = [
                {
                    "sector": "银行",
                    "symbol_count": 36,
                    "avg_change_percent": "2.15%",
                    "total_volume": "1234567890",
                    "total_amount": "123456789000.00",
                    "leaders": [
                        {"symbol": "000001", "name": "平安银行", "change_percent": "5.20%"},
                        {"symbol": "600036", "name": "招商银行", "change_percent": "4.80%"}
                    ]
                },
                {
                    "sector": "科技",
                    "symbol_count": 85,
                    "avg_change_percent": "-1.25%",
                    "total_volume": "2345678901",
                    "total_amount": "234567890000.00",
                    "leaders": [
                        {"symbol": "000858", "name": "五粮液", "change_percent": "3.10%"}
                    ]
                }
            ]
            mock_get.return_value = mock_sectors

            response = await client.get("/api/v1/market/sectors")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["sector"] == "银行"
            assert "leaders" in data[0]

    async def test_invalid_symbol_format(self, client: AsyncClient):
        """测试无效合约格式"""
        invalid_symbols = ["", "TOOLONG123456", "123ABC", "invalid!@#"]
        
        for symbol in invalid_symbols:
            response = await client.get(f"/api/v1/market/tick/{symbol}")
            # 应该返回400（格式错误）或404（未找到）
            assert response.status_code in [400, 404]

    async def test_api_response_caching(self, client: AsyncClient):
        """测试API响应缓存"""
        symbol = "000001"
        
        with patch("app.services.market_data_service.MarketDataService.get_latest_tick") as mock_get:
            mock_tick = TickData(
                symbol=symbol,
                timestamp=datetime.now(),
                price=Decimal("10.50"),
                volume=1000,
                bid_price=Decimal("10.49"),
                ask_price=Decimal("10.51"),
                bid_volume=500,
                ask_volume=600
            )
            mock_get.return_value = mock_tick

            # 第一次请求
            response1 = await client.get(f"/api/v1/market/tick/{symbol}")
            
            # 第二次请求（应该使用缓存）
            response2 = await client.get(f"/api/v1/market/tick/{symbol}")

            assert response1.status_code == 200
            assert response2.status_code == 200
            
            # 检查缓存头
            if "Cache-Control" in response1.headers:
                assert "max-age" in response1.headers["Cache-Control"]

    async def test_api_error_handling(self, client: AsyncClient):
        """测试API错误处理"""
        symbol = "000001"
        
        # 测试服务异常
        with patch("app.services.market_data_service.MarketDataService.get_latest_tick") as mock_get:
            mock_get.side_effect = Exception("服务临时不可用")

            response = await client.get(f"/api/v1/market/tick/{symbol}")

            assert response.status_code == 500
            data = response.json()
            assert "error" in data or "detail" in data

    async def test_parameter_validation(self, client: AsyncClient):
        """测试参数验证"""
        symbol = "000001"
        
        # 测试无效的时间间隔
        response = await client.get(f"/api/v1/market/kline/{symbol}", params={"interval": "invalid"})
        assert response.status_code == 422
        
        # 测试无效的档位数
        response = await client.get(f"/api/v1/market/depth/{symbol}", params={"level": -1})
        assert response.status_code == 422
        
        # 测试无效的日期格式
        response = await client.get(
            f"/api/v1/market/kline/{symbol}",
            params={"interval": "1d", "start_time": "invalid-date"}
        )
        assert response.status_code == 422

    async def test_pagination_support(self, client: AsyncClient):
        """测试分页支持"""
        with patch("app.services.market_data_service.MarketDataService.search_symbols") as mock_search:
            # Mock大量搜索结果
            mock_results = [
                {
                    "symbol": f"00000{i:03d}",
                    "name": f"测试股票{i}",
                    "exchange": "SZSE",
                    "market": "stock"
                } for i in range(100)
            ]
            mock_search.return_value = mock_results

            # 测试分页参数
            response = await client.get(
                "/api/v1/market/symbols/search",
                params={"keyword": "测试", "page": 1, "size": 20}
            )

            assert response.status_code == 200
            data = response.json()
            
            # 检查分页信息
            if isinstance(data, dict) and "items" in data:
                assert len(data["items"]) <= 20
                assert "total" in data
                assert "page" in data
                assert "size" in data