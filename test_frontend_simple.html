<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        .nav-item {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
        .card h3 {
            margin-top: 0;
            color: #ffd700;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status.online {
            background: #4CAF50;
            color: white;
        }
        .status.offline {
            background: #f44336;
            color: white;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 量化投资平台测试中心</h1>
        
        <div class="nav">
            <div class="nav-item" onclick="testPage('仪表盘')">仪表盘</div>
            <div class="nav-item" onclick="testPage('市场数据')">市场数据</div>
            <div class="nav-item" onclick="testPage('交易终端')">交易终端</div>
            <div class="nav-item" onclick="testPage('投资组合')">投资组合</div>
            <div class="nav-item" onclick="testPage('策略中心')">策略中心</div>
            <div class="nav-item" onclick="testPage('风险管理')">风险管理</div>
        </div>

        <div class="content">
            <div class="card">
                <h3>📊 服务状态</h3>
                <p>前端服务: <span class="status online" id="frontend-status">在线</span></p>
                <p>后端API: <span class="status" id="backend-status">检查中...</span></p>
                <p>WebSocket: <span class="status" id="ws-status">检查中...</span></p>
                <button class="test-button" onclick="checkServices()">刷新状态</button>
            </div>

            <div class="card">
                <h3>🔧 功能测试</h3>
                <button class="test-button" onclick="testAPI()">测试API连接</button>
                <button class="test-button" onclick="testMarketData()">测试市场数据</button>
                <button class="test-button" onclick="testTrading()">测试交易功能</button>
                <button class="test-button" onclick="testWebSocket()">测试WebSocket</button>
            </div>

            <div class="card">
                <h3>📈 模拟数据</h3>
                <div id="market-data">
                    <p>上证指数: <span style="color: #4CAF50;">3245.67 (+1.23%)</span></p>
                    <p>深证成指: <span style="color: #f44336;">10876.54 (-0.45%)</span></p>
                    <p>创业板指: <span style="color: #4CAF50;">2234.89 (+0.78%)</span></p>
                </div>
                <button class="test-button" onclick="updateMarketData()">更新数据</button>
            </div>

            <div class="card">
                <h3>💹 交易模拟</h3>
                <p>账户余额: ¥100,000.00</p>
                <p>持仓市值: ¥85,432.10</p>
                <p>今日盈亏: <span style="color: #4CAF50;">+¥1,234.56</span></p>
                <button class="test-button" onclick="simulateTrade()">模拟交易</button>
            </div>
        </div>

        <div class="test-results" id="test-results">
            <strong>测试日志:</strong><br>
            <span id="log-content">等待测试...</span>
        </div>
    </div>

    <script>
        let logContent = '';

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('log-content').textContent = logContent;
        }

        function testPage(pageName) {
            log(`导航到页面: ${pageName}`);
            // 模拟页面切换
            setTimeout(() => {
                log(`${pageName} 页面加载完成`);
            }, 500);
        }

        async function checkServices() {
            log('开始检查服务状态...');
            
            // 检查后端API
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    document.getElementById('backend-status').textContent = '在线';
                    document.getElementById('backend-status').className = 'status online';
                    log('后端API服务正常');
                } else {
                    throw new Error('API响应异常');
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = '离线';
                document.getElementById('backend-status').className = 'status offline';
                log('后端API服务异常: ' + error.message);
            }

            // 检查WebSocket (模拟)
            setTimeout(() => {
                document.getElementById('ws-status').textContent = '在线';
                document.getElementById('ws-status').className = 'status online';
                log('WebSocket连接正常');
            }, 1000);
        }

        async function testAPI() {
            log('测试API连接...');
            try {
                const response = await fetch('http://localhost:8000/api/v1/market/stocks');
                const data = await response.json();
                log(`API测试成功，获取到${data.data ? data.data.length : 0}条股票数据`);
            } catch (error) {
                log('API测试失败: ' + error.message);
            }
        }

        function testMarketData() {
            log('测试市场数据功能...');
            // 模拟市场数据测试
            setTimeout(() => {
                log('市场数据功能测试完成');
            }, 800);
        }

        function testTrading() {
            log('测试交易功能...');
            // 模拟交易功能测试
            setTimeout(() => {
                log('交易功能测试完成');
            }, 1200);
        }

        function testWebSocket() {
            log('测试WebSocket连接...');
            // 模拟WebSocket测试
            setTimeout(() => {
                log('WebSocket连接测试完成');
            }, 600);
        }

        function updateMarketData() {
            log('更新市场数据...');
            // 模拟数据更新
            const indices = ['3245.67', '10876.54', '2234.89'];
            const changes = ['+1.23%', '-0.45%', '+0.78%'];
            
            setTimeout(() => {
                log('市场数据更新完成');
            }, 400);
        }

        function simulateTrade() {
            log('执行模拟交易...');
            // 模拟交易执行
            setTimeout(() => {
                log('模拟交易执行完成 - 买入平安银行100股');
            }, 1000);
        }

        // 页面加载完成后自动检查服务
        window.onload = function() {
            log('页面加载完成，开始初始化...');
            setTimeout(checkServices, 1000);
        };
    </script>
</body>
</html>
