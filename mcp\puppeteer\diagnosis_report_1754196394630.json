{"timestamp": "2025-08-03T04:46:34.630Z", "pages": {"homepage": {"title": "仪表盘 - 量化投资平台", "url": "http://localhost:5173/", "bodyContent": "量化平台\n仪表盘\n行情中心\n交易中心\n策略中心\n回测分析\n投资组合\n风险管理\n组件展示\n仪表盘\n📊 投资仪表盘\n今日\n本周\n本月\n刷新数据\n📈\n总资产\n¥1,234,567.89\n+23.45%\n💰\n今日盈亏\n+12,345.67\n+2.34%\n📊\n总盈亏\n+234,567.89\n+23.45%\n🎯\n持仓股票\n8\n活跃策略: 3\n仓位使用率\n📊 投资组合趋势\n1日\n1周\n1月\n3月\n1年\n当前净值\n1.2456\n今日涨跌\n+2.34%\n累计收益\n+24.56%\n📈 图表组件加载中...\n📰 市场资讯\n市场分析：科技股表现强劲\n\n今日科技板块整体上涨2.5%，人工智能概念股领涨...\n\n2小时前\n央行政策解读\n\n央行维持基准利率不变，市场流动性保持合理充裕...\n\n4小时前\n新能源汽车销量创新高\n\n11月新能源汽车销量同比增长35%，产业链受益...\n\n6小时前", "hasVueApp": true, "elementCount": 398, "scriptCount": 3, "styleCount": 67}, "simulated": {"title": "模拟交易 - 量化投资平台", "bodyContent": "量化平台\n仪表盘\n行情中心\n交易中心\n交易终端\n模拟交易\nMiniQMT实盘\n订单管理\n持仓管理\n策略中心\n回测分析\n投资组合\n风险管理\n组件展示\n模拟交易\n模拟交易\n无风险练习\n总资产:\n¥1,000,000\n可用资金:\n¥800,000\n持仓市值:\n¥200,000\n今日盈亏:\n+¥5,000\n买入\n卖出\n股票代码:\n委托价格:\n委托数量:\n委托金额:\n¥0\n买入\n持仓\n委托\n成交\n股票代码\n\t\n股票名称\n\t\n持仓数量\n\t\n成本价\n\t\n现价\n\t\n市值\n\t\n盈亏\n000001\n\t\n平安银行\n\t\n1000\n\t\n10.5\n\t\n10.8\n\t\n10800\n\t\n+300.00", "hasVueApp": true, "elementCount": 555, "foundElements": {".simulated-trading-modern": {"exists": false, "visible": false, "text": ""}, ".simulated-trading": {"exists": false, "visible": false, "text": ""}, ".modern-header": {"exists": false, "visible": false, "text": ""}, ".trading-header": {"exists": true, "visible": true, "text": "模拟交易无风险练习总资产:¥1,000,000可用资金:¥800,000持仓市值:¥200,000今日盈亏:+¥5,000"}, ".modern-search": {"exists": false, "visible": false, "text": ""}, ".search-input": {"exists": true, "visible": true, "text": ""}, ".account-dashboard": {"exists": false, "visible": false, "text": ""}, ".account-overview": {"exists": true, "visible": true, "text": "总资产:¥1,000,000可用资金:¥800,000持仓市值:¥200,000今日盈亏:+¥5,000"}, ".trading-workspace": {"exists": false, "visible": false, "text": ""}, ".trading-main": {"exists": false, "visible": false, "text": ""}, ".left-panel": {"exists": true, "visible": true, "text": ""}, ".right-panel": {"exists": true, "visible": true, "text": " 买入  卖出 股票代码:委托价格:委托数量:委托金额:¥0 买入 "}, ".trade-form": {"exists": false, "visible": false, "text": ""}, ".trading-forms": {"exists": false, "visible": false, "text": ""}}, "allClasses": ["", "default-layout", "layout-sidebar", "sidebar-header", "logo", "logo-image", "logo-text", "el-button is-text", "el-icon", "sidebar-nav", "el-menu el-menu--vertical", "el-menu-item", "el-icon", "el-sub-menu", "el-sub-menu__title", "el-icon", "el-icon el-sub-menu__icon-arrow", "el-menu el-menu--inline", "el-menu-item", "el-icon"]}, "live": {"title": "实盘交易 - 量化投资平台", "bodyContent": "量化平台\n仪表盘\n行情中心\n交易中心\n策略中心\n回测分析\n投资组合\n风险管理\n组件展示\n实盘交易\n总资产\n¥1,000,000\n可用资金\n¥800,000\n当日盈亏\n+¥5000\n极简模式\n高级模式\n MiniQMT未连接\nMiniQMT连接提示\n\n请确保MiniQMT客户端已启动并配置正确，然后点击右上角连接按钮。\n\n确认MiniQMT客户端正在运行\n检查API接口配置（默认端口：58609）\n验证账户信息和交易权限\n连接MiniQMT\n\n●\nMiniQMT连接断开 | 请检查客户端\n行情延迟: 124ms\n市场有风险，投资需谨慎 | 理性投资，远离非法证券活动\nF1:买入 F2:卖出 Esc:清空 Ctrl+C:全撤\n\n实时连接已建立\n\nMiniQMT连接失败，请检查配置和网络", "foundElements": {".live-trading": {"exists": false, "visible": false}, ".trading-terminal": {"exists": false, "visible": false}, ".modern-header": {"exists": false, "visible": false}, ".connection-status": {"exists": true, "visible": true}, ".order-panel": {"exists": false, "visible": false}, ".depth-panel": {"exists": false, "visible": false}}, "allClasses": ["", "default-layout", "layout-sidebar", "sidebar-header", "logo", "logo-image", "logo-text", "el-button is-text", "el-icon", "sidebar-nav", "el-menu el-menu--vertical", "el-menu-item", "el-icon", "el-sub-menu", "el-sub-menu__title", "el-icon", "el-icon el-sub-menu__icon-arrow", "el-menu el-menu--inline", "el-menu-item", "el-icon"]}}, "network": {"totalRequests": 235, "requestTypes": {"document": 1, "script": 233, "image": 1}, "apiRequests": [{"url": "http://localhost:5173/src/api/http.ts?t=1754196283149", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/interceptors/security.ts?t=1754194714868", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/market.ts?t=1754196283149", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/index.ts?t=1754196283149", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/user.ts?t=1754196283149", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/trading.ts?t=1754196283149", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/strategy.ts?t=1754196283149", "method": "GET", "resourceType": "script"}, {"url": "http://localhost:5173/src/api/backtest.ts?t=1754196283149", "method": "GET", "resourceType": "script"}]}, "errors": [], "consoleMessages": [{"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:46:21.082Z"}, {"type": "warn", "text": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-03T04:46:21.348Z"}, {"type": "debug", "text": "[vite] connecting...", "timestamp": "2025-08-03T04:46:21.358Z"}, {"type": "log", "text": "🔒 安全头通过后端HTTP响应头设置，跳过meta标签设置", "timestamp": "2025-08-03T04:46:21.712Z"}, {"type": "log", "text": "Security initialization completed", "timestamp": "2025-08-03T04:46:21.712Z"}, {"type": "log", "text": "✅ 全局错误处理器已初始化", "timestamp": "2025-08-03T04:46:21.712Z"}, {"type": "log", "text": "🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问", "timestamp": "2025-08-03T04:46:21.712Z"}, {"type": "log", "text": "📱 应用版本: 1.0.0", "timestamp": "2025-08-03T04:46:21.712Z"}, {"type": "log", "text": "🌐 API地址: http://localhost:8000/api/v1", "timestamp": "2025-08-03T04:46:21.713Z"}, {"type": "log", "text": "🔌 WebSocket地址: ws://localhost:8000/api/v1/ws", "timestamp": "2025-08-03T04:46:21.713Z"}, {"type": "log", "text": "🚀 量化投资平台 v1.0.0 启动成功!", "timestamp": "2025-08-03T04:46:21.720Z"}, {"type": "log", "text": "🌍 运行环境: 开发", "timestamp": "2025-08-03T04:46:21.720Z"}, {"type": "log", "text": "⏰ 启动时间: 2025/8/3 12:46:21", "timestamp": "2025-08-03T04:46:21.736Z"}, {"type": "log", "text": "📊 页面加载时间: NaNms", "timestamp": "2025-08-03T04:46:21.743Z"}, {"type": "debug", "text": "[vite] connected.", "timestamp": "2025-08-03T04:46:21.760Z"}, {"type": "log", "text": "✅ Element Plus图标加载完成", "timestamp": "2025-08-03T04:46:21.760Z"}, {"type": "warn", "text": "<meta name=\"apple-mobile-web-app-capable\" content=\"yes\"> is deprecated. Please include <meta name=\"mobile-web-app-capable\" content=\"yes\">", "timestamp": "2025-08-03T04:46:21.761Z"}, {"type": "log", "text": "🔧 开发环境自动登录", "timestamp": "2025-08-03T04:46:21.762Z"}, {"type": "log", "text": "🔧 开发环境自动登录成功", "timestamp": "2025-08-03T04:46:21.764Z"}, {"type": "log", "text": "✅ ECharts加载完成", "timestamp": "2025-08-03T04:46:22.011Z"}, {"type": "log", "text": "🧭 路由跳转: / -> /", "timestamp": "2025-08-03T04:46:22.494Z"}, {"type": "log", "text": "✅ 全局组件加载完成", "timestamp": "2025-08-03T04:46:22.668Z"}, {"type": "log", "text": "🎉 所有资源加载完成", "timestamp": "2025-08-03T04:46:22.668Z"}, {"type": "log", "text": "✅ Service Worker 注册成功: http://localhost:5173/", "timestamp": "2025-08-03T04:46:22.668Z"}, {"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:46:23.549Z"}, {"type": "warn", "text": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-03T04:46:23.561Z"}, {"type": "debug", "text": "[vite] connecting...", "timestamp": "2025-08-03T04:46:23.565Z"}, {"type": "debug", "text": "[vite] connected.", "timestamp": "2025-08-03T04:46:23.885Z"}, {"type": "log", "text": "🔒 安全头通过后端HTTP响应头设置，跳过meta标签设置", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "Security initialization completed", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "✅ 全局错误处理器已初始化", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "📱 应用版本: 1.0.0", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "🌐 API地址: http://localhost:8000/api/v1", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "🔌 WebSocket地址: ws://localhost:8000/api/v1/ws", "timestamp": "2025-08-03T04:46:24.032Z"}, {"type": "log", "text": "🚀 量化投资平台 v1.0.0 启动成功!", "timestamp": "2025-08-03T04:46:24.035Z"}, {"type": "log", "text": "🌍 运行环境: 开发", "timestamp": "2025-08-03T04:46:24.035Z"}, {"type": "log", "text": "⏰ 启动时间: 2025/8/3 12:46:24", "timestamp": "2025-08-03T04:46:24.035Z"}, {"type": "log", "text": "📊 页面加载时间: NaNms", "timestamp": "2025-08-03T04:46:24.045Z"}, {"type": "log", "text": "✅ Service Worker 注册成功: http://localhost:5173/", "timestamp": "2025-08-03T04:46:24.095Z"}, {"type": "log", "text": "✅ Element Plus图标加载完成", "timestamp": "2025-08-03T04:46:24.109Z"}, {"type": "warn", "text": "<meta name=\"apple-mobile-web-app-capable\" content=\"yes\"> is deprecated. Please include <meta name=\"mobile-web-app-capable\" content=\"yes\">", "timestamp": "2025-08-03T04:46:24.110Z"}, {"type": "log", "text": "✅ ECharts加载完成", "timestamp": "2025-08-03T04:46:24.401Z"}, {"type": "log", "text": "🧭 路由跳转: / -> /trading/simulated", "timestamp": "2025-08-03T04:46:24.984Z"}, {"type": "log", "text": "SimulatedTradingFixed 组件已挂载", "timestamp": "2025-08-03T04:46:25.108Z"}, {"type": "log", "text": "✅ 全局组件加载完成", "timestamp": "2025-08-03T04:46:25.225Z"}, {"type": "log", "text": "🎉 所有资源加载完成", "timestamp": "2025-08-03T04:46:25.225Z"}, {"type": "warn", "text": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-03T04:46:28.259Z"}, {"type": "warn", "text": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-03T04:46:28.259Z"}, {"type": "error", "text": "🚨 页面错误: ", "timestamp": "2025-08-03T04:46:28.915Z"}, {"type": "warn", "text": "🔧 ResizeObserver 错误已被自动处理", "timestamp": "2025-08-03T04:46:28.915Z"}, {"type": "error", "text": "🚨 全局异常: JSHandle@object", "timestamp": "2025-08-03T04:46:28.916Z"}, {"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:46:29.151Z"}, {"type": "warn", "text": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-03T04:46:29.157Z"}, {"type": "debug", "text": "[vite] connecting...", "timestamp": "2025-08-03T04:46:29.161Z"}, {"type": "log", "text": "🔒 安全头通过后端HTTP响应头设置，跳过meta标签设置", "timestamp": "2025-08-03T04:46:29.277Z"}, {"type": "log", "text": "Security initialization completed", "timestamp": "2025-08-03T04:46:29.277Z"}, {"type": "log", "text": "✅ 全局错误处理器已初始化", "timestamp": "2025-08-03T04:46:29.277Z"}, {"type": "log", "text": "🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问", "timestamp": "2025-08-03T04:46:29.277Z"}, {"type": "log", "text": "📱 应用版本: 1.0.0", "timestamp": "2025-08-03T04:46:29.277Z"}, {"type": "log", "text": "🌐 API地址: http://localhost:8000/api/v1", "timestamp": "2025-08-03T04:46:29.277Z"}, {"type": "log", "text": "🔌 WebSocket地址: ws://localhost:8000/api/v1/ws", "timestamp": "2025-08-03T04:46:29.278Z"}, {"type": "log", "text": "🚀 量化投资平台 v1.0.0 启动成功!", "timestamp": "2025-08-03T04:46:29.280Z"}, {"type": "log", "text": "🌍 运行环境: 开发", "timestamp": "2025-08-03T04:46:29.280Z"}, {"type": "log", "text": "⏰ 启动时间: 2025/8/3 12:46:29", "timestamp": "2025-08-03T04:46:29.280Z"}, {"type": "log", "text": "📊 页面加载时间: NaNms", "timestamp": "2025-08-03T04:46:29.286Z"}, {"type": "log", "text": "✅ Service Worker 注册成功: http://localhost:5173/", "timestamp": "2025-08-03T04:46:29.348Z"}, {"type": "log", "text": "✅ Element Plus图标加载完成", "timestamp": "2025-08-03T04:46:29.364Z"}, {"type": "warn", "text": "<meta name=\"apple-mobile-web-app-capable\" content=\"yes\"> is deprecated. Please include <meta name=\"mobile-web-app-capable\" content=\"yes\">", "timestamp": "2025-08-03T04:46:29.364Z"}, {"type": "log", "text": "🔧 开发环境自动登录", "timestamp": "2025-08-03T04:46:29.364Z"}, {"type": "log", "text": "🔧 开发环境自动登录成功", "timestamp": "2025-08-03T04:46:29.364Z"}, {"type": "log", "text": "✅ ECharts加载完成", "timestamp": "2025-08-03T04:46:29.469Z"}, {"type": "debug", "text": "[vite] connected.", "timestamp": "2025-08-03T04:46:29.547Z"}, {"type": "log", "text": "🧭 路由跳转: / -> /trading/live", "timestamp": "2025-08-03T04:46:29.592Z"}, {"type": "log", "text": "✅ 全局组件加载完成", "timestamp": "2025-08-03T04:46:29.816Z"}, {"type": "log", "text": "🎉 所有资源加载完成", "timestamp": "2025-08-03T04:46:29.817Z"}, {"type": "log", "text": "WebSocket连接成功", "timestamp": "2025-08-03T04:46:30.162Z"}, {"type": "error", "text": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-08-03T04:46:31.847Z"}, {"type": "error", "text": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toFixed')\n    at http://localhost:5173/src/views/Trading/LiveTrading.vue?t=1754196283149:1901:99\n    at Proxy.renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2789:13)\n    at column2.renderCell (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:39640:39)\n    at cellChildren (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:37931:20)\n    at default (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:37926:24)\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:6602:31\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2789:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:5112:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:37820:9)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8671:17)", "timestamp": "2025-08-03T04:46:31.847Z"}, {"type": "error", "text": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-08-03T04:46:31.848Z"}, {"type": "error", "text": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toFixed')\n    at http://localhost:5173/src/views/Trading/LiveTrading.vue?t=1754196283149:1901:99\n    at Proxy.renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2789:13)\n    at column2.renderCell (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:39640:39)\n    at cellChildren (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:37931:20)\n    at default (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:37926:24)\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:6602:31\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2789:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:5112:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:37820:9)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8671:17)", "timestamp": "2025-08-03T04:46:31.849Z"}, {"type": "warn", "text": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-03T04:46:32.839Z"}, {"type": "warn", "text": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-03T04:46:32.839Z"}, {"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:46:33.650Z"}, {"type": "warn", "text": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-03T04:46:33.657Z"}, {"type": "debug", "text": "[vite] connecting...", "timestamp": "2025-08-03T04:46:33.664Z"}, {"type": "log", "text": "🔒 安全头通过后端HTTP响应头设置，跳过meta标签设置", "timestamp": "2025-08-03T04:46:33.766Z"}, {"type": "log", "text": "Security initialization completed", "timestamp": "2025-08-03T04:46:33.766Z"}, {"type": "log", "text": "✅ 全局错误处理器已初始化", "timestamp": "2025-08-03T04:46:33.766Z"}, {"type": "log", "text": "🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问", "timestamp": "2025-08-03T04:46:33.766Z"}, {"type": "log", "text": "📱 应用版本: 1.0.0", "timestamp": "2025-08-03T04:46:33.767Z"}, {"type": "log", "text": "🌐 API地址: http://localhost:8000/api/v1", "timestamp": "2025-08-03T04:46:33.767Z"}, {"type": "log", "text": "🔌 WebSocket地址: ws://localhost:8000/api/v1/ws", "timestamp": "2025-08-03T04:46:33.767Z"}, {"type": "log", "text": "🚀 量化投资平台 v1.0.0 启动成功!", "timestamp": "2025-08-03T04:46:33.769Z"}, {"type": "log", "text": "🌍 运行环境: 开发", "timestamp": "2025-08-03T04:46:33.769Z"}, {"type": "log", "text": "⏰ 启动时间: 2025/8/3 12:46:33", "timestamp": "2025-08-03T04:46:33.769Z"}, {"type": "log", "text": "📊 页面加载时间: NaNms", "timestamp": "2025-08-03T04:46:33.776Z"}, {"type": "log", "text": "✅ Service Worker 注册成功: http://localhost:5173/", "timestamp": "2025-08-03T04:46:33.780Z"}, {"type": "log", "text": "✅ Element Plus图标加载完成", "timestamp": "2025-08-03T04:46:33.784Z"}, {"type": "warn", "text": "<meta name=\"apple-mobile-web-app-capable\" content=\"yes\"> is deprecated. Please include <meta name=\"mobile-web-app-capable\" content=\"yes\">", "timestamp": "2025-08-03T04:46:33.784Z"}, {"type": "log", "text": "✅ ECharts加载完成", "timestamp": "2025-08-03T04:46:33.874Z"}, {"type": "log", "text": "🧭 路由跳转: / -> /trading/simulated", "timestamp": "2025-08-03T04:46:33.915Z"}, {"type": "log", "text": "SimulatedTradingFixed 组件已挂载", "timestamp": "2025-08-03T04:46:33.953Z"}, {"type": "log", "text": "✅ 全局组件加载完成", "timestamp": "2025-08-03T04:46:33.988Z"}, {"type": "log", "text": "🎉 所有资源加载完成", "timestamp": "2025-08-03T04:46:33.989Z"}, {"type": "debug", "text": "[vite] connected.", "timestamp": "2025-08-03T04:46:33.995Z"}], "issues": ["模拟交易页面主容器未找到，可能是Vue组件未正确加载"], "recommendations": ["检查Vue路由配置和组件导入"]}