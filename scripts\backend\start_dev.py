"""开发环境启动脚本"""
import uvicorn
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["ENVIRONMENT"] = "development"
os.environ["TESTING"] = "false"
os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///./test.db"
os.environ["JWT_SECRET"] = "dev-secret-key"
os.environ["JWT_ACCESS_TOKEN_EXPIRE_MINUTES"] = "1440"

if __name__ == "__main__":
    # 运行开发服务器
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )