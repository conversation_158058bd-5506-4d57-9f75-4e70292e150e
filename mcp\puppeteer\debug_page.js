const puppeteer = require('puppeteer');

async function debugPage() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized']
  });
  
  try {
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
      console.log(`🖥️ 控制台 [${msg.type()}]:`, msg.text());
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      console.log('❌ 页面错误:', error.message);
    });
    
    console.log('🚀 访问主页...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const homeContent = await page.evaluate(() => {
      return {
        title: document.title,
        bodyText: document.body.textContent.trim(),
        bodyTextLength: document.body.textContent.length,
        hasVueApp: !!document.querySelector('#app'),
        appChildren: document.querySelector('#app')?.children.length || 0,
        appHTML: document.querySelector('#app')?.innerHTML.substring(0, 500) || 'No app element'
      };
    });
    
    console.log('🏠 主页信息:', homeContent);
    
    console.log('\n🚀 访问仪表板...');
    await page.goto('http://localhost:5173/dashboard', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const dashboardContent = await page.evaluate(() => {
      return {
        title: document.title,
        bodyText: document.body.textContent.trim(),
        bodyTextLength: document.body.textContent.length,
        hasVueApp: !!document.querySelector('#app'),
        appChildren: document.querySelector('#app')?.children.length || 0,
        appHTML: document.querySelector('#app')?.innerHTML.substring(0, 500) || 'No app element',
        hasCards: document.querySelectorAll('.el-card').length,
        hasEnhancedCards: document.querySelectorAll('.enhanced-metric-card').length,
        allElements: document.querySelectorAll('*').length
      };
    });
    
    console.log('📊 仪表板信息:', dashboardContent);
    
    // 等待用户查看
    console.log('\n⏳ 等待30秒供查看...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ 调试过程中出现错误:', error.message);
  } finally {
    await browser.close();
  }
}

debugPage().catch(console.error);
