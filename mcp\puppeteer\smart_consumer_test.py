#!/usr/bin/env python3
"""
智能消费者测试
专门针对交易中心进行消费者使用习惯测试，避免UI层级问题
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartConsumerTest:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'functionality_tests': [],
            'user_experience_issues': [],
            'consumer_feedback': []
        }
        
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
        logger.info("🧠 智能消费者测试环境初始化完成")

    async def close_overlays(self):
        """关闭可能遮挡的覆盖层"""
        try:
            # 关闭可能的抽屉
            close_buttons = await self.page.query_selector_all('.el-drawer__close, .el-dialog__close, [aria-label="Close"]')
            for button in close_buttons:
                try:
                    if await button.is_visible():
                        await button.click()
                        await asyncio.sleep(0.5)
                except:
                    pass
            
            # 按ESC键关闭模态框
            await self.page.keyboard.press('Escape')
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.debug(f"关闭覆盖层时出错: {e}")

    async def test_trading_center_access(self):
        """测试交易中心访问"""
        logger.info("🏠 测试交易中心访问")
        
        test_result = {
            'name': '交易中心访问',
            'success': False,
            'details': [],
            'issues': []
        }
        
        try:
            # 访问交易中心
            await self.page.goto('http://localhost:5173/trading/center', wait_until='networkidle')
            await asyncio.sleep(3)
            
            # 检查页面标题
            title = await self.page.text_content('h1, .page-title')
            if title and '交易中心' in title:
                test_result['details'].append('页面标题正确显示')
                test_result['success'] = True
            else:
                test_result['issues'].append('页面标题不明确')
            
            # 检查账户信息显示
            account_info = await self.page.query_selector('.account-info')
            if account_info:
                test_result['details'].append('账户信息正常显示')
            else:
                test_result['issues'].append('缺少账户信息显示')
            
            # 检查导航按钮
            nav_buttons = await self.page.query_selector_all('.nav-right button')
            if len(nav_buttons) >= 3:
                test_result['details'].append(f'发现{len(nav_buttons)}个导航按钮')
            else:
                test_result['issues'].append('导航按钮数量不足')
            
        except Exception as e:
            test_result['issues'].append(f'访问失败: {str(e)}')
        
        self.test_results['functionality_tests'].append(test_result)

    async def test_module_navigation(self):
        """测试模块导航功能"""
        logger.info("🔄 测试模块导航功能")
        
        modules = [
            ('交易终端', 'terminal'),
            ('账户管理', 'account'), 
            ('数据中心', 'data')
        ]
        
        for module_name, module_key in modules:
            test_result = {
                'name': f'{module_name}模块导航',
                'success': False,
                'details': [],
                'issues': []
            }
            
            try:
                await self.close_overlays()
                
                # 查找并点击模块按钮
                module_button = await self.page.query_selector(f'button:has-text("{module_name}")')
                if module_button:
                    await module_button.click()
                    await asyncio.sleep(2)
                    test_result['details'].append('模块按钮点击成功')
                    
                    # 检查模块内容是否显示
                    module_content = await self.page.query_selector('.module-container')
                    if module_content and await module_content.is_visible():
                        test_result['details'].append('模块内容正常显示')
                        test_result['success'] = True
                    else:
                        test_result['issues'].append('模块内容未显示')
                else:
                    test_result['issues'].append('未找到模块按钮')
                
            except Exception as e:
                test_result['issues'].append(f'模块切换失败: {str(e)}')
            
            self.test_results['functionality_tests'].append(test_result)

    async def test_trading_functionality(self):
        """测试交易功能"""
        logger.info("💰 测试交易功能")
        
        test_result = {
            'name': '交易功能测试',
            'success': False,
            'details': [],
            'issues': []
        }
        
        try:
            await self.close_overlays()
            
            # 确保在交易终端模块
            terminal_button = await self.page.query_selector('button:has-text("交易终端")')
            if terminal_button:
                await terminal_button.click()
                await asyncio.sleep(2)
                test_result['details'].append('切换到交易终端')
            
            # 测试模式切换
            sim_radio = await self.page.query_selector('label:has-text("模拟交易")')
            if sim_radio:
                await sim_radio.click()
                await asyncio.sleep(1)
                test_result['details'].append('模拟交易模式切换成功')
            
            # 测试股票搜索
            search_input = await self.page.query_selector('.el-autocomplete input')
            if search_input:
                await search_input.fill('000001')
                await asyncio.sleep(1)
                test_result['details'].append('股票搜索输入成功')
                
                # 选择搜索结果
                await self.page.keyboard.press('ArrowDown')
                await self.page.keyboard.press('Enter')
                await asyncio.sleep(2)
                test_result['details'].append('股票选择成功')
            
            # 检查交易表单
            quantity_input = await self.page.query_selector('.el-input-number input')
            if quantity_input:
                await quantity_input.fill('200')
                test_result['details'].append('交易数量填写成功')
            
            # 检查买入按钮
            buy_button = await self.page.query_selector('button:has-text("买入"):not(.el-button--disabled)')
            if buy_button:
                test_result['details'].append('买入按钮可用')
                test_result['success'] = True
            else:
                test_result['issues'].append('买入按钮不可用')
            
        except Exception as e:
            test_result['issues'].append(f'交易功能测试失败: {str(e)}')
        
        self.test_results['functionality_tests'].append(test_result)

    async def test_data_center_functionality(self):
        """测试数据中心功能"""
        logger.info("📊 测试数据中心功能")
        
        try:
            await self.close_overlays()
            
            # 切换到数据中心
            data_button = await self.page.query_selector('button:has-text("数据中心")')
            if data_button:
                await data_button.click()
                await asyncio.sleep(2)
            
            # 测试各个标签页
            tabs = ['订单管理', '持仓管理', '成交记录']
            
            for tab_name in tabs:
                test_result = {
                    'name': f'{tab_name}标签页',
                    'success': False,
                    'details': [],
                    'issues': []
                }
                
                try:
                    tab_button = await self.page.query_selector(f'.el-tabs__item:has-text("{tab_name}")')
                    if tab_button:
                        await tab_button.click()
                        await asyncio.sleep(2)
                        test_result['details'].append('标签页切换成功')
                        
                        # 检查表格
                        table = await self.page.query_selector('.el-table')
                        if table:
                            rows = await table.query_selector_all('.el-table__row')
                            test_result['details'].append(f'表格显示正常，{len(rows)}行数据')
                            test_result['success'] = True
                        else:
                            test_result['issues'].append('表格未显示')
                    else:
                        test_result['issues'].append('标签页按钮未找到')
                
                except Exception as e:
                    test_result['issues'].append(f'标签页测试失败: {str(e)}')
                
                self.test_results['functionality_tests'].append(test_result)
                
        except Exception as e:
            logger.error(f"数据中心测试失败: {e}")

    async def test_user_experience_elements(self):
        """测试用户体验元素"""
        logger.info("👥 测试用户体验元素")
        
        ux_tests = [
            {
                'name': '模式切换说明',
                'selector': '.mode-info, .el-alert',
                'description': '检查是否有模式切换说明'
            },
            {
                'name': '价格快速设置',
                'selector': 'button:has-text("现价"), button:has-text("买一")',
                'description': '检查价格快速设置按钮'
            },
            {
                'name': '金额预估',
                'selector': '.amount-info',
                'description': '检查交易金额预估功能'
            },
            {
                'name': '账户资金显示',
                'selector': '.available-funds, .funds',
                'description': '检查账户资金显示'
            }
        ]
        
        for ux_test in ux_tests:
            test_result = {
                'name': ux_test['name'],
                'success': False,
                'details': [],
                'issues': []
            }
            
            try:
                await self.close_overlays()
                
                elements = await self.page.query_selector_all(ux_test['selector'])
                if elements:
                    test_result['details'].append(f'发现{len(elements)}个相关元素')
                    test_result['success'] = True
                else:
                    test_result['issues'].append(f'{ux_test["description"]}缺失')
                
            except Exception as e:
                test_result['issues'].append(f'测试失败: {str(e)}')
            
            self.test_results['functionality_tests'].append(test_result)

    async def evaluate_consumer_experience(self):
        """评估消费者体验"""
        logger.info("📝 评估消费者体验")
        
        # 统计成功率
        total_tests = len(self.test_results['functionality_tests'])
        successful_tests = sum(1 for test in self.test_results['functionality_tests'] if test['success'])
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        # 生成消费者反馈
        feedback = []
        
        if success_rate >= 0.9:
            feedback.append("整体用户体验优秀，功能完整易用")
        elif success_rate >= 0.7:
            feedback.append("用户体验良好，但仍有改进空间")
        else:
            feedback.append("用户体验需要大幅改进")
        
        # 检查关键功能
        key_functions = ['交易中心访问', '交易功能测试']
        key_success = sum(1 for test in self.test_results['functionality_tests'] 
                         if test['name'] in key_functions and test['success'])
        
        if key_success == len(key_functions):
            feedback.append("核心交易功能正常工作")
        else:
            feedback.append("核心交易功能存在问题，需要优先修复")
        
        # 检查导航功能
        nav_tests = [test for test in self.test_results['functionality_tests'] 
                    if '模块导航' in test['name']]
        nav_success = sum(1 for test in nav_tests if test['success'])
        
        if nav_success == len(nav_tests):
            feedback.append("模块导航功能完善")
        else:
            feedback.append("模块导航需要优化")
        
        self.test_results['consumer_feedback'] = feedback
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': success_rate,
            'feedback': feedback
        }

    async def generate_report(self):
        """生成测试报告"""
        evaluation = await self.evaluate_consumer_experience()
        
        print("\n" + "="*80)
        print("🧠 智能消费者使用测试报告")
        print("="*80)
        print(f"测试时间: {self.test_results['timestamp']}")
        print(f"总测试项: {evaluation['total_tests']}")
        print(f"成功项目: {evaluation['successful_tests']}")
        print(f"成功率: {evaluation['success_rate']:.1%}")
        
        print("\n📋 功能测试详情:")
        for test in self.test_results['functionality_tests']:
            status = "✅" if test['success'] else "❌"
            print(f"{status} {test['name']}")
            
            for detail in test['details']:
                print(f"   ✓ {detail}")
            
            for issue in test['issues']:
                print(f"   ⚠️ {issue}")
        
        print("\n💡 消费者体验反馈:")
        for feedback in evaluation['feedback']:
            print(f"   • {feedback}")
        
        # 生成改进建议
        print("\n🔧 改进建议:")
        failed_tests = [test for test in self.test_results['functionality_tests'] if not test['success']]
        
        if failed_tests:
            print("   优先修复以下问题:")
            for test in failed_tests:
                print(f"   - {test['name']}: {', '.join(test['issues'])}")
        else:
            print("   • 所有核心功能正常，建议继续优化用户体验细节")
        
        print("   • 建议添加更多用户引导和帮助信息")
        print("   • 建议优化移动端响应式设计")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    test = SmartConsumerTest()
    
    try:
        await test.setup()
        
        # 执行智能消费者测试
        await test.test_trading_center_access()
        await test.test_module_navigation()
        await test.test_trading_functionality()
        await test.test_data_center_functionality()
        await test.test_user_experience_elements()
        
        # 生成报告
        await test.generate_report()
        
        print(f"\n🎉 智能消费者测试完成！")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
