{"timestamp": "2025-08-02T12:55:07.419322", "page_title": "仪表盘 - 量化投资平台", "html_content_length": 1060233, "app_content_length": 31683, "console_errors": 0, "console_warnings": 11, "total_console_messages": 55, "failed_requests": 0, "total_requests": 244, "api_requests": 15, "js_requests": 0, "css_requests": 19, "current_url": "http://localhost:5173/", "screenshot": "page_issue_test_125507.png", "console_messages": [{"type": "debug", "text": "[vite] connecting...", "location": "{'url': 'http://localhost:5173/@vite/client', 'lineNumber': 788, 'columnNumber': 8}"}, {"type": "debug", "text": "[vite] connected.", "location": "{'url': 'http://localhost:5173/@vite/client', 'lineNumber': 911, 'columnNumber': 14}"}, {"type": "log", "text": "Security initialization completed", "location": "{'url': 'http://localhost:5173/src/utils/security.ts', 'lineNumber': 377, 'columnNumber': 10}"}, {"type": "log", "text": "✅ 全局错误处理器已初始化", "location": "{'url': 'http://localhost:5173/src/utils/error-handler.ts', 'lineNumber': 432, 'columnNumber': 10}"}, {"type": "log", "text": "🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 180, 'columnNumber': 10}"}, {"type": "log", "text": "📱 应用版本: 1.0.0", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 181, 'columnNumber': 10}"}, {"type": "log", "text": "🌐 API地址: http://localhost:8000/api/v1", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 182, 'columnNumber': 10}"}, {"type": "log", "text": "🔌 WebSocket地址: ws://localhost:8000/api/v1/ws", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 183, 'columnNumber': 10}"}, {"type": "log", "text": "🚀 量化投资平台 v1.0.0 启动成功!", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 195, 'columnNumber': 8}"}, {"type": "log", "text": "🌍 运行环境: 开发", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 196, 'columnNumber': 8}"}, {"type": "log", "text": "⏰ 启动时间: 2025/8/2 12:54:57", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 197, 'columnNumber': 8}"}, {"type": "log", "text": "🔧 开发环境自动登录", "location": "{'url': 'http://localhost:5173/src/router/guards.ts', 'lineNumber': 28, 'columnNumber': 16}"}, {"type": "log", "text": "🔧 开发环境自动登录成功", "location": "{'url': 'http://localhost:5173/src/stores/modules/user.ts', 'lineNumber': 139, 'columnNumber': 14}"}, {"type": "log", "text": "✅ Service Worker 注册成功: http://localhost:5173/", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 188, 'columnNumber': 14}"}, {"type": "log", "text": "🧭 路由跳转: / -> /", "location": "{'url': 'http://localhost:5173/src/router/guards.ts', 'lineNumber': 70, 'columnNumber': 14}"}, {"type": "log", "text": "🚀 开始获取股票列表，参数: {pageSize: 100}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 166, 'columnNumber': 14}"}, {"type": "log", "text": "✅ 账户信息获取成功: Proxy(Object)", "location": "{'url': 'http://localhost:5173/src/stores/modules/trading.ts', 'lineNumber': 95, 'columnNumber': 16}"}, {"type": "log", "text": "✅ 持仓信息获取成功: 2 个持仓", "location": "{'url': 'http://localhost:5173/src/stores/modules/trading.ts', 'lineNumber': 129, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Request] GET /trading/orders {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /trading/trades {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/overview {headers: Object, data: undefined, params: undefined}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/sectors {headers: Object, data: undefined, params: undefined}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/news {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/rankings {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/rankings {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "✅ 成功获取股票数据: 15 只股票", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 168, 'columnNumber': 14}"}, {"type": "log", "text": "📊 前3只股票详情: [Object, Object, Object]", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 170, 'columnNumber': 16}"}, {"type": "log", "text": "💾 股票列表已保存到store，当前数量: 15", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 173, 'columnNumber': 14}"}, {"type": "log", "text": "WebSocket连接成功", "location": "{'url': 'http://localhost:5173/src/services/websocket.service.ts', 'lineNumber': 55, 'columnNumber': 14}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /trading/orders {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /trading/trades {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/sectors {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "🔍 板块数据API响应: {success: true, data: Array(5)}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 421, 'columnNumber': 14}"}, {"type": "warning", "text": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(5)}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 434, 'columnNumber': 18}"}, {"type": "log", "text": "✅ 从板块数据提取行业信息: 0 个行业", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 443, 'columnNumber': 14}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/news {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/rankings {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "🔍 排行榜API响应: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 481, 'columnNumber': 14}"}, {"type": "warning", "text": "⚠️ 排行榜数据格式异常或为空: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 497, 'columnNumber': 18}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/overview {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "⚠️ 指数数据格式异常或为空: {success: true, data: Object}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 307, 'columnNumber': 18}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/rankings {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "🔍 排行榜API响应: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 481, 'columnNumber': 14}"}, {"type": "warning", "text": "⚠️ 排行榜数据格式异常或为空: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 497, 'columnNumber': 18}"}, {"type": "log", "text": "📊 图表容器尺寸检查: 991x300", "location": "{'url': 'http://localhost:5173/src/utils/chartUtils.ts', 'lineNumber': 31, 'columnNumber': 18}"}, {"type": "log", "text": "🎨 开始初始化ECharts实例", "location": "{'url': 'http://localhost:5173/src/utils/chartUtils.ts', 'lineNumber': 51, 'columnNumber': 18}"}, {"type": "log", "text": "✅ 图表初始化成功", "location": "{'url': 'http://localhost:5173/src/utils/chartUtils.ts', 'lineNumber': 56, 'columnNumber': 18}"}, {"type": "log", "text": "投资组合图表初始化成功", "location": "{'url': 'http://localhost:5173/src/views/Dashboard/DashboardView.vue', 'lineNumber': 215, 'columnNumber': 20}"}], "failed_network_requests": []}