<template>
  <div class="miniqmt-trading">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="connection-status">
        <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
          <el-icon><Link /></el-icon>
          {{ connectionStatus === 'connected' ? 'MiniQMT已连接' : 'MiniQMT未连接' }}
        </el-tag>
      </div>
      
      <div class="account-status">
        <el-tag type="warning">
          <el-icon><User /></el-icon>
          实盘账户: {{ accountInfo.accountId }}
        </el-tag>
        <span class="funds">可用资金: ¥{{ formatMoney(accountInfo.availableFunds) }}</span>
      </div>
      
      <div class="actions">
        <el-button @click="connectMiniQMT" :loading="connecting" type="primary">
          {{ connectionStatus === 'connected' ? '重新连接' : '连接MiniQMT' }}
        </el-button>
        <el-button @click="showSettings = true" type="info" plain>
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div v-if="connectionStatus !== 'connected'" class="connection-guide">
        <div class="guide-content">
          <el-icon class="guide-icon"><Warning /></el-icon>
          <h2>MiniQMT连接指南</h2>
          <div class="guide-steps">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>启动MiniQMT</h4>
                <p>请确保MiniQMT客户端已经启动并登录</p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>检查API设置</h4>
                <p>确认MiniQMT的API接口已开启，端口设置正确</p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>点击连接</h4>
                <p>点击上方"连接MiniQMT"按钮建立连接</p>
              </div>
            </div>
          </div>
          
          <div class="guide-actions">
            <el-button @click="connectMiniQMT" :loading="connecting" type="primary" size="large">
              <el-icon><Link /></el-icon>
              连接MiniQMT
            </el-button>
            <el-button @click="showSettings = true" type="info" size="large">
              <el-icon><Setting /></el-icon>
              连接设置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 已连接状态的交易界面 -->
      <div v-else class="trading-interface">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="实盘交易" name="trading">
            <div class="trading-content">
              <div class="trading-panels">
                <!-- 左侧：股票信息和行情 -->
                <div class="left-panel">
                  <div class="stock-selector">
                    <el-input
                      v-model="stockCode"
                      placeholder="输入股票代码"
                      @keyup.enter="loadStockInfo"
                    >
                      <template #append>
                        <el-button @click="loadStockInfo" :loading="loadingStock">
                          <el-icon><Search /></el-icon>
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                  
                  <div v-if="currentStock" class="stock-info">
                    <div class="stock-header">
                      <h3>{{ currentStock.code }} {{ currentStock.name }}</h3>
                      <el-tag :type="currentStock.changePercent >= 0 ? 'danger' : 'success'">
                        {{ currentStock.changePercent >= 0 ? '涨' : '跌' }}
                      </el-tag>
                    </div>
                    
                    <div class="price-display">
                      <div class="current-price" :class="getPriceClass(currentStock.changePercent)">
                        ¥{{ formatPrice(currentStock.currentPrice) }}
                      </div>
                      <div class="price-change">
                        <span :class="getPriceClass(currentStock.changePercent)">
                          {{ currentStock.changePercent >= 0 ? '+' : '' }}{{ currentStock.changePercent.toFixed(2) }}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 右侧：交易表单 -->
                <div class="right-panel">
                  <div class="trading-form">
                    <el-form :model="orderForm" label-width="80px">
                      <el-form-item label="交易方向">
                        <el-radio-group v-model="orderForm.side">
                          <el-radio label="buy">买入</el-radio>
                          <el-radio label="sell">卖出</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      
                      <el-form-item label="委托类型">
                        <el-select v-model="orderForm.orderType" style="width: 100%">
                          <el-option label="限价委托" value="limit" />
                          <el-option label="市价委托" value="market" />
                          <el-option label="五档即成剩撤" value="fak" />
                          <el-option label="五档即成转限价" value="fok" />
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item label="委托数量">
                        <el-input-number
                          v-model="orderForm.quantity"
                          :min="100"
                          :step="100"
                          style="width: 100%"
                        />
                      </el-form-item>
                      
                      <el-form-item v-if="orderForm.orderType === 'limit'" label="委托价格">
                        <el-input-number
                          v-model="orderForm.price"
                          :precision="2"
                          :step="0.01"
                          style="width: 100%"
                        />
                      </el-form-item>
                      
                      <el-form-item>
                        <el-button
                          :type="orderForm.side === 'buy' ? 'danger' : 'success'"
                          @click="submitOrder"
                          :loading="submittingOrder"
                          style="width: 100%"
                          size="large"
                        >
                          {{ orderForm.side === 'buy' ? '买入' : '卖出' }}
                        </el-button>
                      </el-form-item>
                    </el-form>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="委托查询" name="orders">
            <div class="orders-content">
              <div class="orders-header">
                <h3>当日委托</h3>
                <el-button @click="refreshOrders" :loading="loadingOrders">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
              
              <el-table :data="orders" stripe>
                <el-table-column prop="orderTime" label="委托时间" width="120" />
                <el-table-column prop="stockCode" label="证券代码" width="100" />
                <el-table-column prop="stockName" label="证券名称" width="120" />
                <el-table-column prop="side" label="买卖方向" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.side === 'buy' ? 'danger' : 'success'">
                      {{ row.side === 'buy' ? '买入' : '卖出' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="委托数量" width="100" />
                <el-table-column prop="price" label="委托价格" width="100" />
                <el-table-column prop="status" label="委托状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getOrderStatusType(row.status)">
                      {{ getOrderStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
                      v-if="row.status === 'pending'"
                      type="text"
                      size="small"
                      @click="cancelOrder(row)"
                    >
                      撤单
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="持仓查询" name="positions">
            <div class="positions-content">
              <div class="positions-header">
                <h3>当前持仓</h3>
                <el-button @click="refreshPositions" :loading="loadingPositions">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
              
              <el-table :data="positions" stripe>
                <el-table-column prop="stockCode" label="证券代码" width="100" />
                <el-table-column prop="stockName" label="证券名称" width="120" />
                <el-table-column prop="totalQuantity" label="总持仓" width="100" />
                <el-table-column prop="availableQuantity" label="可用数量" width="100" />
                <el-table-column prop="avgCost" label="成本价" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.avgCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="currentPrice" label="现价" width="100">
                  <template #default="{ row }">
                    ¥{{ formatPrice(row.currentPrice) }}
                  </template>
                </el-table-column>
                <el-table-column prop="marketValue" label="市值" width="120">
                  <template #default="{ row }">
                    ¥{{ formatMoney(row.marketValue) }}
                  </template>
                </el-table-column>
                <el-table-column prop="pnl" label="盈亏" width="120">
                  <template #default="{ row }">
                    <span :class="row.pnl >= 0 ? 'profit' : 'loss'">
                      {{ row.pnl >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(row.pnl)) }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 连接设置对话框 -->
    <el-dialog v-model="showSettings" title="MiniQMT连接设置" width="500px">
      <el-form :model="settings" label-width="100px">
        <el-form-item label="服务器地址">
          <el-input v-model="settings.host" placeholder="localhost" />
        </el-form-item>
        <el-form-item label="端口">
          <el-input-number v-model="settings.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        <el-form-item label="账户ID">
          <el-input v-model="settings.accountId" placeholder="请输入账户ID" />
        </el-form-item>
        <el-form-item label="超时时间">
          <el-input-number v-model="settings.timeout" :min="1000" :max="30000" style="width: 100%" />
          <span style="margin-left: 8px; color: #909399;">毫秒</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Link, User, Setting, Warning, Search, Refresh } from '@element-plus/icons-vue'

// 响应式数据
const connectionStatus = ref<'connected' | 'disconnected' | 'connecting'>('disconnected')
const connecting = ref(false)
const showSettings = ref(false)
const activeTab = ref('trading')

const accountInfo = reactive({
  accountId: 'A123456789',
  availableFunds: 100000.00
})

const settings = reactive({
  host: 'localhost',
  port: 58610,
  accountId: 'A123456789',
  timeout: 5000
})

const stockCode = ref('')
const loadingStock = ref(false)
const currentStock = ref(null)

const orderForm = reactive({
  side: 'buy',
  orderType: 'limit',
  quantity: 100,
  price: 0
})

const submittingOrder = ref(false)
const loadingOrders = ref(false)
const loadingPositions = ref(false)

const orders = ref([])
const positions = ref([])

// 计算属性
const formatPrice = (price: number) => price.toFixed(2)
const formatMoney = (amount: number) => amount.toFixed(2)

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    filled: 'success',
    cancelled: 'info',
    rejected: 'danger'
  }
  return statusMap[status] || 'default'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待成交',
    filled: '已成交',
    cancelled: '已撤销',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

// 方法
const connectMiniQMT = async () => {
  connecting.value = true
  connectionStatus.value = 'connecting'
  
  try {
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 这里应该调用实际的MiniQMT API连接
    connectionStatus.value = 'connected'
    ElMessage.success('MiniQMT连接成功')
    
    // 连接成功后刷新数据
    await Promise.all([
      refreshOrders(),
      refreshPositions()
    ])
    
  } catch (error) {
    connectionStatus.value = 'disconnected'
    ElMessage.error('MiniQMT连接失败')
  } finally {
    connecting.value = false
  }
}

const loadStockInfo = async () => {
  if (!stockCode.value.trim()) {
    ElMessage.warning('请输入股票代码')
    return
  }
  
  loadingStock.value = true
  
  try {
    // 模拟股票信息加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    currentStock.value = {
      code: stockCode.value,
      name: '测试股票',
      currentPrice: 12.45,
      changePercent: 1.88
    }
    
    orderForm.price = currentStock.value.currentPrice
    
  } catch (error) {
    ElMessage.error('股票信息加载失败')
  } finally {
    loadingStock.value = false
  }
}

const submitOrder = async () => {
  if (!currentStock.value) {
    ElMessage.warning('请先选择股票')
    return
  }
  
  submittingOrder.value = true
  
  try {
    // 模拟订单提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('订单提交成功')
    
    // 刷新委托查询
    await refreshOrders()
    
  } catch (error) {
    ElMessage.error('订单提交失败')
  } finally {
    submittingOrder.value = false
  }
}

const refreshOrders = async () => {
  loadingOrders.value = true
  
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500))
    
    orders.value = [
      {
        orderTime: '09:30:15',
        stockCode: '000001',
        stockName: '平安银行',
        side: 'buy',
        quantity: 100,
        price: 12.45,
        status: 'pending'
      }
    ]
    
  } catch (error) {
    ElMessage.error('委托查询失败')
  } finally {
    loadingOrders.value = false
  }
}

const refreshPositions = async () => {
  loadingPositions.value = true
  
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500))
    
    positions.value = [
      {
        stockCode: '000001',
        stockName: '平安银行',
        totalQuantity: 1000,
        availableQuantity: 1000,
        avgCost: 12.30,
        currentPrice: 12.45,
        marketValue: 12450,
        pnl: 150
      }
    ]
    
  } catch (error) {
    ElMessage.error('持仓查询失败')
  } finally {
    loadingPositions.value = false
  }
}

const cancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm('确定要撤销这个委托吗？', '撤单确认')
    
    // 模拟撤单
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('撤单成功')
    await refreshOrders()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤单失败')
    }
  }
}

const saveSettings = () => {
  ElMessage.success('设置已保存')
  showSettings.value = false
}

onMounted(() => {
  console.log('MiniQMT交易页面初始化完成')
})
</script>

<style scoped lang="scss">
.miniqmt-trading {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.status-bar {
  background: white;
  padding: 12px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .connection-status {
    .el-tag {
      .el-icon {
        margin-right: 6px;
      }
    }
  }
  
  .account-status {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .funds {
      color: #67c23a;
      font-weight: 600;
    }
  }
  
  .actions {
    display: flex;
    gap: 12px;
  }
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow: auto;
}

.connection-guide {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  
  .guide-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    max-width: 600px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    
    .guide-icon {
      font-size: 64px;
      color: #f56c6c;
      margin-bottom: 24px;
    }
    
    h2 {
      margin: 0 0 32px 0;
      color: #303133;
    }
    
    .guide-steps {
      display: flex;
      flex-direction: column;
      gap: 24px;
      margin-bottom: 32px;
      
      .step {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        text-align: left;
        
        .step-number {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #409eff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          flex-shrink: 0;
        }
        
        .step-content {
          h4 {
            margin: 0 0 8px 0;
            color: #303133;
          }
          
          p {
            margin: 0;
            color: #606266;
            line-height: 1.6;
          }
        }
      }
    }
    
    .guide-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
    }
  }
}

.trading-interface {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  
  .trading-content {
    padding: 20px;
    
    .trading-panels {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 24px;
      height: 500px;
      
      .left-panel {
        .stock-selector {
          margin-bottom: 20px;
        }
        
        .stock-info {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          
          .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            
            h3 {
              margin: 0;
              color: #303133;
            }
          }
          
          .price-display {
            .current-price {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 8px;
              
              &.price-up { color: #f56c6c; }
              &.price-down { color: #67c23a; }
              &.price-neutral { color: #909399; }
            }
            
            .price-change {
              font-size: 16px;
              font-weight: 600;
              
              span {
                &.price-up { color: #f56c6c; }
                &.price-down { color: #67c23a; }
                &.price-neutral { color: #909399; }
              }
            }
          }
        }
      }
      
      .right-panel {
        .trading-form {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
        }
      }
    }
  }
  
  .orders-content,
  .positions-content {
    padding: 20px;
    
    .orders-header,
    .positions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        color: #303133;
      }
    }
    
    .profit {
      color: #f56c6c;
    }
    
    .loss {
      color: #67c23a;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .trading-panels {
    grid-template-columns: 1fr !important;
    grid-template-rows: auto 1fr;
    height: auto !important;
  }
}

@media (max-width: 768px) {
  .status-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    
    .account-status {
      justify-content: center;
    }
    
    .actions {
      justify-content: center;
    }
  }
  
  .main-content {
    padding: 16px;
  }
}
</style>
