"""
回测API接口
提供回测相关的RESTful API
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import J<PERSON>NResponse
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime, date
import asyncio
import uuid
import pandas as pd

from ..backtest.vectorized_engine import VectorizedBacktestEngine, BacktestConfig
from ..strategies.base_strategy import StrategyConfig, StrategyType
from ..risk_management import RiskManager, RiskLimits
from ..analysis.report_generator import ReportGenerator
from .models import BacktestRequest, BacktestResponse, BacktestStatus

router = APIRouter(prefix="/api/v1/backtest", tags=["backtest"])

# 全局存储正在运行的回测任务
running_backtests: Dict[str, Dict[str, Any]] = {}

class BacktestCreateRequest(BaseModel):
    """创建回测请求"""
    strategy_config: Dict[str, Any] = Field(..., description="策略配置")
    backtest_config: Dict[str, Any] = Field(..., description="回测配置")
    risk_limits: Optional[Dict[str, Any]] = Field(None, description="风险限制")
    data_source: str = Field("akshare", description="数据源")
    
class BacktestCreateResponse(BaseModel):
    """创建回测响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="消息")
    
class BacktestResultResponse(BaseModel):
    """回测结果响应"""
    task_id: str
    status: str
    progress: float
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None

@router.post("/create", response_model=BacktestCreateResponse)
async def create_backtest(
    request: BacktestCreateRequest,
    background_tasks: BackgroundTasks
):
    """创建新的回测任务"""
    try:
        task_id = str(uuid.uuid4())
        
        # 验证配置
        strategy_config = StrategyConfig(**request.strategy_config)
        backtest_config = BacktestConfig(**request.backtest_config)
        
        # 初始化任务状态
        running_backtests[task_id] = {
            'status': 'created',
            'progress': 0.0,
            'created_at': datetime.now(),
            'strategy_config': strategy_config,
            'backtest_config': backtest_config,
            'risk_limits': request.risk_limits,
            'data_source': request.data_source,
            'results': None,
            'error': None
        }
        
        # 启动后台回测任务
        background_tasks.add_task(run_backtest_task, task_id)
        
        return BacktestCreateResponse(
            task_id=task_id,
            status='created',
            message='回测任务已创建并开始执行'
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建回测任务失败: {str(e)}")

@router.get("/status/{task_id}", response_model=BacktestResultResponse)
async def get_backtest_status(task_id: str):
    """获取回测任务状态"""
    if task_id not in running_backtests:
        raise HTTPException(status_code=404, detail="回测任务不存在")
    
    task_info = running_backtests[task_id]
    
    return BacktestResultResponse(
        task_id=task_id,
        status=task_info['status'],
        progress=task_info['progress'],
        results=task_info.get('results'),
        error=task_info.get('error'),
        created_at=task_info['created_at'],
        completed_at=task_info.get('completed_at')
    )

@router.get("/list")
async def list_backtests():
    """列出所有回测任务"""
    tasks = []
    for task_id, task_info in running_backtests.items():
        tasks.append({
            'task_id': task_id,
            'status': task_info['status'],
            'progress': task_info['progress'],
            'created_at': task_info['created_at'],
            'strategy_name': task_info['strategy_config'].name,
            'strategy_type': task_info['strategy_config'].strategy_type.value
        })
    
    return {'tasks': sorted(tasks, key=lambda x: x['created_at'], reverse=True)}

@router.delete("/cancel/{task_id}")
async def cancel_backtest(task_id: str):
    """取消回测任务"""
    if task_id not in running_backtests:
        raise HTTPException(status_code=404, detail="回测任务不存在")
    
    task_info = running_backtests[task_id]
    if task_info['status'] in ['completed', 'failed', 'cancelled']:
        raise HTTPException(status_code=400, detail="任务已结束，无法取消")
    
    task_info['status'] = 'cancelled'
    task_info['completed_at'] = datetime.now()
    
    return {'message': '任务已取消'}

@router.delete("/delete/{task_id}")  
async def delete_backtest(task_id: str):
    """删除回测任务"""
    if task_id not in running_backtests:
        raise HTTPException(status_code=404, detail="回测任务不存在")
    
    del running_backtests[task_id]
    return {'message': '任务已删除'}

@router.get("/results/{task_id}")
async def get_backtest_results(task_id: str):
    """获取回测详细结果"""
    if task_id not in running_backtests:
        raise HTTPException(status_code=404, detail="回测任务不存在")
    
    task_info = running_backtests[task_id]
    
    if task_info['status'] != 'completed':
        raise HTTPException(status_code=400, detail="回测任务尚未完成")
    
    if not task_info.get('results'):
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    return task_info['results']

@router.get("/report/{task_id}")
async def get_backtest_report(task_id: str, format: str = 'json'):
    """获取回测报告"""
    if task_id not in running_backtests:
        raise HTTPException(status_code=404, detail="回测任务不存在")
    
    task_info = running_backtests[task_id]
    
    if task_info['status'] != 'completed':
        raise HTTPException(status_code=400, detail="回测任务尚未完成")
    
    results = task_info.get('results')
    if not results:
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    try:
        # 生成报告
        report_generator = ReportGenerator({'risk_free_rate': 0.02})
        
        portfolio_data = pd.Series(results['portfolio_value'])
        portfolio_data.index = pd.to_datetime(results['dates'])
        
        benchmark_data = None
        if 'benchmark_value' in results:
            benchmark_data = pd.Series(results['benchmark_value'])
            benchmark_data.index = pd.to_datetime(results['dates'])
        
        trades_df = None
        if 'trades' in results:
            trades_df = pd.DataFrame(results['trades'])
        
        report = report_generator.generate_backtest_report(
            portfolio_data=portfolio_data,
            benchmark_data=benchmark_data,
            trades=trades_df,
            positions=results.get('positions'),
            strategy_info=task_info['strategy_config'].__dict__
        )
        
        if format.lower() == 'html':
            html_content = report_generator.export_report_html(report)
            return JSONResponse(
                content={'html': html_content},
                media_type="application/json"
            )
        else:  # JSON格式
            json_content = report_generator.export_report_json(report)
            return JSONResponse(
                content={'report': json_content},
                media_type="application/json"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")

@router.post("/compare")
async def compare_backtests(task_ids: List[str]):
    """比较多个回测结果"""
    if len(task_ids) < 2:
        raise HTTPException(status_code=400, detail="至少需要2个回测任务进行比较")
    
    comparison_data = []
    
    for task_id in task_ids:
        if task_id not in running_backtests:
            raise HTTPException(status_code=404, detail=f"回测任务 {task_id} 不存在")
        
        task_info = running_backtests[task_id]
        if task_info['status'] != 'completed':
            raise HTTPException(status_code=400, detail=f"回测任务 {task_id} 尚未完成")
        
        results = task_info.get('results')
        if not results:
            continue
            
        # 提取关键指标用于比较
        comparison_data.append({
            'task_id': task_id,
            'strategy_name': task_info['strategy_config'].name,
            'total_return': results.get('total_return', 0),
            'annual_return': results.get('annual_return', 0),
            'sharpe_ratio': results.get('sharpe_ratio', 0),
            'max_drawdown': results.get('max_drawdown', 0),
            'win_rate': results.get('win_rate', 0),
            'profit_factor': results.get('profit_factor', 0)
        })
    
    if not comparison_data:
        raise HTTPException(status_code=400, detail="没有有效的回测结果可供比较")
    
    # 排序比较结果
    comparison_data.sort(key=lambda x: x['sharpe_ratio'], reverse=True)
    
    return {
        'comparison': comparison_data,
        'best_strategy': comparison_data[0] if comparison_data else None,
        'metrics_comparison': {
            'best_return': max(comparison_data, key=lambda x: x['total_return']),
            'best_sharpe': max(comparison_data, key=lambda x: x['sharpe_ratio']),
            'lowest_drawdown': min(comparison_data, key=lambda x: x['max_drawdown'])
        }
    }

async def run_backtest_task(task_id: str):
    """执行回测任务的后台函数"""
    task_info = running_backtests[task_id]
    
    try:
        task_info['status'] = 'running'
        task_info['progress'] = 0.1
        
        # 初始化回测引擎
        engine = VectorizedBacktestEngine(task_info['backtest_config'])
        
        # 设置风险管理器
        if task_info['risk_limits']:
            risk_limits = RiskLimits(**task_info['risk_limits'])
            risk_manager = RiskManager(risk_limits)
            engine.set_risk_manager(risk_manager)
        
        task_info['progress'] = 0.2
        
        # 创建策略实例（这里需要根据策略类型动态创建）
        # 简化版本，实际需要策略工厂
        strategy_config = task_info['strategy_config']
        
        # 模拟策略创建过程
        task_info['progress'] = 0.3
        
        # 准备数据
        # 这里需要根据data_source从不同数据源获取数据
        # 简化版本，使用模拟数据
        
        task_info['progress'] = 0.5
        
        # 运行回测
        # results = engine.run_backtest(strategy, data)
        
        # 模拟回测执行
        await asyncio.sleep(2)  # 模拟计算时间
        
        task_info['progress'] = 0.8
        
        # 生成模拟结果
        results = {
            'total_return': 0.15,
            'annual_return': 0.12,
            'sharpe_ratio': 1.2,
            'max_drawdown': 0.08,
            'win_rate': 0.65,
            'profit_factor': 1.8,
            'portfolio_value': [100000 * (1 + i * 0.001) for i in range(252)],
            'dates': pd.date_range('2023-01-01', periods=252, freq='D').strftime('%Y-%m-%d').tolist(),
            'trades': [],
            'positions': {}
        }
        
        task_info['results'] = results
        task_info['status'] = 'completed'
        task_info['progress'] = 1.0
        task_info['completed_at'] = datetime.now()
        
    except Exception as e:
        task_info['status'] = 'failed'
        task_info['error'] = str(e)
        task_info['completed_at'] = datetime.now()

@router.get("/strategies")
async def list_available_strategies():
    """列出可用的策略"""
    strategies = []
    
    for strategy_type in StrategyType:
        strategies.append({
            'type': strategy_type.value,
            'name': strategy_type.name,
            'description': f'{strategy_type.value} 策略'
        })
    
    return {'strategies': strategies}

@router.get("/config/template")
async def get_config_template(strategy_type: str):
    """获取策略配置模板"""
    templates = {
        'momentum': {
            'strategy_config': {
                'name': '动量策略',
                'description': '基于价格动量的交易策略',
                'strategy_type': 'momentum',
                'parameters': {
                    'lookback_period': 20,
                    'momentum_threshold': 0.02
                },
                'max_positions': 10,
                'position_size': 0.1
            },
            'backtest_config': {
                'initial_capital': 1000000,
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'benchmark': '000300.SH',
                'slippage_type': 'percentage',
                'slippage_value': 0.001,
                'commission_rate': 0.0003
            },
            'risk_limits': {
                'max_position_size': 0.1,
                'max_drawdown': 0.2,
                'max_daily_loss': 0.05
            }
        },
        'mean_reversion': {
            'strategy_config': {
                'name': '均值回归策略',
                'description': '基于价格均值回归的交易策略',
                'strategy_type': 'mean_reversion',
                'parameters': {
                    'lookback_period': 20,
                    'std_multiplier': 2.0
                },
                'max_positions': 15,
                'position_size': 0.08
            },
            'backtest_config': {
                'initial_capital': 1000000,
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'benchmark': '000300.SH',
                'slippage_type': 'percentage',
                'slippage_value': 0.001,
                'commission_rate': 0.0003
            },
            'risk_limits': {
                'max_position_size': 0.08,
                'max_drawdown': 0.15,
                'max_daily_loss': 0.03
            }
        }
    }
    
    if strategy_type not in templates:
        raise HTTPException(status_code=404, detail="策略类型不存在")
    
    return templates[strategy_type]