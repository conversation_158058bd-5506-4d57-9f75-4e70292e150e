#!/usr/bin/env python3
"""
策略详情页面链接审计 - 使用MCP工具检测所有链接问题
专门检测 http://localhost:5173/strategy/detail/mock-1 页面中的链接状态
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StrategyLinksAudit:
    def __init__(self):
        self.session_id = f"strategy_audit_{int(time.time())}"
        self.browser = None
        self.page = None
        self.audit_results = {
            'session_id': self.session_id,
            'start_time': datetime.now().isoformat(),
            'page_url': 'http://localhost:5173/strategy/detail/mock-1',
            'total_links': 0,
            'working_links': 0,
            'broken_links': 0,
            'empty_links': 0,
            'link_details': [],
            'issues_found': [],
            'recommendations': []
        }
        
    async def initialize_mcp_browser(self):
        """初始化MCP浏览器工具"""
        logger.info("🔧 初始化MCP浏览器工具...")
        
        # 创建测试目录
        Path('screenshots').mkdir(exist_ok=True)
        Path('reports').mkdir(exist_ok=True)
        
        # 启动浏览器 (模拟BrowserTools MCP)
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        # 设置视口大小
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
        logger.info("✅ MCP浏览器工具初始化完成")

    async def navigate_to_strategy_page(self):
        """导航到策略详情页面"""
        logger.info("🌐 导航到策略详情页面...")
        
        try:
            await self.page.goto(self.audit_results['page_url'], wait_until='networkidle', timeout=30000)
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 截图记录初始状态
            await self.page.screenshot(path=f'screenshots/{self.session_id}_initial_page.png', full_page=True)
            
            logger.info("✅ 成功导航到策略详情页面")
            return True
            
        except Exception as e:
            logger.error(f"❌ 页面导航失败: {e}")
            return False

    async def find_all_clickable_elements(self):
        """查找页面中所有可点击的元素"""
        logger.info("🔍 查找所有可点击元素...")
        
        # 定义可点击元素的选择器
        clickable_selectors = [
            'a[href]',  # 所有带href的链接
            'button',   # 所有按钮
            '[role="button"]',  # 角色为按钮的元素
            '.el-button',  # Element Plus按钮
            '[onclick]',   # 带onclick事件的元素
            'div[class*="card"]',  # 卡片类元素
            'div[class*="item"]',  # 项目类元素
            'div[class*="clickable"]',  # 明确标记为可点击的元素
        ]
        
        all_elements = []
        
        for selector in clickable_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    # 获取元素信息
                    element_info = await self.get_element_info(element, selector)
                    if element_info:
                        all_elements.append(element_info)
                        
            except Exception as e:
                logger.warning(f"⚠️ 查找选择器 {selector} 时出错: {e}")
        
        # 去重（基于位置和文本）
        unique_elements = self.deduplicate_elements(all_elements)
        
        logger.info(f"📊 找到 {len(unique_elements)} 个唯一的可点击元素")
        return unique_elements

    async def get_element_info(self, element, selector_type):
        """获取元素的详细信息"""
        try:
            # 检查元素是否可见
            is_visible = await element.is_visible()
            if not is_visible:
                return None
            
            # 获取元素位置
            bounding_box = await element.bounding_box()
            if not bounding_box:
                return None
            
            # 获取元素属性
            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
            href = await element.get_attribute('href')
            onclick = await element.get_attribute('onclick')
            class_name = await element.get_attribute('class')
            text_content = await element.text_content()
            
            # 获取元素的唯一标识
            element_id = await element.get_attribute('id')
            
            return {
                'selector_type': selector_type,
                'tag_name': tag_name,
                'href': href,
                'onclick': onclick,
                'class_name': class_name or '',
                'text_content': (text_content or '').strip()[:100],  # 限制文本长度
                'element_id': element_id,
                'position': {
                    'x': bounding_box['x'],
                    'y': bounding_box['y'],
                    'width': bounding_box['width'],
                    'height': bounding_box['height']
                },
                'is_visible': is_visible
            }
            
        except Exception as e:
            logger.warning(f"⚠️ 获取元素信息时出错: {e}")
            return None

    def deduplicate_elements(self, elements):
        """去除重复的元素"""
        unique_elements = []
        seen_positions = set()
        
        for element in elements:
            # 使用位置和文本内容作为唯一标识
            position_key = (
                round(element['position']['x']),
                round(element['position']['y']),
                element['text_content'][:50]
            )
            
            if position_key not in seen_positions:
                seen_positions.add(position_key)
                unique_elements.append(element)
        
        return unique_elements

    async def test_element_click(self, element_info, index):
        """测试单个元素的点击行为"""
        logger.info(f"🖱️ 测试元素 {index + 1}: {element_info['text_content'][:30]}...")
        
        test_result = {
            'index': index + 1,
            'element_info': element_info,
            'test_status': 'unknown',
            'error_message': None,
            'navigation_result': None,
            'screenshot_path': None
        }
        
        try:
            # 记录当前URL
            current_url = self.page.url
            
            # 尝试点击元素
            if element_info['href']:
                # 如果是链接，直接导航
                await self.page.goto(element_info['href'], wait_until='networkidle', timeout=10000)
                test_result['test_status'] = 'navigation_attempted'
                
            else:
                # 如果不是链接，尝试点击
                await self.page.click(
                    f"text={element_info['text_content']}" if element_info['text_content'] 
                    else f".{element_info['class_name'].split()[0]}" if element_info['class_name']
                    else element_info['selector_type'],
                    timeout=5000
                )
                test_result['test_status'] = 'click_attempted'
            
            # 等待页面响应
            await asyncio.sleep(2)
            
            # 检查导航结果
            new_url = self.page.url
            
            if new_url != current_url:
                test_result['navigation_result'] = {
                    'from_url': current_url,
                    'to_url': new_url,
                    'navigation_successful': True
                }
                
                # 检查新页面是否有内容
                page_content = await self.page.content()
                if len(page_content) < 1000:  # 页面内容太少可能是空页面
                    test_result['test_status'] = 'empty_page'
                    self.audit_results['empty_links'] += 1
                    
                    self.audit_results['issues_found'].append({
                        'type': 'empty_page',
                        'severity': 'medium',
                        'element': element_info,
                        'message': f'链接指向空页面或内容很少的页面: {new_url}'
                    })
                else:
                    test_result['test_status'] = 'working'
                    self.audit_results['working_links'] += 1
                
                # 截图记录
                screenshot_path = f'screenshots/{self.session_id}_element_{index + 1}_result.png'
                await self.page.screenshot(path=screenshot_path)
                test_result['screenshot_path'] = screenshot_path
                
                # 返回原页面
                await self.page.goto(self.audit_results['page_url'], wait_until='networkidle')
                await asyncio.sleep(1)
                
            else:
                test_result['test_status'] = 'no_navigation'
                test_result['navigation_result'] = {
                    'from_url': current_url,
                    'to_url': new_url,
                    'navigation_successful': False
                }
            
        except Exception as e:
            test_result['test_status'] = 'error'
            test_result['error_message'] = str(e)
            self.audit_results['broken_links'] += 1
            
            self.audit_results['issues_found'].append({
                'type': 'click_error',
                'severity': 'high',
                'element': element_info,
                'message': f'点击元素时发生错误: {str(e)}'
            })
            
            logger.warning(f"⚠️ 元素点击测试失败: {e}")
        
        return test_result

    async def audit_all_links(self):
        """审计所有链接"""
        logger.info("🔍 开始全面链接审计...")
        
        # 查找所有可点击元素
        clickable_elements = await self.find_all_clickable_elements()
        self.audit_results['total_links'] = len(clickable_elements)
        
        if not clickable_elements:
            logger.warning("⚠️ 未找到任何可点击元素")
            return
        
        # 测试每个元素
        for index, element in enumerate(clickable_elements):
            test_result = await self.test_element_click(element, index)
            self.audit_results['link_details'].append(test_result)
            
            # 显示进度
            progress = (index + 1) / len(clickable_elements) * 100
            logger.info(f"📈 测试进度: {progress:.1f}% ({index + 1}/{len(clickable_elements)})")
        
        logger.info("✅ 链接审计完成")

    async def generate_audit_report(self):
        """生成审计报告"""
        logger.info("📋 生成审计报告...")
        
        # 计算统计信息
        total = self.audit_results['total_links']
        working = self.audit_results['working_links']
        broken = self.audit_results['broken_links']
        empty = self.audit_results['empty_links']
        
        # 生成建议
        recommendations = []
        
        if broken > 0:
            recommendations.append(f"🚨 发现 {broken} 个无法点击的元素，需要修复点击事件")
        
        if empty > 0:
            recommendations.append(f"⚠️ 发现 {empty} 个指向空页面的链接，需要添加内容或移除链接")
        
        if working / total < 0.8 if total > 0 else False:
            recommendations.append("💡 建议检查页面的整体链接质量，成功率偏低")
        
        recommendations.extend([
            "💡 建议为所有可点击元素添加明确的视觉反馈",
            "💡 建议为空链接添加占位页面或禁用状态",
            "💡 建议添加链接状态的自动化测试"
        ])
        
        self.audit_results['recommendations'] = recommendations
        self.audit_results['end_time'] = datetime.now().isoformat()
        
        # 保存详细报告
        report_path = f'reports/strategy_links_audit_{self.session_id}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.audit_results, f, ensure_ascii=False, indent=2)
        
        # 打印报告摘要
        print("\n" + "="*80)
        print("🔍 策略详情页面链接审计报告")
        print("="*80)
        print(f"📄 页面URL: {self.audit_results['page_url']}")
        print(f"🔗 总链接数: {total}")
        print(f"✅ 正常工作: {working}")
        print(f"❌ 无法点击: {broken}")
        print(f"📄 空页面: {empty}")
        print(f"📊 成功率: {(working/total*100):.1f}%" if total > 0 else "📊 成功率: N/A")
        
        if self.audit_results['issues_found']:
            print(f"\n🚨 发现的问题:")
            for i, issue in enumerate(self.audit_results['issues_found'], 1):
                print(f"  {i}. [{issue['severity'].upper()}] {issue['message']}")
                if issue['element']['text_content']:
                    print(f"     元素文本: {issue['element']['text_content'][:50]}")
                if issue['element']['href']:
                    print(f"     链接地址: {issue['element']['href']}")
        
        print(f"\n💡 改进建议:")
        for rec in recommendations:
            print(f"  {rec}")
        
        print(f"\n📋 详细报告已保存: {report_path}")
        
        logger.info(f"📋 审计报告生成完成: {report_path}")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        logger.info("🧹 资源清理完成")

    async def run_audit(self):
        """运行完整的链接审计"""
        logger.info("🚀 开始策略详情页面链接审计")
        
        try:
            await self.initialize_mcp_browser()
            
            if await self.navigate_to_strategy_page():
                await self.audit_all_links()
                await self.generate_audit_report()
            else:
                logger.error("❌ 无法访问策略详情页面，审计终止")
                
        except Exception as e:
            logger.error(f"❌ 审计过程中发生错误: {e}")
        
        finally:
            await self.cleanup()
        
        logger.info("✅ 策略详情页面链接审计完成")

async def main():
    """主函数"""
    auditor = StrategyLinksAudit()
    await auditor.run_audit()

if __name__ == "__main__":
    asyncio.run(main())
