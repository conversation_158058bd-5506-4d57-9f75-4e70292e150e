#!/usr/bin/env python3
"""
增强版量化交易平台深度功能分析
专门检查拼图验证页面的实现细节
"""

import asyncio
import logging
import time
from datetime import datetime
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedPlatformAnalyzer:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.api_url = "http://localhost:8000"
        self.browser = None
        self.page = None
        self.analysis_results = {
            "login_flow": {},
            "puzzle_page": {},
            "backend_apis": {},
            "issues": []
        }
        
    async def setup_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        logger.info("🚀 浏览器初始化完成")

    async def deep_analyze_puzzle_page(self):
        """深度分析拼图验证页面"""
        logger.info("🔍 深度分析拼图验证页面...")
        
        try:
            # 1. 登录并跳转到拼图页面
            await self.page.goto(f"{self.base_url}/login")
            await self.page.wait_for_load_state('networkidle')
            
            # 清除存储
            await self.page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
            await self.page.reload()
            await self.page.wait_for_load_state('networkidle')
            
            # 点击演示登录
            demo_btn = await self.page.query_selector('button:has-text("演示登录")')
            if demo_btn:
                await demo_btn.click()
                await asyncio.sleep(3)
                
                current_url = self.page.url
                if 'puzzle-verify' in current_url:
                    logger.info("✅ 成功跳转到拼图验证页面")
                    self.analysis_results["login_flow"]["redirect_success"] = True
                    
                    # 2. 详细分析页面结构
                    await self.analyze_page_structure()
                    
                    # 3. 检查Vue组件状态
                    await self.check_vue_component_state()
                    
                    # 4. 测试拼图功能
                    await self.test_puzzle_functionality()
                    
                else:
                    logger.error("❌ 未能跳转到拼图验证页面")
                    self.analysis_results["login_flow"]["redirect_success"] = False
            else:
                logger.error("❌ 未找到演示登录按钮")
                
        except Exception as e:
            logger.error(f"❌ 拼图页面分析失败: {e}")
            self.analysis_results["issues"].append(str(e))

    async def analyze_page_structure(self):
        """分析页面结构"""
        logger.info("🔍 分析页面DOM结构...")
        
        try:
            # 等待页面完全渲染
            await asyncio.sleep(2)
            
            # 获取页面基本信息
            title = await self.page.title()
            url = self.page.url
            
            logger.info(f"   - 页面标题: {title}")
            logger.info(f"   - 页面URL: {url}")
            
            # 检查主要容器
            containers = [
                '.puzzle-verify-container',
                '.verification-container', 
                '.puzzle-container',
                '.el-container',
                'main',
                '.app'
            ]
            
            for container in containers:
                element = await self.page.query_selector(container)
                logger.info(f"   - {container}: {'存在' if element else '不存在'}")
            
            # 检查Canvas元素
            canvas_elements = await self.page.query_selector_all('canvas')
            logger.info(f"   - Canvas元素数量: {len(canvas_elements)}")
            
            for i, canvas in enumerate(canvas_elements):
                width = await canvas.get_attribute('width')
                height = await canvas.get_attribute('height')
                logger.info(f"     Canvas {i+1}: {width}x{height}")
            
            # 检查所有可能的拖拽相关元素
            drag_selectors = [
                '.drag-piece',
                '.puzzle-piece', 
                '.slider-piece',
                '.drag-block',
                '.draggable',
                '[draggable="true"]',
                '.piece'
            ]
            
            found_drag_elements = []
            for selector in drag_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    found_drag_elements.append(f"{selector}: {len(elements)}个")
                    logger.info(f"   - {selector}: {len(elements)}个元素")
            
            self.analysis_results["puzzle_page"]["drag_elements"] = found_drag_elements
            
            # 检查按钮
            buttons = await self.page.query_selector_all('button')
            logger.info(f"   - 按钮总数: {len(buttons)}")
            
            for button in buttons:
                text = await button.inner_text()
                if text.strip():
                    logger.info(f"     按钮文本: '{text.strip()}'")
            
        except Exception as e:
            logger.error(f"❌ 页面结构分析失败: {e}")

    async def check_vue_component_state(self):
        """检查Vue组件状态"""
        logger.info("🔍 检查Vue组件状态...")
        
        try:
            # 检查Vue实例
            vue_info = await self.page.evaluate("""
                () => {
                    if (window.Vue || window.__VUE__) {
                        return {
                            vue_detected: true,
                            vue_version: window.Vue?.version || 'unknown'
                        };
                    }
                    return { vue_detected: false };
                }
            """)
            
            logger.info(f"   - Vue检测: {vue_info}")
            
            # 检查组件数据
            component_data = await self.page.evaluate("""
                () => {
                    const app = document.querySelector('#app');
                    if (app && app.__vue__) {
                        return {
                            component_found: true,
                            data_keys: Object.keys(app.__vue__.$data || {})
                        };
                    }
                    return { component_found: false };
                }
            """)
            
            logger.info(f"   - 组件数据: {component_data}")
            
            # 检查控制台错误
            console_logs = []
            def handle_console(msg):
                console_logs.append(f"{msg.type}: {msg.text}")
            
            self.page.on("console", handle_console)
            await asyncio.sleep(1)
            
            if console_logs:
                logger.info("   - 控制台消息:")
                for log in console_logs[-5:]:  # 只显示最后5条
                    logger.info(f"     {log}")
            
        except Exception as e:
            logger.error(f"❌ Vue组件状态检查失败: {e}")

    async def test_puzzle_functionality(self):
        """测试拼图功能"""
        logger.info("🔍 测试拼图功能...")
        
        try:
            # 等待拼图加载
            await asyncio.sleep(3)
            
            # 尝试多种方式找到拖拽元素
            drag_element = None
            drag_selectors = [
                '.drag-piece',
                '.puzzle-piece',
                '.slider-piece', 
                '.draggable',
                '[draggable="true"]'
            ]
            
            for selector in drag_selectors:
                element = await self.page.query_selector(selector)
                if element:
                    drag_element = element
                    logger.info(f"✅ 找到拖拽元素: {selector}")
                    break
            
            if drag_element:
                # 获取元素位置和大小
                box = await drag_element.bounding_box()
                if box:
                    logger.info(f"   - 拖拽元素位置: x={box['x']}, y={box['y']}")
                    logger.info(f"   - 拖拽元素大小: w={box['width']}, h={box['height']}")
                    
                    # 尝试拖拽
                    start_x = box['x'] + box['width'] / 2
                    start_y = box['y'] + box['height'] / 2
                    end_x = start_x + 150
                    end_y = start_y
                    
                    logger.info(f"   - 开始拖拽: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
                    
                    await self.page.mouse.move(start_x, start_y)
                    await self.page.mouse.down()
                    await self.page.mouse.move(end_x, end_y, steps=10)
                    await self.page.mouse.up()
                    
                    await asyncio.sleep(2)
                    
                    # 检查拖拽结果
                    success_indicators = [
                        '.success-message',
                        '.el-message--success',
                        '.verification-success',
                        'text="验证成功"',
                        'text="拼图完成"'
                    ]
                    
                    for indicator in success_indicators:
                        element = await self.page.query_selector(indicator)
                        if element:
                            logger.info(f"✅ 发现成功指示器: {indicator}")
                            self.analysis_results["puzzle_page"]["drag_success"] = True
                            return
                    
                    logger.info("⚠️ 拖拽完成但未发现明确的成功指示器")
                    self.analysis_results["puzzle_page"]["drag_completed"] = True
                    
            else:
                logger.warning("⚠️ 未找到任何拖拽元素")
                self.analysis_results["puzzle_page"]["drag_element_found"] = False
                
                # 尝试点击继续按钮
                continue_btn = await self.page.query_selector('button:has-text("继续访问")')
                if continue_btn:
                    logger.info("✅ 找到继续访问按钮，尝试点击")
                    await continue_btn.click()
                    await asyncio.sleep(2)
                    
                    new_url = self.page.url
                    if new_url != self.page.url:
                        logger.info(f"✅ 点击继续按钮后跳转到: {new_url}")
                        self.analysis_results["puzzle_page"]["continue_button_works"] = True
                    else:
                        logger.info("⚠️ 点击继续按钮但未发生跳转")
                
        except Exception as e:
            logger.error(f"❌ 拼图功能测试失败: {e}")

    async def test_backend_integration(self):
        """测试后端集成"""
        logger.info("🔍 测试后端集成...")
        
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                # 测试健康检查
                async with session.get(f"{self.api_url}/") as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        logger.info("✅ 后端健康检查通过")
                        self.analysis_results["backend_apis"]["health"] = True
                    else:
                        logger.error(f"❌ 后端健康检查失败: {resp.status}")
                
                # 测试登录API
                login_data = {"username": "admin", "password": "admin123"}
                async with session.post(f"{self.api_url}/api/v1/auth/login", json=login_data) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        logger.info("✅ 登录API正常")
                        self.analysis_results["backend_apis"]["login"] = True
                    else:
                        logger.error(f"❌ 登录API异常: {resp.status}")
                        
        except Exception as e:
            logger.error(f"❌ 后端集成测试失败: {e}")

    async def generate_comprehensive_report(self):
        """生成综合报告"""
        logger.info("📊 生成综合分析报告...")
        
        report = {
            "analysis_time": datetime.now().isoformat(),
            "platform_status": "analyzed",
            "results": self.analysis_results,
            "recommendations": []
        }
        
        # 分析结果并生成建议
        if not self.analysis_results["puzzle_page"].get("drag_element_found", True):
            report["recommendations"].append("拼图验证页面缺少拖拽元素，需要检查Vue组件实现")
        
        if self.analysis_results["issues"]:
            report["recommendations"].append("发现技术问题，需要进一步调试")
        
        # 输出报告
        logger.info("=" * 60)
        logger.info("📋 综合分析报告")
        logger.info("=" * 60)
        
        logger.info("🔐 登录流程:")
        for key, value in self.analysis_results["login_flow"].items():
            status = "✅" if value else "❌"
            logger.info(f"   {status} {key}: {value}")
        
        logger.info("🧩 拼图验证页面:")
        for key, value in self.analysis_results["puzzle_page"].items():
            if isinstance(value, list):
                logger.info(f"   📋 {key}:")
                for item in value:
                    logger.info(f"      - {item}")
            else:
                status = "✅" if value else "❌"
                logger.info(f"   {status} {key}: {value}")
        
        logger.info("🔧 后端API:")
        for key, value in self.analysis_results["backend_apis"].items():
            status = "✅" if value else "❌"
            logger.info(f"   {status} {key}: {value}")
        
        if report["recommendations"]:
            logger.info("💡 建议:")
            for rec in report["recommendations"]:
                logger.info(f"   - {rec}")
        
        if not self.analysis_results["issues"]:
            logger.info("🎉 平台核心功能运行正常！")
        else:
            logger.info("⚠️ 发现以下问题:")
            for issue in self.analysis_results["issues"]:
                logger.info(f"   - {issue}")
        
        return report

    async def run_enhanced_analysis(self):
        """运行增强分析"""
        logger.info("🚀 开始增强版量化交易平台分析...")
        logger.info("=" * 60)
        
        try:
            await self.setup_browser()
            await self.deep_analyze_puzzle_page()
            await self.test_backend_integration()
            await self.generate_comprehensive_report()
            
        except Exception as e:
            logger.error(f"❌ 分析过程中发生错误: {e}")
        finally:
            if self.browser:
                await self.browser.close()
                logger.info("🔚 浏览器已关闭")

async def main():
    analyzer = EnhancedPlatformAnalyzer()
    await analyzer.run_enhanced_analysis()

if __name__ == "__main__":
    asyncio.run(main())
