#!/usr/bin/env python3
"""
循环导入检测工具
分析Python模块之间的导入关系，检测潜在的循环导入问题
"""

import ast
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict, deque


class ImportAnalyzer(ast.NodeVisitor):
    """AST访问器，用于分析导入语句"""

    def __init__(self, module_name: str):
        self.module_name = module_name
        self.imports = []

    def visit_Import(self, node):
        """访问import语句"""
        for alias in node.names:
            self.imports.append(alias.name)

    def visit_ImportFrom(self, node):
        """访问from...import语句"""
        if node.module:
            self.imports.append(node.module)


class CircularImportDetector:
    """循环导入检测器"""

    def __init__(self, project_root: str, package_name: str = "app"):
        self.project_root = Path(project_root)
        self.package_name = package_name
        self.import_graph = defaultdict(set)
        self.module_files = {}

    def scan_modules(self):
        """扫描所有Python模块"""
        app_dir = self.project_root / self.package_name

        for py_file in app_dir.rglob("*.py"):
            if py_file.name == "__init__.py":
                # 处理包初始化文件
                rel_path = py_file.relative_to(self.project_root)
                module_name = str(rel_path.parent).replace(os.sep, ".")
            else:
                # 处理普通模块文件
                rel_path = py_file.relative_to(self.project_root)
                module_name = str(rel_path.with_suffix("")).replace(os.sep, ".")

            self.module_files[module_name] = py_file

    def analyze_imports(self):
        """分析所有模块的导入关系"""
        for module_name, file_path in self.module_files.items():
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                tree = ast.parse(content)
                analyzer = ImportAnalyzer(module_name)
                analyzer.visit(tree)

                # 过滤出项目内部的导入
                for imported_module in analyzer.imports:
                    if imported_module.startswith(self.package_name):
                        # 处理相对导入和绝对导入
                        normalized_import = self._normalize_import(
                            module_name, imported_module
                        )
                        if normalized_import in self.module_files:
                            self.import_graph[module_name].add(normalized_import)

            except Exception as e:
                print(f"警告: 无法分析模块 {module_name}: {e}")

    def _normalize_import(self, current_module: str, imported_module: str) -> str:
        """规范化导入模块名"""
        # 简单处理，实际应用中可能需要更复杂的逻辑
        return imported_module

    def find_cycles(self) -> List[List[str]]:
        """使用DFS查找循环导入"""
        visited = set()
        rec_stack = set()
        cycles = []

        def dfs(node, path):
            if node in rec_stack:
                # 找到循环
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return

            if node in visited:
                return

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            for neighbor in self.import_graph.get(node, []):
                dfs(neighbor, path.copy())

            rec_stack.remove(node)

        for module in self.module_files:
            if module not in visited:
                dfs(module, [])

        return cycles

    def find_potential_issues(self) -> Dict[str, List[str]]:
        """查找潜在的循环导入问题"""
        issues = {
            "missing_modules": [],
            "complex_dependencies": [],
            "service_cycles": [],
        }

        # 检查缺失的模块
        for module, imports in self.import_graph.items():
            for imported in imports:
                if imported not in self.module_files and imported.startswith(
                    self.package_name
                ):
                    issues["missing_modules"].append(f"{module} -> {imported}")

        # 检查复杂依赖（入度出度都很高的模块）
        in_degree = defaultdict(int)
        out_degree = defaultdict(int)

        for module, imports in self.import_graph.items():
            out_degree[module] = len(imports)
            for imported in imports:
                in_degree[imported] += 1

        for module in self.module_files:
            if in_degree[module] > 5 and out_degree[module] > 5:
                issues["complex_dependencies"].append(
                    f"{module} (入度: {in_degree[module]}, 出度: {out_degree[module]})"
                )

        # 检查services目录内的循环依赖
        service_modules = [m for m in self.module_files if ".services." in m]
        for service in service_modules:
            for imported in self.import_graph.get(service, []):
                if imported in service_modules and service in self.import_graph.get(
                    imported, []
                ):
                    issues["service_cycles"].append(f"{service} <-> {imported}")

        return issues

    def generate_report(self) -> str:
        """生成分析报告"""
        self.scan_modules()
        self.analyze_imports()

        cycles = self.find_cycles()
        issues = self.find_potential_issues()

        report = []
        report.append("=" * 80)
        report.append("循环导入依赖分析报告")
        report.append("=" * 80)
        report.append("")

        # 基本统计
        report.append(f"扫描的模块数量: {len(self.module_files)}")
        report.append(
            f"导入关系数量: {sum(len(imports) for imports in self.import_graph.values())}"
        )
        report.append("")

        # 循环导入
        if cycles:
            report.append("🔴 发现循环导入:")
            report.append("-" * 40)
            for i, cycle in enumerate(cycles, 1):
                report.append(f"{i}. {' -> '.join(cycle)}")
        else:
            report.append("✅ 未发现明显的循环导入")
        report.append("")

        # 缺失模块
        if issues["missing_modules"]:
            report.append("⚠️  缺失的模块依赖:")
            report.append("-" * 40)
            for missing in issues["missing_modules"]:
                report.append(f"- {missing}")
        else:
            report.append("✅ 所有导入的模块都存在")
        report.append("")

        # 复杂依赖
        if issues["complex_dependencies"]:
            report.append("⚠️  复杂依赖的模块 (可能需要重构):")
            report.append("-" * 40)
            for complex_dep in issues["complex_dependencies"]:
                report.append(f"- {complex_dep}")
        else:
            report.append("✅ 模块依赖关系相对简单")
        report.append("")

        # Services循环依赖
        if issues["service_cycles"]:
            report.append("⚠️  Services模块间的相互依赖:")
            report.append("-" * 40)
            for service_cycle in issues["service_cycles"]:
                report.append(f"- {service_cycle}")
        else:
            report.append("✅ Services模块间无直接循环依赖")
        report.append("")

        # 建议
        report.append("💡 修复建议:")
        report.append("-" * 40)

        if cycles:
            report.append("1. 循环导入修复:")
            report.append("   - 使用延迟导入 (在函数内部导入)")
            report.append("   - 重构代码结构，提取公共依赖")
            report.append("   - 使用抽象基类或接口")

        if issues["missing_modules"]:
            report.append("2. 缺失模块修复:")
            report.append("   - 创建缺失的服务模块")
            report.append("   - 检查导入路径是否正确")
            report.append("   - 移除不必要的导入语句")

        if issues["complex_dependencies"]:
            report.append("3. 复杂依赖重构:")
            report.append("   - 考虑依赖注入模式")
            report.append("   - 使用工厂模式或服务定位器")
            report.append("   - 将大模块拆分为更小的模块")

        if issues["service_cycles"]:
            report.append("4. Services循环依赖修复:")
            report.append("   - 将共同依赖提取到base service")
            report.append("   - 使用事件驱动架构")
            report.append("   - 重新设计服务之间的接口")

        report.append("")
        report.append("=" * 80)

        return "\n".join(report)


if __name__ == "__main__":
    # 分析当前项目
    detector = CircularImportDetector(".")
    report = detector.generate_report()
    print(report)

    # 保存报告
    with open("circular_import_analysis.txt", "w", encoding="utf-8") as f:
        f.write(report)

    print("\n报告已保存到 circular_import_analysis.txt")
