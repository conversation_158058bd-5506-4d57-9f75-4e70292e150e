{"user_profiles": [{"id": "user_001", "type": "novice_investor", "name": "新手投资者小王", "characteristics": ["谨慎", "学习型", "小额投资"], "goals": ["学习股票投资", "模拟交易练习", "风险控制"]}, {"id": "user_002", "type": "experienced_trader", "name": "资深交易员老李", "characteristics": ["激进", "技术分析", "大额交易"], "goals": ["快速交易", "技术指标分析", "算法交易"]}, {"id": "user_003", "type": "institutional_investor", "name": "机构投资者张总", "characteristics": ["专业", "风控严格", "组合投资"], "goals": ["资产配置", "风险管理", "合规交易"]}], "test_stocks": [{"symbol": "000001", "name": "平安银行", "sector": "金融"}, {"symbol": "000002", "name": "万科A", "sector": "房地产"}, {"symbol": "600000", "name": "浦发银行", "sector": "金融"}, {"symbol": "600036", "name": "招商银行", "sector": "金融"}, {"symbol": "600519", "name": "贵州茅台", "sector": "消费"}, {"symbol": "000858", "name": "五粮液", "sector": "消费"}], "trading_scenarios": [{"name": "日常买入操作", "steps": ["搜索股票", "查看行情", "设置价格", "确认买入"], "expected_time": 120}, {"name": "快速卖出操作", "steps": ["查看持仓", "选择股票", "设置卖出价格", "确认卖出"], "expected_time": 90}, {"name": "资金划转操作", "steps": ["进入账户管理", "选择划转类型", "输入金额", "确认划转"], "expected_time": 150}]}