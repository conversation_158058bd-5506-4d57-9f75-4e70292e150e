/**
 * 测试现代化组件
 */

const puppeteer = require('puppeteer');

async function testModernComponent() {
    console.log('🧪 测试现代化组件...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 },
        args: ['--start-maximized']
    });

    const page = await browser.newPage();
    
    try {
        console.log('📱 访问模拟交易页面...');
        
        // 测试模拟交易页面
        await page.goto('http://localhost:5173/trading/simulated', { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });
        
        console.log('✅ 页面访问成功');
        
        // 等待Vue组件加载
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查页面内容
        const pageContent = await page.evaluate(() => {
            return {
                title: document.title,
                bodyText: document.body.innerText.substring(0, 300),
                hasModernClass: !!document.querySelector('.simulated-trading-modern'),
                hasHeader: !!document.querySelector('.modern-header'),
                hasWorkspace: !!document.querySelector('.trading-workspace'),
                hasLeftPanel: !!document.querySelector('.left-panel'),
                hasRightPanel: !!document.querySelector('.right-panel'),
                hasBottomPanel: !!document.querySelector('.bottom-data-panel'),
                allClasses: Array.from(document.querySelectorAll('[class]')).map(el => el.className).slice(0, 10)
            };
        });
        
        console.log('📊 页面内容检查:');
        console.log('  标题:', pageContent.title);
        console.log('  现代化类:', pageContent.hasModernClass ? '✅' : '❌');
        console.log('  头部:', pageContent.hasHeader ? '✅' : '❌');
        console.log('  工作区:', pageContent.hasWorkspace ? '✅' : '❌');
        console.log('  左面板:', pageContent.hasLeftPanel ? '✅' : '❌');
        console.log('  右面板:', pageContent.hasRightPanel ? '✅' : '❌');
        console.log('  底部面板:', pageContent.hasBottomPanel ? '✅' : '❌');
        console.log('  页面文本:', pageContent.bodyText);
        console.log('  CSS类名:', pageContent.allClasses);
        
        await page.screenshot({ path: 'test_modern_component.png', fullPage: true });
        console.log('📸 截图已保存: test_modern_component.png');
        
        // 保持浏览器打开
        console.log('🌐 浏览器保持打开状态，可以手动检查...');
        await new Promise(resolve => setTimeout(resolve, 15000));
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

testModernComponent().catch(console.error);
