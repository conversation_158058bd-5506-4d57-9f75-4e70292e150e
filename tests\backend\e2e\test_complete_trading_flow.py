"""
端到端测试 - 完整交易流程
"""

import pytest
import asyncio
from httpx import AsyncClient
from unittest.mock import AsyncMock, <PERSON><PERSON>, patch
from datetime import datetime, timedelta
from decimal import Decimal

from app.main import app
from app.services.trading_service import TradingService
from app.services.market_data_service import MarketDataService
from app.services.risk_service import RiskService
from app.services.strategy_service import StrategyService


@pytest.mark.e2e
@pytest.mark.integration
@pytest.mark.asyncio
class TestCompleteTradingFlow:
    """完整交易流程端到端测试"""

    @pytest.fixture
    async def authenticated_client(self, client: AsyncClient):
        """认证客户端"""
        # 模拟用户注册和登录
        register_data = {
            "username": "e2e_testuser",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        with patch("app.services.auth_service.AuthService.register") as mock_register:
            with patch("app.services.auth_service.AuthService.login") as mock_login:
                # Mock注册成功
                mock_user = Mock()
                mock_user.id = "e2e-user-id"
                mock_user.username = "e2e_testuser"
                mock_register.return_value = mock_user
                
                # Mock登录成功
                mock_login.return_value = {
                    "access_token": "e2e-test-token",
                    "token_type": "bearer",
                    "user": mock_user
                }
                
                # 注册用户
                register_response = await client.post("/api/v1/auth/register", json=register_data)
                assert register_response.status_code == 201
                
                # 登录
                login_data = {"username": "e2e_testuser", "password": "testpassword123"}
                login_response = await client.post("/api/v1/auth/login", json=login_data)
                assert login_response.status_code == 200
                
                token_data = login_response.json()
                headers = {"Authorization": f"Bearer {token_data['access_token']}"}
                
                return client, headers, mock_user

    async def test_complete_stock_trading_workflow(self, authenticated_client):
        """测试完整的股票交易工作流"""
        client, auth_headers, mock_user = await authenticated_client
        
        with patch("app.api.v1.trading.get_current_user", return_value=mock_user):
            with patch.multiple(
                "app.services.trading_service.TradingService",
                create_order=AsyncMock(),
                get_order_by_id=AsyncMock(),
                get_user_positions=AsyncMock(),
                get_portfolio_summary=AsyncMock()
            ):
                with patch.multiple(
                    "app.services.market_data_service.MarketDataService",
                    get_latest_tick=AsyncMock(),
                    get_symbol_info=AsyncMock()
                ):
                    with patch.multiple(
                        "app.services.risk_service.RiskService",
                        check_order_risk=AsyncMock()
                    ):
                        # 1. 获取市场数据
                        await self._test_market_data_retrieval(client, auth_headers)
                        
                        # 2. 下单前风险检查
                        await self._test_pre_order_risk_check(client, auth_headers)
                        
                        # 3. 创建买入订单
                        buy_order = await self._test_create_buy_order(client, auth_headers)
                        
                        # 4. 监控订单状态
                        await self._test_monitor_order_status(client, auth_headers, buy_order["id"])
                        
                        # 5. 检查持仓
                        await self._test_check_positions(client, auth_headers)
                        
                        # 6. 创建卖出订单
                        sell_order = await self._test_create_sell_order(client, auth_headers)
                        
                        # 7. 查看投资组合
                        await self._test_portfolio_summary(client, auth_headers)
                        
                        # 8. 查看交易历史
                        await self._test_trading_history(client, auth_headers)

    async def _test_market_data_retrieval(self, client: AsyncClient, auth_headers: dict):
        """测试市场数据获取"""
        symbol = "000001"
        
        # Mock市场数据
        with patch("app.services.market_data_service.MarketDataService.get_latest_tick") as mock_tick:
            mock_tick.return_value = Mock(
                symbol=symbol,
                price=Decimal("10.50"),
                volume=1000000,
                timestamp=datetime.now()
            )
            
            # 获取实时行情
            response = await client.get(f"/api/v1/market/tick/{symbol}", headers=auth_headers)
            assert response.status_code == 200
            tick_data = response.json()
            assert tick_data["symbol"] == symbol
            assert float(tick_data["price"]) == 10.50

    async def _test_pre_order_risk_check(self, client: AsyncClient, auth_headers: dict):
        """测试下单前风险检查"""
        # 风险检查应该在创建订单时自动执行
        # 这里我们验证风险服务的集成
        pass

    async def _test_create_buy_order(self, client: AsyncClient, auth_headers: dict) -> dict:
        """测试创建买入订单"""
        order_data = {
            "symbol": "000001",
            "side": "buy",
            "order_type": "limit",
            "quantity": 100,
            "price": "10.50",
            "time_in_force": "GTC"
        }
        
        # Mock订单创建
        with patch("app.services.trading_service.TradingService.create_order") as mock_create:
            mock_order = Mock()
            mock_order.id = "buy-order-001"
            mock_order.symbol = "000001"
            mock_order.side = "buy"
            mock_order.status = "pending"
            mock_order.quantity = 100
            mock_order.price = Decimal("10.50")
            mock_create.return_value = mock_order
            
            # 创建订单
            response = await client.post(
                "/api/v1/trading/orders",
                json=order_data,
                headers=auth_headers
            )
            
            assert response.status_code == 201
            order = response.json()
            assert order["symbol"] == "000001"
            assert order["side"] == "buy"
            assert order["status"] == "pending"
            
            return order

    async def _test_monitor_order_status(self, client: AsyncClient, auth_headers: dict, order_id: str):
        """测试监控订单状态"""
        # Mock订单状态查询
        with patch("app.services.trading_service.TradingService.get_order_by_id") as mock_get:
            # 模拟订单状态变化：pending -> partial_filled -> filled
            status_sequence = ["pending", "partial_filled", "filled"]
            
            for status in status_sequence:
                mock_order = Mock()
                mock_order.id = order_id
                mock_order.status = status
                mock_order.user_id = "e2e-user-id"
                mock_get.return_value = mock_order
                
                response = await client.get(
                    f"/api/v1/trading/orders/{order_id}",
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                order = response.json()
                assert order["status"] == status
                
                # 模拟等待状态变化
                await asyncio.sleep(0.1)

    async def _test_check_positions(self, client: AsyncClient, auth_headers: dict):
        """测试检查持仓"""
        # Mock持仓查询
        with patch("app.services.trading_service.TradingService.get_user_positions") as mock_get:
            mock_positions = [
                Mock(
                    id="pos-001",
                    symbol="000001",
                    quantity=100,
                    average_price=Decimal("10.50"),
                    market_value=Decimal("1050.00"),
                    unrealized_pnl=Decimal("0.00")
                )
            ]
            mock_get.return_value = mock_positions
            
            response = await client.get("/api/v1/trading/positions", headers=auth_headers)
            
            assert response.status_code == 200
            positions = response.json()
            assert len(positions) == 1
            assert positions[0]["symbol"] == "000001"
            assert positions[0]["quantity"] == 100

    async def _test_create_sell_order(self, client: AsyncClient, auth_headers: dict) -> dict:
        """测试创建卖出订单"""
        order_data = {
            "symbol": "000001",
            "side": "sell",
            "order_type": "limit",
            "quantity": 100,
            "price": "10.80",
            "time_in_force": "GTC"
        }
        
        # Mock订单创建
        with patch("app.services.trading_service.TradingService.create_order") as mock_create:
            mock_order = Mock()
            mock_order.id = "sell-order-001"
            mock_order.symbol = "000001"
            mock_order.side = "sell"
            mock_order.status = "pending"
            mock_order.quantity = 100
            mock_order.price = Decimal("10.80")
            mock_create.return_value = mock_order
            
            response = await client.post(
                "/api/v1/trading/orders",
                json=order_data,
                headers=auth_headers
            )
            
            assert response.status_code == 201
            order = response.json()
            assert order["side"] == "sell"
            assert float(order["price"]) == 10.80
            
            return order

    async def _test_portfolio_summary(self, client: AsyncClient, auth_headers: dict):
        """测试投资组合摘要"""
        # Mock投资组合摘要
        with patch("app.services.trading_service.TradingService.get_portfolio_summary") as mock_get:
            mock_summary = {
                "total_market_value": Decimal("10800.00"),
                "total_unrealized_pnl": Decimal("30.00"),
                "daily_pnl": Decimal("30.00"),
                "position_count": 1,
                "cash_balance": Decimal("89200.00"),
                "buying_power": Decimal("178400.00"),
                "total_assets": Decimal("100000.00")
            }
            mock_get.return_value = mock_summary
            
            response = await client.get("/api/v1/trading/portfolio/summary", headers=auth_headers)
            
            assert response.status_code == 200
            summary = response.json()
            assert summary["position_count"] == 1
            assert "total_market_value" in summary
            assert "daily_pnl" in summary

    async def _test_trading_history(self, client: AsyncClient, auth_headers: dict):
        """测试交易历史"""
        # Mock交易历史
        with patch("app.services.trading_service.TradingService.get_user_trades") as mock_get:
            mock_trades = [
                Mock(
                    id="trade-001",
                    order_id="buy-order-001",
                    symbol="000001",
                    side="buy",
                    quantity=100,
                    price=Decimal("10.50"),
                    amount=Decimal("1050.00"),
                    commission=Decimal("2.10"),
                    trade_time=datetime.now() - timedelta(minutes=30)
                ),
                Mock(
                    id="trade-002", 
                    order_id="sell-order-001",
                    symbol="000001",
                    side="sell",
                    quantity=100,
                    price=Decimal("10.80"),
                    amount=Decimal("1080.00"),
                    commission=Decimal("2.16"),
                    trade_time=datetime.now()
                )
            ]
            mock_get.return_value = mock_trades
            
            response = await client.get("/api/v1/trading/trades", headers=auth_headers)
            
            assert response.status_code == 200
            trades = response.json()
            assert len(trades) == 2
            assert trades[0]["side"] == "buy"
            assert trades[1]["side"] == "sell"

    async def test_strategy_development_and_execution_flow(self, authenticated_client):
        """测试策略开发和执行流程"""
        client, auth_headers, mock_user = await authenticated_client
        
        with patch("app.api.v1.strategy.get_current_user", return_value=mock_user):
            with patch.multiple(
                "app.services.strategy_service.StrategyService",
                create_strategy=AsyncMock(),
                validate_strategy_code=AsyncMock(),
                start_strategy=AsyncMock(),
                get_strategy_performance=AsyncMock()
            ):
                # 1. 创建策略
                strategy = await self._test_create_strategy(client, auth_headers)
                
                # 2. 验证策略代码
                await self._test_validate_strategy_code(client, auth_headers, strategy["id"])
                
                # 3. 启动策略
                await self._test_start_strategy(client, auth_headers, strategy["id"])
                
                # 4. 监控策略表现
                await self._test_monitor_strategy_performance(client, auth_headers, strategy["id"])

    async def _test_create_strategy(self, client: AsyncClient, auth_headers: dict) -> dict:
        """测试创建策略"""
        strategy_data = {
            "name": "E2E测试策略",
            "description": "端到端测试用策略",
            "strategy_type": "trend_following",
            "code": """
def initialize(context):
    context.symbol = '000001'
    context.target_position = 0

def handle_data(context, data):
    current_price = data.current(context.symbol, 'price')
    ma20 = data.history(context.symbol, 'price', 20, '1d').mean()
    
    if current_price > ma20 and context.target_position <= 0:
        order_target_percent(context.symbol, 0.1)
        context.target_position = 0.1
    elif current_price < ma20 and context.target_position >= 0:
        order_target_percent(context.symbol, -0.1)
        context.target_position = -0.1
            """,
            "parameters": {
                "lookback_period": 20,
                "position_size": 0.1
            },
            "symbols": ["000001"],
            "timeframe": "1d",
            "risk_level": "medium"
        }
        
        # Mock策略创建
        with patch("app.services.strategy_service.StrategyService.create_strategy") as mock_create:
            mock_strategy = Mock()
            mock_strategy.id = "e2e-strategy-001"
            mock_strategy.name = "E2E测试策略"
            mock_strategy.status = "draft"
            mock_create.return_value = mock_strategy
            
            response = await client.post(
                "/api/v1/strategy/strategies",
                json=strategy_data,
                headers=auth_headers
            )
            
            assert response.status_code == 201
            strategy = response.json()
            assert strategy["name"] == "E2E测试策略"
            assert strategy["status"] == "draft"
            
            return strategy

    async def _test_validate_strategy_code(self, client: AsyncClient, auth_headers: dict, strategy_id: str):
        """测试验证策略代码"""
        # Mock代码验证
        with patch("app.services.strategy_service.StrategyService.validate_strategy_code") as mock_validate:
            from app.schemas.strategy import ValidationResult
            mock_validate.return_value = ValidationResult(
                is_valid=True,
                error_message=None,
                warnings=[]
            )
            
            response = await client.post(
                f"/api/v1/strategy/strategies/{strategy_id}/validate",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            result = response.json()
            assert result["is_valid"] is True

    async def _test_start_strategy(self, client: AsyncClient, auth_headers: dict, strategy_id: str):
        """测试启动策略"""
        # Mock策略启动
        with patch("app.services.strategy_service.StrategyService.start_strategy") as mock_start:
            mock_start.return_value = True
            
            response = await client.post(
                f"/api/v1/strategy/strategies/{strategy_id}/start",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            result = response.json()
            assert result["message"] == "策略已启动"

    async def _test_monitor_strategy_performance(self, client: AsyncClient, auth_headers: dict, strategy_id: str):
        """测试监控策略表现"""
        # Mock策略绩效
        with patch("app.services.strategy_service.StrategyService.get_strategy_performance") as mock_perf:
            mock_perf.return_value = {
                "total_return": 0.05,
                "annual_return": 0.12,
                "max_drawdown": -0.02,
                "sharpe_ratio": 1.5,
                "win_rate": 0.6,
                "total_trades": 25,
                "profit_factor": 1.8
            }
            
            response = await client.get(
                f"/api/v1/strategy/strategies/{strategy_id}/performance",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            performance = response.json()
            assert performance["total_return"] == 0.05
            assert performance["sharpe_ratio"] == 1.5

    async def test_risk_management_integration_flow(self, authenticated_client):
        """测试风险管理集成流程"""
        client, auth_headers, mock_user = await authenticated_client
        
        with patch("app.api.v1.risk.get_current_user", return_value=mock_user):
            with patch.multiple(
                "app.services.risk_service.RiskService",
                get_risk_limits=AsyncMock(),
                check_daily_loss_limit=AsyncMock(),
                calculate_portfolio_risk=AsyncMock()
            ):
                # 1. 获取风险限制
                await self._test_get_risk_limits(client, auth_headers)
                
                # 2. 检查日亏损限制
                await self._test_check_daily_loss_limit(client, auth_headers)
                
                # 3. 计算投资组合风险
                await self._test_calculate_portfolio_risk(client, auth_headers)

    async def _test_get_risk_limits(self, client: AsyncClient, auth_headers: dict):
        """测试获取风险限制"""
        # Mock风险限制
        with patch("app.services.risk_service.RiskService.get_risk_limits") as mock_get:
            from app.schemas.trading import RiskLimitData
            mock_get.return_value = RiskLimitData(
                max_position=Decimal("20000.00"),
                max_order_size=Decimal("10000.00"),
                max_daily_loss=50000.0,
                max_total_loss=100000.0,
                allowed_symbols=["000001", "000002"],
                forbidden_symbols=["ST*"]
            )
            
            response = await client.get("/api/v1/risk/limits", headers=auth_headers)
            
            assert response.status_code == 200
            limits = response.json()
            assert float(limits["max_position"]) == 20000.00
            assert limits["max_daily_loss"] == 50000.0

    async def _test_check_daily_loss_limit(self, client: AsyncClient, auth_headers: dict):
        """测试检查日亏损限制"""
        # Mock日亏损检查
        with patch("app.services.risk_service.RiskService.check_daily_loss_limit") as mock_check:
            from app.schemas.trading import RiskCheckResult
            mock_check.return_value = RiskCheckResult(
                passed=True,
                message="日亏损检查通过",
                details={"current_loss": -1000.00, "limit": -50000.00}
            )
            
            response = await client.get("/api/v1/risk/daily-loss-check", headers=auth_headers)
            
            assert response.status_code == 200
            result = response.json()
            assert result["passed"] is True

    async def _test_calculate_portfolio_risk(self, client: AsyncClient, auth_headers: dict):
        """测试计算投资组合风险"""
        # Mock投资组合风险计算
        with patch("app.services.risk_service.RiskService.calculate_portfolio_risk") as mock_calc:
            mock_calc.return_value = {
                "total_value": Decimal("100000.00"),
                "var_95": -0.05,
                "concentration": 0.3,
                "leverage_ratio": 1.2,
                "correlation_risk": "medium",
                "sector_exposure": {
                    "金融": 0.4,
                    "科技": 0.3,
                    "消费": 0.3
                }
            }
            
            response = await client.get("/api/v1/risk/portfolio-risk", headers=auth_headers)
            
            assert response.status_code == 200
            risk = response.json()
            assert risk["var_95"] == -0.05
            assert risk["concentration"] == 0.3

    async def test_market_data_integration_flow(self, authenticated_client):
        """测试市场数据集成流程"""
        client, auth_headers, mock_user = await authenticated_client
        
        # 1. 订阅实时数据
        await self._test_subscribe_realtime_data(client, auth_headers)
        
        # 2. 获取历史数据
        await self._test_get_historical_data(client, auth_headers)
        
        # 3. 获取市场统计
        await self._test_get_market_statistics(client, auth_headers)

    async def _test_subscribe_realtime_data(self, client: AsyncClient, auth_headers: dict):
        """测试订阅实时数据"""
        # Mock实时数据订阅
        symbols = ["000001", "000002", "000300"]
        
        response = await client.post(
            "/api/v1/market/subscribe",
            json={"symbols": symbols, "data_types": ["tick", "depth"]},
            headers=auth_headers
        )
        
        # 根据实际API响应调整断言
        if response.status_code == 404:
            # 如果端点不存在，跳过测试
            pytest.skip("Market subscription endpoint not implemented")
        else:
            assert response.status_code in [200, 201]

    async def _test_get_historical_data(self, client: AsyncClient, auth_headers: dict):
        """测试获取历史数据"""
        symbol = "000001"
        
        # Mock历史K线数据
        with patch("app.services.market_data_service.MarketDataService.get_kline_history") as mock_get:
            from app.schemas.market_data import KlineData
            mock_klines = [
                KlineData(
                    symbol=symbol,
                    timestamp=datetime.now() - timedelta(days=i),
                    interval="1d",
                    open_price=Decimal("10.00"),
                    high_price=Decimal("10.50"),
                    low_price=Decimal("9.80"),
                    close_price=Decimal("10.20"),
                    volume=1000000,
                    amount=Decimal("10200000.00")
                ) for i in range(30)
            ]
            mock_get.return_value = mock_klines
            
            response = await client.get(
                f"/api/v1/market/kline/{symbol}",
                params={"interval": "1d", "limit": 30},
                headers=auth_headers
            )
            
            assert response.status_code == 200
            klines = response.json()
            assert len(klines) == 30

    async def _test_get_market_statistics(self, client: AsyncClient, auth_headers: dict):
        """测试获取市场统计"""
        # Mock市场统计
        with patch("app.services.market_data_service.MarketDataService.get_market_statistics") as mock_get:
            mock_get.return_value = {
                "total_symbols": 5000,
                "active_symbols": 4800,
                "up_count": 2400,
                "down_count": 1800,
                "total_volume": "123456789000",
                "total_amount": "9876543210000.00"
            }
            
            response = await client.get("/api/v1/market/statistics", headers=auth_headers)
            
            assert response.status_code == 200
            stats = response.json()
            assert stats["total_symbols"] == 5000

    async def test_error_recovery_and_resilience(self, authenticated_client):
        """测试错误恢复和系统韧性"""
        client, auth_headers, mock_user = await authenticated_client
        
        # 1. 测试网络超时恢复
        await self._test_network_timeout_recovery(client, auth_headers)
        
        # 2. 测试服务故障恢复
        await self._test_service_failure_recovery(client, auth_headers)
        
        # 3. 测试数据库连接恢复
        await self._test_database_connection_recovery(client, auth_headers)

    async def _test_network_timeout_recovery(self, client: AsyncClient, auth_headers: dict):
        """测试网络超时恢复"""
        # 模拟网络超时
        with patch("app.services.market_data_service.MarketDataService.get_latest_tick") as mock_get:
            mock_get.side_effect = asyncio.TimeoutError("Network timeout")
            
            response = await client.get("/api/v1/market/tick/000001", headers=auth_headers)
            
            # 应该返回适当的错误响应
            assert response.status_code in [500, 503, 504]

    async def _test_service_failure_recovery(self, client: AsyncClient, auth_headers: dict):
        """测试服务故障恢复"""
        # 模拟服务故障
        with patch("app.services.trading_service.TradingService.create_order") as mock_create:
            mock_create.side_effect = Exception("Service temporarily unavailable")
            
            order_data = {
                "symbol": "000001",
                "side": "buy",
                "order_type": "limit",
                "quantity": 100,
                "price": "10.50"
            }
            
            response = await client.post(
                "/api/v1/trading/orders",
                json=order_data,
                headers=auth_headers
            )
            
            # 应该返回服务器错误
            assert response.status_code == 500

    async def _test_database_connection_recovery(self, client: AsyncClient, auth_headers: dict):
        """测试数据库连接恢复"""
        # 模拟数据库连接失败
        with patch("app.services.trading_service.TradingService.get_user_orders") as mock_get:
            mock_get.side_effect = Exception("Database connection lost")
            
            response = await client.get("/api/v1/trading/orders", headers=auth_headers)
            
            # 应该返回服务器错误
            assert response.status_code == 500

    async def test_concurrent_user_operations(self, client: AsyncClient):
        """测试并发用户操作"""
        # 创建多个模拟用户
        users_count = 10
        tasks = []
        
        for i in range(users_count):
            task = self._simulate_user_trading_session(client, f"user-{i:03d}")
            tasks.append(task)
        
        # 并发执行所有用户操作
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证大部分操作成功完成
        success_count = sum(1 for result in results if not isinstance(result, Exception))
        assert success_count >= users_count * 0.8  # 至少80%成功

    async def _simulate_user_trading_session(self, client: AsyncClient, user_id: str):
        """模拟用户交易会话"""
        # Mock用户认证
        with patch("app.api.v1.trading.get_current_user") as mock_auth:
            mock_user = Mock()
            mock_user.id = user_id
            mock_auth.return_value = mock_user
            
            # Mock服务调用
            with patch.multiple(
                "app.services.trading_service.TradingService",
                create_order=AsyncMock(return_value=Mock(id=f"order-{user_id}")),
                get_user_positions=AsyncMock(return_value=[]),
                get_portfolio_summary=AsyncMock(return_value={})
            ):
                headers = {"Authorization": f"Bearer token-{user_id}"}
                
                # 执行一系列操作
                operations = [
                    client.get("/api/v1/market/tick/000001", headers=headers),
                    client.post("/api/v1/trading/orders", 
                              json={"symbol": "000001", "side": "buy", "order_type": "market", "quantity": 100},
                              headers=headers),
                    client.get("/api/v1/trading/positions", headers=headers),
                    client.get("/api/v1/trading/portfolio/summary", headers=headers)
                ]
                
                responses = await asyncio.gather(*operations, return_exceptions=True)
                
                # 返回成功操作的数量
                return sum(1 for response in responses 
                          if not isinstance(response, Exception) and response.status_code < 400)