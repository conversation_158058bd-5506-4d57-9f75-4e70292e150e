# 🚀 MiniQMT专业实盘交易终端使用指南

## 📋 功能概览

基于您的专业设计要求，我们实现了一个**高效执行、实时性、风控合规**的专业实盘交易终端，完全适配中国投资者的操作习惯。

## 🎯 核心特性

### 1. 三栏垂直分区布局
- **头部控制栏（10%）**：智能搜索 + 资金总览 + 模式切换
- **核心交易区（70%）**：行情图表 + 下单面板 + 深度盘口
- **辅助功能区（30%）**：账户持仓 + 委托队列 + 智能提示
- **底部状态栏（5%）**：连接状态 + 风险提示 + 快捷键

### 2. 智能搜索系统
- 支持股票代码直接输入（如：000001）
- 支持股票名称搜索（如：平安银行）
- 支持拼音缩写快速定位（如：PAYH→平安银行）
- 实时显示价格和涨跌幅

### 3. 专业下单面板
- **大按钮设计**：市价买入、限价卖出、一键撤单
- **智能价格填充**：买入预填卖一价，卖出预填买一价
- **数量快捷设置**：1手、5手、10手、全仓/全卖
- **实时费用计算**：佣金、印花税自动计算
- **闪电确认模式**：勾选后跳过二次确认

### 4. Level-2深度盘口
- **五档行情显示**：买卖五档实时更新
- **大单标记**：超过500手的订单红框高亮
- **右键快速跟单**：右键价格快速设置委托价
- **逐笔成交明细**：时间、价格、数量、买卖性质

### 5. 实时监控面板
- **持仓管理**：成本价、现价、盈亏率实时显示
- **委托队列**：订单状态实时更新，支持快速撤单
- **智能提示**：主力资金流向、涨速预警等

## ⌨️ 快捷键操作

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `F1` | 快速买入 | 自动设置买入方向并填充卖一价 |
| `F2` | 快速卖出 | 自动设置卖出方向并填充买一价 |
| `Esc` | 清空输入 | 清空所有输入框和选中股票 |
| `Ctrl+C` | 一键撤单 | 撤销所有待成交订单 |

## 🎨 中国化设计优化

### 1. 颜色体系
- **严格遵循红涨绿跌**（与国际市场相反）
- 盈利显示红色，亏损显示绿色
- 买入按钮绿色，卖出按钮红色

### 2. 操作习惯
- 股票数量以"手"为单位（1手=100股）
- 价格显示精确到分（小数点后2位）
- 涨跌停价格自动显示和限制

### 3. 合规要求
- 非交易时段委托提示
- 大额委托风险提醒
- 资金不足中文提示
- 风险揭示书展示

## 🔧 技术特性

### 1. 实时性保障
- WebSocket实时行情推送
- 本地化数据缓存
- 行情延迟≤1秒显示
- 增量数据更新

### 2. 风控系统
- 实时资金检查
- 涨跌停价格限制
- 持仓风险监控
- 智能预警提示

### 3. 性能优化
- 虚拟滚动大数据表格
- 图表数据压缩传输
- 本地SQLite持久化
- 响应式布局设计

## 📱 响应式适配

- **大屏显示（>1400px）**：完整三栏布局
- **中等屏幕（1200-1400px）**：核心区域垂直排列
- **小屏设备（<1200px）**：主辅区域分层显示

## 🚀 使用流程

### 1. 连接MiniQMT
1. 确保MiniQMT客户端正在运行
2. 点击右上角"连接MiniQMT"按钮
3. 等待连接状态变为绿色"已连接"

### 2. 选择股票
1. 在顶部搜索框输入股票代码或名称
2. 从下拉列表选择目标股票
3. 图表和盘口数据自动加载

### 3. 下单交易
1. 选择买入/卖出方向
2. 设置委托价格和数量
3. 点击大按钮快速下单或使用详细表单
4. 确认订单信息后提交

### 4. 监控管理
1. 在持仓面板查看实时盈亏
2. 在委托队列监控订单状态
3. 根据智能提示调整策略

## ⚠️ 风险提示

- 市场有风险，投资需谨慎
- 本系统仅供专业投资者使用
- 请确保充分理解交易规则
- 建议先在模拟环境测试

## 🔗 访问地址

- **实盘交易终端**：http://localhost:5173/trading/live
- **API文档**：http://localhost:8000/docs
- **健康检查**：http://localhost:8000/health

---

*本交易终端严格按照中国证监会相关规定设计，确保合规安全。*
