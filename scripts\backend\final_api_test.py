#!/usr/bin/env python3
"""最终API测试脚本"""

import json
import time
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# API基础URL
BASE_URL = "http://localhost:8000"

# 颜色代码
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def test_endpoint(method, path, data=None, expected_status=[200], headers=None):
    """测试单个端点"""
    url = f"{BASE_URL}{path}"
    
    try:
        req_headers = {'Content-Type': 'application/json'}
        if headers:
            req_headers.update(headers)
            
        if data:
            data = json.dumps(data).encode('utf-8')
            
        request = Request(url, data=data, headers=req_headers)
        request.get_method = lambda: method
        
        try:
            response = urlopen(request)
            status_code = response.getcode()
            response_data = response.read().decode('utf-8')
        except HTTPError as e:
            status_code = e.code
            response_data = e.read().decode('utf-8')
            
        if status_code in expected_status:
            return True, f"状态码: {status_code}"
        else:
            return False, f"状态码: {status_code}, 响应: {response_data[:100]}"
            
    except URLError as e:
        return False, f"连接错误: {str(e)}"
    except Exception as e:
        return False, f"错误: {str(e)}"

def main():
    """主测试函数"""
    print(f"\n{BLUE}{'='*80}")
    print(f"📊 量化投资平台 最终功能测试")
    print(f"{'='*80}{RESET}\n")
    
    # 定义测试组
    test_groups = {
        "认证模块": [
            ("POST", "/api/v1/auth/login", {"username": "admin", "password": "admin123"}, [200], "登录API"),
            ("POST", "/api/v1/auth/register", {"username": f"test_{int(time.time())}", "email": f"test{int(time.time())}@example.com", "password": "test123"}, [200], "注册API"),
        ],
        "市场数据": [
            ("GET", "/api/v1/market/stocks", None, [200], "股票列表"),
            ("GET", "/api/market_data", None, [200], "实时行情"),
            ("GET", "/api/v1/market/kline/000001.SZ", None, [200], "K线数据"),
        ],
        "交易系统": [
            ("POST", "/api/v1/trade/order", {"symbol": "000001.SZ", "side": "buy", "price": 10.0, "quantity": 100}, [200], "下单接口"),
            ("GET", "/api/v1/trade/orders", None, [200], "订单查询"),
            ("GET", "/api/v1/trade/positions", None, [200], "持仓查询"),
        ],
        "策略系统": [
            ("GET", "/api/v1/strategies", None, [200], "策略列表"),
            ("POST", "/api/v1/strategies/backtest", {"strategy_id": "1"}, [200], "策略回测"),
        ],
        "风险管理": [
            ("GET", "/api/v1/risk/metrics", None, [200], "风险指标"),
        ],
        "前端界面": [
            ("GET", "http://localhost:5173/", None, [200, 304], "首页"),
            ("GET", "http://localhost:5173/auth/login", None, [200, 304], "登录页面"),
        ],
        "基础设施": [
            ("GET", "/", None, [200], "根路径"),
            ("GET", "/health", None, [200], "健康检查"),
            ("GET", "/docs", None, [200], "API文档"),
        ]
    }
    
    # 统计结果
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    results = []
    
    # 执行测试
    for group_name, tests in test_groups.items():
        print(f"\n{YELLOW}【{group_name}】{RESET}")
        
        for method, path, data, expected_status, name in tests:
            # 特殊处理前端URL
            if path.startswith("http://"):
                url = path
                success, message = test_endpoint("GET", url.replace("http://localhost:8000", ""), None, expected_status)
            else:
                success, message = test_endpoint(method, path, data, expected_status)
            
            total_tests += 1
            
            if success:
                print(f"  {GREEN}✅ 通过{RESET} {name}: {message}")
                passed_tests += 1
                results.append({
                    "group": group_name,
                    "name": name,
                    "method": method,
                    "path": path,
                    "status": "通过",
                    "message": message
                })
            else:
                print(f"  {RED}❌ 失败{RESET} {name}: {message}")
                failed_tests += 1
                results.append({
                    "group": group_name,
                    "name": name,
                    "method": method,
                    "path": path,
                    "status": "失败",
                    "message": message
                })
            
            time.sleep(0.1)  # 避免请求过快
    
    # 输出统计
    print(f"\n{BLUE}{'='*80}")
    print(f"📊 测试统计")
    print(f"{'='*80}{RESET}")
    print(f"测试项目: {total_tests} 个")
    print(f"{GREEN}✅ 通过: {passed_tests} 项 ({passed_tests/total_tests*100:.1f}%){RESET}")
    print(f"{RED}❌ 失败: {failed_tests} 项 ({failed_tests/total_tests*100:.1f}%){RESET}")
    
    # 生成测试报告
    report = {
        "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
        "测试统计": {
            "总数": total_tests,
            "通过": passed_tests,
            "失败": failed_tests,
            "通过率": f"{passed_tests/total_tests*100:.1f}%"
        },
        "详细结果": results
    }
    
    # 保存报告
    report_file = f"final_test_report_{int(time.time())}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 测试报告已保存至: {report_file}")
    
    # 最终总结
    print(f"\n{BLUE}{'='*80}")
    print(f"🎯 最终评估")
    print(f"{'='*80}{RESET}")
    
    if passed_tests == total_tests:
        print(f"{GREEN}🎉 恭喜！所有测试全部通过！{RESET}")
    elif passed_tests >= total_tests * 0.8:
        print(f"{GREEN}👍 良好！大部分功能正常工作。{RESET}")
    elif passed_tests >= total_tests * 0.6:
        print(f"{YELLOW}⚠️ 一般！部分功能需要修复。{RESET}")
    else:
        print(f"{RED}❌ 较差！大部分功能存在问题。{RESET}")
    
    # 功能完成情况
    print(f"\n{YELLOW}📋 功能完成情况：{RESET}")
    print("✅ 已完成：")
    print("  - 认证模块（登录/注册）")
    print("  - 市场数据API")
    print("  - 交易系统API")
    print("  - 策略管理API")
    print("  - 风险管理API")
    print("  - 前端界面")
    print("  - API文档")
    print("\n⚠️ 部分完成：")
    print("  - WebSocket实时推送（需要真实数据源）")
    print("  - 数据库连接（使用内存存储）")
    print("\n🚧 待开发：")
    print("  - 策略编辑器")
    print("  - 参数优化")
    print("  - 风险报告")
    print("  - 暗黑模式")
    print("  - Prometheus监控")

if __name__ == "__main__":
    main()