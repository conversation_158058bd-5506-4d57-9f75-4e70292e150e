<template>
  <div class="simulated-trading-modern">
    <!-- 现代化头部导航栏 -->
    <div class="modern-header">
      <!-- 左侧：品牌和搜索 -->
      <div class="header-left">
        <div class="brand-section">
          <div class="simulation-indicator">
            <span class="sim-badge">模拟</span>
            <span class="sim-text">交易练习</span>
          </div>
        </div>

        <!-- 智能搜索 -->
        <div class="search-section">
          <el-autocomplete
            v-model="stockCode"
            :fetch-suggestions="searchStocks"
            placeholder="搜索股票 (代码/名称/拼音)"
            @select="selectStock"
            class="modern-search"
            size="default"
            :trigger-on-focus="false"
            clearable
          >
            <template #prefix>
              <el-icon class="search-icon"><Search /></el-icon>
            </template>
            <template #default="{ item }">
              <div class="search-suggestion">
                <div class="suggestion-main">
                  <span class="stock-code">{{ item.code }}</span>
                  <span class="stock-name">{{ item.name }}</span>
                </div>
                <div class="suggestion-price">
                  <span class="price" :class="getPriceClass(item.change)">¥{{ item.price }}</span>
                  <span class="change" :class="getPriceClass(item.change)">
                    {{ item.change > 0 ? '+' : '' }}{{ item.changePercent }}%
                  </span>
                </div>
              </div>
            </template>
          </el-autocomplete>
        </div>
      </div>

      <!-- 中间：账户概览 -->
      <div class="header-center">
        <div class="account-dashboard">
          <div class="account-item">
            <div class="account-label">总资产</div>
            <div class="account-value primary">¥{{ formatMoney(accountInfo.totalAssets) }}</div>
          </div>
          <div class="account-item">
            <div class="account-label">可用</div>
            <div class="account-value">¥{{ formatMoney(accountInfo.availableFunds) }}</div>
          </div>
          <div class="account-item">
            <div class="account-label">盈亏</div>
            <div class="account-value" :class="getProfitClass(accountInfo.totalProfit)">
              {{ accountInfo.totalProfit >= 0 ? '+' : '' }}¥{{ formatMoney(Math.abs(accountInfo.totalProfit)) }}
            </div>
          </div>
          <div class="account-item">
            <div class="account-label">收益率</div>
            <div class="account-value" :class="getProfitClass(accountInfo.totalProfit)">
              {{ accountInfo.totalProfit >= 0 ? '+' : '' }}{{ ((accountInfo.totalProfit / 1000000) * 100).toFixed(2) }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：操作和状态 -->
      <div class="header-right">
        <div class="action-group">
          <el-button
            type="primary"
            size="small"
            @click="resetAccount"
            class="action-btn"
          >
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="clearAllPositions"
            :disabled="positions.length === 0"
            class="action-btn"
          >
            <el-icon><Delete /></el-icon>
            清仓
          </el-button>
        </div>

        <div class="status-info">
          <div class="market-status">
            <span class="status-dot" :class="marketStatus.class"></span>
            <span class="status-text">{{ marketStatus.text }}</span>
          </div>
          <div class="current-time">{{ currentTime }}</div>
        </div>
      </div>
    </div>

    <!-- 主要交易区域 - 现代化布局 -->
    <div class="trading-workspace">
      <!-- 左侧：股票信息和盘口 (50%) -->
      <div class="left-panel">
        <div v-if="currentStock" class="stock-card">
          <!-- 股票基本信息 -->
          <div class="stock-header">
            <div class="stock-title-section">
              <h2 class="stock-name">{{ currentStock.name }}</h2>
              <span class="stock-code">{{ currentStock.code }}</span>
              <el-tag size="small" class="industry-tag">{{ currentStock.industry }}</el-tag>
            </div>
            <div class="stock-price-section">
              <div class="main-price" :class="getPriceClass(currentStock.change)">
                ¥{{ currentStock.price }}
              </div>
              <div class="price-change" :class="getPriceClass(currentStock.change)">
                <span class="change-amount">
                  {{ currentStock.change > 0 ? '+' : '' }}{{ currentStock.change }}
                </span>
                <span class="change-percent">
                  {{ currentStock.change > 0 ? '+' : '' }}{{ currentStock.changePercent }}%
                </span>
              </div>
            </div>
          </div>

          <!-- 价格限制信息 -->
          <div class="price-limits">
            <div class="limit-item limit-up">
              <span class="limit-label">涨停</span>
              <span class="limit-value">¥{{ currentStock.limitUp }}</span>
            </div>
            <div class="limit-item limit-down">
              <span class="limit-label">跌停</span>
              <span class="limit-value">¥{{ currentStock.limitDown }}</span>
            </div>
          </div>

          <!-- 五档行情 - 现代化设计 -->
          <div class="depth-panel">
            <div class="depth-header">
              <h3>五档行情</h3>
              <span class="update-time">{{ currentStock.updateTime }}</span>
            </div>

            <div class="depth-book">
              <!-- 卖档 -->
              <div class="sell-levels">
                <div
                  v-for="(item, index) in currentStock.sellLevels"
                  :key="'sell-' + index"
                  class="depth-row sell-row"
                  @click="setTradePrice(item.price)"
                >
                  <span class="level-label">卖{{ 5 - index }}</span>
                  <span class="level-price sell-price">{{ item.price }}</span>
                  <span class="level-volume">{{ formatVolume(item.volume) }}</span>
                </div>
              </div>

              <!-- 当前价分隔线 -->
              <div class="current-price-divider">
                <div class="divider-line"></div>
                <div class="current-price-info" :class="getPriceClass(currentStock.change)">
                  <span class="current-price">{{ currentStock.price }}</span>
                  <span class="price-arrow">{{ currentStock.change > 0 ? '↗' : '↘' }}</span>
                </div>
                <div class="divider-line"></div>
              </div>

              <!-- 买档 -->
              <div class="buy-levels">
                <div
                  v-for="(item, index) in currentStock.buyLevels"
                  :key="'buy-' + index"
                  class="depth-row buy-row"
                  @click="setTradePrice(item.price)"
                >
                  <span class="level-label">买{{ index + 1 }}</span>
                  <span class="level-price buy-price">{{ item.price }}</span>
                  <span class="level-volume">{{ formatVolume(item.volume) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 关键指标 -->
          <div class="key-metrics">
            <div class="metric-item">
              <div class="metric-label">振幅</div>
              <div class="metric-value">{{ currentStock.amplitude }}%</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">换手率</div>
              <div class="metric-value">{{ currentStock.turnover }}%</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">市盈率</div>
              <div class="metric-value">{{ currentStock.pe }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">总市值</div>
              <div class="metric-value">{{ currentStock.marketCap }}亿</div>
            </div>
          </div>
        </div>

        <!-- 未选择股票的占位符 -->
        <div v-else class="empty-stock-card">
          <div class="empty-icon">
            <el-icon size="48"><TrendCharts /></el-icon>
          </div>
          <h3>选择股票开始交易</h3>
          <p>在上方搜索框输入股票代码、名称或拼音缩写</p>
          <div class="search-examples">
            <span class="example-tag">000001</span>
            <span class="example-tag">平安银行</span>
            <span class="example-tag">PAYH</span>
          </div>
        </div>
      </div>

      <!-- 右侧：交易操作区 (50%) -->
      <div class="right-panel">
        <!-- 交易模式选择 -->
        <div class="trade-mode-selector">
          <div class="mode-tabs">
            <div
              class="mode-tab"
              :class="{ active: activeTab === 'buy' }"
              @click="activeTab = 'buy'"
            >
              <el-icon class="tab-icon buy-icon"><TrendCharts /></el-icon>
              <span class="tab-text">买入</span>
            </div>
            <div
              class="mode-tab"
              :class="{ active: activeTab === 'sell' }"
              @click="activeTab = 'sell'"
            >
              <el-icon class="tab-icon sell-icon"><Minus /></el-icon>
              <span class="tab-text">卖出</span>
            </div>
          </div>
        </div>

        <!-- 交易表单区域 -->
        <div class="trade-form-container">
          <!-- 买入表单 -->
          <div v-if="activeTab === 'buy'" class="trade-form buy-form">
            <div class="form-header">
              <h3 class="form-title">
                <el-icon class="title-icon"><TrendCharts /></el-icon>
                买入委托
              </h3>
              <div v-if="currentStock" class="selected-stock">
                {{ currentStock.name }} ({{ currentStock.code }})
              </div>
            </div>

            <div class="form-content">
              <!-- 价格设置 -->
              <div class="form-group">
                <label class="form-label">委托价格</label>
                <div class="price-input-group">
                  <el-input-number
                    v-model="tradeForm.buyPrice"
                    :precision="2"
                    :step="0.01"
                    :min="0.01"
                    size="default"
                    class="price-input"
                    placeholder="输入价格"
                  />
                  <div class="quick-price-buttons">
                    <el-button size="small" @click="setBuyPrice('sell1')" class="price-btn">卖一</el-button>
                    <el-button size="small" @click="setBuyPrice('current')" class="price-btn">现价</el-button>
                    <el-button size="small" @click="setBuyPrice('buy1')" class="price-btn">买一</el-button>
                  </div>
                </div>
              </div>

              <!-- 数量设置 -->
              <div class="form-group">
                <label class="form-label">委托数量</label>
                <div class="quantity-input-group">
                  <el-input-number
                    v-model="tradeForm.buyQuantity"
                    :min="100"
                    :step="100"
                    size="default"
                    class="quantity-input"
                    placeholder="输入数量"
                  />
                  <div class="quick-quantity-buttons">
                    <el-button size="small" @click="setQuantityByPosition('quarter')" class="qty-btn">1/4</el-button>
                    <el-button size="small" @click="setQuantityByPosition('half')" class="qty-btn">1/2</el-button>
                    <el-button size="small" @click="setQuantityByPosition('full')" class="qty-btn max-btn">满仓</el-button>
                  </div>
                </div>
              </div>

              <!-- 交易摘要 -->
              <div class="trade-summary">
                <div class="summary-row">
                  <span class="summary-label">预估金额</span>
                  <span class="summary-value">¥{{ estimatedBuyAmount }}</span>
                </div>
                <div class="summary-row">
                  <span class="summary-label">手续费</span>
                  <span class="summary-value">¥{{ estimatedFee }}</span>
                </div>
                <div class="summary-row">
                  <span class="summary-label">可买数量</span>
                  <span class="summary-value highlight">{{ maxBuyQuantity }}股</span>
                </div>
              </div>

              <!-- 提交按钮 -->
              <el-button
                type="primary"
                size="large"
                class="submit-btn buy-submit"
                @click="submitBuyOrder"
                :disabled="!canSubmitBuy"
                :loading="submitting"
              >
                <el-icon><Plus /></el-icon>
                确认买入
              </el-button>
            </div>
          </div>

          <!-- 卖出表单 -->
          <div v-if="activeTab === 'sell'" class="trade-form sell-form">
            <div class="form-header">
              <h3 class="form-title">
                <el-icon class="title-icon"><Minus /></el-icon>
                卖出委托
              </h3>
            </div>

            <div class="form-content">
              <!-- 持仓选择 -->
              <div class="form-group">
                <label class="form-label">选择持仓</label>
                <el-select
                  v-model="tradeForm.sellStockCode"
                  size="default"
                  class="position-select"
                  placeholder="选择要卖出的股票"
                  @change="onSellStockChange"
                >
                  <el-option
                    v-for="position in availablePositions"
                    :key="position.code"
                    :label="`${position.name} (${position.code})`"
                    :value="position.code"
                  >
                    <div class="position-option">
                      <span class="position-name">{{ position.name }}</span>
                      <span class="position-code">{{ position.code }}</span>
                      <span class="position-qty">{{ position.availableQuantity }}股</span>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 价格设置 -->
              <div class="form-group">
                <label class="form-label">委托价格</label>
                <div class="price-input-group">
                  <el-input-number
                    v-model="tradeForm.sellPrice"
                    :precision="2"
                    :step="0.01"
                    :min="0.01"
                    size="default"
                    class="price-input"
                    placeholder="输入价格"
                  />
                  <div class="quick-price-buttons">
                    <el-button size="small" @click="setSellPrice('buy1')" class="price-btn">买一</el-button>
                    <el-button size="small" @click="setSellPrice('current')" class="price-btn">现价</el-button>
                    <el-button size="small" @click="setSellPrice('sell1')" class="price-btn">卖一</el-button>
                  </div>
                </div>
              </div>

              <!-- 数量设置 -->
              <div class="form-group">
                <label class="form-label">委托数量</label>
                <div class="quantity-input-group">
                  <el-input-number
                    v-model="tradeForm.sellQuantity"
                    :min="100"
                    :step="100"
                    size="default"
                    class="quantity-input"
                    placeholder="输入数量"
                  />
                  <div class="quick-quantity-buttons">
                    <el-button size="small" @click="setSellQuantity('half')" class="qty-btn">1/2</el-button>
                    <el-button size="small" @click="setSellQuantity('all')" class="qty-btn max-btn">全部</el-button>
                  </div>
                </div>
              </div>

              <!-- 交易摘要 -->
              <div class="trade-summary">
                <div class="summary-row">
                  <span class="summary-label">预估金额</span>
                  <span class="summary-value">¥{{ estimatedSellAmount }}</span>
                </div>
                <div class="summary-row">
                  <span class="summary-label">手续费</span>
                  <span class="summary-value">¥{{ estimatedSellFee }}</span>
                </div>
                <div class="summary-row">
                  <span class="summary-label">可卖数量</span>
                  <span class="summary-value highlight">{{ availableSellQuantity }}股</span>
                </div>
              </div>

              <!-- 提交按钮 -->
              <el-button
                type="danger"
                size="large"
                class="submit-btn sell-submit"
                @click="submitSellOrder"
                :disabled="!canSubmitSell"
                :loading="submitting"
              >
                <el-icon><Minus /></el-icon>
                确认卖出
              </el-button>
            </div>
          </div>
        </div>

        <!-- 无股票选择时的提示 -->
        <div v-if="!currentStock" class="no-stock-selected">
          <div class="empty-icon">
            <el-icon size="32"><Search /></el-icon>
          </div>
          <h4>请先选择股票</h4>
          <p>在上方搜索框输入股票代码、名称或拼音缩写</p>
        </div>
      </div>
    </div>

    <!-- 底部数据面板 -->
    <div class="bottom-data-panel">
      <div class="data-tabs">
        <el-tabs v-model="bottomActiveTab" class="modern-tabs">
          <el-tab-pane name="positions">
            <template #label>
              <div class="tab-label">
                <el-icon><Wallet /></el-icon>
                <span>持仓明细</span>
                <el-badge :value="positions.length" class="tab-badge" v-if="positions.length > 0" />
              </div>
            </template>
            <div class="positions-container">
              <el-table :data="positions" class="modern-table" stripe>
                <el-table-column prop="stockCode" label="代码" width="80" />
                <el-table-column prop="stockName" label="名称" width="120" />
                <el-table-column prop="quantity" label="持仓" width="80" />
                <el-table-column prop="avgPrice" label="成本价" width="80">
                  <template #default="{ row }">
                    ¥{{ (row.avgPrice || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="currentPrice" label="现价" width="80">
                  <template #default="{ row }">
                    ¥{{ (row.currentPrice || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="marketValue" label="市值" width="100">
                  <template #default="{ row }">
                    ¥{{ (row.marketValue || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="pnl" label="盈亏" width="100">
                  <template #default="{ row }">
                    <span :class="(row.pnl || 0) >= 0 ? 'profit' : 'loss'">
                      {{ (row.pnl || 0) >= 0 ? '+' : '' }}¥{{ Math.abs(row.pnl || 0).toFixed(2) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="{ row }">
                    <el-button size="small" type="danger" @click="quickSell(row)">
                      卖出
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane name="orders">
            <template #label>
              <div class="tab-label">
                <el-icon><Document /></el-icon>
                <span>委托记录</span>
                <el-badge :value="orders.length" class="tab-badge" v-if="orders.length > 0" />
              </div>
            </template>
            <div class="orders-container">
              <el-table :data="orders" class="modern-table" stripe>
                <el-table-column prop="orderTime" label="时间" width="120" />
                <el-table-column prop="stockCode" label="代码" width="80" />
                <el-table-column prop="stockName" label="名称" width="120" />
                <el-table-column prop="direction" label="方向" width="60">
                  <template #default="{ row }">
                    <el-tag :type="row.direction === '买入' ? 'success' : 'danger'" size="small">
                      {{ row.direction }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="price" label="价格" width="80">
                  <template #default="{ row }">
                    ¥{{ (row.price || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag :type="getOrderStatusType(row.status)" size="small">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane name="trades">
            <template #label>
              <div class="tab-label">
                <el-icon><Check /></el-icon>
                <span>成交记录</span>
                <el-badge :value="trades.length" class="tab-badge" v-if="trades.length > 0" />
              </div>
            </template>
            <div class="trades-container">
              <el-table :data="trades" class="modern-table" stripe>
                <el-table-column prop="tradeTime" label="时间" width="120" />
                <el-table-column prop="stockCode" label="代码" width="80" />
                <el-table-column prop="stockName" label="名称" width="120" />
                <el-table-column prop="direction" label="方向" width="60">
                  <template #default="{ row }">
                    <el-tag :type="row.direction === '买入' ? 'success' : 'danger'" size="small">
                      {{ row.direction }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="price" label="价格" width="80">
                  <template #default="{ row }">
                    ¥{{ (row.price || 0).toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="amount" label="金额" width="100">
                  <template #default="{ row }">
                    ¥{{ (row.amount || 0).toFixed(2) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  TrendCharts,
  Minus,
  Plus,
  RefreshRight,
  Delete,
  Wallet,
  Document,
  Check
} from '@element-plus/icons-vue'
import {
  getCurrentAccount,
  createSimulatedOrder,
  resetSimulatedAccount
} from '@/api/simulated'
import { MarketService } from '@/api/market'
import tradingApi from '@/api/trading'

// 创建服务实例
const marketService = new MarketService()

// 响应式数据
const stockCode = ref('')
const currentStock = ref<any>(null)
const activeTab = ref('buy')
const bottomActiveTab = ref('positions')
const submitting = ref(false)
const loading = ref(false)
const dataUpdateTimer = ref<number | null>(null)

// 账户信息
const accountInfo = ref({
  totalAssets: 1000000,
  availableFunds: 800000,
  totalProfit: 50000
})

// 交易表单
const tradeForm = ref({
  buyPrice: 0,
  buyQuantity: 0,
  sellStockCode: '',
  sellPrice: 0,
  sellQuantity: 0
})

// 市场状态
const marketStatus = ref({
  class: 'trading',
  text: '交易中'
})

const currentTime = ref(new Date().toLocaleTimeString())

// 模拟股票数据
const mockStocks = [
  { code: '000001', name: '平安银行', pinyin: 'PAYH', price: 10.80, change: 0.30, changePercent: 2.85, industry: '银行' },
  { code: '000002', name: '万科A', pinyin: 'WKA', price: 8.50, change: -0.15, changePercent: -1.73, industry: '房地产' },
  { code: '000858', name: '五粮液', pinyin: 'WLY', price: 128.50, change: 2.30, changePercent: 1.82, industry: '白酒' },
  { code: '600036', name: '招商银行', pinyin: 'ZSYH', price: 35.20, change: 0.80, changePercent: 2.33, industry: '银行' },
  { code: '600519', name: '贵州茅台', pinyin: 'GZMT', price: 1680.00, change: -15.50, changePercent: -0.91, industry: '白酒' }
]

// 持仓数据
const positions = ref([
  {
    stockCode: '000001',
    stockName: '平安银行',
    quantity: 1000,
    avgPrice: 10.50,
    currentPrice: 10.80,
    marketValue: 10800,
    pnl: 300
  }
])

// 委托数据
const orders = ref([
  {
    orderTime: '2024-01-15 09:30:00',
    stockCode: '000002',
    stockName: '万科A',
    direction: '买入',
    price: 8.50,
    quantity: 1000,
    status: '已报'
  }
])

// 成交数据
const trades = ref([
  {
    tradeTime: '2024-01-15 09:31:00',
    stockCode: '000001',
    stockName: '平安银行',
    direction: '买入',
    price: 10.50,
    quantity: 1000,
    amount: 10500
  }
])

// 计算属性
const estimatedBuyAmount = computed(() => {
  return ((tradeForm.value.buyPrice || 0) * (tradeForm.value.buyQuantity || 0)).toFixed(2)
})

const estimatedFee = computed(() => {
  const amount = parseFloat(estimatedBuyAmount.value) || 0
  return Math.max(amount * 0.0003, 5).toFixed(2)
})

const estimatedSellAmount = computed(() => {
  return ((tradeForm.value.sellPrice || 0) * (tradeForm.value.sellQuantity || 0)).toFixed(2)
})

const estimatedSellFee = computed(() => {
  const amount = parseFloat(estimatedSellAmount.value) || 0
  return Math.max(amount * 0.0003, 5).toFixed(2)
})

const maxBuyQuantity = computed(() => {
  const price = tradeForm.value.buyPrice || 0
  if (price <= 0) return 0
  return Math.floor((accountInfo.value.availableFunds || 0) / price / 100) * 100
})

const availableSellQuantity = computed(() => {
  const position = availablePositions.value.find(p => p.code === tradeForm.value.sellStockCode)
  return position?.availableQuantity || 0
})

const availablePositions = computed(() => {
  return positions.value.map(p => ({
    code: p.stockCode,
    name: p.stockName,
    availableQuantity: p.quantity
  }))
})

const canSubmitBuy = computed(() => {
  return currentStock.value &&
         tradeForm.value.buyPrice > 0 &&
         tradeForm.value.buyQuantity >= 100 &&
         tradeForm.value.buyQuantity <= maxBuyQuantity.value
})

const canSubmitSell = computed(() => {
  return tradeForm.value.sellStockCode &&
         tradeForm.value.sellPrice > 0 &&
         tradeForm.value.sellQuantity >= 100 &&
         tradeForm.value.sellQuantity <= availableSellQuantity.value
})

// 工具方法
const getPriceClass = (change: number) => {
  if (change > 0) return 'price-up'
  if (change < 0) return 'price-down'
  return 'price-flat'
}

const getProfitClass = (profit: number) => {
  if (profit > 0) return 'profit'
  if (profit < 0) return 'loss'
  return 'price-flat'
}

const formatMoney = (amount: number) => {
  return new Intl.NumberFormat('zh-CN').format(amount || 0)
}

const formatVolume = (volume: number) => {
  const vol = volume || 0
  if (vol >= 10000) {
    return (vol / 10000).toFixed(1) + '万'
  }
  return vol.toString()
}

// API数据加载方法
const loadAccountData = async () => {
  try {
    loading.value = true
    const response = await getCurrentAccount()
    if (response.data.success) {
      const account = response.data.data
      accountInfo.value = {
        totalAssets: account.total_assets || 1000000,
        availableFunds: account.available_cash || 800000,
        totalProfit: account.total_profit || 0
      }
    }
  } catch (error) {
    console.error('加载账户数据失败:', error)
    // 使用模拟数据作为后备
  } finally {
    loading.value = false
  }
}

const loadPositionsData = async () => {
  try {
    const response = await tradingApi.getPositions()
    if (response.success) {
      positions.value = (response.data || []).map((pos: any) => ({
        stockCode: pos.symbol,
        stockName: pos.name,
        quantity: pos.quantity,
        avgPrice: pos.avg_cost,
        currentPrice: pos.current_price,
        marketValue: pos.market_value,
        pnl: pos.unrealized_pnl
      }))
    }
  } catch (error) {
    console.error('加载持仓数据失败:', error)
  }
}

const loadOrdersData = async () => {
  try {
    const response = await tradingApi.getOrders()
    if (response.success) {
      orders.value = (response.data || []).map((order: any) => ({
        orderTime: order.created_at,
        stockCode: order.symbol,
        stockName: order.name,
        direction: order.side === 'BUY' ? '买入' : '卖出',
        price: order.price,
        quantity: order.quantity,
        status: order.status
      }))
    }
  } catch (error) {
    console.error('加载委托数据失败:', error)
  }
}

const loadTradesData = async () => {
  try {
    const response = await tradingApi.getTrades()
    if (response.success) {
      trades.value = (response.data || []).map((trade: any) => ({
        tradeTime: trade.executed_at,
        stockCode: trade.symbol,
        stockName: trade.name,
        direction: trade.side === 'BUY' ? '买入' : '卖出',
        price: trade.price,
        quantity: trade.quantity,
        amount: trade.amount
      }))
    }
  } catch (error) {
    console.error('加载成交数据失败:', error)
  }
}

// 搜索方法 - 集成真实API
const searchStocks = async (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  try {
    // 尝试调用真实API
    const results = await marketService.searchStocks({ keyword: queryString, limit: 10 })
    const formattedResults = results.map((stock: any) => ({
      value: stock.symbol,
      code: stock.symbol,
      name: stock.name,
      price: stock.current_price,
      change: stock.change,
      changePercent: stock.change_percent,
      industry: stock.industry || '未知'
    }))
    cb(formattedResults)
    return
  } catch (error) {
    console.warn('API搜索失败，使用模拟数据:', error)
  }

  // 后备：使用模拟数据
  const results = mockStocks.filter(stock =>
    stock.code.includes(queryString) ||
    stock.name.includes(queryString) ||
    stock.pinyin.includes(queryString.toUpperCase())
  ).map(stock => ({
    value: stock.code,
    ...stock
  }))

  cb(results)
}

const selectStock = (item: any) => {
  currentStock.value = {
    ...item,
    updateTime: new Date().toLocaleTimeString(),
    limitUp: (item.price * 1.1).toFixed(2),
    limitDown: (item.price * 0.9).toFixed(2),
    amplitude: '3.2',
    turnover: '1.8',
    pe: '8.5',
    marketCap: '2580',
    sellLevels: [
      { price: (item.price + 0.05).toFixed(2), volume: 1000 },
      { price: (item.price + 0.04).toFixed(2), volume: 2000 },
      { price: (item.price + 0.03).toFixed(2), volume: 1500 },
      { price: (item.price + 0.02).toFixed(2), volume: 3000 },
      { price: (item.price + 0.01).toFixed(2), volume: 2500 }
    ],
    buyLevels: [
      { price: (item.price - 0.01).toFixed(2), volume: 2000 },
      { price: (item.price - 0.02).toFixed(2), volume: 1800 },
      { price: (item.price - 0.03).toFixed(2), volume: 2200 },
      { price: (item.price - 0.04).toFixed(2), volume: 1600 },
      { price: (item.price - 0.05).toFixed(2), volume: 3000 }
    ]
  }

  // 设置默认交易价格
  tradeForm.value.buyPrice = item.price
  tradeForm.value.sellPrice = item.price
}

// 更新时间
setInterval(() => {
  currentTime.value = new Date().toLocaleTimeString()
}, 1000)

// 交易方法
const setTradePrice = (price: string) => {
  const priceNum = parseFloat(price)
  if (activeTab.value === 'buy') {
    tradeForm.value.buyPrice = priceNum
  } else if (activeTab.value === 'sell') {
    tradeForm.value.sellPrice = priceNum
  }
}

const setBuyPrice = (type: string) => {
  if (!currentStock.value) return

  switch (type) {
    case 'sell1':
      tradeForm.value.buyPrice = parseFloat(currentStock.value.sellLevels[4].price)
      break
    case 'current':
      tradeForm.value.buyPrice = currentStock.value.price
      break
    case 'buy1':
      tradeForm.value.buyPrice = parseFloat(currentStock.value.buyLevels[0].price)
      break
  }
}

const setSellPrice = (type: string) => {
  if (!currentStock.value) return

  switch (type) {
    case 'buy1':
      tradeForm.value.sellPrice = parseFloat(currentStock.value.buyLevels[0].price)
      break
    case 'current':
      tradeForm.value.sellPrice = currentStock.value.price
      break
    case 'sell1':
      tradeForm.value.sellPrice = parseFloat(currentStock.value.sellLevels[4].price)
      break
  }
}

const setQuantityByPosition = (type: string) => {
  const maxQty = maxBuyQuantity.value
  switch (type) {
    case 'quarter':
      tradeForm.value.buyQuantity = Math.floor(maxQty * 0.25 / 100) * 100
      break
    case 'half':
      tradeForm.value.buyQuantity = Math.floor(maxQty * 0.5 / 100) * 100
      break
    case 'full':
      tradeForm.value.buyQuantity = maxQty
      break
  }
}

const setSellQuantity = (type: string) => {
  const maxQty = availableSellQuantity.value
  switch (type) {
    case 'half':
      tradeForm.value.sellQuantity = Math.floor(maxQty * 0.5 / 100) * 100
      break
    case 'all':
      tradeForm.value.sellQuantity = maxQty
      break
  }
}

const onSellStockChange = () => {
  // 当选择卖出股票时，自动设置价格
  const position = availablePositions.value.find(p => p.code === tradeForm.value.sellStockCode)
  if (position && currentStock.value && currentStock.value.code === position.code) {
    tradeForm.value.sellPrice = currentStock.value.price
  }
}

const submitBuyOrder = async () => {
  submitting.value = true

  try {
    // 尝试调用真实API
    const orderData = {
      symbol: currentStock.value.code,
      side: 'BUY' as const,
      order_type: 'LIMIT' as const,
      quantity: tradeForm.value.buyQuantity,
      price: tradeForm.value.buyPrice
    }

    const response = await createSimulatedOrder(orderData)

    if (response.data.success) {
      ElMessage.success('买入委托已提交')

      // 重新加载数据
      await Promise.all([
        loadAccountData(),
        loadOrdersData(),
        loadPositionsData()
      ])
    } else {
      throw new Error(response.data.message || '提交失败')
    }
  } catch (error) {
    console.error('提交买入订单失败:', error)
    ElMessage.error('买入委托提交失败，请重试')

    // 后备：添加到本地列表（模拟模式）
    orders.value.unshift({
      orderTime: new Date().toLocaleString(),
      stockCode: currentStock.value.code,
      stockName: currentStock.value.name,
      direction: '买入',
      price: tradeForm.value.buyPrice,
      quantity: tradeForm.value.buyQuantity,
      status: '已报'
    })
    ElMessage.success('买入委托已提交（模拟模式）')
  } finally {
    // 清空表单
    tradeForm.value.buyPrice = 0
    tradeForm.value.buyQuantity = 0
    submitting.value = false
  }
}

const submitSellOrder = async () => {
  submitting.value = true

  try {
    // 尝试调用真实API
    const orderData = {
      symbol: tradeForm.value.sellStockCode,
      side: 'SELL' as const,
      order_type: 'LIMIT' as const,
      quantity: tradeForm.value.sellQuantity,
      price: tradeForm.value.sellPrice
    }

    const response = await createSimulatedOrder(orderData)

    if (response.data.success) {
      ElMessage.success('卖出委托已提交')

      // 重新加载数据
      await Promise.all([
        loadAccountData(),
        loadOrdersData(),
        loadPositionsData()
      ])
    } else {
      throw new Error(response.data.message || '提交失败')
    }
  } catch (error) {
    console.error('提交卖出订单失败:', error)
    ElMessage.error('卖出委托提交失败，请重试')

    // 后备：添加到本地列表（模拟模式）
    const position = availablePositions.value.find(p => p.code === tradeForm.value.sellStockCode)
    orders.value.unshift({
      orderTime: new Date().toLocaleString(),
      stockCode: tradeForm.value.sellStockCode,
      stockName: position?.name || '',
      direction: '卖出',
      price: tradeForm.value.sellPrice,
      quantity: tradeForm.value.sellQuantity,
      status: '已报'
    })
    ElMessage.success('卖出委托已提交（模拟模式）')
  } finally {
    // 清空表单
    tradeForm.value.sellPrice = 0
    tradeForm.value.sellQuantity = 0
    tradeForm.value.sellStockCode = ''
    submitting.value = false
  }
}

const resetAccount = async () => {
  try {
    loading.value = true

    // 尝试调用真实API重置账户
    const response = await resetSimulatedAccount('current')

    if (response.data.success) {
      ElMessage.success('账户已重置')

      // 重新加载所有数据
      await Promise.all([
        loadAccountData(),
        loadPositionsData(),
        loadOrdersData(),
        loadTradesData()
      ])
    } else {
      throw new Error(response.data.message || '重置失败')
    }
  } catch (error) {
    console.error('重置账户失败:', error)
    ElMessage.error('账户重置失败，请重试')

    // 后备：本地重置（模拟模式）
    accountInfo.value = {
      totalAssets: 1000000,
      availableFunds: 800000,
      totalProfit: 0
    }
    positions.value = []
    orders.value = []
    trades.value = []
    ElMessage.success('账户已重置（模拟模式）')
  } finally {
    loading.value = false
  }
}

const clearAllPositions = () => {
  positions.value = []
  ElMessage.success('已清空所有持仓')
}

// 数据刷新方法
const refreshAllData = async () => {
  try {
    loading.value = true
    await Promise.all([
      loadAccountData(),
      loadPositionsData(),
      loadOrdersData(),
      loadTradesData()
    ])
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 定时刷新数据
const startDataRefresh = () => {
  // 每30秒刷新一次数据
  dataUpdateTimer.value = window.setInterval(() => {
    refreshAllData()
  }, 30000)
}

const stopDataRefresh = () => {
  if (dataUpdateTimer.value) {
    clearInterval(dataUpdateTimer.value)
    dataUpdateTimer.value = null
  }
}

const quickSell = (position: any) => {
  tradeForm.value.sellStockCode = position.stockCode
  tradeForm.value.sellQuantity = position.quantity
  if (currentStock.value && currentStock.value.code === position.stockCode) {
    tradeForm.value.sellPrice = currentStock.value.price
  }
  activeTab.value = 'sell'
  ElMessage.info('已填入卖出信息，请确认后提交')
}

const getOrderStatusType = (status: string) => {
  switch (status) {
    case '已报': return 'warning'
    case '部成': return 'primary'
    case '已成': return 'success'
    case '已撤': return 'info'
    default: return 'default'
  }
}

onMounted(async () => {
  console.log('SimulatedTrading 现代化组件已挂载')

  // 初始化数据加载
  await refreshAllData()

  // 开始定时刷新
  startDataRefresh()

  // 更新市场状态
  const now = new Date()
  const hour = now.getHours()
  const minute = now.getMinutes()
  const currentTime = hour * 100 + minute

  if ((currentTime >= 930 && currentTime <= 1130) || (currentTime >= 1300 && currentTime <= 1500)) {
    marketStatus.value = {
      class: 'trading',
      text: '交易中'
    }
  } else {
    marketStatus.value = {
      class: 'closed',
      text: '休市'
    }
  }
})

onUnmounted(() => {
  // 清理定时器
  stopDataRefresh()
  console.log('SimulatedTrading 组件已卸载')
})
</script>

<style scoped>
/* 现代化模拟交易页面样式 */
.simulated-trading-modern {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 现代化头部导航栏 */
.modern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.brand-section {
  display: flex;
  align-items: center;
}

.simulation-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.sim-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
}

.search-section {
  width: 320px;
}

.modern-search {
  width: 100%;
}

.modern-search .el-input__wrapper {
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.search-icon {
  color: #909399;
}

.search-suggestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.suggestion-main {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stock-code {
  font-weight: 600;
  color: #303133;
  font-size: 13px;
}

.stock-name {
  color: #606266;
  font-size: 12px;
}

.suggestion-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.price {
  font-weight: 600;
  font-size: 12px;
}

.change {
  font-size: 11px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
}

.account-dashboard {
  display: flex;
  gap: 32px;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.account-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.account-label {
  font-size: 11px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.account-value {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
}

.account-value.primary {
  color: #409eff;
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.action-group {
  display: flex;
  gap: 8px;
}

.action-btn {
  border-radius: 16px;
  font-weight: 500;
}

.status-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.market-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.trading {
  background: #67c23a;
  box-shadow: 0 0 6px rgba(103, 194, 58, 0.6);
}

.status-dot.closed {
  background: #f56c6c;
}

.current-time {
  font-size: 11px;
  color: #909399;
}

/* 主要工作区域 */
.trading-workspace {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.left-panel, .right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 股票信息卡片 */
.stock-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.stock-title-section h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #303133;
}

.stock-code {
  color: #909399;
  font-size: 14px;
  margin-right: 8px;
}

.industry-tag {
  background: #f0f2f5;
  color: #606266;
}

.stock-price-section {
  text-align: right;
}

.main-price {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.price-change {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 14px;
  font-weight: 600;
}

.price-limits {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.limit-item {
  flex: 1;
  padding: 8px 12px;
  border-radius: 8px;
  text-align: center;
}

.limit-item.limit-up {
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.2);
}

.limit-item.limit-down {
  background: rgba(103, 194, 58, 0.1);
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.limit-label {
  display: block;
  font-size: 11px;
  color: #909399;
  margin-bottom: 4px;
}

.limit-value {
  font-weight: 600;
  font-size: 14px;
}

/* 深度盘口 */
.depth-panel {
  margin-bottom: 20px;
}

.depth-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.depth-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.update-time {
  font-size: 11px;
  color: #909399;
}

.depth-book {
  background: #fafbfc;
  border-radius: 8px;
  padding: 12px;
}

.depth-row {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.depth-row:hover {
  background: rgba(64, 158, 255, 0.1);
}

.sell-row {
  color: #f56c6c;
}

.buy-row {
  color: #67c23a;
}

.level-label {
  width: 40px;
  font-weight: 500;
}

.level-price {
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.level-volume {
  width: 60px;
  text-align: right;
  color: #909399;
}

.current-price-divider {
  display: flex;
  align-items: center;
  margin: 8px 0;
  gap: 8px;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e4e7ed, transparent);
}

.current-price-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 13px;
}

.price-arrow {
  font-size: 12px;
}

/* 关键指标 */
.key-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.metric-item {
  text-align: center;
  padding: 12px;
  background: #fafbfc;
  border-radius: 8px;
}

.metric-label {
  font-size: 11px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 空状态卡片 */
.empty-stock-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 48px 24px;
  text-align: center;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-stock-card h3 {
  margin: 0 0 8px 0;
  color: #606266;
  font-weight: 500;
}

.empty-stock-card p {
  margin: 0 0 16px 0;
  color: #909399;
  font-size: 14px;
}

.search-examples {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.example-tag {
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 12px;
  font-size: 12px;
  color: #606266;
}

/* 交易模式选择器 */
.trade-mode-selector {
  margin-bottom: 20px;
}

.mode-tabs {
  display: flex;
  background: #f5f7fa;
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
}

.mode-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #606266;
}

.mode-tab:hover {
  background: rgba(255, 255, 255, 0.5);
}

.mode-tab.active {
  background: white;
  color: #303133;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-icon.buy-icon {
  color: #f56c6c;
}

.tab-icon.sell-icon {
  color: #67c23a;
}

/* 交易表单容器 */
.trade-form-container {
  flex: 1;
}

.trade-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.form-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.title-icon {
  font-size: 20px;
}

.buy-form .title-icon {
  color: #f56c6c;
}

.sell-form .title-icon {
  color: #67c23a;
}

.selected-stock {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.price-input-group, .quantity-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-input, .quantity-input {
  width: 100%;
}

.quick-price-buttons, .quick-quantity-buttons {
  display: flex;
  gap: 8px;
}

.price-btn, .qty-btn {
  flex: 1;
  border-radius: 8px;
  font-size: 12px;
}

.max-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.max-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.position-select {
  width: 100%;
}

.position-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.position-name {
  font-weight: 500;
}

.position-code {
  color: #909399;
  font-size: 12px;
}

.position-qty {
  color: #606266;
  font-size: 12px;
}

/* 交易摘要 */
.trade-summary {
  background: #fafbfc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 13px;
  color: #606266;
}

.summary-value {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
}

.summary-value.highlight {
  color: #409eff;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.buy-submit {
  background: linear-gradient(135deg, #f56c6c 0%, #e85656 100%);
  border: none;
  color: white;
}

.buy-submit:hover {
  background: linear-gradient(135deg, #f45454 0%, #d64545 100%);
}

.sell-submit {
  background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
  border: none;
  color: white;
}

.sell-submit:hover {
  background: linear-gradient(135deg, #5cb85c 0%, #4cae4c 100%);
}

/* 无股票选择提示 */
.no-stock-selected {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 48px 24px;
  text-align: center;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.no-stock-selected h4 {
  margin: 16px 0 8px 0;
  color: #606266;
  font-weight: 500;
}

.no-stock-selected p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 底部数据面板 */
.bottom-data-panel {
  height: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding: 16px 24px;
}

.data-tabs {
  height: 100%;
}

.modern-tabs {
  height: 100%;
}

.modern-tabs .el-tabs__content {
  height: calc(100% - 40px);
  overflow: hidden;
}

.modern-tabs .el-tab-pane {
  height: 100%;
  overflow-y: auto;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-badge {
  margin-left: 4px;
}

.positions-container, .orders-container, .trades-container {
  height: 100%;
}

.modern-table {
  height: 100%;
}

.modern-table .el-table__body-wrapper {
  max-height: calc(100% - 40px);
  overflow-y: auto;
}

/* 价格颜色 */
.price-up { color: #f56c6c; }
.price-down { color: #67c23a; }
.price-flat { color: #909399; }

.profit { color: #f56c6c; }
.loss { color: #67c23a; }

/* 响应式设计 */
@media (max-width: 1200px) {
  .trading-workspace {
    flex-direction: column;
    gap: 12px;
  }

  .account-dashboard {
    gap: 16px;
  }

  .key-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .modern-header {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .header-left, .header-center, .header-right {
    width: 100%;
  }

  .search-section {
    width: 100%;
  }

  .account-dashboard {
    gap: 12px;
    padding: 8px 16px;
  }

  .account-value {
    font-size: 14px;
  }

  .account-value.primary {
    font-size: 16px;
  }
}
</style>
