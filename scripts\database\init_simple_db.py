#!/usr/bin/env python3
"""
简化版数据库初始化脚本
专为Python 3.13和简化环境设计
只依赖最基本的库，不依赖复杂的数据模型
"""
import asyncio
import sqlite3
import os
import sys
from pathlib import Path
import hashlib
import uuid
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

DB_PATH = "./quant_simple.db"


def hash_password(password: str) -> str:
    """简单的密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()


def generate_uuid() -> str:
    """生成UUID"""
    return str(uuid.uuid4())


def init_database():
    """初始化数据库"""
    print("🚀 Initializing Simple Quant Platform Database...")
    print("=" * 50)
    
    # 删除旧数据库文件
    if os.path.exists(DB_PATH):
        os.remove(DB_PATH)
        print("✓ Removed old database file")
    
    # 创建数据库连接
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # 创建用户表
        cursor.execute("""
            CREATE TABLE users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT,
                hashed_password TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                is_verified BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✓ Created users table")
        
        # 创建股票表
        cursor.execute("""
            CREATE TABLE stocks (
                id TEXT PRIMARY KEY,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                exchange TEXT NOT NULL,
                sector TEXT,
                industry TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✓ Created stocks table")
        
        # 创建行情数据表
        cursor.execute("""
            CREATE TABLE market_data (
                id TEXT PRIMARY KEY,
                stock_code TEXT NOT NULL,
                price REAL,
                volume INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                FOREIGN KEY (stock_code) REFERENCES stocks (code)
            )
        """)
        print("✓ Created market_data table")
        
        # 创建订单表
        cursor.execute("""
            CREATE TABLE orders (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                order_type TEXT NOT NULL,  -- buy/sell
                quantity INTEGER NOT NULL,
                price REAL,
                status TEXT DEFAULT 'pending',  -- pending/filled/cancelled
                submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                filled_time TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (stock_code) REFERENCES stocks (code)
            )
        """)
        print("✓ Created orders table")
        
        # 创建持仓表
        cursor.execute("""
            CREATE TABLE positions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                current_price REAL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (stock_code) REFERENCES stocks (code),
                UNIQUE(user_id, stock_code)
            )
        """)
        print("✓ Created positions table")
        
        # 创建策略表
        cursor.execute("""
            CREATE TABLE strategies (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                status TEXT DEFAULT 'draft',  -- draft/active/stopped
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        print("✓ Created strategies table")
        
        # 创建索引
        cursor.execute("CREATE INDEX idx_market_data_stock_time ON market_data(stock_code, timestamp DESC)")
        cursor.execute("CREATE INDEX idx_orders_user_status ON orders(user_id, status)")
        cursor.execute("CREATE INDEX idx_positions_user ON positions(user_id)")
        print("✓ Created database indexes")
        
        # 插入管理员用户
        admin_id = generate_uuid()
        cursor.execute("""
            INSERT INTO users (id, username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            admin_id,
            "admin",
            "<EMAIL>", 
            "系统管理员",
            hash_password("admin123456"),
            1,
            1,
            1
        ))
        print("✓ Created admin user")
        
        # 插入示例股票数据
        stocks_data = [
            ("000001", "平安银行", "SZSE", "金融", "银行"),
            ("000002", "万科A", "SZSE", "房地产", "房地产开发"),
            ("600000", "浦发银行", "SSE", "金融", "银行"),
            ("600036", "招商银行", "SSE", "金融", "银行"),
            ("600519", "贵州茅台", "SSE", "消费", "白酒"),
            ("000858", "五粮液", "SZSE", "消费", "白酒"),
            ("300015", "爱尔眼科", "SZSE", "医疗", "医疗服务"),
            ("002415", "海康威视", "SZSE", "科技", "安防设备"),
        ]
        
        for code, name, exchange, sector, industry in stocks_data:
            cursor.execute("""
                INSERT INTO stocks (id, code, name, exchange, sector, industry)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (generate_uuid(), code, name, exchange, sector, industry))
        
        print(f"✓ Inserted {len(stocks_data)} sample stocks")
        
        # 插入示例策略
        strategies_data = [
            ("双均线策略", "基于5日和20日移动平均线的经典趋势跟踪策略"),
            ("RSI均值回归策略", "基于RSI指标的均值回归策略"),
            ("布林带突破策略", "基于布林带的突破策略"),
        ]
        
        for name, description in strategies_data:
            cursor.execute("""
                INSERT INTO strategies (id, user_id, name, description, status)
                VALUES (?, ?, ?, ?, ?)
            """, (generate_uuid(), admin_id, name, description, "draft"))
        
        print(f"✓ Inserted {len(strategies_data)} sample strategies")
        
        # 插入一些示例行情数据
        import random
        import time
        
        base_prices = {
            "000001": 12.50,
            "000002": 18.30,
            "600000": 9.80,
            "600036": 32.50,
            "600519": 1680.00,
            "000858": 128.50,
            "300015": 45.20,
            "002415": 28.90,
        }
        
        for code, base_price in base_prices.items():
            for i in range(10):  # 为每只股票插入10条历史数据
                price_change = random.uniform(-0.05, 0.05)  # ±5%波动
                current_price = base_price * (1 + price_change)
                volume = random.randint(1000000, 50000000)
                
                # 模拟OHLC数据
                open_price = current_price * random.uniform(0.98, 1.02)
                high_price = max(open_price, current_price) * random.uniform(1.0, 1.03)
                low_price = min(open_price, current_price) * random.uniform(0.97, 1.0)
                
                timestamp = datetime.now() - timedelta(days=i)
                
                cursor.execute("""
                    INSERT INTO market_data (id, stock_code, price, volume, timestamp, 
                                           open_price, high_price, low_price, close_price)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    generate_uuid(),
                    code,
                    current_price,
                    volume,
                    timestamp,
                    open_price,
                    high_price,
                    low_price,
                    current_price
                ))
        
        print("✓ Inserted sample market data")
        
        # 提交事务
        conn.commit()
        
        print("=" * 50)
        print("✅ Simple database initialization completed successfully!")
        print(f"\n📁 Database file: {os.path.abspath(DB_PATH)}")
        print("\n📋 Default Login Credentials:")
        print("   Email: <EMAIL>")
        print("   Password: admin123456")
        print("\n🔗 Database schema successfully created with sample data")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


def verify_database():
    """验证数据库是否正确创建"""
    print("\n🔍 Verifying database...")
    
    if not os.path.exists(DB_PATH):
        print("❌ Database file not found")
        return False
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        expected_tables = ['users', 'stocks', 'market_data', 'orders', 'positions', 'strategies']
        
        for table in expected_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✓ Table '{table}': {count} records")
            else:
                print(f"❌ Table '{table}' not found")
                return False
        
        print("✅ Database verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False
    finally:
        conn.close()


if __name__ == "__main__":
    try:
        init_database()
        verify_database()
    except Exception as e:
        print(f"❌ Script failed: {e}")
        sys.exit(1)