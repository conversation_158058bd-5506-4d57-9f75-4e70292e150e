"""
市场数据API路由
提供行情数据、合约信息、市场统计等功能
"""

from fastapi import APIRouter, Depends
from typing import List

from app.core.dependencies_fixed import get_current_user
from app.db.models.user import User
from app.schemas.market_data import WatchlistItemCreate, WatchlistItemResponse
from app.services.market_service import MarketService

router = APIRouter()


@router.get("/watchlist", response_model=List[WatchlistItemResponse])
async def get_watchlist(
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: MarketService()),
):
    """
    获取用户的自选股列表
    """
    return await market_service.get_watchlist(user_id=current_user.id)


@router.post("/watchlist", response_model=WatchlistItemResponse)
async def add_to_watchlist(
    item: WatchlistItemCreate,
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: MarketService()),
):
    """
    向用户的自选股列表添加一个项目
    """
    return await market_service.add_to_watchlist(user_id=current_user.id, item=item)


@router.delete("/watchlist/{symbol}", status_code=204)
async def remove_from_watchlist(
    symbol: str,
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: MarketService()),
):
    """
    从用户的自选股列表中删除一个项目
    """
    await market_service.remove_from_watchlist(user_id=current_user.id, symbol=symbol)
    return {"ok": True}


@router.post("/watchlist/reorder", status_code=204)
async def reorder_watchlist(
    ordered_ids: List[int],
    current_user: User = Depends(get_current_user),
    market_service: MarketService = Depends(lambda: MarketService()),
):
    """
    重新排序用户的自选股列表
    """
    await market_service.reorder_watchlist(
        user_id=current_user.id, ordered_ids=ordered_ids
    )
    return
