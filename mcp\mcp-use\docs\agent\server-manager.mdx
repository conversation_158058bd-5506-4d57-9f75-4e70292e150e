---
title: 'Server Manager'
description: 'Intelligent management of multiple MCP servers and dynamic tool discovery'
icon: 'server-cog'
---

# Server Manager: Dynamic Multi-Server Intelligence

The Server Manager is the brain behind intelligent MCP server orchestration. It transforms your agent from a static tool user into a dynamic, adaptive assistant that can discover, connect to, and intelligently utilize tools from multiple MCP servers on-demand.

## 🎯 What Makes It Special

Instead of overwhelming your agent with hundreds of tools from all servers at once, the Server Manager:

- **Dynamically loads tools** only when needed, with full schemas
- **Intelligently discovers** the right server for each task
- **Automatically updates** available tools as connections change
- **Provides semantic search** across all server tools
- **Manages connection lifecycle** efficiently

## 🏗️ Architecture Overview

```mermaid
graph TB
    Agent[MCPAgent] --> SM[ServerManager]
    SM --> S1[Web Server]
    SM --> S2[File Server]
    SM --> S3[Database Server]

    SM --> Tools[Dynamic Tools]
```

## 🔄 Dynamic Tool Loading Process

```mermaid
sequenceDiagram
    Agent->>ServerManager: Connect to server
    ServerManager->>Server: Get tools
    Server->>ServerManager: Return tools
    ServerManager->>Agent: Tools now available
    Agent->>Server: Use tools directly
```

## 🚀 Getting Started

Enable the Server Manager in your agent:

```python
from mcp_use import MCPClient, MCPAgent
from langchain_openai import ChatOpenAI

# Create client with multiple servers
client = MCPClient.from_dict({
    "mcpServers": {
        "playwright": {
            "command": "npx",
            "args": ["@playwright/mcp@latest"]
        },
        "filesystem": {
            "command": "uvx",
            "args": ["mcp-server-filesystem", "/tmp"]
        }
    }
})

# 🎯 Enable Server Manager
agent = MCPAgent(
    llm=ChatOpenAI(model="gpt-4"),
    client=client,
    use_server_manager=True  # ✨ This is the magic switch!
)
```

## 🔍 Tool Discovery Flow




## 🛠️ Management Tools Deep Dive

### Core Server Management

| Tool | Purpose | Example |
|------|---------|---------|
| `list_mcp_servers` | Discovery of available servers and their tools | "What servers do I have access to?" |
| `connect_to_mcp_server` | Activate a server and load its tools | "Connect to the filesystem server" |
| `get_active_mcp_server` | Check current connection status | "Which server am I currently using?" |
| `disconnect_from_mcp_server` | Deactivate server and remove its tools | "Disconnect from current server" |
| `search_mcp_tools` | Semantic search across all server tools | "Find tools for image processing" |

### Smart Tool Search Example

```python
# Agent automatically discovers and uses the right tools
result = await agent.run("""
I need to:
1. Find tools for web scraping
2. Connect to the right server
3. Scrape data from https://example.com
4. Save it to a file

Start by searching for relevant tools.
""")
```

**Agent Process:**
```mermaid
graph LR
    Search[Search Tools] --> Connect[Connect Server]
    Connect --> Use[Use Tools]
```

## 🎭 Real-World Use Cases

### 1. Multi-Server Data Pipeline

```python
result = await agent.run("""
Create a data pipeline that:
1. Scrapes product data from an e-commerce site
2. Processes and cleans the data
3. Saves it to a CSV file
4. Loads it into a SQLite database

Figure out which servers and tools you need.
""")
```

**Server Flow:**
```
playwright → filesystem → database
    ↓            ↓          ↓
 scraping → save CSV → load data
```

### 2. Content Creation Workflow

```python
result = await agent.run("""
I want to:
1. Search for trending topics online
2. Generate an image based on the topic
3. Write a blog post about it
4. Save everything to files

What tools do I need for this?
""")
```

## 🚀 Performance Benefits

The Server Manager provides focused tool access:

- **Without Server Manager**: All 100+ tools from all servers loaded at once, overwhelming the model
- **With Server Manager**: Only 5-15 relevant tools from the active server, providing clear focus

##  Core Features

The Server Manager provides these powerful capabilities:

- **🔗 Dynamic Tool Addition**: Server tools automatically added with full schemas
- **⚡ Real-time Updates**: Tool list updates immediately when connecting/disconnecting
- **🧹 Clean Architecture**: Direct tool access with proper schemas
- **🎯 Model Understanding**: Tools come with native schemas and validation
- **📊 Smart Logging**: Detailed insights into tool changes and server status

## 🏁 Complete Example

```python
import asyncio
from mcp_use import MCPClient, MCPAgent
from langchain_openai import ChatOpenAI

async def demo_server_manager():
    # Multi-server configuration
    client = MCPClient.from_dict({
        "mcpServers": {
            "web": {"command": "npx", "args": ["@playwright/mcp@latest"]},
            "files": {"command": "uvx", "args": ["mcp-server-filesystem", "/tmp"]},
            "database": {"command": "uvx", "args": ["mcp-server-sqlite"]}
        }
    })

    # Agent with Server Manager
    agent = MCPAgent(
        llm=ChatOpenAI(model="gpt-4"),
        client=client,
        use_server_manager=True,
        verbose=True  # See the magic happen!
    )

    # Complex multi-server task
    result = await agent.run("""
    I need to build a complete data collection system:

    1. First, show me what servers and tools are available
    2. Scrape product information from https://example-store.com
    3. Clean and structure the data
    4. Save it as both JSON and CSV files
    5. Load the data into a SQLite database
    6. Generate a summary report

    Guide me through each step and show me how you discover and use the right tools.
    """)

    print("🎉 Task completed!")
    print(result)

    await agent.close()

if __name__ == "__main__":
    asyncio.run(demo_server_manager())
```

The Server Manager transforms your MCP agent from a static tool user into an intelligent, adaptive assistant that can dynamically discover and utilize the perfect tools for any task! 🚀
