"""
pytest配置文件
提供测试夹具和配置
"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import AsyncMock, Mock, patch
from typing import AsyncGenerator, Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from httpx import AsyncClient
import os
import tempfile

from app.main import app
from app.core.config import settings
from app.core.database import get_db, Base
from app.db.models.user import User
from app.db.models.trading import Order, Position
from app.db.models.strategy import Strategy
from app.db.models.backtest import BacktestTask
from app.db.models.market import MarketData
from app.services.market_data_service import MarketDataService
from app.schemas.market_data import TickData, MarketDepth, KlineData
from tests.utils.market_data_generator import MarketDataGenerator
from datetime import datetime, timedelta
from decimal import Decimal

# 使用从market_data.py导入的类型


# 测试数据库配置
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False, future=True)

    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    # 清理
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    await engine.dispose()


@pytest_asyncio.fixture
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建数据库会话"""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session
        await session.rollback()


@pytest_asyncio.fixture
async def client(db_session, test_engine) -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""
    from fastapi.testclient import TestClient
    from httpx import AsyncClient, ASGITransport
    from app.core.database import db_manager

    def override_get_db():
        return db_session

    app.dependency_overrides[get_db] = override_get_db

    # 初始化数据库管理器用于测试
    db_manager.engine = test_engine
    db_manager.async_session_maker = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )

    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac

    app.dependency_overrides.clear()
    # 清理数据库管理器
    db_manager.engine = None
    db_manager.async_session_maker = None


@pytest.fixture
def mock_user():
    """模拟用户"""
    user = Mock(spec=User)
    user.id = "test-user-id"
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.is_active = True
    user.permissions = ["read:market_data", "place:order"]
    return user


@pytest.fixture
def mock_strategy():
    """模拟策略"""
    strategy = Mock(spec=Strategy)
    strategy.id = "test-strategy-id"
    strategy.name = "Test Strategy"
    strategy.description = "Test strategy description"
    strategy.code = "# Test strategy code"
    strategy.is_active = True
    return strategy


@pytest.fixture
def mock_order():
    """模拟订单"""
    order = Mock(spec=Order)
    order.id = "test-order-id"
    order.symbol = "000001"
    order.side = "buy"
    order.quantity = 100
    order.price = 10.0
    order.status = "pending"
    return order


@pytest.fixture
def mock_market_data():
    """模拟行情数据"""
    return {
        "symbol": "000001",
        "price": 10.0,
        "volume": 1000,
        "timestamp": "2024-01-01T00:00:00Z",
        "bid": 9.99,
        "ask": 10.01,
        "high": 10.5,
        "low": 9.5,
        "open": 9.8,
        "close": 10.0,
    }


@pytest.fixture
def mock_backtest_config():
    """模拟回测配置"""
    return {
        "strategy_id": "test-strategy-id",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "initial_capital": 100000,
        "benchmark": "000300",
        "commission": 0.001,
    }


# 行情数据测试夹具
@pytest_asyncio.fixture
async def market_data_service():
    """行情数据服务实例"""
    service = MarketDataService()

    # 重置服务状态
    service.subscribed_symbols.clear()
    service.tick_callbacks.clear()
    service.kline_callbacks.clear()
    service.depth_callbacks.clear()
    service.clients.clear()
    service.client_subscriptions.clear()
    service.tick_cache.clear()
    service.kline_cache.clear()
    service.stats = {
        "tick_count": 0,
        "kline_count": 0,
        "depth_count": 0,
        "error_count": 0,
        "last_update": None,
    }

    yield service

    # 清理
    await service.cleanup()


@pytest.fixture
def data_generator():
    """行情数据生成器"""
    generator = MarketDataGenerator()
    yield generator
    # 重置价格
    generator.reset_prices()


@pytest.fixture
def sample_tick_data():
    """示例Tick数据"""
    return TickData(
        symbol="rb2405",
        timestamp=datetime.now(),
        price=Decimal("3850.0"),
        volume=12345,
        bid_price=Decimal("3849.0"),
        ask_price=Decimal("3851.0"),
        bid_volume=100,
        ask_volume=150,
    )


@pytest.fixture
def sample_kline_data():
    """示例K线数据"""
    return KlineData(
        symbol="rb2405",
        timestamp=datetime.now(),
        interval="1m",
        open_price=Decimal("3845.0"),
        high_price=Decimal("3855.0"),
        low_price=Decimal("3840.0"),
        close_price=Decimal("3850.0"),
        volume=5000,
        amount=Decimal("19250000.0"),
    )


@pytest.fixture
def sample_depth_data():
    """示例深度数据"""
    return MarketDepth(
        symbol="rb2405",
        timestamp=datetime.now(),
        bids=[
            [Decimal("3849.0"), Decimal("100")],
            [Decimal("3848.0"), Decimal("200")],
            [Decimal("3847.0"), Decimal("150")],
        ],
        asks=[
            [Decimal("3851.0"), Decimal("150")],
            [Decimal("3852.0"), Decimal("180")],
            [Decimal("3853.0"), Decimal("120")],
        ],
    )


@pytest.fixture
def mock_websocket():
    """模拟WebSocket连接"""
    mock_ws = AsyncMock()
    mock_ws.send_json = AsyncMock()
    mock_ws.send_text = AsyncMock()
    mock_ws.close = AsyncMock()
    mock_ws.closed = False
    return mock_ws


@pytest.fixture
def test_symbols():
    """测试用合约列表"""
    return [
        "rb2405",
        "hc2405",
        "i2405",
        "j2405",
        "jm2405",  # 黑色系
        "cu2405",
        "al2405",
        "zn2405",  # 有色金属
        "au2406",
        "ag2406",  # 贵金属
        "c2405",
        "m2405",
        "y2405",  # 农产品
        "MA2405",
        "TA2405",
        "PF2405",  # 化工
    ]


@pytest.fixture
def batch_tick_data(data_generator, test_symbols):
    """批量Tick数据"""
    all_ticks = []
    for symbol in test_symbols[:5]:  # 使用前5个合约
        ticks = list(data_generator.generate_tick_stream(symbol, 20))
        all_ticks.extend(ticks)
    return all_ticks


@pytest.fixture
def performance_config():
    """性能测试配置"""
    return {
        "high_frequency_tick_count": 10000,
        "concurrent_client_count": 100,
        "stress_test_duration": 30,  # 秒
        "target_throughput": 5000,  # 条/秒
        "max_latency_ms": 10,
        "max_memory_growth_mb": 100,
    }


@pytest_asyncio.fixture
async def test_redis():
    """测试Redis连接"""
    mock_redis = AsyncMock()
    mock_redis.set = AsyncMock()
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.delete = AsyncMock()
    mock_redis.publish = AsyncMock()
    mock_redis.hset = AsyncMock()
    mock_redis.hget = AsyncMock()
    mock_redis.expire = AsyncMock()
    mock_redis.exists = AsyncMock(return_value=False)
    mock_redis.keys = AsyncMock(return_value=[])

    yield mock_redis


# 测试标记配置
def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line("markers", "integration: marks tests as integration tests")
    config.addinivalue_line("markers", "performance: marks tests as performance tests")
    config.addinivalue_line("markers", "stress: marks tests as stress tests")
    config.addinivalue_line("markers", "unit: marks tests as unit tests")
    config.addinivalue_line("markers", "api: marks tests as API tests")
    config.addinivalue_line("markers", "e2e: marks tests as end-to-end tests")
    config.addinivalue_line("markers", "load: marks tests as load tests")
    config.addinivalue_line("markers", "security: marks tests as security tests")
    config.addinivalue_line("markers", "websocket: marks tests as WebSocket tests")
    config.addinivalue_line("markers", "trading: marks tests as trading-related tests")
    config.addinivalue_line("markers", "market: marks tests as market data tests")
    config.addinivalue_line("markers", "error_handling: marks tests as error handling tests")


# 安全测试夹具
@pytest.fixture
def security_test_data():
    """安全测试数据夹具"""
    return {
        "sql_injection_payloads": [
            "'; DROP TABLE orders; --",
            "' OR '1'='1",
            "1'; UPDATE users SET password='hacked' WHERE '1'='1'; --",
            "' UNION SELECT * FROM users --",
        ],
        "xss_payloads": [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src='x' onerror='alert(1)'>",
            "<svg onload='alert(1)'>",
        ],
        "weak_passwords": [
            "123",
            "password",
            "12345678",
            "abcdefgh",
        ]
    }


@pytest.fixture
def load_test_config():
    """负载测试配置夹具"""
    return {
        "concurrent_users": 50,
        "test_duration": 60,
        "ramp_up_time": 10,
        "think_time": 1.0,
        "timeout": 30.0
    }


@pytest.fixture
def stress_test_config():
    """压力测试配置夹具"""
    return {
        "max_connections": 1000,
        "messages_per_connection": 1000,
        "target_throughput": 5000,
        "max_response_time": 2000
    }


# 清理夹具
@pytest.fixture(autouse=True)
async def cleanup_after_test():
    """测试后自动清理"""
    yield
    
    # 清理测试数据
    try:
        import os
        test_db_file = "./test.db"
        if os.path.exists(test_db_file):
            # 注意：在实际使用中可能需要更安全的清理方式
            pass
    except Exception:
        pass


# 测试标记
pytest_plugins = ["pytest_asyncio"]
