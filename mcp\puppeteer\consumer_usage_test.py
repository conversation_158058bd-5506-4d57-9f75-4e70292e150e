#!/usr/bin/env python3
"""
消费者使用习惯测试
模拟真实用户按照消费者习惯使用交易中心的完整流程
"""

import asyncio
import json
import time
import random
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConsumerUsageTest:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'user_scenarios': [],
            'button_tests': [],
            'link_tests': [],
            'functionality_tests': [],
            'usability_issues': [],
            'consumer_feedback': []
        }
        self.screenshot_counter = 0
        
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器观察用户行为
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
        # 监听控制台错误
        self.page.on('console', self.handle_console_message)
        
        logger.info("🛒 消费者使用习惯测试环境初始化完成")

    async def handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type == 'error':
            self.test_results['usability_issues'].append({
                'type': 'console_error',
                'message': msg.text,
                'timestamp': datetime.now().isoformat()
            })

    async def take_screenshot(self, name: str):
        """截图记录用户操作"""
        self.screenshot_counter += 1
        screenshot_path = f"screenshots/consumer_test_{self.screenshot_counter:03d}_{name}.png"
        Path("screenshots").mkdir(exist_ok=True)
        await self.page.screenshot(path=screenshot_path)
        logger.info(f"📸 用户操作截图: {screenshot_path}")
        return screenshot_path

    async def simulate_consumer_behavior(self):
        """模拟消费者行为（随机停顿、鼠标移动等）"""
        # 随机等待时间，模拟用户思考
        await asyncio.sleep(random.uniform(1.0, 3.0))
        
        # 随机鼠标移动，模拟用户浏览
        await self.page.mouse.move(
            random.randint(200, 1700),
            random.randint(200, 800)
        )

    async def scenario_first_time_user(self):
        """场景1: 首次使用用户的完整体验"""
        logger.info("👤 开始场景1: 首次使用用户体验")
        scenario = {
            'name': '首次使用用户体验',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'consumer_feedback': []
        }
        
        try:
            # 步骤1: 访问交易中心
            await self.page.goto('http://localhost:5173/trading/center', wait_until='networkidle')
            await self.take_screenshot('01_first_visit_trading_center')
            scenario['steps'].append('访问交易中心首页')
            await self.simulate_consumer_behavior()
            
            # 步骤2: 观察页面布局和导航
            page_title = await self.page.text_content('h1, .page-title')
            if not page_title or '交易中心' not in page_title:
                scenario['issues'].append('页面标题不明确，用户不知道这是什么页面')
            
            # 检查是否有明显的功能入口
            nav_buttons = await self.page.query_selector_all('button, .el-button')
            if len(nav_buttons) < 3:
                scenario['issues'].append('功能入口不够明显，用户难以找到主要功能')
            
            scenario['steps'].append(f'发现{len(nav_buttons)}个可点击按钮')
            
            # 步骤3: 尝试理解交易终端
            terminal_button = await self.page.query_selector('button:has-text("交易终端")')
            if terminal_button:
                await terminal_button.click()
                await asyncio.sleep(2)
                await self.take_screenshot('02_trading_terminal_view')
                scenario['steps'].append('点击交易终端按钮')
                
                # 检查是否有模式选择说明
                mode_info = await self.page.query_selector('.mode-info, .el-alert')
                if mode_info:
                    scenario['consumer_feedback'].append('模式切换有说明，用户体验良好')
                else:
                    scenario['issues'].append('缺少模式切换说明，新用户不理解模拟/实盘区别')
            
            await self.simulate_consumer_behavior()
            
            # 步骤4: 测试股票搜索功能
            search_input = await self.page.query_selector('input[placeholder*="搜索"], .el-autocomplete input')
            if search_input:
                await search_input.fill('000001')
                await asyncio.sleep(1)
                await self.page.keyboard.press('ArrowDown')
                await self.page.keyboard.press('Enter')
                await asyncio.sleep(2)
                await self.take_screenshot('03_stock_search_result')
                scenario['steps'].append('测试股票搜索功能')
                scenario['consumer_feedback'].append('股票搜索功能直观易用')
            else:
                scenario['issues'].append('未找到股票搜索功能，用户无法选择股票')
            
            # 步骤5: 尝试下单操作
            buy_button = await self.page.query_selector('button:has-text("买入")')
            if buy_button:
                # 检查买入表单
                quantity_input = await self.page.query_selector('input[placeholder*="数量"], .el-input-number input')
                if quantity_input:
                    await quantity_input.fill('100')
                    scenario['steps'].append('填写交易数量')
                
                # 检查是否有价格输入
                price_input = await self.page.query_selector('input[placeholder*="价格"]')
                if price_input:
                    await price_input.fill('12.50')
                    scenario['steps'].append('填写交易价格')
                
                await self.take_screenshot('04_order_form_filled')
                
                # 尝试提交订单（但不实际提交）
                submit_button = await self.page.query_selector('button:has-text("买入"):not(:disabled)')
                if submit_button:
                    scenario['consumer_feedback'].append('交易表单完整，可以正常下单')
                else:
                    scenario['issues'].append('买入按钮不可用，用户无法完成交易')
            
            await self.simulate_consumer_behavior()
            
        except Exception as e:
            scenario['issues'].append(f'场景执行异常: {str(e)}')
            logger.error(f"场景1执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景1完成，耗时: {scenario['duration']:.2f}秒")

    async def test_all_buttons_and_links(self):
        """测试所有按钮和链接的可用性"""
        logger.info("🔘 开始测试所有按钮和链接")
        
        # 访问交易中心
        await self.page.goto('http://localhost:5173/trading/center', wait_until='networkidle')
        await asyncio.sleep(2)
        
        # 获取所有按钮
        buttons = await self.page.query_selector_all('button, .el-button, [role="button"]')
        logger.info(f"发现 {len(buttons)} 个按钮")
        
        for i, button in enumerate(buttons):
            try:
                # 获取按钮文本
                button_text = await button.text_content()
                button_text = button_text.strip() if button_text else f'按钮{i+1}'
                
                # 检查按钮是否可见和可用
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()
                
                button_test = {
                    'index': i + 1,
                    'text': button_text,
                    'visible': is_visible,
                    'enabled': is_enabled,
                    'clickable': False,
                    'response_time': 0,
                    'error': None
                }
                
                if is_visible and is_enabled and button_text:
                    try:
                        start_time = time.time()
                        await button.click()
                        await asyncio.sleep(0.5)  # 等待响应
                        end_time = time.time()
                        
                        button_test['clickable'] = True
                        button_test['response_time'] = end_time - start_time
                        
                        logger.info(f"✅ 按钮 '{button_text}' 点击成功")
                        
                    except Exception as e:
                        button_test['error'] = str(e)
                        logger.warning(f"❌ 按钮 '{button_text}' 点击失败: {e}")
                
                self.test_results['button_tests'].append(button_test)
                
                # 避免过快点击
                await asyncio.sleep(0.3)
                
            except Exception as e:
                logger.error(f"测试按钮 {i+1} 时出错: {e}")
        
        await self.take_screenshot('05_all_buttons_tested')

    async def test_module_switching(self):
        """测试模块切换功能"""
        logger.info("🔄 测试模块切换功能")
        
        modules = [
            ('交易终端', 'terminal'),
            ('账户管理', 'account'),
            ('数据中心', 'data')
        ]
        
        for module_name, module_key in modules:
            try:
                # 查找模块按钮
                module_button = await self.page.query_selector(f'button:has-text("{module_name}")')
                if module_button:
                    await module_button.click()
                    await asyncio.sleep(2)
                    
                    # 检查模块是否正确切换
                    module_content = await self.page.query_selector('.module-container')
                    if module_content:
                        is_visible = await module_content.is_visible()
                        
                        module_test = {
                            'module': module_name,
                            'button_found': True,
                            'content_visible': is_visible,
                            'switch_success': is_visible
                        }
                        
                        await self.take_screenshot(f'06_module_{module_key}')
                        logger.info(f"✅ {module_name}模块切换成功")
                    else:
                        module_test = {
                            'module': module_name,
                            'button_found': True,
                            'content_visible': False,
                            'switch_success': False
                        }
                        logger.warning(f"❌ {module_name}模块内容未显示")
                else:
                    module_test = {
                        'module': module_name,
                        'button_found': False,
                        'content_visible': False,
                        'switch_success': False
                    }
                    logger.warning(f"❌ 未找到{module_name}按钮")
                
                self.test_results['functionality_tests'].append(module_test)
                await self.simulate_consumer_behavior()
                
            except Exception as e:
                logger.error(f"测试{module_name}模块时出错: {e}")

    async def test_trading_workflow(self):
        """测试完整的交易流程"""
        logger.info("💰 测试完整交易流程")
        
        workflow_test = {
            'name': '完整交易流程',
            'steps': [],
            'success': True,
            'issues': []
        }
        
        try:
            # 确保在交易终端模块
            terminal_button = await self.page.query_selector('button:has-text("交易终端")')
            if terminal_button:
                await terminal_button.click()
                await asyncio.sleep(2)
                workflow_test['steps'].append('切换到交易终端')
            
            # 测试模式切换
            sim_mode = await self.page.query_selector('label:has-text("模拟交易")')
            if sim_mode:
                await sim_mode.click()
                await asyncio.sleep(1)
                workflow_test['steps'].append('切换到模拟交易模式')
            
            # 测试股票搜索
            search_input = await self.page.query_selector('.el-autocomplete input')
            if search_input:
                await search_input.fill('000001')
                await asyncio.sleep(1)
                
                # 选择搜索结果
                suggestions = await self.page.query_selector_all('.el-autocomplete-suggestion__list li')
                if suggestions:
                    await suggestions[0].click()
                    await asyncio.sleep(1)
                    workflow_test['steps'].append('选择股票000001')
                else:
                    workflow_test['issues'].append('搜索结果未显示')
            
            # 测试买入流程
            buy_tab = await self.page.query_selector('.el-tabs__item:has-text("买入")')
            if buy_tab:
                await buy_tab.click()
                await asyncio.sleep(1)
                workflow_test['steps'].append('切换到买入标签')
            
            # 填写交易表单
            quantity_input = await self.page.query_selector('.el-input-number input')
            if quantity_input:
                await quantity_input.fill('200')
                workflow_test['steps'].append('填写交易数量200股')
            
            # 检查价格输入
            price_inputs = await self.page.query_selector_all('input[placeholder*="价格"]')
            if price_inputs:
                await price_inputs[0].fill('12.50')
                workflow_test['steps'].append('填写交易价格12.50')
            
            await self.take_screenshot('07_trading_form_complete')
            
            # 检查买入按钮状态
            buy_button = await self.page.query_selector('button:has-text("买入"):not(.el-button--disabled)')
            if buy_button:
                workflow_test['steps'].append('买入按钮可用，交易流程完整')
            else:
                workflow_test['issues'].append('买入按钮不可用')
                workflow_test['success'] = False
            
        except Exception as e:
            workflow_test['issues'].append(f'交易流程测试异常: {str(e)}')
            workflow_test['success'] = False
            logger.error(f"交易流程测试失败: {e}")
        
        self.test_results['functionality_tests'].append(workflow_test)

    async def test_data_center_functionality(self):
        """测试数据中心功能"""
        logger.info("📊 测试数据中心功能")
        
        # 切换到数据中心
        data_button = await self.page.query_selector('button:has-text("数据中心")')
        if data_button:
            await data_button.click()
            await asyncio.sleep(2)
        
        # 测试各个标签页
        tabs = ['订单管理', '持仓管理', '成交记录']
        
        for tab_name in tabs:
            try:
                tab_button = await self.page.query_selector(f'.el-tabs__item:has-text("{tab_name}")')
                if tab_button:
                    await tab_button.click()
                    await asyncio.sleep(2)
                    
                    # 检查表格是否显示
                    table = await self.page.query_selector('.el-table')
                    if table:
                        rows = await table.query_selector_all('.el-table__row')
                        
                        tab_test = {
                            'tab': tab_name,
                            'accessible': True,
                            'has_table': True,
                            'row_count': len(rows)
                        }
                        
                        logger.info(f"✅ {tab_name}标签页正常，显示{len(rows)}行数据")
                    else:
                        tab_test = {
                            'tab': tab_name,
                            'accessible': True,
                            'has_table': False,
                            'row_count': 0
                        }
                        logger.warning(f"❌ {tab_name}标签页缺少数据表格")
                else:
                    tab_test = {
                        'tab': tab_name,
                        'accessible': False,
                        'has_table': False,
                        'row_count': 0
                    }
                    logger.warning(f"❌ 未找到{tab_name}标签页")
                
                self.test_results['functionality_tests'].append(tab_test)
                await self.take_screenshot(f'08_data_center_{tab_name}')
                
            except Exception as e:
                logger.error(f"测试{tab_name}标签页时出错: {e}")

    async def generate_consumer_report(self):
        """生成消费者使用报告"""
        # 统计数据
        total_buttons = len(self.test_results['button_tests'])
        working_buttons = sum(1 for btn in self.test_results['button_tests'] if btn['clickable'])
        total_scenarios = len(self.test_results['user_scenarios'])
        successful_scenarios = sum(1 for s in self.test_results['user_scenarios'] if not s['issues'])
        
        # 生成报告
        print("\n" + "="*80)
        print("🛒 消费者使用习惯测试报告")
        print("="*80)
        print(f"测试时间: {self.test_results['timestamp']}")
        print(f"总测试场景: {total_scenarios}")
        print(f"成功场景: {successful_scenarios}")
        print(f"场景成功率: {successful_scenarios/total_scenarios*100:.1f}%" if total_scenarios > 0 else "0%")
        print(f"总按钮数: {total_buttons}")
        print(f"可用按钮: {working_buttons}")
        print(f"按钮可用率: {working_buttons/total_buttons*100:.1f}%" if total_buttons > 0 else "0%")
        
        print("\n📋 用户场景测试结果:")
        for scenario in self.test_results['user_scenarios']:
            status = "✅" if not scenario['issues'] else "❌"
            print(f"{status} {scenario['name']}: {scenario['duration']:.2f}秒")
            
            if scenario['issues']:
                for issue in scenario['issues']:
                    print(f"   ⚠️ {issue}")
            
            if scenario.get('consumer_feedback'):
                for feedback in scenario['consumer_feedback']:
                    print(f"   💡 {feedback}")
        
        print("\n🔘 按钮测试摘要:")
        button_categories = {}
        for btn in self.test_results['button_tests']:
            if btn['text']:
                category = '导航按钮' if any(word in btn['text'] for word in ['交易终端', '账户管理', '数据中心']) else \
                          '交易按钮' if any(word in btn['text'] for word in ['买入', '卖出', '提交']) else \
                          '功能按钮'
                
                if category not in button_categories:
                    button_categories[category] = {'total': 0, 'working': 0}
                
                button_categories[category]['total'] += 1
                if btn['clickable']:
                    button_categories[category]['working'] += 1
        
        for category, stats in button_categories.items():
            success_rate = stats['working'] / stats['total'] * 100 if stats['total'] > 0 else 0
            print(f"   {category}: {stats['working']}/{stats['total']} ({success_rate:.1f}%)")
        
        print("\n🔧 功能测试结果:")
        for test in self.test_results['functionality_tests']:
            if 'module' in test:
                status = "✅" if test['switch_success'] else "❌"
                print(f"   {status} {test['module']}模块切换")
            elif 'tab' in test:
                status = "✅" if test['has_table'] else "❌"
                print(f"   {status} {test['tab']}标签页 ({test['row_count']}行数据)")
            elif 'name' in test:
                status = "✅" if test['success'] else "❌"
                print(f"   {status} {test['name']}")
        
        if self.test_results['usability_issues']:
            print("\n⚠️ 可用性问题:")
            for issue in self.test_results['usability_issues']:
                print(f"   • {issue['type']}: {issue['message']}")
        
        # 消费者建议
        print("\n💡 消费者使用建议:")
        if working_buttons / total_buttons < 0.9:
            print("   • 部分按钮无法正常使用，建议检查按钮功能")
        
        if successful_scenarios / total_scenarios < 0.8:
            print("   • 用户场景成功率较低，建议改善用户体验")
        
        print("   • 建议添加更多用户引导和帮助信息")
        print("   • 建议优化交易流程，使其更符合消费者习惯")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    test = ConsumerUsageTest()
    
    try:
        await test.setup()
        
        # 执行消费者使用测试
        await test.scenario_first_time_user()
        await test.test_all_buttons_and_links()
        await test.test_module_switching()
        await test.test_trading_workflow()
        await test.test_data_center_functionality()
        
        # 生成报告
        await test.generate_consumer_report()
        
        print(f"\n🎉 消费者使用习惯测试完成！")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
