#!/usr/bin/env python3
"""完整功能测试脚本 - 验证所有新实现的功能"""

import json
import time
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# API基础URL
BASE_URL = "http://localhost:8000"

# 颜色代码
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def test_api(method, path, data=None, expected_status=[200], description=""):
    """测试API端点"""
    url = f"{BASE_URL}{path}"
    
    try:
        req_headers = {'Content-Type': 'application/json'}
        if data:
            data = json.dumps(data).encode('utf-8')
            
        request = Request(url, data=data, headers=req_headers)
        request.get_method = lambda: method
        
        try:
            response = urlopen(request)
            status_code = response.getcode()
            response_data = response.read().decode('utf-8')
            result = json.loads(response_data) if response_data else {}
        except HTTPError as e:
            status_code = e.code
            response_data = e.read().decode('utf-8')
            result = json.loads(response_data) if response_data else {}
            
        if status_code in expected_status:
            return True, result, f"状态码: {status_code}"
        else:
            return False, result, f"状态码: {status_code}"
            
    except Exception as e:
        return False, {}, f"错误: {str(e)}"

def main():
    """主测试函数"""
    print(f"\n{BLUE}{'='*80}")
    print(f"🚀 量化投资平台完整功能测试")
    print(f"{'='*80}{RESET}\n")
    
    # 测试计数器
    passed = 0
    failed = 0
    
    print(f"{YELLOW}📊 正在测试服务状态...{RESET}")
    
    # 1. 基础服务测试
    print(f"\n{BLUE}1. 基础服务测试{RESET}")
    
    tests = [
        ("GET", "/", None, [200], "根路径"),
        ("GET", "/health", None, [200], "健康检查"),
        ("GET", "/docs", None, [200], "API文档"),
    ]
    
    for method, path, data, expected, desc in tests:
        success, result, message = test_api(method, path, data, expected, desc)
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{desc}] {message}")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{desc}] {message}")
            failed += 1
    
    # 2. 认证系统测试
    print(f"\n{BLUE}2. 认证系统测试{RESET}")
    
    tests = [
        ("POST", "/api/auth/login", {"username": "admin", "password": "admin123"}, [200], "管理员登录"),
        ("POST", "/api/auth/register", {"username": "test_user", "email": "<EMAIL>", "password": "test123"}, [200], "用户注册"),
        ("GET", "/api/captcha/slider", None, [200], "滑动验证码"),
        ("POST", "/api/captcha/slider/verify", {"id": "test", "position": 150}, [200], "验证码验证"),
    ]
    
    for method, path, data, expected, desc in tests:
        success, result, message = test_api(method, path, data, expected, desc)
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{desc}] {message}")
            if 'data' in result:
                if desc == "管理员登录" and 'token' in result['data']:
                    print(f"    Token: {result['data']['token'][:20]}...")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{desc}] {message}")
            failed += 1
    
    # 3. 市场数据API测试
    print(f"\n{BLUE}3. 市场数据API测试{RESET}")
    
    tests = [
        ("GET", "/api/v1/market/quote/000001.SZ", None, [200], "股票报价"),
        ("GET", "/api/v1/market/kline/000001.SZ?limit=10", None, [200], "K线数据"),
    ]
    
    for method, path, data, expected, desc in tests:
        success, result, message = test_api(method, path, data, expected, desc)
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{desc}] {message}")
            if desc == "股票报价" and 'data' in result:
                price_data = result['data']
                print(f"    价格: {price_data.get('price', 'N/A')}, 涨跌: {price_data.get('change', 'N/A')}")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{desc}] {message}")
            failed += 1
    
    # 4. 交易系统测试
    print(f"\n{BLUE}4. 交易系统测试{RESET}")
    
    tests = [
        ("GET", "/api/v1/trade/account", None, [200], "账户信息"),
        ("GET", "/api/v1/trade/orders", None, [200], "订单列表"),
        ("GET", "/api/v1/trade/positions", None, [200], "持仓列表"),
    ]
    
    for method, path, data, expected, desc in tests:
        success, result, message = test_api(method, path, data, expected, desc)
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{desc}] {message}")
            if desc == "账户信息" and 'data' in result:
                account = result['data']
                print(f"    总资产: {account.get('totalBalance', 'N/A')}, 可用: {account.get('availableBalance', 'N/A')}")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{desc}] {message}")
            failed += 1
    
    # 5. 策略系统测试（新功能）
    print(f"\n{BLUE}5. 策略系统测试 (新功能){RESET}")
    
    # 获取策略列表
    success, result, message = test_api("GET", "/api/v1/strategies", None, [200], "策略列表")
    if success:
        print(f"{GREEN}✅ 通过{RESET} [策略列表] {message}")
        strategies = result['data']['strategies']
        print(f"    策略数量: {len(strategies)}")
        if strategies:
            strategy_id = strategies[0]['id']
            print(f"    首个策略: {strategies[0]['name']}")
            
            # 测试策略详情
            success2, result2, message2 = test_api("GET", f"/api/v1/strategies/{strategy_id}", None, [200], "策略详情")
            if success2:
                print(f"{GREEN}✅ 通过{RESET} [策略详情] {message2}")
                passed += 1
            else:
                print(f"{RED}❌ 失败{RESET} [策略详情] {message2}")
                failed += 1
            
            # 测试策略控制
            control_data = {"action": "start"}
            success3, result3, message3 = test_api("POST", f"/api/v1/strategies/{strategy_id}/control", control_data, [200], "策略控制")
            if success3:
                print(f"{GREEN}✅ 通过{RESET} [策略控制] {message3}")
                print(f"    状态变更: {result3.get('message', 'N/A')}")
                passed += 1
            else:
                print(f"{RED}❌ 失败{RESET} [策略控制] {message3}")
                failed += 1
            
            # 测试绩效分析
            success4, result4, message4 = test_api("GET", f"/api/v1/strategies/{strategy_id}/performance", None, [200], "绩效分析")
            if success4:
                print(f"{GREEN}✅ 通过{RESET} [绩效分析] {message4}")
                perf = result4['data']['performance']
                print(f"    总收益: {perf.get('total_return', 'N/A')}, 夏普比: {perf.get('sharpe_ratio', 'N/A')}")
                passed += 1
            else:
                print(f"{RED}❌ 失败{RESET} [绩效分析] {message4}")
                failed += 1
            
            # 测试参数优化
            opt_data = {
                "strategy_id": strategy_id,
                "parameters": {"fast_period": [5, 10, 15], "slow_period": [20, 30, 40]},
                "optimization_target": "sharpe_ratio"
            }
            success5, result5, message5 = test_api("POST", f"/api/v1/strategies/{strategy_id}/optimize", opt_data, [200], "参数优化")
            if success5:
                print(f"{GREEN}✅ 通过{RESET} [参数优化] {message5}")
                print(f"    最优评分: {result5['data'].get('best_score', 'N/A')}")
                passed += 1
            else:
                print(f"{RED}❌ 失败{RESET} [参数优化] {message5}")
                failed += 1
        
        passed += 1
    else:
        print(f"{RED}❌ 失败{RESET} [策略列表] {message}")
        failed += 1
    
    # 创建新策略测试
    create_data = {
        "name": "测试策略",
        "description": "自动化测试创建的策略",
        "code": "def test_strategy(): return 1",
        "parameters": {"period": 20}
    }
    success, result, message = test_api("POST", "/api/v1/strategies", create_data, [200], "创建策略")
    if success:
        print(f"{GREEN}✅ 通过{RESET} [创建策略] {message}")
        print(f"    新策略ID: {result['data'].get('id', 'N/A')}")
        passed += 1
    else:
        print(f"{RED}❌ 失败{RESET} [创建策略] {message}")
        failed += 1
    
    # 6. 风险管理测试（新功能）
    print(f"\n{BLUE}6. 风险管理测试 (新功能){RESET}")
    
    tests = [
        ("GET", "/api/v1/risk/report", None, [200], "风险报告"),
        ("GET", "/api/v1/risk/rules", None, [200], "风控规则"),
        ("POST", "/api/v1/risk/check", {}, [200], "风控检查"),
    ]
    
    for method, path, data, expected, desc in tests:
        success, result, message = test_api(method, path, data, expected, desc)
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{desc}] {message}")
            if desc == "风险报告" and 'data' in result:
                report = result['data']
                print(f"    组合价值: {report['portfolio_overview'].get('total_value', 'N/A')}")
                print(f"    合规状态: {report.get('compliance_status', 'N/A')}")
            elif desc == "风控检查" and 'data' in result:
                check = result['data']
                print(f"    风险评分: {check.get('risk_score', 'N/A')}, 状态: {check.get('compliance_status', 'N/A')}")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{desc}] {message}")
            failed += 1
    
    # 7. 基础设施测试（新功能）
    print(f"\n{BLUE}7. 基础设施测试 (新功能){RESET}")
    
    tests = [
        ("GET", "/api/v1/cache/status", None, [200], "缓存状态"),
        ("POST", "/api/v1/tasks/submit", {"task_type": "backtest", "parameters": {}}, [200], "任务提交"),
    ]
    
    task_id = None
    for method, path, data, expected, desc in tests:
        success, result, message = test_api(method, path, data, expected, desc)
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{desc}] {message}")
            if desc == "缓存状态" and 'data' in result:
                cache = result['data']
                print(f"    缓存类型: {cache.get('type', 'N/A')}, 命中率: {cache.get('hit_rate', 'N/A')}")
            elif desc == "任务提交" and 'data' in result:
                task_id = result['data'].get('task_id')
                print(f"    任务ID: {task_id}")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{desc}] {message}")
            failed += 1
    
    # 测试任务状态查询
    if task_id:
        success, result, message = test_api("GET", f"/api/v1/tasks/{task_id}", None, [200], "任务状态")
        if success:
            print(f"{GREEN}✅ 通过{RESET} [任务状态] {message}")
            task = result['data']
            print(f"    任务状态: {task.get('status', 'N/A')}")
            passed += 1
        else:
            print(f"{RED}❌ 失败{RESET} [任务状态] {message}")
            failed += 1
    
    # 8. 回测功能测试
    print(f"\n{BLUE}8. 回测功能测试{RESET}")
    
    backtest_data = {
        "strategy_id": "strategy_1",
        "start_date": "2025-01-01",
        "end_date": "2025-07-26"
    }
    success, result, message = test_api("POST", "/api/v1/strategies/backtest", backtest_data, [200], "策略回测")
    if success:
        print(f"{GREEN}✅ 通过{RESET} [策略回测] {message}")
        perf = result['data']['performance']
        print(f"    回测收益: {perf.get('total_return', 'N/A')}, 胜率: {perf.get('win_rate', 'N/A')}")
        passed += 1
    else:
        print(f"{RED}❌ 失败{RESET} [策略回测] {message}")
        failed += 1
    
    # 输出测试结果
    total = passed + failed
    print(f"\n{BLUE}{'='*80}")
    print(f"📊 测试结果汇总")
    print(f"{'='*80}{RESET}")
    print(f"总测试数: {total}")
    print(f"{GREEN}✅ 通过: {passed} ({passed/total*100:.1f}%){RESET}")
    print(f"{RED}❌ 失败: {failed} ({failed/total*100:.1f}%){RESET}")
    
    if failed == 0:
        print(f"\n{GREEN}🎉 所有功能测试通过！量化投资平台完全就绪！{RESET}")
    else:
        print(f"\n{YELLOW}⚠️  有 {failed} 个测试失败，请检查相关功能{RESET}")
    
    # 功能完成情况总结
    print(f"\n{BLUE}✅ 已完成的新功能:{RESET}")
    completed_features = [
        "策略CRUD操作（创建、读取、更新、删除）",
        "策略运行控制（启动、停止、暂停）", 
        "策略绩效分析和报告生成",
        "参数优化功能（网格搜索、遗传算法）",
        "风险报告生成（投资组合风险分析）",
        "实时风控规则引擎和合规检查",
        "模拟数据库连接和数据管理",
        "模拟Redis缓存系统",
        "模拟Celery任务队列",
        "策略回测功能",
        "滑动验证码系统"
    ]
    
    for i, feature in enumerate(completed_features, 1):
        print(f"{GREEN}  {i:2d}. {feature}{RESET}")
    
    print(f"\n{BLUE}🔗 服务访问地址:{RESET}")
    print(f"  前端应用: http://localhost:5173")
    print(f"  后端API: http://localhost:8000")
    print(f"  API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()