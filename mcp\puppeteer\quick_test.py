#!/usr/bin/env python3
"""
快速测试 - 检查页面基本功能
"""

import asyncio
from playwright.async_api import async_playwright
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def quick_test():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            logger.info("访问交易中心页面...")
            await page.goto('http://localhost:5173/trading/center', wait_until='networkidle', timeout=60000)
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 检查页面标题
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 检查页面内容
            content = await page.content()
            if 'trading-center' in content:
                logger.info("✅ 页面包含交易中心内容")
            else:
                logger.warning("❌ 页面不包含交易中心内容")
            
            # 检查是否有错误
            errors = await page.evaluate("""
                () => {
                    const errors = [];
                    const errorElements = document.querySelectorAll('.error, .el-message--error');
                    errorElements.forEach(el => errors.push(el.textContent));
                    return errors;
                }
            """)
            
            if errors:
                logger.warning(f"页面错误: {errors}")
            else:
                logger.info("✅ 页面无明显错误")
            
            # 检查按钮
            buttons = await page.query_selector_all('button')
            logger.info(f"发现 {len(buttons)} 个按钮")
            
            # 检查导航按钮
            nav_buttons = await page.query_selector_all('.nav-right button')
            logger.info(f"发现 {len(nav_buttons)} 个导航按钮")
            
            if len(nav_buttons) >= 3:
                logger.info("✅ 导航按钮数量正常")
                
                # 尝试点击第一个按钮
                try:
                    await nav_buttons[0].click()
                    await page.wait_for_timeout(1000)
                    logger.info("✅ 第一个导航按钮点击成功")
                except Exception as e:
                    logger.warning(f"❌ 导航按钮点击失败: {e}")
            else:
                logger.warning("❌ 导航按钮数量不足")
            
            # 截图
            await page.screenshot(path='screenshots/quick_test.png')
            logger.info("📸 截图已保存")
            
        except Exception as e:
            logger.error(f"测试失败: {e}")
            await page.screenshot(path='screenshots/error.png')
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(quick_test())
