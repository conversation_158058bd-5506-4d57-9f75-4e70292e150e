#!/usr/bin/env python3
"""
测试权限数据流
"""

import asyncio
from puppeteer import <PERSON>rowserManager

async def test_permissions_flow():
    manager = BrowserManager()
    try:
        page = await manager.ensure_browser()
        print('🔧 测试权限数据流...')
        
        await page.goto('http://localhost:5173/login')
        await page.wait_for_timeout(2000)
        
        # 监听控制台日志
        def handle_console(msg):
            if 'HTTP Response' in msg.text:
                print(f'[CONSOLE] {msg.text}')
        
        page.on('console', handle_console)
        
        # 点击演示登录
        await page.wait_for_selector('button:has-text("演示登录")')
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        
        await page.wait_for_timeout(3000)
        
        # 检查登录后的数据
        result = await page.evaluate('''
            () => {
                try {
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                    
                    // 获取localStorage中的数据
                    const userInfoStr = localStorage.getItem('userInfo');
                    const userInfoFromStorage = userInfoStr ? JSON.parse(userInfoStr) : null;
                    
                    return {
                        store: {
                            userInfo: userStore.userInfo,
                            permissions: userStore.permissions,
                            isLoggedIn: userStore.isLoggedIn,
                            token: userStore.token ? 'exists' : 'missing'
                        },
                        localStorage: {
                            userInfo: userInfoFromStorage,
                            token: localStorage.getItem('token') ? 'exists' : 'missing'
                        }
                    };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f'📊 数据流检查结果:')
        print(f'  Store数据: {result.get("store", {})}')
        print(f'  LocalStorage数据: {result.get("localStorage", {})}')
        
        # 检查是否有权限相关错误
        errors = await page.evaluate('''
            () => {
                return window.__APP_DEBUG__?.errors || [];
            }
        ''')
        
        print(f'❌ 应用错误: {len(errors)} 个')
        for error in errors:
            print(f'  - {error}')
        
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_permissions_flow())
