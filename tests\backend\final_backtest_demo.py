#!/usr/bin/env python3
"""
回测功能最终演示脚本
展示完整的回测流程和功能
"""
import json
import urllib.request
import urllib.parse
import urllib.error
import time
from datetime import datetime

class BacktestDemo:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.token = None
        
    def make_request(self, url, method="GET", data=None, headers=None):
        """发送HTTP请求"""
        try:
            if headers is None:
                headers = {"Content-Type": "application/json"}
            
            if data and isinstance(data, dict):
                data = json.dumps(data).encode('utf-8')
                
            req = urllib.request.Request(url, data=data, headers=headers, method=method)
            
            with urllib.request.urlopen(req, timeout=10) as response:
                response_data = response.read().decode('utf-8')
                return {
                    "status_code": response.getcode(),
                    "data": json.loads(response_data) if response_data else {},
                    "success": True
                }
                
        except urllib.error.HTTPError as e:
            try:
                error_data = e.read().decode('utf-8')
                error_json = json.loads(error_data) if error_data else {}
            except:
                error_json = {"error": e.reason}
            
            return {
                "status_code": e.code,
                "error": error_json,
                "success": False
            }
        except Exception as e:
            return {
                "error": str(e),
                "success": False
            }
    
    def step_1_login(self):
        """步骤1: 用户登录"""
        print("🔐 步骤1: 用户登录")
        print("-" * 40)
        
        login_data = {"username": "admin", "password": "admin"}
        result = self.make_request(
            f"{self.backend_url}/api/v1/auth/login",
            method="POST",
            data=login_data
        )
        
        if result.get("success"):
            self.token = result["data"]["token"]
            user = result["data"]["user"]
            
            print(f"✅ 登录成功!")
            print(f"   用户: {user['nickname']} ({user['username']})")
            print(f"   邮箱: {user['email']}")
            print(f"   权限: {', '.join(user['permissions'])}")
            print(f"   Token: {self.token}")
            return True
        else:
            print("❌ 登录失败")
            return False
    
    def step_2_check_market_data(self):
        """步骤2: 检查市场数据"""
        print(f"\n📊 步骤2: 检查市场数据")
        print("-" * 40)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        
        result = self.make_request(f"{self.backend_url}/api/v1/market/quotes", headers=headers)
        
        if result.get("success"):
            quotes_data = result["data"]
            quotes = quotes_data.get("data", [])
            
            print(f"✅ 成功获取市场数据!")
            print(f"   股票数量: {len(quotes)}")
            print(f"   数据时间: {quotes[0]['timestamp'] if quotes else 'N/A'}")
            
            print(f"\n   📈 实时行情:")
            for quote in quotes:
                change_symbol = "📈" if quote['change'] > 0 else "📉" if quote['change'] < 0 else "➡️"
                print(f"   {change_symbol} {quote['symbol']} {quote['name']}")
                print(f"      价格: ¥{quote['price']:.2f} ({quote['change_percent']:+.2f}%)")
                print(f"      成交量: {quote['volume']:,}")
            
            return True
        else:
            print("❌ 获取市场数据失败")
            return False
    
    def step_3_view_existing_backtests(self):
        """步骤3: 查看现有回测"""
        print(f"\n📋 步骤3: 查看现有回测")
        print("-" * 40)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        
        result = self.make_request(f"{self.backend_url}/api/v1/backtest", headers=headers)
        
        if result.get("success"):
            backtests = result["data"]["backtests"]
            
            print(f"✅ 获取回测列表成功!")
            print(f"   回测总数: {len(backtests)}")
            
            # 按状态分组
            status_groups = {}
            for bt in backtests:
                status = bt['status']
                if status not in status_groups:
                    status_groups[status] = []
                status_groups[status].append(bt)
            
            print(f"\n   📊 状态分布:")
            for status, bt_list in status_groups.items():
                status_icon = {
                    "COMPLETED": "✅",
                    "RUNNING": "🔄", 
                    "FAILED": "❌",
                    "PENDING": "⏳"
                }.get(status, "❓")
                print(f"   {status_icon} {status}: {len(bt_list)}个")
            
            print(f"\n   📝 回测详情:")
            for bt in backtests:
                status_icon = {
                    "COMPLETED": "✅",
                    "RUNNING": "🔄", 
                    "FAILED": "❌",
                    "PENDING": "⏳"
                }.get(bt['status'], "❓")
                
                print(f"   {status_icon} [{bt['id']}] {bt['name']}")
                print(f"      股票: {bt['symbol']} | 期间: {bt['start_date']} ~ {bt['end_date']}")
                print(f"      初始资金: ¥{bt['initial_capital']:,}")
                
                if bt['status'] == 'COMPLETED':
                    print(f"      📈 总收益率: {bt['total_return']:.2%}")
                    print(f"      📊 年化收益: {bt['annual_return']:.2%}")
                    print(f"      📉 最大回撤: {bt['max_drawdown']:.2%}")
                    print(f"      ⭐ 夏普比率: {bt['sharpe_ratio']:.2f}")
                    print(f"      🎯 胜率: {bt['win_rate']:.1%}")
                
                print(f"      🕒 创建时间: {bt['create_time'][:19]}")
                print()
            
            return backtests
        else:
            print("❌ 获取回测列表失败")
            return []
    
    def step_4_create_new_backtest(self):
        """步骤4: 创建新回测"""
        print(f"\n🚀 步骤4: 创建新回测")
        print("-" * 40)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        
        print("   ⚙️  回测配置:")
        print("   策略类型: 双均线策略")
        print("   标的股票: 随机选择")
        print("   回测期间: 2024年上半年")
        print("   初始资金: ¥1,000,000")
        print("   参数设置: 短期5日均线，长期20日均线")
        
        result = self.make_request(
            f"{self.backend_url}/api/v1/backtest",
            method="POST",
            headers=headers
        )
        
        if result.get("success"):
            task_info = result["data"]
            new_backtest = task_info["data"]
            
            print(f"\n✅ 创建回测成功!")
            print(f"   任务ID: {task_info['task_id']}")
            print(f"   回测ID: {new_backtest['id']}")
            print(f"   回测名称: {new_backtest['name']}")
            print(f"   目标股票: {new_backtest['symbol']}")
            print(f"   策略ID: {new_backtest['strategy_id']}")
            print(f"   初始资金: ¥{new_backtest['initial_capital']:,}")
            print(f"   当前状态: {new_backtest['status']}")
            print(f"   创建时间: {new_backtest['create_time'][:19]}")
            
            return new_backtest['id']
        else:
            print("❌ 创建回测失败")
            return None
    
    def step_5_start_backtest(self, backtest_id):
        """步骤5: 启动回测"""
        print(f"\n▶️  步骤5: 启动回测")
        print("-" * 40)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        
        print(f"   正在启动回测任务 ID: {backtest_id}")
        
        result = self.make_request(
            f"{self.backend_url}/api/v1/backtest/{backtest_id}/start",
            method="POST",
            headers=headers
        )
        
        if result.get("success"):
            task_info = result["data"]
            
            print(f"✅ 回测启动成功!")
            print(f"   任务ID: {task_info['task_id']}")
            print(f"   状态: {task_info['message']}")
            
            # 模拟等待
            print(f"\n   ⏳ 正在执行回测...")
            for i in range(3):
                time.sleep(1)
                print(f"   {'.' * (i + 1)} 计算中...")
            
            return True
        else:
            print("❌ 启动回测失败")
            return False
    
    def step_6_get_backtest_results(self, backtest_id):
        """步骤6: 获取回测结果"""
        print(f"\n📊 步骤6: 获取回测结果")
        print("-" * 40)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        
        # 获取详细结果
        detail_result = self.make_request(
            f"{self.backend_url}/api/v1/backtest/{backtest_id}",
            headers=headers
        )
        
        if detail_result.get("success"):
            detail_data = detail_result["data"]
            config = detail_data["config"]
            result = detail_data["result"]
            
            print(f"✅ 获取回测结果成功!")
            print(f"\n   📋 回测配置:")
            print(f"   回测名称: {config['name']}")
            print(f"   目标股票: {config['symbol']}")
            print(f"   回测期间: {config['start_date']} ~ {config['end_date']}")
            print(f"   初始资金: ¥{config['initial_capital']:,}")
            print(f"   策略ID: {config['strategy_id']}")
            
            print(f"\n   📈 回测结果:")
            print(f"   💰 最终价值: ¥{result['final_value']:,}")
            print(f"   📊 总收益率: {result['total_return']:.2%}")
            print(f"   📅 年化收益: {result['annual_return']:.2%}")
            print(f"   📉 最大回撤: {result['max_drawdown']:.2%}")
            print(f"   ⭐ 夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"   🎯 胜率: {result['win_rate']:.1%}")
            print(f"   💹 盈亏比: {result['profit_loss_ratio']:.2f}")
            
            print(f"\n   🔢 交易统计:")
            print(f"   总交易次数: {result['total_trades']}")
            print(f"   盈利交易: {result['winning_trades']}")
            print(f"   亏损交易: {result['losing_trades']}")
            print(f"   平均盈利: {result['avg_win']:.2%}")
            print(f"   平均亏损: {result['avg_loss']:.2%}")
            
            print(f"\n   📈 业绩表现:")
            performance_data = result['performance_data']
            for perf in performance_data:
                profit = perf['portfolio_value'] - 1000000
                print(f"   {perf['date']}: ¥{perf['portfolio_value']:,} (收益: ¥{profit:+,})")
            
            # 获取详细收益数据
            result_api = self.make_request(
                f"{self.backend_url}/api/v1/backtest/{backtest_id}/result",
                headers=headers
            )
            
            if result_api.get("success"):
                daily_results = result_api["data"]
                print(f"\n   📊 每日收益 (前5天):")
                daily_returns = daily_results.get('daily_returns', [])
                for daily in daily_returns[:5]:
                    return_pct = daily['return'] * 100
                    cumulative_pct = daily['cumulative_return'] * 100
                    print(f"   {daily['date']}: 日收益 {return_pct:+.2f}%, 累计收益 {cumulative_pct:+.2f}%")
            
            return True
        else:
            print("❌ 获取回测结果失败")
            return False
    
    def step_7_health_check(self):
        """步骤7: 系统健康检查"""
        print(f"\n🔍 步骤7: 系统健康检查")
        print("-" * 40)
        
        # 基础健康检查
        health_result = self.make_request(f"{self.backend_url}/health")
        
        if health_result.get("success"):
            health_data = health_result["data"]
            print(f"✅ 系统健康状态: {health_data['status']}")
            print(f"   检查时间: {health_data['timestamp'][:19]}")
        
        # 回测服务健康检查
        backtest_health = self.make_request(f"{self.backend_url}/api/v1/backtest/health")
        
        if backtest_health.get("success"):
            bt_health = backtest_health["data"]
            print(f"✅ 回测服务状态: {bt_health['status']}")
            print(f"   支持基准: {', '.join(bt_health['supported_benchmarks'])}")
            print(f"   支持频率: {', '.join(bt_health['supported_frequencies'])}")
        
        # K线数据测试
        kline_result = self.make_request(f"{self.backend_url}/api/v1/market/kline/000001?limit=5")
        
        if kline_result.get("success"):
            kline_data = kline_result["data"]
            print(f"✅ K线数据服务正常")
            print(f"   测试股票: {kline_data['symbol']}")
            print(f"   数据周期: {kline_data['period']}")
            print(f"   数据量: {len(kline_data['data'])}条")
    
    def run_complete_demo(self):
        """运行完整的回测演示"""
        print("🎯 量化投资平台 - 回测功能完整演示")
        print("=" * 50)
        print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"后端服务: {self.backend_url}")
        print("=" * 50)
        
        try:
            # 步骤1: 登录
            if not self.step_1_login():
                print("\n❌ 演示失败: 无法登录")
                return False
            
            # 步骤2: 检查市场数据
            if not self.step_2_check_market_data():
                print("\n⚠️  警告: 市场数据获取失败，继续演示其他功能")
            
            # 步骤3: 查看现有回测
            existing_backtests = self.step_3_view_existing_backtests()
            
            # 步骤4: 创建新回测
            new_backtest_id = self.step_4_create_new_backtest()
            
            if new_backtest_id:
                # 步骤5: 启动回测
                if self.step_5_start_backtest(new_backtest_id):
                    # 步骤6: 获取结果（使用现有的完成回测）
                    if existing_backtests:
                        completed_backtest = next((bt for bt in existing_backtests if bt['status'] == 'COMPLETED'), None)
                        if completed_backtest:
                            self.step_6_get_backtest_results(completed_backtest['id'])
            
            # 步骤7: 系统健康检查
            self.step_7_health_check()
            
            # 演示总结
            print(f"\n🎉 演示完成!")
            print("=" * 50)
            print("✅ 已完成功能演示:")
            print("   • 用户认证和权限验证")
            print("   • 实时市场数据获取")
            print("   • 回测任务管理")
            print("   • 回测创建和启动")
            print("   • 回测结果分析")
            print("   • 系统健康监控")
            
            print(f"\n📊 演示数据统计:")
            print(f"   • 支持股票数量: 5+ (000001, 000002, 600000, 600036, 000858)")
            print(f"   • 历史数据时间范围: 2024-2025年")
            print(f"   • 支持策略: 双均线策略")
            print(f"   • 回测指标: 收益率、夏普比率、最大回撤、胜率等")
            
            print(f"\n🔗 相关链接:")
            print(f"   • 后端API文档: {self.backend_url}/docs")
            print(f"   • 前端界面: http://localhost:5175")
            print(f"   • 健康检查: {self.backend_url}/health")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 演示过程中发生异常: {e}")
            return False

def main():
    """主函数"""
    demo = BacktestDemo()
    
    try:
        success = demo.run_complete_demo()
        
        if success:
            print(f"\n🏆 演示成功完成!")
            return 0
        else:
            print(f"\n💥 演示未能完全成功")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n⏹️  演示被用户中断")
        return 130
    except Exception as e:
        print(f"\n💥 演示失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())