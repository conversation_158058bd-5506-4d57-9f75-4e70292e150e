#!/usr/bin/env python3
"""
修复认证系统脚本
解决登录功能和演示账户问题
"""

import asyncio
import sqlite3
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional
import bcrypt
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def hash_password_bcrypt(password: str) -> str:
    """使用bcrypt哈希密码"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password_bcrypt(password: str, hashed: str) -> bool:
    """验证bcrypt密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def hash_password_simple(password: str) -> str:
    """简单哈希（用于兼容性）"""
    return hashlib.sha256(password.encode()).hexdigest()

def fix_database_users():
    """修复数据库中的用户数据"""
    print("🔧 修复数据库用户数据...")
    
    try:
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        # 检查当前用户
        cursor.execute('SELECT id, username, email, hashed_password FROM users')
        users = cursor.fetchall()
        print(f"📋 当前用户数量: {len(users)}")
        
        for user in users:
            user_id, username, email, current_hash = user
            print(f"  - {username} ({email})")
        
        # 更新admin用户密码为admin123
        admin_password = "admin123"
        admin_hash = hash_password_bcrypt(admin_password)
        
        cursor.execute('''
            UPDATE users 
            SET hashed_password = ?, is_active = 1, is_verified = 1 
            WHERE username = 'admin'
        ''', (admin_hash,))
        
        # 如果admin用户不存在，创建一个
        if not any(user[1] == 'admin' for user in users):
            print("📝 创建admin用户...")
            cursor.execute('''
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
                VALUES (?, ?, ?, ?, 1, 1, 1)
            ''', ('admin', '<EMAIL>', '系统管理员', admin_hash))
        
        # 创建demo用户
        demo_password = "demo123"
        demo_hash = hash_password_bcrypt(demo_password)
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('demo',))
        if cursor.fetchone()[0] == 0:
            print("📝 创建demo用户...")
            cursor.execute('''
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, is_verified)
                VALUES (?, ?, ?, ?, 1, 0, 1)
            ''', ('demo', '<EMAIL>', '演示用户', demo_hash))
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库用户修复完成")
        print(f"   - admin用户密码: {admin_password}")
        print(f"   - demo用户密码: {demo_password}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        return False

def create_simple_auth_service():
    """创建简化的认证服务"""
    print("🔧 创建简化认证服务...")
    
    auth_service_code = '''"""
简化的认证服务 - 用于修复登录问题
"""

import sqlite3
import bcrypt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

class SimpleAuthService:
    def __init__(self, db_path: str = "test.db"):
        self.db_path = db_path
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except:
            # 兼容简单哈希
            import hashlib
            return hashlib.sha256(password.encode()).hexdigest() == hashed
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """验证用户凭据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, email, full_name, hashed_password, is_active, is_superuser
                FROM users WHERE username = ? OR email = ?
            ''', (username, username))
            
            user_data = cursor.fetchone()
            conn.close()
            
            if not user_data:
                return None
            
            user_id, username, email, full_name, hashed_password, is_active, is_superuser = user_data
            
            if not is_active:
                return None
            
            if not self.verify_password(password, hashed_password):
                return None
            
            return {
                "id": user_id,
                "username": username,
                "email": email,
                "full_name": full_name,
                "is_active": bool(is_active),
                "is_superuser": bool(is_superuser)
            }
            
        except Exception as e:
            print(f"认证错误: {e}")
            return None
    
    def create_token(self, user_data: Dict[str, Any]) -> str:
        """创建简单的token"""
        return f"token_{secrets.token_hex(16)}_{user_data['id']}"

# 全局实例
auth_service = SimpleAuthService()
'''
    
    with open('simple_auth_service.py', 'w', encoding='utf-8') as f:
        f.write(auth_service_code)
    
    print("✅ 简化认证服务创建完成")

def test_authentication():
    """测试认证功能"""
    print("🧪 测试认证功能...")
    
    try:
        from simple_auth_service import auth_service
        
        # 测试admin登录
        test_cases = [
            ("admin", "admin123"),
            ("admin", "admin"),
            ("demo", "demo123"),
            ("<EMAIL>", "admin123")
        ]
        
        for username, password in test_cases:
            user = auth_service.authenticate_user(username, password)
            if user:
                print(f"✅ 登录成功: {username} / {password}")
                print(f"   用户信息: {user['username']} ({user['email']})")
                token = auth_service.create_token(user)
                print(f"   Token: {token[:50]}...")
                return True
            else:
                print(f"❌ 登录失败: {username} / {password}")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_auth_middleware():
    """创建认证中间件"""
    print("🔧 创建认证中间件...")
    
    middleware_code = '''"""
认证中间件 - 处理JWT和简单token
"""

from fastapi import HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import sqlite3

security = HTTPBearer(auto_error=False)

class AuthMiddleware:
    def __init__(self, db_path: str = "test.db"):
        self.db_path = db_path
    
    def get_current_user(self, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
        """获取当前用户"""
        if not credentials:
            return None
        
        token = credentials.credentials
        
        # 解析简单token
        if token.startswith("token_"):
            try:
                parts = token.split("_")
                if len(parts) >= 3:
                    user_id = parts[-1]
                    return self.get_user_by_id(user_id)
            except:
                pass
        
        return None
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, email, full_name, is_active, is_superuser
                FROM users WHERE id = ?
            ''', (user_id,))
            
            user_data = cursor.fetchone()
            conn.close()
            
            if user_data:
                user_id, username, email, full_name, is_active, is_superuser = user_data
                return {
                    "id": user_id,
                    "username": username,
                    "email": email,
                    "full_name": full_name,
                    "is_active": bool(is_active),
                    "is_superuser": bool(is_superuser)
                }
            
            return None
            
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
    
    def require_auth(self, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Dict[str, Any]:
        """要求认证"""
        user = self.get_current_user(credentials)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user

# 全局实例
auth_middleware = AuthMiddleware()
'''
    
    with open('auth_middleware.py', 'w', encoding='utf-8') as f:
        f.write(middleware_code)
    
    print("✅ 认证中间件创建完成")

def main():
    """主修复流程"""
    print("🚀 开始修复认证系统...")
    print("=" * 50)
    
    # 1. 修复数据库用户
    if not fix_database_users():
        print("❌ 数据库修复失败，退出")
        return False
    
    # 2. 创建简化认证服务
    create_simple_auth_service()
    
    # 3. 创建认证中间件
    create_auth_middleware()
    
    # 4. 测试认证功能
    if test_authentication():
        print("\n✅ 认证系统修复完成！")
        print("📋 可用账户:")
        print("   - admin / admin123")
        print("   - demo / demo123")
        print("   - <EMAIL> / admin123")
        return True
    else:
        print("\n❌ 认证测试失败")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 修复成功！请重启后端服务以应用更改。")
    else:
        print("\n💥 修复失败！请检查错误信息。")
