"""
CTP安全加固模块
提供API访问控制、数据加密、审计日志和安全防护机制
"""

import base64
import hashlib
import hmac
import ipaddress
import json
import logging
import os
import secrets
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.core.cache import cache_manager, CacheKeyPrefix
from app.core.config import settings

logger = logging.getLogger(__name__)


class SecurityLevel(str, Enum):
    """安全级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AuditEventType(str, Enum):
    """审计事件类型"""

    LOGIN = "login"
    LOGOUT = "logout"
    ORDER_SUBMIT = "order_submit"
    ORDER_CANCEL = "order_cancel"
    POSITION_QUERY = "position_query"
    ACCOUNT_QUERY = "account_query"
    CONFIG_CHANGE = "config_change"
    SECURITY_VIOLATION = "security_violation"
    API_ACCESS = "api_access"
    DATA_EXPORT = "data_export"


@dataclass
class AuditEvent:
    """审计事件"""

    event_type: AuditEventType
    user_id: Optional[int]
    ip_address: str
    user_agent: str
    endpoint: str
    method: str
    request_data: Dict[str, Any]
    response_status: int
    timestamp: datetime = field(default_factory=datetime.now)
    session_id: Optional[str] = None
    risk_level: SecurityLevel = SecurityLevel.LOW
    additional_info: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "event_type": self.event_type.value,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "endpoint": self.endpoint,
            "method": self.method,
            "request_data": self.request_data,
            "response_status": self.response_status,
            "timestamp": self.timestamp.isoformat(),
            "session_id": self.session_id,
            "risk_level": self.risk_level.value,
            "additional_info": self.additional_info,
        }


class DataEncryption:
    """数据加密器"""

    def __init__(self, password: Optional[str] = None):
        self.password = password or settings.SECRET_KEY
        self._fernet = None
        self._initialize_encryption()

    def _initialize_encryption(self):
        """初始化加密器"""
        # 使用PBKDF2从密码生成密钥
        password_bytes = self.password.encode()
        # 使用环境变量中的盐值，如果没有则使用默认值（仅用于开发环境）
        salt_str = os.getenv("ENCRYPTION_SALT", "dev_salt_only_for_testing")
        salt = salt_str.encode()[:16].ljust(16, b"0")  # 确保盐值长度为16字节
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        self._fernet = Fernet(key)

    def encrypt(self, data: str) -> str:
        """加密数据"""
        try:
            encrypted_data = self._fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise

    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise

    def encrypt_dict(self, data: Dict[str, Any]) -> str:
        """加密字典数据"""
        json_data = json.dumps(data, default=str)
        return self.encrypt(json_data)

    def decrypt_dict(self, encrypted_data: str) -> Dict[str, Any]:
        """解密字典数据"""
        json_data = self.decrypt(encrypted_data)
        return json.loads(json_data)


class AccessController:
    """访问控制器"""

    def __init__(self):
        self.rate_limits = {
            "default": {"requests": 100, "window": 60},  # 每分钟100次
            "trading": {"requests": 50, "window": 60},  # 交易接口每分钟50次
            "query": {"requests": 200, "window": 60},  # 查询接口每分钟200次
        }
        self.ip_whitelist = set()
        self.ip_blacklist = set()
        self.blocked_users = set()

    async def check_rate_limit(
        self, identifier: str, endpoint_type: str = "default"
    ) -> Tuple[bool, Dict[str, Any]]:
        """检查速率限制"""
        limit_config = self.rate_limits.get(endpoint_type, self.rate_limits["default"])
        window = limit_config["window"]
        max_requests = limit_config["requests"]

        # 使用Redis计数器
        cache_key = f"rate_limit:{endpoint_type}:{identifier}"

        try:
            # 获取当前计数
            full_cache_key = f"{CacheKeyPrefix.SYSTEM_CONFIG}:{cache_key}"
            current_count = await cache_manager.get(full_cache_key) or 0

            if current_count >= max_requests:
                return False, {
                    "allowed": False,
                    "current_count": current_count,
                    "max_requests": max_requests,
                    "window_seconds": window,
                    "reset_time": datetime.now() + timedelta(seconds=window),
                }

            # 增加计数
            new_count = current_count + 1
            await cache_manager.set(full_cache_key, new_count, expire=window)

            return True, {
                "allowed": True,
                "current_count": new_count,
                "max_requests": max_requests,
                "window_seconds": window,
                "remaining": max_requests - new_count,
            }

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # 在错误情况下允许访问，但记录日志
            return True, {"allowed": True, "error": str(e)}

    def check_ip_access(self, ip_address: str) -> bool:
        """检查IP访问权限"""
        try:
            ip = ipaddress.ip_address(ip_address)

            # 检查黑名单
            if ip_address in self.ip_blacklist:
                return False

            # 如果有白名单，只允许白名单IP
            if self.ip_whitelist and ip_address not in self.ip_whitelist:
                return False

            return True

        except ValueError:
            logger.warning(f"Invalid IP address: {ip_address}")
            return False

    def add_to_whitelist(self, ip_address: str):
        """添加到IP白名单"""
        self.ip_whitelist.add(ip_address)
        logger.info(f"Added {ip_address} to IP whitelist")

    def add_to_blacklist(self, ip_address: str):
        """添加到IP黑名单"""
        self.ip_blacklist.add(ip_address)
        logger.warning(f"Added {ip_address} to IP blacklist")

    def block_user(self, user_id: int):
        """阻止用户访问"""
        self.blocked_users.add(user_id)
        logger.warning(f"Blocked user {user_id}")

    def unblock_user(self, user_id: int):
        """解除用户阻止"""
        self.blocked_users.discard(user_id)
        logger.info(f"Unblocked user {user_id}")

    def is_user_blocked(self, user_id: int) -> bool:
        """检查用户是否被阻止"""
        return user_id in self.blocked_users


class AuditLogger:
    """审计日志记录器"""

    def __init__(self):
        self.encryption = DataEncryption()
        self.max_log_size = 10000  # 最大日志条数
        self.log_retention_days = 90  # 日志保留天数

    async def log_event(self, event: AuditEvent):
        """记录审计事件"""
        try:
            # 加密敏感数据
            encrypted_event = self._encrypt_sensitive_data(event)

            # 存储到缓存（用于快速查询）
            cache_key = (
                f"audit_{event.timestamp.strftime('%Y%m%d')}_{secrets.token_hex(8)}"
            )
            full_cache_key = f"{CacheKeyPrefix.SYSTEM_CONFIG}:{cache_key}"
            await cache_manager.set(
                full_cache_key,
                encrypted_event.to_dict(),
                expire=self.log_retention_days * 24 * 3600,
            )

            # 记录到应用日志
            log_message = (
                f"AUDIT: {event.event_type.value} | "
                f"User: {event.user_id} | "
                f"IP: {event.ip_address} | "
                f"Endpoint: {event.endpoint} | "
                f"Status: {event.response_status} | "
                f"Risk: {event.risk_level.value}"
            )

            if event.risk_level in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]:
                logger.warning(log_message)
            else:
                logger.info(log_message)

        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")

    def _encrypt_sensitive_data(self, event: AuditEvent) -> AuditEvent:
        """加密敏感数据"""
        # 创建事件副本
        encrypted_event = AuditEvent(
            event_type=event.event_type,
            user_id=event.user_id,
            ip_address=event.ip_address,
            user_agent=event.user_agent,
            endpoint=event.endpoint,
            method=event.method,
            request_data={},  # 将被加密
            response_status=event.response_status,
            timestamp=event.timestamp,
            session_id=event.session_id,
            risk_level=event.risk_level,
            additional_info={},  # 将被加密
        )

        # 加密请求数据
        if event.request_data:
            encrypted_event.request_data = {
                "encrypted": self.encryption.encrypt_dict(event.request_data)
            }

        # 加密附加信息
        if event.additional_info:
            encrypted_event.additional_info = {
                "encrypted": self.encryption.encrypt_dict(event.additional_info)
            }

        return encrypted_event

    async def query_events(
        self,
        start_date: datetime,
        end_date: datetime,
        event_type: Optional[AuditEventType] = None,
        user_id: Optional[int] = None,
        risk_level: Optional[SecurityLevel] = None,
    ) -> List[Dict[str, Any]]:
        """查询审计事件"""
        try:
            # 获取日期范围内的所有审计日志键
            events = []
            current_date = start_date.date()
            end_date_only = end_date.date()

            while current_date <= end_date_only:
                date_str = current_date.strftime("%Y%m%d")
                pattern = f"audit_{date_str}_*"

                # 简化实现：直接返回空列表，因为缓存管理器没有 get_keys 方法
                # 在实际应用中，应该使用数据库存储审计日志
                current_date += timedelta(days=1)
                continue

            # 按时间排序
            events.sort(key=lambda x: x["timestamp"], reverse=True)
            return events

        except Exception as e:
            logger.error(f"Failed to query audit events: {e}")
            return []

    def _decrypt_event_data(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """解密事件数据"""
        try:
            # 解密请求数据
            if (
                "request_data" in event_data
                and "encrypted" in event_data["request_data"]
            ):
                event_data["request_data"] = self.encryption.decrypt_dict(
                    event_data["request_data"]["encrypted"]
                )

            # 解密附加信息
            if (
                "additional_info" in event_data
                and "encrypted" in event_data["additional_info"]
            ):
                event_data["additional_info"] = self.encryption.decrypt_dict(
                    event_data["additional_info"]["encrypted"]
                )

            return event_data

        except Exception as e:
            logger.error(f"Failed to decrypt event data: {e}")
            return event_data

    def _matches_filter(
        self,
        event_data: Dict[str, Any],
        event_type: Optional[AuditEventType],
        user_id: Optional[int],
        risk_level: Optional[SecurityLevel],
    ) -> bool:
        """检查事件是否匹配过滤条件"""
        if event_type and event_data.get("event_type") != event_type.value:
            return False

        if user_id and event_data.get("user_id") != user_id:
            return False

        if risk_level and event_data.get("risk_level") != risk_level.value:
            return False

        return True


class SecurityHardening:
    """安全加固主类"""

    def __init__(self):
        self.access_controller = AccessController()
        self.audit_logger = AuditLogger()
        self.encryption = DataEncryption()
        self.security_enabled = True
        self.threat_detection_enabled = True

    async def validate_request(
        self,
        user_id: Optional[int],
        ip_address: str,
        endpoint: str,
        method: str,
        user_agent: str,
        request_data: Dict[str, Any],
    ) -> Tuple[bool, Dict[str, Any]]:
        """验证请求安全性"""
        if not self.security_enabled:
            return True, {"message": "Security disabled"}

        # 检查IP访问权限
        if not self.access_controller.check_ip_access(ip_address):
            await self._log_security_violation(
                user_id, ip_address, endpoint, "IP blocked", user_agent, request_data
            )
            return False, {"error": "IP address blocked"}

        # 检查用户是否被阻止
        if user_id and self.access_controller.is_user_blocked(user_id):
            await self._log_security_violation(
                user_id, ip_address, endpoint, "User blocked", user_agent, request_data
            )
            return False, {"error": "User blocked"}

        # 检查速率限制
        identifier = f"{user_id}:{ip_address}" if user_id else ip_address
        endpoint_type = self._get_endpoint_type(endpoint)

        rate_limit_ok, rate_limit_info = await self.access_controller.check_rate_limit(
            identifier, endpoint_type
        )

        if not rate_limit_ok:
            await self._log_security_violation(
                user_id,
                ip_address,
                endpoint,
                "Rate limit exceeded",
                user_agent,
                request_data,
            )
            return False, {"error": "Rate limit exceeded", "details": rate_limit_info}

        # 威胁检测
        if self.threat_detection_enabled:
            threat_detected, threat_info = await self._detect_threats(
                user_id, ip_address, endpoint, request_data
            )

            if threat_detected:
                await self._log_security_violation(
                    user_id,
                    ip_address,
                    endpoint,
                    f"Threat detected: {threat_info}",
                    user_agent,
                    request_data,
                )
                return False, {
                    "error": "Security threat detected",
                    "details": threat_info,
                }

        return True, {"message": "Request validated", "rate_limit": rate_limit_info}

    def _get_endpoint_type(self, endpoint: str) -> str:
        """获取端点类型"""
        if "/trading/" in endpoint or "/orders/" in endpoint:
            return "trading"
        elif (
            "/query/" in endpoint
            or "/positions/" in endpoint
            or "/accounts/" in endpoint
        ):
            return "query"
        else:
            return "default"

    async def _detect_threats(
        self,
        user_id: Optional[int],
        ip_address: str,
        endpoint: str,
        request_data: Dict[str, Any],
    ) -> Tuple[bool, str]:
        """威胁检测"""
        # SQL注入检测
        if self._detect_sql_injection(request_data):
            return True, "SQL injection attempt"

        # XSS检测
        if self._detect_xss(request_data):
            return True, "XSS attempt"

        # 异常大量请求检测
        if await self._detect_abnormal_requests(user_id, ip_address):
            return True, "Abnormal request pattern"

        return False, ""

    def _detect_sql_injection(self, data: Dict[str, Any]) -> bool:
        """检测SQL注入"""
        sql_patterns = [
            "union select",
            "drop table",
            "delete from",
            "insert into",
            "update set",
            "exec(",
            "execute(",
            "sp_",
            "xp_",
        ]

        data_str = json.dumps(data).lower()
        return any(pattern in data_str for pattern in sql_patterns)

    def _detect_xss(self, data: Dict[str, Any]) -> bool:
        """检测XSS攻击"""
        xss_patterns = ["<script", "javascript:", "onload=", "onerror=", "onclick="]

        data_str = json.dumps(data).lower()
        return any(pattern in data_str for pattern in xss_patterns)

    async def _detect_abnormal_requests(
        self, user_id: Optional[int], ip_address: str
    ) -> bool:
        """检测异常请求模式"""
        if not user_id:
            return False

        # 检查短时间内的请求频率
        cache_key = (
            f"{CacheKeyPrefix.SYSTEM_CONFIG}:request_count:{user_id}:{ip_address}"
        )
        request_count = await cache_manager.get(cache_key) or 0

        # 如果5分钟内超过500次请求，认为异常
        return request_count > 500

    async def _log_security_violation(
        self,
        user_id: Optional[int],
        ip_address: str,
        endpoint: str,
        violation_type: str,
        user_agent: str,
        request_data: Dict[str, Any],
    ):
        """记录安全违规事件"""
        event = AuditEvent(
            event_type=AuditEventType.SECURITY_VIOLATION,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            endpoint=endpoint,
            method="N/A",
            request_data=request_data,
            response_status=403,
            risk_level=SecurityLevel.HIGH,
            additional_info={"violation_type": violation_type},
        )
        await self.audit_logger.log_event(event)


security_hardening = SecurityHardening()
