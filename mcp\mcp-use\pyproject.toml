[project]
name = "mcp-use"
version = "1.3.8"
description = "MCP Library for LLMs"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "mcp>=1.10.0",
    "langchain>=0.1.0",
    "websockets>=12.0",
    "aiohttp>=3.9.0",
    "pydantic>=2.0.0",
    "jsonschema-pydantic>=0.1.0",
    "python-dotenv>=1.0.0",
    "posthog>=4.8.0",
    "scarf-sdk>=0.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "ruff>=0.1.0",
    "fastmcp==2.10.5",
    "fastapi",
]
anthropic = [
    "langchain_anthropic",
]
openai = [
    "langchain_openai",
]
search = [
    "fastembed>=0.0.1",
]
e2b = [
    "e2b-code-interpreter>=1.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
