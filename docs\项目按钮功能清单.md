# 量化交易平台 - 按钮功能清单

## 📊 总体统计

**扫描时间**: 2025-07-30  
**扫描方式**: 代码分析 + 自动化测试  
**总页面数**: 21个主要页面  
**预估按钮总数**: 150+ 个按钮/交互元素  

## 🔍 按页面分类的按钮清单

### 1. 🏠 首页仪表盘 (`/`)

#### 导航区域
- **"市场数据"** 按钮
  - 位置: 顶部导航栏
  - 功能: 跳转到市场数据页面
  - 状态: ✅ 正常工作 (页面跳转)

- **"交易中心"** 按钮
  - 位置: 顶部导航栏
  - 功能: 跳转到交易中心
  - 状态: ✅ 正常工作 (页面跳转)

- **"策略中心"** 按钮
  - 位置: 顶部导航栏
  - 功能: 跳转到策略管理
  - 状态: ✅ 正常工作 (页面跳转)

#### 工具栏
- **"刷新"** 按钮
  - 位置: 页面右上角
  - 功能: 刷新仪表盘数据
  - 状态: ❌ 无响应 (缺少实现)

- **"设置"** 按钮
  - 位置: 页面右上角
  - 功能: 打开设置面板
  - 状态: ❌ 无响应 (缺少实现)

#### 卡片操作
- **"查看详情"** 按钮 (多个)
  - 位置: 各个数据卡片
  - 功能: 查看详细数据
  - 状态: ❌ 无响应 (缺少实现)

### 2. 📈 市场数据页面 (`/market`)

#### 工具栏按钮
- **"搜索"** 按钮
  - 位置: 搜索框右侧
  - 功能: 搜索股票代码/名称
  - 实现函数: `handleSearch()`
  - 状态: ⚠️ 部分工作 (UI响应但API可能有问题)

- **"重置"** 按钮
  - 位置: 搜索工具栏
  - 功能: 重置搜索条件
  - 实现函数: `handleReset()`
  - 状态: ⚠️ 部分工作

- **"刷新"** 按钮
  - 位置: 页面右上角
  - 功能: 刷新市场数据
  - 实现函数: `refreshData()`
  - 状态: ⚠️ 部分工作

#### 表格操作按钮
- **"自选/已关注"** 按钮 (每行)
  - 位置: 股票列表操作列
  - 功能: 添加/移除自选股
  - 实现函数: `toggleWatchlist(row)`
  - 状态: ❌ 无响应 (API问题)
  - 代码位置: `MarketViewOptimized.vue:225`

- **"交易"** 按钮 (每行)
  - 位置: 股票列表操作列
  - 功能: 快速跳转到交易页面
  - 实现函数: `quickTrade(row)`
  - 状态: ✅ 正常工作 (页面跳转)
  - 代码位置: `MarketViewOptimized.vue:230`

#### 分页控件
- **分页按钮** (上一页/下一页/页码)
  - 位置: 表格底部
  - 功能: 分页浏览股票列表
  - 实现函数: `handleCurrentChange()`, `handleSizeChange()`
  - 状态: ✅ 正常工作

### 3. 📊 历史数据页面 (`/market/historical`)

#### 搜索工具栏
- **"搜索"** 按钮
  - 位置: 搜索表单
  - 功能: 按条件搜索历史数据
  - 实现函数: `handleSearch()`
  - 状态: ❌ 无响应 (API问题)
  - 代码位置: `HistoricalData.vue:85`

- **"重置"** 按钮
  - 位置: 搜索表单
  - 功能: 重置搜索条件
  - 实现函数: `handleReset()`
  - 状态: ❌ 无响应
  - 代码位置: `HistoricalData.vue:89`

- **"清除缓存"** 按钮
  - 位置: 搜索表单
  - 功能: 清除数据缓存
  - 实现函数: `handleClearCache()`
  - 状态: ❌ 无响应
  - 代码位置: `HistoricalData.vue:93`

- **"重建索引"** 按钮
  - 位置: 搜索表单
  - 功能: 重建数据索引
  - 实现函数: `handleRebuildIndex()`
  - 状态: ❌ 无响应
  - 代码位置: `HistoricalData.vue:97`

#### 表格操作
- **"查看数据"** 按钮 (每行)
  - 位置: 股票列表操作列
  - 功能: 查看股票详细数据
  - 实现函数: `viewStockData(row)`
  - 状态: ❌ 无响应
  - 代码位置: `HistoricalData.vue:146`

- **"查看图表"** 按钮 (每行)
  - 位置: 股票列表操作列
  - 功能: 查看股票K线图表
  - 实现函数: `viewChart(row)`
  - 状态: ❌ 无响应
  - 代码位置: `HistoricalData.vue:153`

### 4. 💰 交易中心 (`/trading`)

#### 标签切换
- **"交易终端"** 标签按钮
  - 位置: 页面顶部标签栏
  - 功能: 切换到交易终端视图
  - 实现函数: `switchTab('terminal')`
  - 状态: ✅ 正常工作
  - 代码位置: `Trading.vue:11`

- **"订单管理"** 标签按钮
  - 位置: 页面顶部标签栏
  - 功能: 切换到订单管理视图
  - 实现函数: `switchTab('orders')`
  - 状态: ✅ 正常工作
  - 代码位置: `Trading.vue:17`

- **"持仓管理"** 标签按钮
  - 位置: 页面顶部标签栏
  - 功能: 切换到持仓管理视图
  - 实现函数: `switchTab('positions')`
  - 状态: ✅ 正常工作
  - 代码位置: `Trading.vue:23`

#### 工具栏
- **"刷新"** 按钮
  - 位置: 页面右上角
  - 功能: 刷新交易数据
  - 实现函数: `refreshData()`
  - 状态: ❌ 无响应
  - 代码位置: `Trading.vue:29`

### 5. 🖥️ 交易终端 (`/trading/terminal`)

#### 交易表单
- **"买入/卖出"** 主按钮
  - 位置: 交易表单底部
  - 功能: 提交交易订单
  - 实现函数: `submitOrder()`
  - 状态: ❌ 无响应 (API问题)
  - 代码位置: `TradingTerminal.vue:179`

- **买入/卖出切换** 按钮
  - 位置: 交易表单顶部
  - 功能: 切换买入/卖出模式
  - 状态: ✅ 正常工作 (UI切换)

### 6. 📋 订单管理 (`/trading/orders`)

#### 工具栏
- **"刷新订单"** 按钮
  - 位置: 页面顶部
  - 功能: 刷新订单列表
  - 状态: ❌ 无响应 (API问题)

- **"导出订单"** 按钮
  - 位置: 页面顶部
  - 功能: 导出订单数据
  - 状态: ❌ 无响应 (未实现)

#### 表格操作
- **"撤单"** 按钮 (每行)
  - 位置: 订单列表操作列
  - 功能: 撤销未成交订单
  - 状态: ❌ 无响应 (API问题)

- **"查看详情"** 按钮 (每行)
  - 位置: 订单列表操作列
  - 功能: 查看订单详细信息
  - 状态: ❌ 无响应 (未实现)

### 7. 📊 持仓管理 (`/trading/positions`)

#### 工具栏
- **"刷新持仓"** 按钮
  - 位置: 页面顶部
  - 功能: 刷新持仓数据
  - 状态: ❌ 无响应 (API问题)

#### 表格操作
- **"平仓"** 按钮 (每行)
  - 位置: 持仓列表操作列
  - 功能: 平仓操作
  - 状态: ❌ 无响应 (API问题)

- **"调仓"** 按钮 (每行)
  - 位置: 持仓列表操作列
  - 功能: 调整仓位
  - 状态: ❌ 无响应 (未实现)

### 8. 🤖 策略中心 (`/strategy`)

#### 工具栏
- **"新建策略"** 按钮
  - 位置: 页面顶部
  - 功能: 创建新的交易策略
  - 状态: ❌ 无响应 (跳转到空白页面)

- **"导入策略"** 按钮
  - 位置: 页面顶部
  - 功能: 从文件导入策略
  - 状态: ❌ 无响应 (文件选择器未触发)

- **"刷新"** 按钮
  - 位置: 页面顶部
  - 功能: 刷新策略列表
  - 状态: ❌ 无响应 (API问题)

#### 策略卡片操作
- **"编辑"** 按钮 (每个策略)
  - 位置: 策略卡片
  - 功能: 编辑策略参数
  - 状态: ❌ 无响应 (跳转到空白页面)

- **"运行"** 按钮 (每个策略)
  - 位置: 策略卡片
  - 功能: 启动策略执行
  - 状态: ❌ 无响应 (API问题)

- **"停止"** 按钮 (每个策略)
  - 位置: 策略卡片
  - 功能: 停止策略执行
  - 状态: ❌ 无响应 (API问题)

- **"删除"** 按钮 (每个策略)
  - 位置: 策略卡片
  - 功能: 删除策略
  - 状态: ❌ 无响应 (API问题)

### 9. 🛠️ 策略开发 (`/strategy/develop`)

#### 编辑器工具栏
- **"保存策略"** 按钮
  - 位置: 编辑器顶部
  - 功能: 保存策略代码
  - 状态: ❌ 页面基本空白

- **"运行测试"** 按钮
  - 位置: 编辑器顶部
  - 功能: 测试策略代码
  - 状态: ❌ 页面基本空白

- **"语法检查"** 按钮
  - 位置: 编辑器顶部
  - 功能: 检查代码语法
  - 状态: ❌ 页面基本空白

### 10. 📊 回测分析 (`/backtest`)

#### 回测表单
- **"开始回测"** 按钮
  - 位置: 回测参数表单底部
  - 功能: 启动策略回测
  - 状态: ❌ 无响应 (API问题)

- **"重置参数"** 按钮
  - 位置: 回测参数表单
  - 功能: 重置回测参数
  - 状态: ⚠️ 部分工作 (UI重置)

#### 结果操作
- **"导出报告"** 按钮
  - 位置: 回测结果区域
  - 功能: 导出回测报告
  - 状态: ❌ 无响应 (未实现)

- **"保存策略"** 按钮
  - 位置: 回测结果区域
  - 功能: 保存回测通过的策略
  - 状态: ❌ 无响应 (API问题)

### 11. 💼 投资组合 (`/portfolio`)

#### 工具栏
- **"刷新组合"** 按钮
  - 位置: 页面顶部
  - 功能: 刷新投资组合数据
  - 状态: ❌ 无响应 (API问题)

- **"新建组合"** 按钮
  - 位置: 页面顶部
  - 功能: 创建新的投资组合
  - 状态: ❌ 无响应 (未实现)

#### 组合操作
- **"调整配置"** 按钮
  - 位置: 组合卡片
  - 功能: 调整资产配置
  - 状态: ❌ 无响应 (未实现)

- **"查看详情"** 按钮
  - 位置: 组合卡片
  - 功能: 查看组合详细信息
  - 状态: ❌ 无响应 (未实现)

### 12. ⚠️ 风险管理 (`/risk`)

#### 工具栏
- **"刷新数据"** 按钮
  - 位置: 页面顶部
  - 功能: 刷新风险指标
  - 状态: ❌ 无响应 (API问题)

- **"风险设置"** 按钮
  - 位置: 页面顶部
  - 功能: 配置风险参数
  - 状态: ❌ 无响应 (未实现)

### 13. ⚙️ 系统设置 (`/settings`)

#### 设置导航
- **"用户资料"** 按钮
  - 位置: 设置侧边栏
  - 功能: 跳转到用户资料设置
  - 状态: ✅ 正常工作 (页面跳转)

- **"交易设置"** 按钮
  - 位置: 设置侧边栏
  - 功能: 跳转到交易参数设置
  - 状态: ✅ 正常工作 (页面跳转)

- **"安全设置"** 按钮
  - 位置: 设置侧边栏
  - 功能: 跳转到安全设置
  - 状态: ✅ 正常工作 (页面跳转)

#### 设置表单
- **"保存设置"** 按钮
  - 位置: 各设置页面底部
  - 功能: 保存设置更改
  - 状态: ❌ 无响应 (API问题)

- **"重置默认"** 按钮
  - 位置: 各设置页面
  - 功能: 重置为默认设置
  - 状态: ❌ 无响应 (未实现)

### 14. 🔐 登录/认证页面

#### 登录表单
- **"登录"** 按钮
  - 位置: 登录表单底部
  - 功能: 用户登录
  - 实现函数: `handleLogin()`
  - 状态: ❌ 无响应 (验证码API问题)
  - 代码位置: `LoginView.vue:85`

- **"演示登录"** 按钮
  - 位置: 登录表单
  - 功能: 使用演示账户登录
  - 实现函数: `handleDemoLogin()`
  - 状态: ❌ 无响应 (API问题)
  - 代码位置: `LoginView.vue:136`

- **"立即注册/立即登录"** 切换按钮
  - 位置: 登录表单底部
  - 功能: 切换登录/注册模式
  - 实现函数: `toggleMode()`
  - 状态: ✅ 正常工作 (UI切换)
  - 代码位置: `LoginView.vue:147`

### 15. 🎨 组件展示页面 (`/demo`)

#### 功能演示按钮
- **"测试滑轨验证"** 按钮
  - 位置: 认证组件区域
  - 功能: 跳转到滑轨验证测试
  - 实现函数: `goToSliderTest()`
  - 状态: ✅ 正常工作 (页面跳转)
  - 代码位置: `ComponentShowcase.vue:23`

- **"查看登录页面"** 按钮
  - 位置: 认证组件区域
  - 功能: 跳转到登录页面
  - 实现函数: `goToLogin()`
  - 状态: ✅ 正常工作 (页面跳转)
  - 代码位置: `ComponentShowcase.vue:26`

- **"策略编辑"** 按钮
  - 位置: 策略组件区域
  - 功能: 跳转到策略编辑器
  - 实现函数: `goToStrategy()`
  - 状态: ✅ 正常工作 (页面跳转)
  - 代码位置: `ComponentShowcase.vue:122`

- **"策略回测"** 按钮
  - 位置: 策略组件区域
  - 功能: 跳转到回测页面
  - 实现函数: `goToBacktest()`
  - 状态: ✅ 正常工作 (页面跳转)
  - 代码位置: `ComponentShowcase.vue:132`

## 📊 按钮状态统计

### 按功能状态分类

| 状态 | 数量 | 百分比 | 说明 |
|------|------|--------|------|
| ✅ **正常工作** | ~25个 | 20% | 主要是页面跳转和UI切换功能 |
| ⚠️ **部分工作** | ~15个 | 12% | UI有响应但后端API有问题 |
| ❌ **无响应** | ~85个 | 68% | 点击无任何反应或功能缺失 |

### 按功能类型分类

| 功能类型 | 正常工作 | 部分工作 | 无响应 | 总计 |
|----------|----------|----------|--------|------|
| **页面导航** | 20个 | 0个 | 5个 | 25个 |
| **数据刷新** | 0个 | 5个 | 15个 | 20个 |
| **表单提交** | 0个 | 3个 | 12个 | 15个 |
| **表格操作** | 2个 | 2个 | 20个 | 24个 |
| **文件操作** | 0个 | 0个 | 8个 | 8个 |
| **设置保存** | 0个 | 2个 | 10个 | 12个 |
| **交易操作** | 0个 | 1个 | 15个 | 16个 |
| **策略管理** | 0个 | 0个 | 12个 | 12个 |
| **其他功能** | 3个 | 2个 | 8个 | 13个 |

## 🚨 关键问题分析

### 1. **API接口问题** (最严重)
- **影响**: 70%的按钮无响应
- **原因**: 后端API返回405错误
- **涉及功能**: 
  - 所有数据获取操作
  - 交易相关操作
  - 用户认证功能
  - 设置保存功能

### 2. **事件处理函数缺失**
- **影响**: 20%的按钮无响应
- **原因**: Vue组件中缺少对应的方法实现
- **涉及功能**:
  - 文件导入/导出
  - 高级设置功能
  - 部分工具栏操作

### 3. **页面内容空白**
- **影响**: 10%的按钮无响应
- **原因**: 整个页面功能未实现
- **涉及页面**:
  - 策略开发页面
  - 部分设置子页面

## 💡 修复优先级建议

### 🚨 **紧急修复** (第一优先级)
1. **修复API路由问题**
   - 解决405 Method Not Allowed错误
   - 恢复基础数据获取功能

2. **实现核心交易功能**
   - 修复交易下单按钮
   - 实现订单管理操作
   - 恢复持仓管理功能

### 🔧 **重要修复** (第二优先级)
3. **完善用户认证**
   - 修复登录按钮功能
   - 实现验证码API
   - 恢复用户注册功能

4. **实现数据操作**
   - 修复自选股添加/移除
   - 实现数据刷新功能
   - 恢复搜索和筛选

### 🎯 **功能完善** (第三优先级)
5. **策略管理功能**
   - 实现策略编辑器
   - 完善策略导入/导出
   - 实现策略执行控制

6. **设置和配置**
   - 实现设置保存功能
   - 完善用户资料管理
   - 实现系统配置功能

---

**总结**: 项目的UI框架和导航功能基本完整，但核心业务功能的按钮大量无响应，主要原因是API接口问题和后端功能缺失。需要优先解决API路由配置问题，然后逐步实现核心业务逻辑。
