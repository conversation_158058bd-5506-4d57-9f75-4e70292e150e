"""
交易服务单元测试
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.trading_service import TradingService
from app.models.trading import Order, Position, Portfolio
from app.schemas.trading import (
    OrderCreate,
    OrderUpdate,
    OrderSide,
    OrderType,
    OrderStatus,
)
from app.core.exceptions import TradingError, ValidationError


@pytest.mark.unit
@pytest.mark.trading
@pytest.mark.asyncio
class TestTradingService:
    """交易服务测试类"""

    @pytest.fixture
    def trading_service(self):
        """创建交易服务实例"""
        return TradingService()

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def sample_order_create(self):
        """示例订单创建数据"""
        return OrderCreate(
            symbol="000001",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=100,
            price=Decimal("10.50"),
            user_id="test-user-id",
        )

    @pytest.fixture
    def sample_order_model(self):
        """示例订单模型"""
        order = Mock(spec=Order)
        order.id = "test-order-id"
        order.symbol = "000001"
        order.side = OrderSide.BUY
        order.order_type = OrderType.LIMIT
        order.quantity = 100
        order.price = Decimal("10.50")
        order.status = OrderStatus.PENDING
        order.user_id = "test-user-id"
        order.created_at = datetime.now()
        return order

    @pytest.fixture
    def sample_position_model(self):
        """示例持仓模型"""
        position = Mock(spec=Position)
        position.id = "test-position-id"
        position.symbol = "000001"
        position.quantity = 100
        position.average_price = Decimal("10.50")
        position.market_value = Decimal("1050.00")
        position.unrealized_pnl = Decimal("0.00")
        position.user_id = "test-user-id"
        return position

    async def test_create_order_success(
        self, trading_service, mock_db_session, sample_order_create
    ):
        """测试成功创建订单"""
        # 模拟数据库操作
        mock_db_session.add = Mock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()

        # 执行测试
        with patch("app.services.trading_service.Order") as mock_order_class:
            mock_order_instance = Mock()
            mock_order_instance.id = "new-order-id"
            mock_order_instance.status = OrderStatus.PENDING
            mock_order_class.return_value = mock_order_instance

            result = await trading_service.create_order(
                mock_db_session, sample_order_create
            )

            # 验证结果
            assert result is not None
            assert result.id == "new-order-id"
            assert result.status == OrderStatus.PENDING
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_called_once()

    async def test_create_order_invalid_quantity(
        self, trading_service, mock_db_session, sample_order_create
    ):
        """测试创建无效数量的订单"""
        # 设置无效数量
        sample_order_create.quantity = 0

        # 执行测试并验证异常
        with pytest.raises(ValueError, match="订单数量必须大于0"):
            await trading_service.create_order(mock_db_session, sample_order_create)

    async def test_create_order_invalid_price(
        self, trading_service, mock_db_session, sample_order_create
    ):
        """测试创建无效价格的订单"""
        # 设置无效价格
        sample_order_create.price = Decimal("0")

        # 执行测试并验证异常
        with pytest.raises(ValueError, match="限价订单价格必须大于0"):
            await trading_service.create_order(mock_db_session, sample_order_create)

    async def test_get_order_by_id_success(
        self, trading_service, mock_db_session, sample_order_model
    ):
        """测试通过ID获取订单成功"""
        # 模拟数据库查询
        mock_db_session.get.return_value = sample_order_model

        # 执行测试
        result = await trading_service.get_order_by_id(mock_db_session, "test-order-id")

        # 验证结果
        assert result == sample_order_model
        mock_db_session.get.assert_called_once_with(Order, "test-order-id")

    async def test_get_order_by_id_not_found(self, trading_service, mock_db_session):
        """测试通过ID获取订单失败"""
        # 模拟订单不存在
        mock_db_session.get.return_value = None

        # 执行测试
        result = await trading_service.get_order_by_id(
            mock_db_session, "nonexistent-id"
        )

        # 验证结果
        assert result is None

    async def test_update_order_status_success(
        self, trading_service, mock_db_session, sample_order_model
    ):
        """测试更新订单状态成功"""
        # 模拟数据库操作
        mock_db_session.get.return_value = sample_order_model
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()

        # 执行测试
        result = await trading_service.update_order_status(
            mock_db_session, "test-order-id", OrderStatus.FILLED
        )

        # 验证结果
        assert result == sample_order_model
        assert sample_order_model.status == OrderStatus.FILLED
        mock_db_session.commit.assert_called_once()

    async def test_update_order_status_not_found(
        self, trading_service, mock_db_session
    ):
        """测试更新不存在订单的状态"""
        # 模拟订单不存在
        mock_db_session.get.return_value = None

        # 执行测试
        result = await trading_service.update_order_status(
            mock_db_session, "nonexistent-id", OrderStatus.FILLED
        )

        # 验证结果
        assert result is None

    async def test_cancel_order_success(
        self, trading_service, mock_db_session, sample_order_model
    ):
        """测试取消订单成功"""
        # 设置订单状态为待处理
        sample_order_model.status = OrderStatus.PENDING
        mock_db_session.get.return_value = sample_order_model
        mock_db_session.commit = AsyncMock()

        # 执行测试
        result = await trading_service.cancel_order(mock_db_session, "test-order-id")

        # 验证结果
        assert result is True
        assert sample_order_model.status == OrderStatus.CANCELLED
        mock_db_session.commit.assert_called_once()

    async def test_cancel_order_already_filled(
        self, trading_service, mock_db_session, sample_order_model
    ):
        """测试取消已成交订单"""
        # 设置订单状态为已成交
        sample_order_model.status = OrderStatus.FILLED
        mock_db_session.get.return_value = sample_order_model

        # 执行测试并验证异常
        with pytest.raises(ValueError, match="无法取消已成交或已取消的订单"):
            await trading_service.cancel_order(mock_db_session, "test-order-id")

    async def test_get_user_orders_success(self, trading_service, mock_db_session):
        """测试获取用户订单成功"""
        # 模拟数据库查询结果
        mock_orders = [Mock(), Mock(), Mock()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_orders
        mock_db_session.execute.return_value = mock_result

        # 执行测试
        result = await trading_service.get_user_orders(mock_db_session, "test-user-id")

        # 验证结果
        assert result == mock_orders
        mock_db_session.execute.assert_called_once()

    async def test_get_user_positions_success(self, trading_service, mock_db_session):
        """测试获取用户持仓成功"""
        # 模拟数据库查询结果
        mock_positions = [Mock(), Mock()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_positions
        mock_db_session.execute.return_value = mock_result

        # 执行测试
        result = await trading_service.get_user_positions(
            mock_db_session, "test-user-id"
        )

        # 验证结果
        assert result == mock_positions
        mock_db_session.execute.assert_called_once()

    async def test_calculate_position_value(
        self, trading_service, sample_position_model
    ):
        """测试计算持仓价值"""
        # 设置当前价格
        current_price = Decimal("11.00")

        # 执行测试
        with patch(
            "app.services.trading_service.TradingService.get_current_price",
            return_value=current_price,
        ):
            market_value, unrealized_pnl = (
                await trading_service.calculate_position_value(
                    sample_position_model, current_price
                )
            )

        # 验证结果
        expected_market_value = Decimal("1100.00")  # 100 * 11.00
        expected_unrealized_pnl = Decimal("50.00")  # (11.00 - 10.50) * 100

        assert market_value == expected_market_value
        assert unrealized_pnl == expected_unrealized_pnl

    async def test_update_position_success(
        self, trading_service, mock_db_session, sample_position_model
    ):
        """测试更新持仓成功"""
        # 模拟数据库操作
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = (
            sample_position_model
        )
        mock_db_session.commit = AsyncMock()

        # 执行测试
        result = await trading_service.update_position(
            mock_db_session, "test-user-id", "000001", 50, Decimal("11.00")
        )

        # 验证结果
        assert result == sample_position_model
        mock_db_session.commit.assert_called_once()

    async def test_create_new_position(self, trading_service, mock_db_session):
        """测试创建新持仓"""
        # 模拟没有现有持仓
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
        mock_db_session.add = Mock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()

        # 执行测试
        with patch("app.services.trading_service.Position") as mock_position_class:
            mock_position_instance = Mock()
            mock_position_instance.id = "new-position-id"
            mock_position_class.return_value = mock_position_instance

            result = await trading_service.update_position(
                mock_db_session, "test-user-id", "000001", 100, Decimal("10.50")
            )

            # 验证结果
            assert result is not None
            assert result.id == "new-position-id"
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_called_once()

    async def test_get_portfolio_summary(self, trading_service, mock_db_session):
        """测试获取投资组合摘要"""
        # 模拟持仓数据
        mock_positions = [
            Mock(
                symbol="000001",
                quantity=100,
                market_value=Decimal("1100.00"),
                unrealized_pnl=Decimal("50.00"),
            ),
            Mock(
                symbol="000002",
                quantity=200,
                market_value=Decimal("2200.00"),
                unrealized_pnl=Decimal("-100.00"),
            ),
        ]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_positions
        mock_db_session.execute.return_value = mock_result

        # 执行测试
        result = await trading_service.get_portfolio_summary(
            mock_db_session, "test-user-id"
        )

        # 验证结果
        assert result["total_market_value"] == Decimal("3300.00")
        assert result["total_unrealized_pnl"] == Decimal("-50.00")
        assert result["position_count"] == 2
        assert len(result["positions"]) == 2

    # 添加更多边界情况和错误处理测试
    
    async def test_create_order_database_error(self, trading_service, mock_db_session, sample_order_create):
        """测试创建订单时数据库错误"""
        # 模拟数据库错误
        mock_db_session.add = Mock()
        mock_db_session.commit = AsyncMock(side_effect=Exception("Database error"))
        
        # 执行测试并验证异常
        with pytest.raises(Exception, match="Database error"):
            await trading_service.create_order(mock_db_session, sample_order_create)
    
    async def test_create_market_order(self, trading_service, mock_db_session):
        """测试创建市价订单"""
        order_create = OrderCreate(
            symbol="000001",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100,
            user_id="test-user-id"
        )
        
        mock_db_session.add = Mock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        with patch("app.services.trading_service.Order") as mock_order_class:
            mock_order_instance = Mock()
            mock_order_instance.id = "market-order-id"
            mock_order_instance.status = OrderStatus.PENDING
            mock_order_class.return_value = mock_order_instance
            
            result = await trading_service.create_order(mock_db_session, order_create)
            
            assert result is not None
            assert result.id == "market-order-id"
    
    async def test_create_order_negative_quantity(self, trading_service, mock_db_session, sample_order_create):
        """测试创建负数量订单"""
        sample_order_create.quantity = -100
        
        with pytest.raises(ValueError, match="订单数量必须大于0"):
            await trading_service.create_order(mock_db_session, sample_order_create)
    
    async def test_create_order_zero_price_limit_order(self, trading_service, mock_db_session):
        """测试创建价格为0的限价订单"""
        order_create = OrderCreate(
            symbol="000001",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=100,
            price=Decimal("0"),
            user_id="test-user-id"
        )
        
        with pytest.raises(ValueError, match="限价订单价格必须大于0"):
            await trading_service.create_order(mock_db_session, order_create)
    
    async def test_cancel_order_not_found(self, trading_service, mock_db_session):
        """测试取消不存在的订单"""
        mock_db_session.get.return_value = None
        
        with pytest.raises(ValueError, match="订单不存在"):
            await trading_service.cancel_order(mock_db_session, "nonexistent-id")
    
    async def test_cancel_order_already_cancelled(self, trading_service, mock_db_session, sample_order_model):
        """测试取消已取消的订单"""
        sample_order_model.status = OrderStatus.CANCELLED
        mock_db_session.get.return_value = sample_order_model
        
        with pytest.raises(ValueError, match="无法取消已成交或已取消的订单"):
            await trading_service.cancel_order(mock_db_session, "test-order-id")
    
    async def test_update_position_with_zero_quantity(self, trading_service, mock_db_session, sample_position_model):
        """测试更新持仓至零数量（清仓）"""
        sample_position_model.quantity = 50
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = sample_position_model
        mock_db_session.commit = AsyncMock()
        mock_db_session.delete = Mock()
        
        result = await trading_service.update_position(
            mock_db_session, "test-user-id", "000001", -50, Decimal("11.00")
        )
        
        # 验证持仓被删除
        mock_db_session.delete.assert_called_once_with(sample_position_model)
        mock_db_session.commit.assert_called_once()
    
    async def test_get_empty_portfolio_summary(self, trading_service, mock_db_session):
        """测试获取空投资组合摘要"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db_session.execute.return_value = mock_result
        
        result = await trading_service.get_portfolio_summary(mock_db_session, "test-user-id")
        
        assert result["total_market_value"] == Decimal("0")
        assert result["total_unrealized_pnl"] == Decimal("0")
        assert result["position_count"] == 0
        assert len(result["positions"]) == 0
    
    async def test_calculate_position_value_with_zero_quantity(self, trading_service):
        """测试计算零数量持仓价值"""
        position = Mock(quantity=0, average_price=Decimal("10.50"))
        current_price = Decimal("11.00")
        
        market_value, unrealized_pnl = await trading_service.calculate_position_value(
            position, current_price
        )
        
        assert market_value == Decimal("0")
        assert unrealized_pnl == Decimal("0")
    
    async def test_order_history_with_filters(self, trading_service, mock_db_session):
        """测试带过滤条件的订单历史查询"""
        mock_orders = [Mock(), Mock()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_orders
        mock_db_session.execute.return_value = mock_result
        
        result = await trading_service.get_user_orders(
            mock_db_session, 
            "test-user-id",
            symbol="000001",
            status=OrderStatus.FILLED,
            limit=10
        )
        
        assert result == mock_orders
        mock_db_session.execute.assert_called_once()
    
    async def test_batch_cancel_orders(self, trading_service, mock_db_session):
        """测试批量取消订单"""
        # 模拟多个待处理订单
        mock_orders = [
            Mock(id="order1", status=OrderStatus.PENDING),
            Mock(id="order2", status=OrderStatus.PENDING),
            Mock(id="order3", status=OrderStatus.FILLED)  # 已成交，不能取消
        ]
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_orders
        mock_db_session.execute.return_value = mock_result
        mock_db_session.commit = AsyncMock()
        
        order_ids = ["order1", "order2", "order3"]
        result = await trading_service.batch_cancel_orders(mock_db_session, order_ids)
        
        # 验证结果
        assert result["cancelled"] == 2
        assert result["failed"] == 1
        assert mock_orders[0].status == OrderStatus.CANCELLED
        assert mock_orders[1].status == OrderStatus.CANCELLED
        assert mock_orders[2].status == OrderStatus.FILLED  # 未改变
    
    async def test_position_risk_calculation(self, trading_service, sample_position_model):
        """测试持仓风险计算"""
        sample_position_model.quantity = 1000
        sample_position_model.average_price = Decimal("50.00")
        current_price = Decimal("45.00")
        
        market_value, unrealized_pnl = await trading_service.calculate_position_value(
            sample_position_model, current_price
        )
        pnl_percentage = (unrealized_pnl / (sample_position_model.average_price * sample_position_model.quantity)) * 100
        
        assert market_value == Decimal("45000.00")
        assert unrealized_pnl == Decimal("-5000.00")
        assert pnl_percentage == Decimal("-10.00")
    
    async def test_order_validation_edge_cases(self, trading_service, mock_db_session):
        """测试订单验证边界情况"""
        # 测试极小数量订单
        order_create = OrderCreate(
            symbol="000001",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1,  # 最小数量
            price=Decimal("0.01"),  # 最小价格
            user_id="test-user-id"
        )
        
        mock_db_session.add = Mock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        with patch("app.services.trading_service.Order") as mock_order_class:
            mock_order_instance = Mock()
            mock_order_instance.id = "edge-case-order"
            mock_order_class.return_value = mock_order_instance
            
            result = await trading_service.create_order(mock_db_session, order_create)
            assert result is not None
    
    async def test_concurrent_position_updates(self, trading_service, mock_db_session, sample_position_model):
        """测试并发持仓更新的处理"""
        # 模拟并发更新场景
        mock_db_session.execute.return_value.scalar_one_or_none.return_value = sample_position_model
        mock_db_session.commit = AsyncMock()
        
        # 第一次更新
        result1 = await trading_service.update_position(
            mock_db_session, "test-user-id", "000001", 50, Decimal("10.75")
        )
        
        # 第二次更新（模拟并发）
        result2 = await trading_service.update_position(
            mock_db_session, "test-user-id", "000001", 25, Decimal("10.80")
        )
        
        assert result1 == sample_position_model
        assert result2 == sample_position_model
        assert mock_db_session.commit.call_count == 2
