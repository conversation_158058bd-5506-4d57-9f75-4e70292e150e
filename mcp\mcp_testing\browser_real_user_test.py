#!/usr/bin/env python3
"""
浏览器真实用户测试 - 使用Playwright进行深度用户体验测试
模拟真实用户使用量化投资平台的完整流程
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
import logging
from playwright.async_api import async_playwright
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('browser_real_user_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrowserRealUserTester:
    def __init__(self):
        self.test_session_id = f"browser_real_user_{int(time.time())}"
        self.platform_url = "http://localhost:5173"
        self.test_results = {
            'session_id': self.test_session_id,
            'start_time': datetime.now().isoformat(),
            'test_type': 'Browser Real User Experience Test',
            'user_scenarios': [],
            'discovered_issues': [],
            'performance_metrics': [],
            'user_feedback': [],
            'recommendations': [],
            'screenshots': []
        }
        self.browser = None
        self.page = None
        
    async def setup_browser(self):
        """设置浏览器环境"""
        logger.info("设置浏览器测试环境")
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 可视化测试
            slow_mo=1000     # 慢动作，便于观察
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        self.page = await context.new_page()
        
        # 设置页面事件监听
        self.page.on('console', self._handle_console_message)
        self.page.on('pageerror', self._handle_page_error)
        
    async def _handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type in ['error', 'warning']:
            self.test_results['discovered_issues'].append({
                'type': 'console',
                'level': msg.type,
                'message': msg.text,
                'timestamp': datetime.now().isoformat()
            })
            
    async def _handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['discovered_issues'].append({
            'type': 'page_error',
            'message': str(error),
            'timestamp': datetime.now().isoformat()
        })
        
    async def take_screenshot(self, name: str):
        """截图"""
        if self.page:
            screenshot_path = f"screenshots/{self.test_session_id}_{name}_{int(time.time())}.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)
            self.test_results['screenshots'].append(screenshot_path)
            logger.info(f"截图已保存: {screenshot_path}")
            return screenshot_path
        return None
        
    async def test_platform_access(self):
        """测试平台访问"""
        logger.info("测试平台访问")
        
        scenario = {
            'name': '平台访问测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 尝试访问平台
            await self.page.goto(self.platform_url, timeout=30000)
            scenario['steps'].append("成功访问平台URL")
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle', timeout=10000)
            scenario['steps'].append("页面加载完成")
            
            # 检查页面标题
            title = await self.page.title()
            scenario['steps'].append(f"页面标题: {title}")
            
            # 截图
            await self.take_screenshot('platform_access')
            
            # 检查是否有明显的错误信息
            error_selectors = [
                'text=404',
                'text=Error',
                'text=错误',
                '.error',
                '[class*="error"]'
            ]
            
            for selector in error_selectors:
                try:
                    error_element = await self.page.wait_for_selector(selector, timeout=1000)
                    if error_element:
                        scenario['issues'].append(f"发现错误信息: {selector}")
                except:
                    pass
                    
            if not scenario['issues']:
                scenario['user_feedback'].append("平台可以正常访问")
            else:
                scenario['user_feedback'].append("平台访问存在问题")
                
        except Exception as e:
            scenario['issues'].append(f"访问失败: {str(e)}")
            scenario['user_feedback'].append("无法访问平台")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
    async def test_navigation_experience(self):
        """测试导航体验"""
        logger.info("测试导航体验")
        
        scenario = {
            'name': '导航体验测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 查找导航元素
            nav_selectors = [
                'nav',
                '.nav',
                '.navigation',
                '.menu',
                '[role="navigation"]'
            ]
            
            nav_found = False
            for selector in nav_selectors:
                try:
                    nav_element = await self.page.wait_for_selector(selector, timeout=2000)
                    if nav_element:
                        scenario['steps'].append(f"发现导航元素: {selector}")
                        nav_found = True
                        break
                except:
                    continue
                    
            if not nav_found:
                scenario['issues'].append("未找到明显的导航元素")
                scenario['user_feedback'].append("导航不够明显，用户可能迷失")
                
            # 查找可点击的链接或按钮
            clickable_elements = await self.page.query_selector_all('a, button, [role="button"]')
            scenario['steps'].append(f"发现{len(clickable_elements)}个可点击元素")
            
            if len(clickable_elements) < 3:
                scenario['issues'].append("可点击元素过少")
                scenario['user_feedback'].append("页面交互性不足")
            else:
                scenario['user_feedback'].append("页面有足够的交互元素")
                
            # 尝试点击一些导航元素
            for i, element in enumerate(clickable_elements[:3]):
                try:
                    text = await element.inner_text()
                    if text and len(text.strip()) > 0:
                        await element.click()
                        await self.page.wait_for_timeout(1000)
                        scenario['steps'].append(f"成功点击: {text[:20]}")
                        await self.take_screenshot(f'navigation_click_{i}')
                except Exception as e:
                    scenario['issues'].append(f"点击失败: {str(e)}")
                    
        except Exception as e:
            scenario['issues'].append(f"导航测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
    async def test_content_visibility(self):
        """测试内容可见性"""
        logger.info("测试内容可见性")
        
        scenario = {
            'name': '内容可见性测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 检查页面是否有实际内容
            text_content = await self.page.inner_text('body')
            if len(text_content.strip()) < 100:
                scenario['issues'].append("页面内容过少")
                scenario['user_feedback'].append("页面内容不够丰富")
            else:
                scenario['steps'].append(f"页面内容长度: {len(text_content)}字符")
                scenario['user_feedback'].append("页面有足够的内容")
                
            # 检查是否有图表或可视化元素
            chart_selectors = [
                'canvas',
                '.chart',
                '.echarts',
                '[id*="chart"]',
                '[class*="chart"]'
            ]
            
            charts_found = 0
            for selector in chart_selectors:
                elements = await self.page.query_selector_all(selector)
                charts_found += len(elements)
                
            scenario['steps'].append(f"发现{charts_found}个图表元素")
            
            if charts_found == 0:
                scenario['issues'].append("未发现图表或可视化元素")
                scenario['user_feedback'].append("缺少数据可视化，不符合量化平台预期")
            else:
                scenario['user_feedback'].append("有数据可视化元素")
                
            # 检查表格
            tables = await self.page.query_selector_all('table, .table, [role="table"]')
            scenario['steps'].append(f"发现{len(tables)}个表格元素")
            
            if len(tables) == 0:
                scenario['issues'].append("未发现数据表格")
                scenario['user_feedback'].append("缺少数据表格展示")
                
            await self.take_screenshot('content_visibility')
            
        except Exception as e:
            scenario['issues'].append(f"内容检查失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
    async def test_performance_perception(self):
        """测试性能感知"""
        logger.info("测试性能感知")
        
        scenario = {
            'name': '性能感知测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 测试页面响应性
            start_time = time.time()
            await self.page.reload()
            await self.page.wait_for_load_state('networkidle')
            load_time = time.time() - start_time
            
            scenario['steps'].append(f"页面重载时间: {load_time:.2f}秒")
            
            if load_time > 5:
                scenario['issues'].append("页面加载时间过长")
                scenario['user_feedback'].append("页面加载速度慢，影响用户体验")
            elif load_time > 2:
                scenario['user_feedback'].append("页面加载速度一般")
            else:
                scenario['user_feedback'].append("页面加载速度快")
                
            # 测试交互响应
            buttons = await self.page.query_selector_all('button')
            if buttons:
                for i, button in enumerate(buttons[:2]):
                    try:
                        start_time = time.time()
                        await button.click()
                        response_time = time.time() - start_time
                        scenario['steps'].append(f"按钮{i+1}响应时间: {response_time:.3f}秒")
                        
                        if response_time > 1:
                            scenario['issues'].append(f"按钮{i+1}响应慢")
                    except:
                        pass
                        
        except Exception as e:
            scenario['issues'].append(f"性能测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
    async def generate_user_experience_report(self):
        """生成用户体验报告"""
        logger.info("生成用户体验报告")
        
        # 分析所有问题
        all_issues = []
        for scenario in self.test_results['user_scenarios']:
            all_issues.extend(scenario.get('issues', []))
            
        # 生成建议
        recommendations = []
        
        if any('导航' in issue for issue in all_issues):
            recommendations.append("改进导航设计，使其更加明显和易用")
            
        if any('内容' in issue for issue in all_issues):
            recommendations.append("增加页面内容丰富度")
            
        if any('图表' in issue for issue in all_issues):
            recommendations.append("添加数据可视化图表，符合量化平台特性")
            
        if any('表格' in issue for issue in all_issues):
            recommendations.append("添加数据表格展示功能")
            
        if any('加载' in issue for issue in all_issues):
            recommendations.append("优化页面加载性能")
            
        if any('响应' in issue for issue in all_issues):
            recommendations.append("提升交互响应速度")
            
        # 通用建议
        recommendations.extend([
            "增加用户引导和帮助信息",
            "优化移动端适配",
            "添加加载状态指示器",
            "改进错误处理和用户反馈"
        ])
        
        self.test_results['recommendations'] = recommendations
        
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
            
    async def save_test_results(self):
        """保存测试结果"""
        self.test_results['end_time'] = datetime.now().isoformat()
        self.test_results['total_duration'] = time.time() - time.mktime(
            datetime.fromisoformat(self.test_results['start_time']).timetuple()
        )
        
        # 保存详细报告
        report_file = f"reports/browser_real_user_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"浏览器测试报告已保存: {report_file}")
        
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 80)
        print("浏览器真实用户体验测试报告")
        print("=" * 80)
        
        total_scenarios = len(self.test_results['user_scenarios'])
        total_issues = sum(len(s.get('issues', [])) for s in self.test_results['user_scenarios'])
        total_screenshots = len(self.test_results['screenshots'])
        
        print(f"测试会话: {self.test_session_id}")
        print(f"测试时间: {self.test_results.get('total_duration', 0):.2f}秒")
        print(f"测试场景: {total_scenarios}个")
        print(f"发现问题: {total_issues}个")
        print(f"截图数量: {total_screenshots}张")
        
        print(f"\n用户体验建议:")
        for i, rec in enumerate(self.test_results['recommendations'], 1):
            print(f"  {i}. {rec}")
            
        if total_issues == 0:
            print("\n✅ 用户体验良好")
        elif total_issues <= 3:
            print("\n⚠️ 用户体验一般，有改进空间")
        else:
            print("\n🔴 用户体验需要重要改进")
            
    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("开始浏览器真实用户体验测试")
        
        try:
            await self.setup_browser()
            await self.test_platform_access()
            await self.test_navigation_experience()
            await self.test_content_visibility()
            await self.test_performance_perception()
            await self.generate_user_experience_report()
            await self.save_test_results()
            
            self.print_summary()
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {str(e)}")
        finally:
            await self.cleanup()
            
        logger.info("浏览器真实用户体验测试完成")

async def main():
    """主函数"""
    tester = BrowserRealUserTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
