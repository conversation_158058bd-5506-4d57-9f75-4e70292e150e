#!/usr/bin/env python3
"""
快速API测试脚本
测试Tushare数据接入的各个API端点
"""
import asyncio
import json
import time
import httpx


async def test_api():
    """测试API端点"""
    base_url = "http://localhost:8000"

    print("🚀 开始API测试...")
    print("=" * 50)

    async with httpx.AsyncClient(timeout=30.0) as client:

        # 测试健康检查
        print("🔍 测试健康检查...")
        try:
            start_time = time.time()
            response = await client.get(f"{base_url}/health")
            duration = time.time() - start_time

            if response.status_code == 200:
                print(f"✅ 健康检查通过 ({duration:.3f}s)")
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")

        print()

        # 测试指数数据
        print("📊 测试指数数据接口...")
        try:
            start_time = time.time()
            response = await client.get(f"{base_url}/api/v1/market/indices")
            duration = time.time() - start_time

            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    print(f"✅ 指数数据获取成功 ({duration:.3f}s)")
                    print(f"   获取到 {len(data)} 个指数")
                    for idx in data[:2]:  # 显示前2个
                        print(
                            f"   - {idx['name']}: {idx['currentPrice']} ({idx['changePercent']:+.2f}%)"
                        )
                else:
                    print(f"⚠️ 指数数据格式异常: {data}")
            else:
                print(f"❌ 指数数据获取失败: {response.status_code}")
                print(f"   响应: {response.text}")
        except Exception as e:
            print(f"❌ 指数数据异常: {e}")

        print()

        # 测试股票列表
        print("📈 测试股票列表接口...")
        try:
            start_time = time.time()
            response = await client.get(
                f"{base_url}/api/v1/market/stocks?page=1&pageSize=5"
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                data = response.json()
                if data.get("stocks") and len(data["stocks"]) > 0:
                    print(f"✅ 股票列表获取成功 ({duration:.3f}s)")
                    print(f"   总股票数: {data.get('total', 0)}")
                    print(f"   当前页: {len(data['stocks'])} 只股票")
                    for stock in data["stocks"][:3]:  # 显示前3只
                        print(
                            f"   - {stock['name']} ({stock['symbol']}): {stock['currentPrice']} ({stock['changePercent']:+.2f}%)"
                        )
                else:
                    print(f"⚠️ 股票列表为空: {data}")
            else:
                print(f"❌ 股票列表获取失败: {response.status_code}")
                print(f"   响应: {response.text}")
        except Exception as e:
            print(f"❌ 股票列表异常: {e}")

        print()

        # 测试K线数据
        print("📊 测试K线数据接口...")
        try:
            start_time = time.time()
            test_symbol = "000001.SZ"  # 平安银行
            response = await client.get(
                f"{base_url}/api/v1/market/kline?symbol={test_symbol}&period=D&limit=5"
            )
            duration = time.time() - start_time

            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and data.get("data"):
                    kline_data = data["data"]
                    print(f"✅ K线数据获取成功 ({duration:.3f}s)")
                    print(f"   股票: {test_symbol}")
                    print(f"   数据量: {len(kline_data)} 条")
                    if kline_data:
                        latest = kline_data[-1]
                        print(
                            f"   最新: {latest['date']} 收盘:{latest['close']} 成交量:{latest['volume']}"
                        )
                else:
                    print(f"⚠️ K线数据为空: {data}")
            else:
                print(f"❌ K线数据获取失败: {response.status_code}")
                print(f"   响应: {response.text}")
        except Exception as e:
            print(f"❌ K线数据异常: {e}")

        print()
        print("=" * 50)
        print("🎉 API测试完成！")


async def main():
    """主函数"""
    try:
        await test_api()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"💥 测试执行异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
