"""optimize database indexes for performance

Revision ID: 006_optimize_database_indexes
Revises: 005_add_composite_indexes
Create Date: 2025-07-16
"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "006_optimize_database_indexes"
down_revision = "005_add_composite_indexes"
branch_labels = None
depends_on = None


def upgrade():
    """添加数据库性能优化索引"""
    
    # 1. 市场数据表优化索引
    try:
        # 复合索引：按股票代码和时间查询（降序）
        op.create_index(
            "ix_market_data_symbol_timestamp_desc",
            "market_data", 
            ["symbol_code", sa.text("timestamp DESC")]
        )
        
        # 覆盖索引：避免回表查询
        op.create_index(
            "ix_market_data_symbol_time_price",
            "market_data",
            ["symbol_code", "timestamp"],
            postgresql_include=["last_price", "volume", "turnover"]
        )
        
        # 交易日期范围查询索引
        op.create_index(
            "ix_market_data_trading_date_range",
            "market_data",
            ["trading_date", "symbol_code"]
        )
    except Exception as e:
        print(f"Market data indexes creation error (might already exist): {e}")
    
    # 2. K线数据表优化索引
    try:
        # 复合索引：股票+类型+日期（降序）
        op.create_index(
            "ix_kline_symbol_type_date_desc",
            "kline_data",
            ["symbol_code", "kline_type", sa.text("trading_date DESC")]
        )
        
        # 周期时间范围查询索引
        op.create_index(
            "ix_kline_period_range",
            "kline_data",
            ["period_start", "period_end", "symbol_code"]
        )
        
        # K线类型专用索引
        op.create_index(
            "ix_kline_type_date",
            "kline_data",
            ["kline_type", "trading_date"]
        )
    except Exception as e:
        print(f"KLine data indexes creation error: {e}")
    
    # 3. 订单表优化索引
    try:
        # 用户订单状态时间复合索引
        op.create_index(
            "ix_orders_user_status_created_desc",
            "orders",
            ["user_id", "status", sa.text("created_at DESC")]
        )
        
        # 股票订单查询索引
        op.create_index(
            "ix_orders_symbol_status_time",
            "orders",
            ["symbol_code", "status", "created_at"]
        )
        
        # 订单类型和方向索引
        op.create_index(
            "ix_orders_type_side_status",
            "orders",
            ["order_type", "side", "status"]
        )
        
        # 活跃订单查询索引
        op.create_index(
            "ix_orders_active",
            "orders",
            ["status", "created_at"],
            postgresql_where=sa.text("status IN ('pending', 'submitted', 'partial')")
        )
    except Exception as e:
        print(f"Orders indexes creation error: {e}")
    
    # 4. 成交记录表优化索引
    try:
        # 用户成交记录时间索引
        op.create_index(
            "ix_trades_user_time_desc",
            "trades",
            ["user_id", sa.text("trade_time DESC")]
        )
        
        # 股票成交记录索引
        op.create_index(
            "ix_trades_symbol_time",
            "trades",
            ["symbol_code", "trade_time"]
        )
        
        # 订单关联成交索引
        op.create_index(
            "ix_trades_order_time",
            "trades",
            ["order_id", "trade_time"]
        )
        
        # 成交方向和时间索引
        op.create_index(
            "ix_trades_side_time",
            "trades",
            ["side", "trade_time"]
        )
    except Exception as e:
        print(f"Trades indexes creation error: {e}")
    
    # 5. 持仓表优化索引
    try:
        # 用户持仓查询索引
        op.create_index(
            "ix_positions_user_symbol",
            "positions",
            ["user_id", "symbol_code"]
        )
        
        # 非空持仓索引
        op.create_index(
            "ix_positions_non_empty",
            "positions",
            ["user_id", "quantity"],
            postgresql_where=sa.text("quantity > 0")
        )
        
        # 持仓方向索引
        op.create_index(
            "ix_positions_side_updated",
            "positions",
            ["side", "updated_at"]
        )
    except Exception as e:
        print(f"Positions indexes creation error: {e}")
    
    # 6. 账户表优化索引
    try:
        # 用户账户查询索引
        op.create_index(
            "ix_accounts_user_active",
            "accounts",
            ["user_id", "is_active"]
        )
        
        # 账户类型索引
        op.create_index(
            "ix_accounts_type_active",
            "accounts",
            ["account_type", "is_active"]
        )
        
        # 可交易账户索引
        op.create_index(
            "ix_accounts_tradable",
            "accounts",
            ["is_tradable", "updated_at"],
            postgresql_where=sa.text("is_tradable = true")
        )
    except Exception as e:
        print(f"Accounts indexes creation error: {e}")
    
    # 7. 深度数据表优化索引
    try:
        # 股票深度数据时间索引
        op.create_index(
            "ix_depth_symbol_timestamp_desc",
            "depth_data",
            ["symbol_code", sa.text("timestamp DESC")]
        )
    except Exception as e:
        print(f"Depth data indexes creation error: {e}")
    
    # 8. 逐笔成交表优化索引
    try:
        # 股票逐笔成交时间索引
        op.create_index(
            "ix_ticks_symbol_trade_time",
            "trade_ticks",
            ["symbol_code", "trade_time"]
        )
        
        # 成交方向时间索引
        op.create_index(
            "ix_ticks_direction_time",
            "trade_ticks",
            ["direction", "trade_time"]
        )
    except Exception as e:
        print(f"Trade ticks indexes creation error: {e}")
    
    # 9. 交易日志表优化索引
    try:
        # 用户交易日志时间索引
        op.create_index(
            "ix_transaction_logs_user_time",
            "transaction_logs",
            ["user_id", "created_at"]
        )
        
        # 交易类型时间索引
        op.create_index(
            "ix_transaction_logs_type_time",
            "transaction_logs",
            ["transaction_type", "created_at"]
        )
    except Exception as e:
        print(f"Transaction logs indexes creation error: {e}")


def downgrade():
    """移除优化索引"""
    
    # 移除市场数据索引
    try:
        op.drop_index("ix_market_data_symbol_timestamp_desc", table_name="market_data")
        op.drop_index("ix_market_data_symbol_time_price", table_name="market_data")
        op.drop_index("ix_market_data_trading_date_range", table_name="market_data")
    except Exception as e:
        print(f"Error dropping market data indexes: {e}")
    
    # 移除K线数据索引
    try:
        op.drop_index("ix_kline_symbol_type_date_desc", table_name="kline_data")
        op.drop_index("ix_kline_period_range", table_name="kline_data")
        op.drop_index("ix_kline_type_date", table_name="kline_data")
    except Exception as e:
        print(f"Error dropping kline indexes: {e}")
    
    # 移除订单索引
    try:
        op.drop_index("ix_orders_user_status_created_desc", table_name="orders")
        op.drop_index("ix_orders_symbol_status_time", table_name="orders")
        op.drop_index("ix_orders_type_side_status", table_name="orders")
        op.drop_index("ix_orders_active", table_name="orders")
    except Exception as e:
        print(f"Error dropping orders indexes: {e}")
    
    # 移除成交记录索引
    try:
        op.drop_index("ix_trades_user_time_desc", table_name="trades")
        op.drop_index("ix_trades_symbol_time", table_name="trades")
        op.drop_index("ix_trades_order_time", table_name="trades")
        op.drop_index("ix_trades_side_time", table_name="trades")
    except Exception as e:
        print(f"Error dropping trades indexes: {e}")
    
    # 移除持仓索引
    try:
        op.drop_index("ix_positions_user_symbol", table_name="positions")
        op.drop_index("ix_positions_non_empty", table_name="positions")
        op.drop_index("ix_positions_side_updated", table_name="positions")
    except Exception as e:
        print(f"Error dropping positions indexes: {e}")
    
    # 移除账户索引
    try:
        op.drop_index("ix_accounts_user_active", table_name="accounts")
        op.drop_index("ix_accounts_type_active", table_name="accounts")
        op.drop_index("ix_accounts_tradable", table_name="accounts")
    except Exception as e:
        print(f"Error dropping accounts indexes: {e}")
    
    # 移除深度数据索引
    try:
        op.drop_index("ix_depth_symbol_timestamp_desc", table_name="depth_data")
    except Exception as e:
        print(f"Error dropping depth data indexes: {e}")
    
    # 移除逐笔成交索引
    try:
        op.drop_index("ix_ticks_symbol_trade_time", table_name="trade_ticks")
        op.drop_index("ix_ticks_direction_time", table_name="trade_ticks")
    except Exception as e:
        print(f"Error dropping trade ticks indexes: {e}")
    
    # 移除交易日志索引
    try:
        op.drop_index("ix_transaction_logs_user_time", table_name="transaction_logs")
        op.drop_index("ix_transaction_logs_type_time", table_name="transaction_logs")
    except Exception as e:
        print(f"Error dropping transaction logs indexes: {e}")