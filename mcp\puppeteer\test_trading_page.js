const puppeteer = require('puppeteer-core');

class TradingPageTester {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await puppeteer.launch({
      executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ 控制台 [${type}]: ${text}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.log(`🚨 页面错误: ${error.message}`);
    });
  }

  async testTradingPage() {
    try {
      console.log('🚀 启动浏览器...');
      await this.init();

      console.log('📊 测试交易页面...');
      await this.page.goto('http://localhost:5173/trading', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 检查页面基本信息
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          contentLength: document.body.textContent.length,
          hasVueApp: !!document.querySelector('#app'),
          totalElements: document.querySelectorAll('*').length
        };
      });

      console.log('📄 页面基本信息:', pageInfo);

      // 检查交易终端组件
      const tradingComponents = await this.page.evaluate(() => {
        return {
          hasTradingTerminal: !!document.querySelector('.trading-terminal'),
          hasChartSection: !!document.querySelector('.chart-section'),
          hasTradingSection: !!document.querySelector('.trading-section'),
          hasKLineChart: !!document.querySelector('.kline-chart'),
          hasOrderBook: !!document.querySelector('.order-book'),
          hasOrderForm: !!document.querySelector('.order-form'),
          hasSymbolSelector: !!document.querySelector('.symbol-selector'),
          hasTerminalHeader: !!document.querySelector('.terminal-header'),
          totalTradingElements: document.querySelectorAll('[class*="trading"], [class*="chart"], [class*="order"]').length
        };
      });

      console.log('💼 交易组件检查:', tradingComponents);

      // 检查专业交易功能
      const professionalFeatures = await this.page.evaluate(() => {
        return {
          hasLayoutSwitcher: !!document.querySelector('.terminal-actions'),
          hasStockSearch: !!document.querySelector('.el-autocomplete'),
          hasMarketTabs: !!document.querySelector('.market-info-tabs'),
          hasOrderBookData: document.querySelectorAll('.order-row').length,
          hasTradeDetails: !!document.querySelector('.trade-details'),
          hasMoneyFlow: !!document.querySelector('.money-flow'),
          hasPeriodSelector: document.querySelectorAll('.period-selector button').length,
          hasIndicatorDropdown: !!document.querySelector('.el-dropdown')
        };
      });

      console.log('🔧 专业功能检查:', professionalFeatures);

      // 检查交互元素
      const interactiveElements = await this.page.evaluate(() => {
        return {
          buttons: document.querySelectorAll('button').length,
          inputs: document.querySelectorAll('input').length,
          selects: document.querySelectorAll('select, .el-select').length,
          tabs: document.querySelectorAll('.el-tab-pane').length,
          cards: document.querySelectorAll('.el-card').length
        };
      });

      console.log('🖱️ 交互元素检查:', interactiveElements);

      // 测试股票搜索功能
      console.log('🔍 测试股票搜索...');
      const searchInput = await this.page.$('.el-autocomplete input');
      if (searchInput) {
        await searchInput.type('000001');
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ 股票搜索输入测试完成');
      } else {
        console.log('❌ 未找到股票搜索输入框');
      }

      // 测试布局切换
      console.log('🔄 测试布局切换...');
      const layoutButtons = await this.page.$$('.terminal-actions button');
      if (layoutButtons.length > 0) {
        await layoutButtons[1].click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ 布局切换测试完成');
      }

      // 获取页面HTML长度
      const htmlLength = await this.page.evaluate(() => document.documentElement.outerHTML.length);
      console.log(`📝 页面HTML长度: ${htmlLength}`);

      // 生成测试报告
      const report = this.generateReport({
        pageInfo,
        tradingComponents,
        professionalFeatures,
        interactiveElements,
        htmlLength
      });

      console.log('\n📋 交易页面测试报告:');
      console.log('='.repeat(50));
      console.log(`💼 交易组件: ${tradingComponents.totalTradingElements} 个 ${tradingComponents.hasTradingTerminal ? '✅' : '❌'}`);
      console.log(`📊 K线图表: ${professionalFeatures.hasPeriodSelector} 个周期 ${tradingComponents.hasKLineChart ? '✅' : '❌'}`);
      console.log(`📖 订单簿: ${professionalFeatures.hasOrderBookData} 行数据 ${tradingComponents.hasOrderBook ? '✅' : '❌'}`);
      console.log(`🔧 专业功能: ${Object.values(professionalFeatures).filter(Boolean).length}/8 ✅`);
      console.log(`🖱️ 交互元素: ${interactiveElements.buttons + interactiveElements.inputs + interactiveElements.selects} 个`);
      console.log(`📈 总体评分: ${report.overallScore}/100`);
      console.log('='.repeat(50));

      if (report.overallScore >= 80) {
        console.log('\n🎉 测试完成！');
        console.log('✅ 交易页面功能丰富，专业化程度高');
      } else if (report.overallScore >= 60) {
        console.log('\n⚠️ 测试完成！');
        console.log('🔧 交易页面基本功能正常，但需要进一步优化');
      } else {
        console.log('\n❌ 测试完成！');
        console.log('🚨 交易页面需要大幅改进');
      }

      return report;

    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
      throw error;
    }
  }

  generateReport(data) {
    let score = 0;
    
    // 基础组件评分 (30分)
    if (data.tradingComponents.hasTradingTerminal) score += 10;
    if (data.tradingComponents.hasKLineChart) score += 10;
    if (data.tradingComponents.hasOrderBook) score += 10;
    
    // 专业功能评分 (40分)
    const professionalCount = Object.values(data.professionalFeatures).filter(Boolean).length;
    score += Math.min(40, professionalCount * 5);
    
    // 交互元素评分 (20分)
    const totalInteractive = data.interactiveElements.buttons + data.interactiveElements.inputs + data.interactiveElements.selects;
    if (totalInteractive >= 20) score += 20;
    else if (totalInteractive >= 10) score += 15;
    else if (totalInteractive >= 5) score += 10;
    
    // 页面完整性评分 (10分)
    if (data.htmlLength > 500000) score += 10;
    else if (data.htmlLength > 100000) score += 5;
    
    return {
      overallScore: Math.min(100, score),
      details: data
    };
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

async function runTest() {
  const tester = new TradingPageTester();
  
  try {
    const report = await tester.testTradingPage();
    return report;
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  } finally {
    await tester.close();
  }
}

// 运行测试
runTest();
