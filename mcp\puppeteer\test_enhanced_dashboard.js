const puppeteer = require('puppeteer');

class EnhancedDashboardTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5173';
  }

  async init() {
    console.log('🚀 启动浏览器...');
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async testEnhancedDashboard() {
    console.log('📊 测试增强版仪表板...');
    
    try {
      // 访问仪表板
      // 先访问主页，然后导航到仪表板
      await this.page.goto(this.baseUrl, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      // 等待一下然后导航到仪表板
      await new Promise(resolve => setTimeout(resolve, 2000));

      await this.page.goto(`${this.baseUrl}/dashboard`, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      // 监听控制台错误
      const consoleErrors = [];
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });

      // 等待页面加载和Vue应用初始化
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 等待Vue应用完全加载
      await this.page.waitForFunction(() => {
        const app = document.querySelector('#app');
        return app && app.children.length > 0;
      }, { timeout: 10000 });

      // 再等待一段时间确保组件渲染完成
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 检查页面基本结构
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          contentLength: document.body.textContent.length,
          hasVueApp: !!document.querySelector('#app'),
          totalElements: document.querySelectorAll('*').length
        };
      });

      console.log('📄 页面基本信息:', pageInfo);

      // 检查 MetricCard 组件
      const metricCards = await this.page.evaluate(() => {
        const enhancedCards = document.querySelectorAll('.enhanced-metric-card');
        const allCards = document.querySelectorAll('.el-card');
        const metricHeaders = document.querySelectorAll('.metric-header');
        const metricIcons = document.querySelectorAll('.metric-icon');
        const trendLines = document.querySelectorAll('.mini-trend-line');

        const cardTexts = Array.from(enhancedCards).slice(0, 5).map(card => {
          const title = card.querySelector('.metric-title')?.textContent?.trim() || '';
          const value = card.querySelector('.metric-value')?.textContent?.trim() || '';
          return `${title}: ${value}`.substring(0, 50);
        });

        return {
          count: allCards.length,
          enhancedCards: enhancedCards.length,
          hasAdvancedCards: enhancedCards.length >= 4,
          hasMetricHeaders: metricHeaders.length >= 4,
          hasMetricIcons: metricIcons.length >= 4,
          hasTrendLines: trendLines.length >= 4,
          cardTexts
        };
      });

      console.log('💳 指标卡片检查:', metricCards);

      // 检查高级图表组件
      const chartComponents = await this.page.evaluate(() => {
        return {
          hasAdvancedChartPanel: !!document.querySelector('.enhanced-chart-panel'),
          hasChartContainer: !!document.querySelector('.chart-container'),
          hasChartStats: !!document.querySelector('.chart-stats'),
          hasStatItems: document.querySelectorAll('.stat-item').length,
          hasAssetTrendChart: !!document.querySelector('[class*="asset-trend"]'),
          hasKLineChart: !!document.querySelector('[class*="kline"], [class*="k-line"]'),
          hasECharts: !!document.querySelector('canvas, .echarts'),
          chartElements: document.querySelectorAll('canvas, .chart, .echarts, .chart-container, .enhanced-chart-container').length
        };
      });

      console.log('📈 图表组件检查:', chartComponents);

      // 检查交互功能
      const interactiveElements = await this.page.evaluate(() => {
        return {
          hasTimeframeButtons: document.querySelectorAll('.el-radio-button').length,
          hasActionButtons: document.querySelectorAll('.el-button').length,
          hasClickableCards: document.querySelectorAll('.metric-card[style*="cursor"]').length,
          hasTooltips: document.querySelectorAll('[title], .el-tooltip').length
        };
      });

      console.log('🖱️ 交互元素检查:', interactiveElements);

      // 检查样式增强
      const styleEnhancements = await this.page.evaluate(() => {
        const enhancedPanel = document.querySelector('.enhanced-chart-panel');
        const enhancedCard = document.querySelector('.enhanced-metric-card');
        const dashboardView = document.querySelector('.dashboard-view');

        return {
          hasGradientBackground: dashboardView ?
            getComputedStyle(dashboardView).background.includes('gradient') : false,
          hasBoxShadow: enhancedCard ?
            getComputedStyle(enhancedCard).boxShadow !== 'none' : false,
          hasTransitions: enhancedCard ?
            getComputedStyle(enhancedCard).transition.includes('transform') : false,
          hasEnhancedCards: !!enhancedCard,
          hasEnhancedPanel: !!enhancedPanel,
          responsiveDesign: window.innerWidth > 1200
        };
      });

      console.log('🎨 样式增强检查:', styleEnhancements);

      // 测试卡片点击功能
      console.log('🖱️ 测试卡片交互...');
      const cardClickable = await this.page.evaluate(() => {
        const enhancedCards = document.querySelectorAll('.enhanced-metric-card');
        return enhancedCards.length > 0;
      });

      if (cardClickable) {
        // 模拟点击第一个增强版卡片
        await this.page.click('.enhanced-metric-card');
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ 卡片点击测试完成');
      }

      // 测试时间周期切换
      console.log('📅 测试时间周期切换...');
      const timeframeButtons = await this.page.$$('.el-radio-button');
      if (timeframeButtons.length > 0) {
        await timeframeButtons[1].click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ 时间周期切换测试完成');
      }

      // 获取页面HTML内容用于调试
      const pageContent = await this.page.content();
      console.log('📝 页面HTML长度:', pageContent.length);

      if (pageContent.length < 1000) {
        console.log('⚠️ 页面内容异常简短，可能存在编译错误');
        console.log('📄 页面内容预览:', pageContent.substring(0, 500));
      }

      // 生成测试报告
      const report = {
        timestamp: new Date().toISOString(),
        pageInfo,
        metricCards,
        chartComponents,
        interactiveElements,
        styleEnhancements,
        consoleErrors: consoleErrors,
        overallScore: this.calculateScore({
          metricCards,
          chartComponents,
          interactiveElements,
          styleEnhancements
        })
      };

      console.log('\n📋 增强版仪表板测试报告:');
      console.log('='.repeat(50));
      console.log(`📊 指标卡片: ${metricCards.count} 个 ${metricCards.hasAdvancedCards ? '✅' : '❌'}`);
      console.log(`📈 图表组件: ${chartComponents.chartElements} 个 ${chartComponents.hasAdvancedChartPanel ? '✅' : '❌'}`);
      console.log(`🖱️ 交互元素: ${interactiveElements.hasActionButtons} 个按钮 ${interactiveElements.hasTimeframeButtons > 0 ? '✅' : '❌'}`);
      console.log(`🎨 样式增强: ${styleEnhancements.hasGradientBackground ? '✅' : '❌'} 渐变背景`);
      console.log(`🚨 控制台错误: ${consoleErrors.length} 个`);
      if (consoleErrors.length > 0) {
        console.log('❌ 控制台错误详情:');
        consoleErrors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }
      console.log(`📈 总体评分: ${report.overallScore}/100`);
      console.log('='.repeat(50));

      return report;

    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
      throw error;
    }
  }

  calculateScore(data) {
    let score = 0;

    // 指标卡片评分 (30分)
    if (data.metricCards.count >= 4) score += 10;
    if (data.metricCards.enhancedCards >= 4) score += 15;
    if (data.metricCards.hasMetricHeaders) score += 5;

    // 图表组件评分 (25分)
    if (data.chartComponents.hasAdvancedChartPanel) score += 15;
    if (data.chartComponents.chartElements > 0) score += 10;
    
    // 交互功能评分 (25分)
    if (data.interactiveElements.hasTimeframeButtons > 0) score += 10;
    if (data.interactiveElements.hasActionButtons > 5) score += 15;
    
    // 样式增强评分 (25分)
    if (data.styleEnhancements.hasGradientBackground) score += 10;
    if (data.styleEnhancements.hasBoxShadow) score += 10;
    if (data.styleEnhancements.hasTransitions) score += 5;
    
    return Math.min(score, 100);
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// 运行测试
async function runTest() {
  const tester = new EnhancedDashboardTester();
  
  try {
    await tester.init();
    const report = await tester.testEnhancedDashboard();
    
    console.log('\n🎉 测试完成！');
    if (report.overallScore >= 80) {
      console.log('✅ 增强版仪表板功能优秀！');
    } else if (report.overallScore >= 60) {
      console.log('⚠️ 增强版仪表板功能良好，还有改进空间');
    } else {
      console.log('❌ 增强版仪表板需要进一步优化');
    }
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  } finally {
    await tester.cleanup();
  }
}

runTest();
