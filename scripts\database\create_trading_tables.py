"""
创建交易相关数据表
"""
import asyncio
from sqlalchemy import create_engine
from app.core.config import get_settings
from app.core.database import Base
from app.models.trading import Order, Trade, Position, Account
from app.db.models.user import User

settings = get_settings()

def create_tables():
    """创建数据表"""
    # 创建同步引擎用于建表
    engine = create_engine(
        settings.DATABASE_URL.replace("postgresql+asyncpg", "postgresql"),
        echo=True
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("数据表创建完成！")

if __name__ == "__main__":
    create_tables()
