/**
 * 简单平台测试脚本
 */

const puppeteer = require('puppeteer');

async function simpleTest() {
    console.log('🚀 开始简单平台测试...');
    
    try {
        const browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // 监听控制台输出
        page.on('console', msg => {
            console.log(`🖥️ [${msg.type()}]: ${msg.text()}`);
        });
        
        // 监听网络请求
        page.on('request', request => {
            if (request.url().includes('localhost:8000')) {
                console.log(`📤 请求: ${request.method()} ${request.url()}`);
            }
        });
        
        page.on('response', response => {
            if (response.url().includes('localhost:8000')) {
                console.log(`📥 响应: ${response.status()} ${response.url()}`);
            }
        });
        
        // 测试首页
        console.log('\n📊 测试首页...');
        await page.goto('http://localhost:5173', { waitUntil: 'networkidle2' });
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const content = await page.evaluate(() => {
            return {
                title: document.title,
                contentLength: document.body.innerText.length,
                url: window.location.href
            };
        });
        
        console.log('📊 首页状态:', content);
        
        // 测试历史数据页面
        console.log('\n📋 测试历史数据页面...');
        await page.goto('http://localhost:5173/market/historical', { waitUntil: 'networkidle2' });
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        const historicalContent = await page.evaluate(() => {
            return {
                contentLength: document.body.innerText.length,
                hasTable: document.querySelectorAll('table, .el-table').length > 0,
                tableRows: document.querySelectorAll('table tr, .el-table__row').length
            };
        });
        
        console.log('📋 历史数据状态:', historicalContent);
        
        console.log('\n✅ 测试完成！');
        
        // 保持浏览器打开
        console.log('浏览器保持打开状态...');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 运行测试
simpleTest();
