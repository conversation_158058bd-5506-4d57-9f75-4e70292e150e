# MCP工具部署完成指南

## 📋 部署概述

已成功下载、安装和部署了以下3个MCP工具：

1. **BrowserTools MCP** - 浏览器自动化和调试工具
2. **FileSystem MCP** - 文件系统操作工具  
3. **mcp-use** - MCP客户端库，用于连接和使用多个MCP服务器

## 🗂️ 目录结构

```
C:\Users\<USER>\Desktop\quant012\mcp\
├── browser-tools-mcp/           # BrowserTools MCP
│   ├── browser-tools-mcp/       # MCP服务器
│   ├── browser-tools-server/    # 浏览器工具服务器
│   └── chrome-extension/        # Chrome扩展
├── servers/                     # 官方MCP服务器集合
│   └── src/
│       └── filesystem/          # FileSystem MCP
└── mcp-use/                     # MCP客户端库
```

## 🚀 各工具状态

### ✅ BrowserTools MCP
- **状态**: 已安装完成
- **位置**: `C:\Users\<USER>\Desktop\quant012\mcp\browser-tools-mcp\`
- **依赖**: 已安装 (98 packages)
- **功能**: 
  - 浏览器控制台日志访问
  - 网络请求分析
  - 截图功能
  - 元素选择和检查
  - 实时浏览器状态监控
  - 可访问性、性能、SEO审计

### ✅ FileSystem MCP  
- **状态**: 已安装完成
- **位置**: `C:\Users\<USER>\Desktop\quant012\mcp\servers\src\filesystem\`
- **依赖**: 已安装 (461 packages)
- **功能**:
  - 文件读写操作
  - 目录管理
  - 文件搜索
  - 路径验证
  - 安全文件访问控制

### ✅ mcp-use
- **状态**: 已安装完成
- **位置**: `C:\Users\<USER>\Desktop\quant012\mcp\mcp-use\`
- **依赖**: 已安装 (Python包)
- **功能**:
  - 连接多个MCP服务器
  - 统一的MCP客户端接口
  - 支持异步操作
  - 集成LangChain
  - 支持Anthropic和OpenAI

## 🛠️ 启动方法

### 1. BrowserTools MCP

#### 启动Browser Tools Server:
```bash
cd C:\Users\<USER>\Desktop\quant012\mcp\browser-tools-mcp\browser-tools-server
npm start
```

#### 启动MCP服务器:
```bash
cd C:\Users\<USER>\Desktop\quant012\mcp\browser-tools-mcp\browser-tools-mcp
npm start
```

#### 或使用全局安装:
```bash
npx @agentdeskai/browser-tools-server
npx @agentdeskai/browser-tools-mcp
```

### 2. FileSystem MCP

```bash
cd C:\Users\<USER>\Desktop\quant012\mcp\servers\src\filesystem
npm start
```

### 3. mcp-use (Python)

```python
from mcp_use import MCPClient

# 创建客户端
client = MCPClient()

# 连接到MCP服务器
await client.connect("stdio", command=["node", "path/to/mcp-server.js"])

# 使用工具
result = await client.call_tool("tool_name", {"param": "value"})
```

## 📝 使用示例

### BrowserTools MCP示例

```python
# 使用mcp-use连接BrowserTools
from mcp_use import MCPClient

async def browser_automation():
    client = MCPClient()
    
    # 连接到BrowserTools MCP
    await client.connect("stdio", command=["node", "browser-tools-mcp/mcp-server.js"])
    
    # 获取可用工具
    tools = await client.list_tools()
    print("可用工具:", tools)
    
    # 截图
    screenshot = await client.call_tool("take_screenshot", {
        "url": "http://localhost:5173"
    })
    
    # 获取控制台日志
    logs = await client.call_tool("get_console_logs", {})
    
    return screenshot, logs
```

### FileSystem MCP示例

```python
# 文件系统操作
async def filesystem_operations():
    client = MCPClient()
    
    # 连接到FileSystem MCP
    await client.connect("stdio", command=["node", "servers/src/filesystem/index.js"])
    
    # 读取文件
    content = await client.call_tool("read_file", {
        "path": "C:/Users/<USER>/Desktop/quant012/README.md"
    })
    
    # 列出目录
    files = await client.call_tool("list_directory", {
        "path": "C:/Users/<USER>/Desktop/quant012/"
    })
    
    # 写入文件
    await client.call_tool("write_file", {
        "path": "C:/Users/<USER>/Desktop/quant012/test.txt",
        "content": "Hello from MCP!"
    })
    
    return content, files
```

### 多服务器连接示例

```python
# 同时使用多个MCP服务器
async def multi_server_example():
    from mcp_use import MCPClient
    
    # 创建多个客户端
    browser_client = MCPClient()
    fs_client = MCPClient()
    
    # 连接到不同的服务器
    await browser_client.connect("stdio", command=["node", "browser-tools-mcp/mcp-server.js"])
    await fs_client.connect("stdio", command=["node", "servers/src/filesystem/index.js"])
    
    # 使用浏览器工具截图
    screenshot = await browser_client.call_tool("take_screenshot", {
        "url": "http://localhost:5173"
    })
    
    # 保存截图到文件
    await fs_client.call_tool("write_file", {
        "path": "C:/Users/<USER>/Desktop/quant012/screenshot.png",
        "content": screenshot["data"]
    })
    
    print("截图已保存到文件系统")
```

## 🔧 配置说明

### Chrome扩展安装 (BrowserTools需要)

1. 打开Chrome浏览器
2. 进入 `chrome://extensions/`
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `C:\Users\<USER>\Desktop\quant012\mcp\browser-tools-mcp\chrome-extension\` 目录

### 环境变量配置

可以创建 `.env` 文件配置各种参数：

```env
# BrowserTools配置
BROWSER_TOOLS_PORT=3000
BROWSER_TOOLS_HOST=localhost

# FileSystem配置  
FILESYSTEM_ROOT=C:\Users\<USER>\Desktop\quant012
FILESYSTEM_READONLY=false

# mcp-use配置
MCP_LOG_LEVEL=info
MCP_TIMEOUT=30000
```

## 🧪 测试验证

### 快速测试脚本

创建 `test_mcp_tools.py`:

```python
import asyncio
from mcp_use import MCPClient

async def test_all_tools():
    print("🧪 开始测试MCP工具...")
    
    # 测试FileSystem MCP
    print("📁 测试FileSystem MCP...")
    fs_client = MCPClient()
    try:
        await fs_client.connect("stdio", command=["node", "servers/src/filesystem/index.js"])
        tools = await fs_client.list_tools()
        print(f"✅ FileSystem MCP工具: {len(tools)}个")
    except Exception as e:
        print(f"❌ FileSystem MCP测试失败: {e}")
    
    # 测试BrowserTools MCP
    print("🌐 测试BrowserTools MCP...")
    browser_client = MCPClient()
    try:
        await browser_client.connect("stdio", command=["node", "browser-tools-mcp/mcp-server.js"])
        tools = await browser_client.list_tools()
        print(f"✅ BrowserTools MCP工具: {len(tools)}个")
    except Exception as e:
        print(f"❌ BrowserTools MCP测试失败: {e}")
    
    print("🎉 MCP工具测试完成!")

if __name__ == "__main__":
    asyncio.run(test_all_tools())
```

## 📚 下一步

1. **安装Chrome扩展** (BrowserTools需要)
2. **配置环境变量** (可选)
3. **运行测试脚本** 验证安装
4. **集成到量化平台** 中使用这些工具

## 🔗 相关资源

- [BrowserTools MCP GitHub](https://github.com/AgentDeskAI/browser-tools-mcp)
- [MCP官方服务器](https://github.com/modelcontextprotocol/servers)
- [mcp-use文档](https://github.com/mcp-use/mcp-use)
- [Model Context Protocol规范](https://modelcontextprotocol.io/)

---

**部署完成时间**: 2025年8月4日  
**部署状态**: ✅ 全部成功  
**需要人工操作**: Chrome扩展安装
