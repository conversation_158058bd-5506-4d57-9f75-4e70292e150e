#!/usr/bin/env python3
"""
简单的 MCP 服务器测试脚本
测试 MCP 服务器的基本功能，不需要下载浏览器
"""

import json
import sys
import subprocess
import time
import os

def test_mcp_server():
    """测试 MCP 服务器的基本功能"""
    print("🧪 开始测试 MCP 服务器...")
    
    # 确保在正确的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 启动 MCP 服务器
        print("📡 启动 MCP 服务器...")
        process = subprocess.Popen(
            [sys.executable, "puppeteer.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 等待服务器启动
        time.sleep(2)
        
        # 测试 1: 发送初始化请求
        print("🔧 测试初始化...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {
                        "listChanged": True
                    },
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # 发送请求
        request_str = json.dumps(init_request) + "\n"
        process.stdin.write(request_str)
        process.stdin.flush()
        
        # 读取响应
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ 初始化响应: {response}")
            
            if "result" in response:
                print("✅ 服务器初始化成功!")
            else:
                print("❌ 服务器初始化失败")
                return False
        else:
            print("❌ 没有收到初始化响应")
            return False
        
        # 发送 initialized 通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        request_str = json.dumps(initialized_notification) + "\n"
        process.stdin.write(request_str)
        process.stdin.flush()

        # 测试 2: 获取工具列表
        print("🛠️  测试获取工具列表...")
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        request_str = json.dumps(tools_request) + "\n"
        process.stdin.write(request_str)
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ 工具列表响应: {response}")
            
            if "result" in response and "tools" in response["result"]:
                tools = response["result"]["tools"]
                print(f"✅ 找到 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"   - {tool['name']}: {tool.get('description', '无描述')}")
                return True
            else:
                print("❌ 获取工具列表失败")
                return False
        else:
            print("❌ 没有收到工具列表响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False
    finally:
        # 清理进程
        if process:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
        print("🧹 清理完成")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 MCP Puppeteer 服务器简单测试")
    print("=" * 50)
    
    # 检查依赖
    print("📋 检查依赖...")
    try:
        import mcp
        print("✅ MCP 库已安装")
    except ImportError:
        print("❌ MCP 库未安装，请运行: pip install mcp")
        return False
    
    # 检查服务器文件
    if not os.path.exists("puppeteer.py"):
        print("❌ 找不到 puppeteer.py 文件")
        return False
    print("✅ 服务器文件存在")
    
    # 运行测试
    success = test_mcp_server()
    
    print("=" * 50)
    if success:
        print("🎉 测试成功! MCP 服务器工作正常")
        print("💡 提示: 要使用浏览器功能，请运行 'playwright install'")
    else:
        print("💥 测试失败! 请检查错误信息")
    print("=" * 50)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
