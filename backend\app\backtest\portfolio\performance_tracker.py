"""
绩效跟踪器
提供全面的绩效指标计算和分析
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

@dataclass
class PerformanceMetrics:
    """绩效指标数据类"""
    # 收益指标
    total_return: float
    annual_return: float
    monthly_return: float
    daily_return: float
    
    # 风险指标
    volatility: float
    downside_deviation: float
    var_95: float
    var_99: float
    cvar_95: float
    
    # 风险调整收益指标
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    information_ratio: float
    
    # 回撤指标
    max_drawdown: float
    avg_drawdown: float
    max_drawdown_duration: int
    
    # 胜率指标
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    
    # 其他指标
    skewness: float
    kurtosis: float
    tail_ratio: float
    
class PerformanceTracker:
    """绩效跟踪器"""
    
    def __init__(self, config):
        self.config = config
        self.risk_free_rate = config.get('cash_interest_rate', 0.02)
        self.benchmark_symbol = config.get('benchmark', '000300.SH')
        
    def calculate_performance_metrics(
        self,
        portfolio_value: pd.Series,
        benchmark_data: Optional[pd.Series] = None,
        trades: Optional[pd.DataFrame] = None
    ) -> PerformanceMetrics:
        """计算全面的绩效指标"""
        
        # 计算收益率
        returns = portfolio_value.pct_change().dropna()
        
        if len(returns) == 0:
            return self._get_empty_metrics()
            
        # 收益指标
        total_return = (portfolio_value.iloc[-1] / portfolio_value.iloc[0]) - 1
        annual_return = self._calculate_annual_return(returns)
        monthly_return = returns.resample('M').apply(lambda x: (1 + x).prod() - 1).mean()
        daily_return = returns.mean()
        
        # 风险指标
        volatility = returns.std() * np.sqrt(252)
        downside_deviation = self._calculate_downside_deviation(returns)
        var_95 = returns.quantile(0.05)
        var_99 = returns.quantile(0.01)
        cvar_95 = returns[returns <= var_95].mean()
        
        # 风险调整收益指标
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        sortino_ratio = self._calculate_sortino_ratio(returns)
        calmar_ratio = self._calculate_calmar_ratio(returns)
        information_ratio = self._calculate_information_ratio(returns, benchmark_data)
        
        # 回撤指标
        drawdown_metrics = self._calculate_drawdown_metrics(portfolio_value)
        
        # 胜率指标
        win_metrics = self._calculate_win_metrics(returns, trades)
        
        # 统计指标
        skewness = returns.skew()
        kurtosis = returns.kurtosis()
        tail_ratio = self._calculate_tail_ratio(returns)
        
        return PerformanceMetrics(
            total_return=total_return,
            annual_return=annual_return,
            monthly_return=monthly_return,
            daily_return=daily_return,
            volatility=volatility,
            downside_deviation=downside_deviation,
            var_95=var_95,
            var_99=var_99,
            cvar_95=cvar_95,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            information_ratio=information_ratio,
            max_drawdown=drawdown_metrics['max_drawdown'],
            avg_drawdown=drawdown_metrics['avg_drawdown'],
            max_drawdown_duration=drawdown_metrics['max_duration'],
            win_rate=win_metrics['win_rate'],
            profit_factor=win_metrics['profit_factor'],
            avg_win=win_metrics['avg_win'],
            avg_loss=win_metrics['avg_loss'],
            skewness=skewness,
            kurtosis=kurtosis,
            tail_ratio=tail_ratio
        )
        
    def _calculate_annual_return(self, returns: pd.Series) -> float:
        """计算年化收益率"""
        if len(returns) == 0:
            return 0
            
        total_return = (1 + returns).prod() - 1
        years = len(returns) / 252  # 假设一年252个交易日
        
        if years <= 0:
            return 0
            
        return (1 + total_return) ** (1 / years) - 1
        
    def _calculate_downside_deviation(self, returns: pd.Series) -> float:
        """计算下行偏差"""
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0:
            return 0
        return negative_returns.std() * np.sqrt(252)
        
    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """计算夏普比率"""
        excess_returns = returns - self.risk_free_rate / 252
        if excess_returns.std() == 0:
            return 0
        return excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        
    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """计算索提诺比率"""
        excess_returns = returns - self.risk_free_rate / 252
        downside_deviation = self._calculate_downside_deviation(returns) / np.sqrt(252)
        
        if downside_deviation == 0:
            return 0
        return excess_returns.mean() / downside_deviation * np.sqrt(252)
        
    def _calculate_calmar_ratio(self, returns: pd.Series) -> float:
        """计算卡玛比率"""
        annual_return = self._calculate_annual_return(returns)
        
        # 计算最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = abs(drawdown.min())
        
        if max_drawdown == 0:
            return 0
        return annual_return / max_drawdown
        
    def _calculate_information_ratio(
        self, 
        returns: pd.Series, 
        benchmark_data: Optional[pd.Series]
    ) -> float:
        """计算信息比率"""
        if benchmark_data is None:
            return 0
            
        # 对齐数据
        aligned_data = pd.DataFrame({
            'portfolio': returns,
            'benchmark': benchmark_data.pct_change()
        }).dropna()
        
        if len(aligned_data) == 0:
            return 0
            
        # 计算超额收益
        excess_returns = aligned_data['portfolio'] - aligned_data['benchmark']
        
        if excess_returns.std() == 0:
            return 0
            
        return excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        
    def _calculate_drawdown_metrics(self, portfolio_value: pd.Series) -> Dict[str, float]:
        """计算回撤指标"""
        # 计算回撤序列
        running_max = portfolio_value.expanding().max()
        drawdown = (portfolio_value - running_max) / running_max
        
        # 最大回撤
        max_drawdown = abs(drawdown.min())
        
        # 平均回撤
        negative_drawdowns = drawdown[drawdown < 0]
        avg_drawdown = abs(negative_drawdowns.mean()) if len(negative_drawdowns) > 0 else 0
        
        # 最大回撤持续期
        max_duration = self._calculate_max_drawdown_duration(drawdown)
        
        return {
            'max_drawdown': max_drawdown,
            'avg_drawdown': avg_drawdown,
            'max_duration': max_duration
        }
        
    def _calculate_max_drawdown_duration(self, drawdown: pd.Series) -> int:
        """计算最大回撤持续期（天数）"""
        max_duration = 0
        current_duration = 0
        
        for dd in drawdown:
            if dd < 0:
                current_duration += 1
                max_duration = max(max_duration, current_duration)
            else:
                current_duration = 0
                
        return max_duration
        
    def _calculate_win_metrics(
        self, 
        returns: pd.Series, 
        trades: Optional[pd.DataFrame]
    ) -> Dict[str, float]:
        """计算胜率相关指标"""
        if trades is not None and not trades.empty:
            return self._calculate_trade_win_metrics(trades)
        else:
            return self._calculate_return_win_metrics(returns)
            
    def _calculate_trade_win_metrics(self, trades: pd.DataFrame) -> Dict[str, float]:
        """基于交易记录计算胜率指标"""
        if trades.empty:
            return {'win_rate': 0, 'profit_factor': 0, 'avg_win': 0, 'avg_loss': 0}
            
        # 计算每笔交易的盈亏
        trade_pnl = []
        
        # 按股票分组计算盈亏
        for symbol in trades['symbol'].unique():
            symbol_trades = trades[trades['symbol'] == symbol].sort_values('date')
            position = 0
            cost_basis = 0
            
            for _, trade in symbol_trades.iterrows():
                if trade['side'] == 'buy':
                    # 买入
                    new_cost = (position * cost_basis + trade['quantity'] * trade['price']) / (position + trade['quantity'])
                    position += trade['quantity']
                    cost_basis = new_cost
                else:
                    # 卖出
                    if position > 0:
                        pnl = (trade['price'] - cost_basis) * trade['quantity'] - trade.get('commission', 0)
                        trade_pnl.append(pnl)
                        position -= trade['quantity']
                        
        if len(trade_pnl) == 0:
            return {'win_rate': 0, 'profit_factor': 0, 'avg_win': 0, 'avg_loss': 0}
            
        # 计算指标
        winning_trades = [pnl for pnl in trade_pnl if pnl > 0]
        losing_trades = [pnl for pnl in trade_pnl if pnl < 0]
        
        win_rate = len(winning_trades) / len(trade_pnl)
        avg_win = np.mean(winning_trades) if winning_trades else 0
        avg_loss = np.mean(losing_trades) if losing_trades else 0
        
        total_wins = sum(winning_trades)
        total_losses = abs(sum(losing_trades))
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss
        }
        
    def _calculate_return_win_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """基于收益率计算胜率指标"""
        if len(returns) == 0:
            return {'win_rate': 0, 'profit_factor': 0, 'avg_win': 0, 'avg_loss': 0}
            
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        win_rate = len(positive_returns) / len(returns)
        avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
        avg_loss = negative_returns.mean() if len(negative_returns) > 0 else 0
        
        total_gains = positive_returns.sum()
        total_losses = abs(negative_returns.sum())
        profit_factor = total_gains / total_losses if total_losses > 0 else 0
        
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss
        }
        
    def _calculate_tail_ratio(self, returns: pd.Series) -> float:
        """计算尾部比率"""
        if len(returns) < 100:  # 数据量太少
            return 1.0
            
        # 95分位数 / 5分位数的绝对值
        p95 = returns.quantile(0.95)
        p5 = returns.quantile(0.05)
        
        if p5 == 0:
            return 1.0
            
        return abs(p95 / p5)
        
    def _get_empty_metrics(self) -> PerformanceMetrics:
        """返回空的绩效指标"""
        return PerformanceMetrics(
            total_return=0, annual_return=0, monthly_return=0, daily_return=0,
            volatility=0, downside_deviation=0, var_95=0, var_99=0, cvar_95=0,
            sharpe_ratio=0, sortino_ratio=0, calmar_ratio=0, information_ratio=0,
            max_drawdown=0, avg_drawdown=0, max_drawdown_duration=0,
            win_rate=0, profit_factor=0, avg_win=0, avg_loss=0,
            skewness=0, kurtosis=0, tail_ratio=1.0
        )
        
    def generate_performance_report(
        self,
        portfolio_value: pd.Series,
        benchmark_data: Optional[pd.Series] = None,
        trades: Optional[pd.DataFrame] = None
    ) -> Dict[str, any]:
        """生成绩效报告"""
        metrics = self.calculate_performance_metrics(portfolio_value, benchmark_data, trades)
        
        # 计算月度收益
        returns = portfolio_value.pct_change().dropna()
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        
        # 计算滚动指标
        rolling_sharpe = self._calculate_rolling_sharpe(returns)
        rolling_volatility = self._calculate_rolling_volatility(returns)
        
        # 基准比较
        benchmark_comparison = None
        if benchmark_data is not None:
            benchmark_comparison = self._compare_with_benchmark(
                portfolio_value, benchmark_data
            )
            
        report = {
            'summary_metrics': metrics.__dict__,
            'monthly_returns': monthly_returns.to_dict(),
            'rolling_metrics': {
                'rolling_sharpe': rolling_sharpe.to_dict(),
                'rolling_volatility': rolling_volatility.to_dict()
            },
            'benchmark_comparison': benchmark_comparison,
            'drawdown_analysis': self._analyze_drawdowns(portfolio_value),
            'return_distribution': self._analyze_return_distribution(returns)
        }
        
        return report
        
    def _calculate_rolling_sharpe(self, returns: pd.Series, window: int = 60) -> pd.Series:
        """计算滚动夏普比率"""
        rolling_sharpe = returns.rolling(window).apply(
            lambda x: (x.mean() - self.risk_free_rate / 252) / x.std() * np.sqrt(252)
            if x.std() > 0 else 0
        )
        return rolling_sharpe.dropna()
        
    def _calculate_rolling_volatility(self, returns: pd.Series, window: int = 60) -> pd.Series:
        """计算滚动波动率"""
        rolling_vol = returns.rolling(window).std() * np.sqrt(252)
        return rolling_vol.dropna()
        
    def _compare_with_benchmark(
        self, 
        portfolio_value: pd.Series, 
        benchmark_data: pd.Series
    ) -> Dict[str, float]:
        """与基准比较"""
        # 对齐数据
        aligned_data = pd.DataFrame({
            'portfolio': portfolio_value,
            'benchmark': benchmark_data
        }).dropna()
        
        if len(aligned_data) < 2:
            return {}
            
        # 计算收益率
        portfolio_returns = aligned_data['portfolio'].pct_change().dropna()
        benchmark_returns = aligned_data['benchmark'].pct_change().dropna()
        
        # 计算比较指标
        portfolio_total_return = (aligned_data['portfolio'].iloc[-1] / aligned_data['portfolio'].iloc[0]) - 1
        benchmark_total_return = (aligned_data['benchmark'].iloc[-1] / aligned_data['benchmark'].iloc[0]) - 1
        
        excess_return = portfolio_total_return - benchmark_total_return
        
        # 计算Beta
        covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 1
        
        # 计算相关性
        correlation = np.corrcoef(portfolio_returns, benchmark_returns)[0, 1]
        
        # 计算跟踪误差
        tracking_error = (portfolio_returns - benchmark_returns).std() * np.sqrt(252)
        
        return {
            'portfolio_return': portfolio_total_return,
            'benchmark_return': benchmark_total_return,
            'excess_return': excess_return,
            'beta': beta,
            'correlation': correlation,
            'tracking_error': tracking_error
        }
        
    def _analyze_drawdowns(self, portfolio_value: pd.Series) -> Dict[str, any]:
        """分析回撤"""
        running_max = portfolio_value.expanding().max()
        drawdown = (portfolio_value - running_max) / running_max
        
        # 找出所有回撤期
        drawdown_periods = []
        start_idx = None
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and start_idx is None:
                start_idx = i
            elif dd >= 0 and start_idx is not None:
                end_idx = i - 1
                max_dd_in_period = drawdown[start_idx:end_idx+1].min()
                
                drawdown_periods.append({
                    'start_date': drawdown.index[start_idx],
                    'end_date': drawdown.index[end_idx],
                    'duration': end_idx - start_idx + 1,
                    'max_drawdown': abs(max_dd_in_period)
                })
                start_idx = None
                
        # 处理未结束的回撤
        if start_idx is not None:
            max_dd_in_period = drawdown[start_idx:].min()
            drawdown_periods.append({
                'start_date': drawdown.index[start_idx],
                'end_date': drawdown.index[-1],
                'duration': len(drawdown) - start_idx,
                'max_drawdown': abs(max_dd_in_period)
            })
            
        # 排序找出最大的几次回撤
        drawdown_periods.sort(key=lambda x: x['max_drawdown'], reverse=True)
        
        return {
            'total_drawdown_periods': len(drawdown_periods),
            'top_5_drawdowns': drawdown_periods[:5],
            'avg_drawdown_duration': np.mean([dd['duration'] for dd in drawdown_periods]) if drawdown_periods else 0
        }
        
    def _analyze_return_distribution(self, returns: pd.Series) -> Dict[str, float]:
        """分析收益率分布"""
        if len(returns) == 0:
            return {}
            
        return {
            'mean': returns.mean(),
            'std': returns.std(), 
            'skewness': returns.skew(),
            'kurtosis': returns.kurtosis(),
            'percentiles': {
                '1%': returns.quantile(0.01),
                '5%': returns.quantile(0.05),
                '25%': returns.quantile(0.25),
                '50%': returns.quantile(0.50),
                '75%': returns.quantile(0.75),
                '95%': returns.quantile(0.95),
                '99%': returns.quantile(0.99)
            }
        }