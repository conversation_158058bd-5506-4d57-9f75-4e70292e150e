{"test_summary": {"total_tests": 15, "total_issues": 82, "high_severity": 0, "medium_severity": 37, "low_severity": 45, "test_time": "2025-08-04T10:06:14.457273"}, "issues_by_category": {"性能": [{"category": "性能", "severity": "中", "title": "市场数据页面加载较慢", "description": "加载时间: 3.15秒，建议优化", "evidence": null, "timestamp": "2025-08-04T10:05:12.854205", "url": "http://localhost:5173/market"}, {"category": "性能", "severity": "中", "title": "交易终端页面加载较慢", "description": "加载时间: 3.56秒，建议优化", "evidence": null, "timestamp": "2025-08-04T10:05:16.411121", "url": "http://localhost:5173/trading/terminal"}], "代码质量": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.601876", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.602154", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.602513", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.602722", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.602927", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603136", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603336", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603461", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603586", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket错误详情: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.603764", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-04T10:05:49.603881", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "evidence": null, "timestamp": "2025-08-04T10:05:49.604050", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.604282", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Network error: XMLHttpRequest", "evidence": null, "timestamp": "2025-08-04T10:05:49.604453", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-04T10:05:49.604572", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-04T10:05:49.604730", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.605023", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.605193", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket错误详情: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.605441", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-04T10:05:49.605576", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "evidence": null, "timestamp": "2025-08-04T10:05:49.605832", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606145", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606327", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606454", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606602", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606726", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606901", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.607092", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.607273", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.607392", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.607566", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.607910", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608311", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608508", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608696", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608888", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.609078", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.609269", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 1/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.609386", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.609505", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 2/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.609620", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.609782", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 3/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.609975", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610125", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 4/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.610242", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接暂时不可用: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.610356", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610469", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 5/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.610582", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610695", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 6/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.610807", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610922", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 7/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.611077", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.611272", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 8/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.611422", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.611558", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 9/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.611674", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.611798", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.612119", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.612432", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.613693", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.614774", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.615821", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接暂时不可用: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.617013", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "交易WebSocket暂时不可用: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.617240", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.617435", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.617662", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.617862", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.619182", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.620270", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.621317", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "evidence": null, "timestamp": "2025-08-04T10:05:49.623040", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "evidence": null, "timestamp": "2025-08-04T10:05:49.623515", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.623872", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.624179", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.624483", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.624779", "url": "http://localhost:5173/risk"}], "UI/UX": [{"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-04T10:06:04.603734", "url": "http://localhost:5173/"}], "可访问性": [{"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-04T10:06:05.348130", "url": "http://localhost:5173/"}], "安全": [{"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-04T10:06:14.456484", "url": "http://localhost:5173/"}]}, "detailed_issues": [{"category": "性能", "severity": "中", "title": "市场数据页面加载较慢", "description": "加载时间: 3.15秒，建议优化", "evidence": null, "timestamp": "2025-08-04T10:05:12.854205", "url": "http://localhost:5173/market"}, {"category": "性能", "severity": "中", "title": "交易终端页面加载较慢", "description": "加载时间: 3.56秒，建议优化", "evidence": null, "timestamp": "2025-08-04T10:05:16.411121", "url": "http://localhost:5173/trading/terminal"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.601876", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.602154", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.602513", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.602722", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.602927", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603136", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603336", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603461", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.603586", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket错误详情: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.603764", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-04T10:05:49.603881", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "evidence": null, "timestamp": "2025-08-04T10:05:49.604050", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.604282", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Network error: XMLHttpRequest", "evidence": null, "timestamp": "2025-08-04T10:05:49.604453", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-04T10:05:49.604572", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-04T10:05:49.604730", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.605023", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.605193", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket错误详情: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.605441", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-04T10:05:49.605576", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "evidence": null, "timestamp": "2025-08-04T10:05:49.605832", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606145", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606327", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606454", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606602", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606726", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "evidence": null, "timestamp": "2025-08-04T10:05:49.606901", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.607092", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.607273", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.607392", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.607566", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-04T10:05:49.607910", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608311", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608508", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608696", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.608888", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.609078", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.609269", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 1/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.609386", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.609505", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 2/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.609620", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.609782", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 3/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.609975", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610125", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 4/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.610242", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接暂时不可用: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.610356", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610469", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 5/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.610582", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610695", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 6/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.610807", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.610922", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 7/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.611077", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.611272", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 8/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.611422", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 市场图表容器引用未找到", "evidence": null, "timestamp": "2025-08-04T10:05:49.611558", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "🔄 容器未准备好，重试 9/10", "evidence": null, "timestamp": "2025-08-04T10:05:49.611674", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.611798", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.612119", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.612432", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.613693", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.614774", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.615821", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接暂时不可用: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.617013", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "交易WebSocket暂时不可用: Event", "evidence": null, "timestamp": "2025-08-04T10:05:49.617240", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.617435", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.617662", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.617862", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.619182", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.620270", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-04T10:05:49.621317", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "evidence": null, "timestamp": "2025-08-04T10:05:49.623040", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "evidence": null, "timestamp": "2025-08-04T10:05:49.623515", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.623872", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.624179", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.624483", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-04T10:05:49.624779", "url": "http://localhost:5173/risk"}, {"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-04T10:06:04.603734", "url": "http://localhost:5173/"}, {"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-04T10:06:05.348130", "url": "http://localhost:5173/"}, {"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-04T10:06:14.456484", "url": "http://localhost:5173/"}], "test_results": [{"test": "首页性能测试", "status": "PASS", "details": "加载时间: 1.58秒", "timestamp": "2025-08-04T10:05:08.770644"}, {"test": "仪表盘性能测试", "status": "PASS", "details": "加载时间: 0.93秒", "timestamp": "2025-08-04T10:05:09.705117"}, {"test": "市场数据性能测试", "status": "PASS", "details": "加载时间: 3.15秒", "timestamp": "2025-08-04T10:05:12.854475"}, {"test": "交易终端性能测试", "status": "PASS", "details": "加载时间: 3.56秒", "timestamp": "2025-08-04T10:05:16.411359"}, {"test": "策略中心性能测试", "status": "PASS", "details": "加载时间: 0.66秒", "timestamp": "2025-08-04T10:05:17.070140"}, {"test": "投资组合性能测试", "status": "PASS", "details": "加载时间: 0.69秒", "timestamp": "2025-08-04T10:05:17.759234"}, {"test": "风险管理性能测试", "status": "PASS", "details": "加载时间: 1.02秒", "timestamp": "2025-08-04T10:05:18.782149"}, {"test": "控制台错误检查", "status": "PASS", "details": "发现 32 个错误, 45 个警告", "timestamp": "2025-08-04T10:05:49.625032"}, {"test": "网络请求分析", "status": "PASS", "details": "发现 0 个失败请求", "timestamp": "2025-08-04T10:05:58.058160"}, {"test": "桌面端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-04T10:06:02.644190"}, {"test": "平板端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-04T10:06:03.686156"}, {"test": "手机端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-04T10:06:04.604103"}, {"test": "可访问性检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-04T10:06:05.354170"}, {"test": "数据加载检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-04T10:06:13.786854"}, {"test": "安全头检查", "status": "PASS", "details": "缺少 5 个安全头", "timestamp": "2025-08-04T10:06:14.457035"}], "recommendations": [{"priority": "高", "title": "性能优化", "description": "优化页面加载性能和响应速度", "actions": ["实施代码分割", "优化图片加载", "减少初始包大小", "添加缓存策略"]}, {"priority": "中", "title": "用户体验改进", "description": "提升界面响应性和用户体验", "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]}]}