/**
 * 用户体验深度测试（修复版）
 * 模拟真实用户使用量化投资平台的完整流程
 */

const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

class UserExperienceTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.baseUrl = 'http://localhost:5173'; // 前端地址
        this.apiUrl = 'http://localhost:8000'; // 后端地址
        this.testResults = [];
        this.screenshots = [];
        this.testUser = {
            username: 'test_user_' + Date.now(),
            password: 'Test123456!',
            email: `test_${Date.now()}@example.com`
        };
    }

    async startServices() {
        console.log('🔧 启动服务...');
        
        // 检查端口是否已被占用
        try {
            await execPromise('lsof -i :8000');
            console.log('  ✓ 后端服务已在运行');
        } catch (e) {
            // 端口未被占用，启动后端
            console.log('  - 启动后端服务...');
            exec('cd backend && uvicorn app.main:app --reload --port 8000 > ../backend.log 2>&1');
        }
        
        try {
            await execPromise('lsof -i :5173');
            console.log('  ✓ 前端服务已在运行');
        } catch (e) {
            // 端口未被占用，启动前端
            console.log('  - 启动前端服务...');
            exec('cd frontend && npm run dev > ../frontend.log 2>&1');
        }
        
        // 等待服务启动
        console.log('  - 等待服务完全启动...');
        await this.delay(10000);
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    async init() {
        console.log('🚀 启动浏览器...');
        this.browser = await puppeteer.launch({
            headless: true,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();
        
        // 监听控制台消息
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.logIssue('控制台错误', msg.text(), 'high');
            }
        });
        
        // 监听页面错误
        this.page.on('pageerror', error => {
            this.logIssue('页面错误', error.message, 'high');
        });
        
        // 监听请求失败
        this.page.on('requestfailed', request => {
            if (!request.url().includes('favicon')) {
                this.logIssue('请求失败', `${request.url()} - ${request.failure().errorText}`, 'medium');
            }
        });
    }

    async takeScreenshot(name) {
        try {
            const filename = `screenshots/ux_test_${name}_${Date.now()}.png`;
            await this.page.screenshot({ path: filename, fullPage: true });
            this.screenshots.push(filename);
            console.log(`📸 截图已保存: ${filename}`);
        } catch (error) {
            console.error(`截图失败: ${error.message}`);
        }
    }

    logIssue(category, description, severity = 'medium') {
        const issue = {
            category,
            description,
            severity,
            timestamp: new Date().toISOString(),
            url: this.page.url()
        };
        this.testResults.push(issue);
        console.log(`❗ ${severity.toUpperCase()} - ${category}: ${description}`);
    }

    async testUserJourney() {
        console.log('\n🧪 开始用户体验测试...\n');

        // 测试基础功能
        await this.testHomePage();
        await this.testAPIEndpoints();
        await this.testUIComponents();
        await this.testUserFlow();
    }

    async testHomePage() {
        console.log('📍 测试首页加载...');
        
        try {
            const response = await this.page.goto(this.baseUrl, { 
                waitUntil: 'networkidle0',
                timeout: 30000 
            });
            
            if (!response) {
                this.logIssue('首页', '页面无响应', 'critical');
                return;
            }
            
            const status = response.status();
            if (status !== 200) {
                this.logIssue('首页', `HTTP状态码: ${status}`, status >= 500 ? 'critical' : 'high');
            }
            
            await this.takeScreenshot('homepage');
            
            // 检查基本元素
            const bodyContent = await this.page.evaluate(() => document.body.innerHTML);
            if (!bodyContent || bodyContent.length < 100) {
                this.logIssue('首页', '页面内容为空或过少', 'high');
            }
            
            // 检查Vue应用是否加载
            const hasVueApp = await this.page.evaluate(() => {
                return window.Vue || document.querySelector('#app') || document.querySelector('[id="app"]');
            });
            
            if (!hasVueApp) {
                this.logIssue('首页', 'Vue应用未正确加载', 'critical');
            }
            
        } catch (error) {
            this.logIssue('首页', `加载失败: ${error.message}`, 'critical');
        }
    }

    async testAPIEndpoints() {
        console.log('📍 测试API端点...');
        
        const endpoints = [
            { path: '/api/v1/market/stocks', name: '股票列表' },
            { path: '/api/v1/market/realtime/000001', name: '实时行情' },
            { path: '/api/v1/auth/login', name: '登录接口', method: 'POST' },
            { path: '/api/v1/trading/accounts', name: '账户信息' },
            { path: '/api/v1/strategy/list', name: '策略列表' }
        ];
        
        for (const endpoint of endpoints) {
            try {
                const response = await this.page.evaluate(async (url, method) => {
                    try {
                        const res = await fetch(url, {
                            method: method || 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        return {
                            status: res.status,
                            ok: res.ok,
                            statusText: res.statusText
                        };
                    } catch (e) {
                        return { error: e.message };
                    }
                }, `${this.apiUrl}${endpoint.path}`, endpoint.method);
                
                if (response.error) {
                    this.logIssue('API测试', `${endpoint.name} 请求失败: ${response.error}`, 'high');
                } else if (!response.ok && response.status !== 401) {
                    this.logIssue('API测试', `${endpoint.name} 返回错误: ${response.status} ${response.statusText}`, 'medium');
                }
            } catch (error) {
                this.logIssue('API测试', `${endpoint.name} 测试失败: ${error.message}`, 'high');
            }
        }
    }

    async testUIComponents() {
        console.log('📍 测试UI组件...');
        
        // 等待页面加载
        await this.delay(3000);
        
        // 检查导航栏
        const navExists = await this.page.evaluate(() => {
            const nav = document.querySelector('nav, .navbar, .navigation, [class*="nav"]');
            return !!nav;
        });
        
        if (!navExists) {
            this.logIssue('UI组件', '未找到导航栏', 'high');
        }
        
        // 检查主要容器
        const mainContent = await this.page.evaluate(() => {
            const main = document.querySelector('main, .main-content, .container, [class*="container"]');
            return !!main;
        });
        
        if (!mainContent) {
            this.logIssue('UI组件', '未找到主要内容容器', 'medium');
        }
        
        // 检查按钮
        const buttons = await this.page.evaluate(() => {
            const btns = document.querySelectorAll('button, .btn, [class*="button"]');
            return btns.length;
        });
        
        console.log(`  ✓ 找到 ${buttons} 个按钮`);
        
        if (buttons === 0) {
            this.logIssue('UI组件', '页面上没有可交互的按钮', 'medium');
        }
    }

    async testUserFlow() {
        console.log('📍 测试用户流程...');
        
        // 模拟用户点击和交互
        try {
            // 查找所有可点击的链接
            const links = await this.page.evaluate(() => {
                const allLinks = Array.from(document.querySelectorAll('a[href], button'));
                return allLinks.map(link => ({
                    text: link.textContent.trim(),
                    href: link.href || '',
                    tagName: link.tagName
                })).filter(link => link.text.length > 0);
            });
            
            console.log(`  ✓ 找到 ${links.length} 个可点击元素`);
            
            // 测试主要功能链接
            const importantLinks = ['市场', '交易', '策略', '登录', '注册'];
            for (const linkText of importantLinks) {
                const found = links.find(l => l.text.includes(linkText));
                if (!found) {
                    this.logIssue('导航', `未找到"${linkText}"链接`, 'medium');
                }
            }
            
        } catch (error) {
            this.logIssue('用户流程', `测试失败: ${error.message}`, 'medium');
        }
    }

    async generateReport() {
        console.log('\n📊 生成测试报告...\n');
        
        const report = {
            testTime: new Date().toISOString(),
            totalIssues: this.testResults.length,
            issuesByCategory: {},
            issuesBySeverity: {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0
            },
            screenshots: this.screenshots,
            recommendations: []
        };
        
        // 分析问题
        for (const issue of this.testResults) {
            // 按类别统计
            if (!report.issuesByCategory[issue.category]) {
                report.issuesByCategory[issue.category] = [];
            }
            report.issuesByCategory[issue.category].push(issue);
            
            // 按严重程度统计
            report.issuesBySeverity[issue.severity]++;
        }
        
        // 生成建议
        this.generateRecommendations(report);
        
        // 输出报告
        this.printReport(report);
        
        // 保存详细报告
        try {
            await fs.writeFile(
                'user_experience_report.json',
                JSON.stringify(report, null, 2),
                'utf-8'
            );
            console.log('\n详细报告已保存到: user_experience_report.json');
        } catch (error) {
            console.error('保存报告失败:', error);
        }
        
        return report;
    }

    generateRecommendations(report) {
        if (report.issuesBySeverity.critical > 0) {
            report.recommendations.push({
                priority: '紧急',
                action: '立即修复关键问题，确保基础服务可用',
                details: '包括前后端服务启动、页面加载等核心功能'
            });
        }
        
        if (report.issuesByCategory['API测试']?.length > 3) {
            report.recommendations.push({
                priority: '高',
                action: '完善API接口实现',
                details: '确保所有API端点返回正确的响应，实现完整的业务逻辑'
            });
        }
        
        if (report.issuesByCategory['UI组件']?.length > 0) {
            report.recommendations.push({
                priority: '中',
                action: '改进前端界面',
                details: '添加必要的UI组件，提升用户体验'
            });
        }
        
        // 具体改进建议
        report.recommendations.push({
            priority: '高',
            action: '实现用户认证系统',
            details: '包括注册、登录、权限管理等功能'
        });
        
        report.recommendations.push({
            priority: '高',
            action: '完善交易功能',
            details: '实现下单、撤单、查询持仓等核心交易功能'
        });
        
        report.recommendations.push({
            priority: '中',
            action: '添加数据可视化',
            details: '使用图表展示K线、收益曲线等数据'
        });
    }

    printReport(report) {
        console.log('=== 用户体验测试报告 ===\n');
        console.log(`测试时间: ${new Date().toLocaleString()}`);
        console.log(`总问题数: ${report.totalIssues}`);
        
        console.log('\n问题严重性分布:');
        console.log(`  🔴 关键 (Critical): ${report.issuesBySeverity.critical}`);
        console.log(`  🟠 高 (High): ${report.issuesBySeverity.high}`);
        console.log(`  🟡 中 (Medium): ${report.issuesBySeverity.medium}`);
        console.log(`  🟢 低 (Low): ${report.issuesBySeverity.low}`);
        
        console.log('\n问题分类:');
        for (const [category, issues] of Object.entries(report.issuesByCategory)) {
            console.log(`  ${category}: ${issues.length} 个问题`);
            // 显示前3个问题
            issues.slice(0, 3).forEach(issue => {
                console.log(`    - ${issue.description}`);
            });
        }
        
        console.log('\n改进建议:');
        report.recommendations.forEach((rec, index) => {
            console.log(`  ${index + 1}. [${rec.priority}] ${rec.action}`);
            console.log(`     ${rec.details}`);
        });
    }

    async cleanup() {
        console.log('\n🧹 清理资源...');
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.startServices();
            await this.init();
            await this.testUserJourney();
            await this.generateReport();
        } catch (error) {
            console.error('测试过程中发生错误:', error);
            this.logIssue('系统', `测试框架错误: ${error.message}`, 'critical');
        } finally {
            await this.cleanup();
        }
    }
}

// 主程序
async function main() {
    console.log('🎯 量化投资平台用户体验测试\n');
    
    const tester = new UserExperienceTest();
    await tester.run();
    
    console.log('\n✅ 测试完成！');
    
    // 退出进程
    process.exit(0);
}

// 运行测试
main().catch(error => {
    console.error('致命错误:', error);
    process.exit(1);
});