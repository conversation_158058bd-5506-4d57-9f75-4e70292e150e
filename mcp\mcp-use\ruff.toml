line-length = 120
target-version = "py311"

[lint]
select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "I",  # isort
    "W",  # pycodestyle warnings
    "B",  # flake8-bugbear
    "UP", # pyupgrade
]

[lint.per-file-ignores]
"__init__.py" = ["F401"]  # Unused imports
"tests/**/*.py" = ["F811", "F401", "B017"]  # Redefinition in test files
"mcp_use/connectors/websocket.py" = ["C901"]  # Function too complex

[lint.isort]
known-first-party = ["mcp_use"]

[format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"
