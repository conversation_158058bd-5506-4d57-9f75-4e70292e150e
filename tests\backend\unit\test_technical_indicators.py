"""
技术指标库单元测试
"""

import pytest
import pandas as pd
import numpy as np
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.utils.technical_indicators import (
    TechnicalIndicators,
    IndicatorResult,
    MovingAverageType,
    BollingerBands,
    MACDResult,
    RSIResult,
    StochasticResult,
    WilliamsRResult,
    ATRResult,
    ADXResult,
    CCIResult,
    ROCResult,
    OBVResult,
    VWAPResult,
    IchimokuResult,
    ParabolicSARResult,
    AroonResult,
    UltimateOscillatorResult,
    TrixResult,
    KeltnerChannelResult,
    DonchianChannelResult,
    StrategySignal,
    SignalType,
)


@pytest.mark.unit
@pytest.mark.technical_indicators
class TestTechnicalIndicators:
    """技术指标测试类"""

    @pytest.fixture
    def sample_data(self):
        """生成样本数据"""
        np.random.seed(42)
        dates = pd.date_range("2023-01-01", periods=100, freq="D")
        base_price = 100.0

        data = []
        for i, date in enumerate(dates):
            # 生成带趋势的价格数据
            trend = i * 0.1
            noise = np.random.normal(0, 2)
            price = base_price + trend + noise

            data.append(
                {
                    "date": date,
                    "open": price * 0.999,
                    "high": price * 1.02,
                    "low": price * 0.98,
                    "close": price,
                    "volume": np.random.randint(10000, 100000),
                }
            )

        return pd.DataFrame(data)

    @pytest.fixture
    def technical_indicators(self):
        """创建技术指标实例"""
        return TechnicalIndicators()

    def test_sma_calculation(self, technical_indicators, sample_data):
        """测试简单移动平均线"""
        period = 20
        result = technical_indicators.sma(sample_data["close"], period)

        assert isinstance(result, IndicatorResult)
        assert len(result.values) == len(sample_data)
        assert result.period == period
        assert result.name == f"SMA_{period}"

        # 验证前period-1个值为NaN
        assert pd.isna(result.values[: period - 1]).all()

        # 验证计算正确性
        manual_sma = sample_data["close"].rolling(window=period).mean()
        pd.testing.assert_series_equal(result.values, manual_sma, check_names=False)

    def test_ema_calculation(self, technical_indicators, sample_data):
        """测试指数移动平均线"""
        period = 12
        result = technical_indicators.ema(sample_data["close"], period)

        assert isinstance(result, IndicatorResult)
        assert len(result.values) == len(sample_data)
        assert result.period == period
        assert result.name == f"EMA_{period}"

        # 验证EMA值不为空（除了第一个值）
        assert not pd.isna(result.values[1:]).any()

    def test_bollinger_bands_calculation(self, technical_indicators, sample_data):
        """测试布林带"""
        period = 20
        std_dev = 2
        result = technical_indicators.bollinger_bands(
            sample_data["close"], period, std_dev
        )

        assert isinstance(result, BollingerBands)
        assert len(result.upper) == len(sample_data)
        assert len(result.middle) == len(sample_data)
        assert len(result.lower) == len(sample_data)

        # 验证上轨大于中轨大于下轨
        valid_indices = ~pd.isna(result.middle)
        assert (result.upper[valid_indices] >= result.middle[valid_indices]).all()
        assert (result.middle[valid_indices] >= result.lower[valid_indices]).all()

    def test_macd_calculation(self, technical_indicators, sample_data):
        """测试MACD"""
        fast_period = 12
        slow_period = 26
        signal_period = 9
        result = technical_indicators.macd(
            sample_data["close"], fast_period, slow_period, signal_period
        )

        assert isinstance(result, MACDResult)
        assert len(result.macd) == len(sample_data)
        assert len(result.signal) == len(sample_data)
        assert len(result.histogram) == len(sample_data)

        # 验证histogram = macd - signal
        valid_indices = ~pd.isna(result.signal)
        expected_histogram = result.macd[valid_indices] - result.signal[valid_indices]
        pd.testing.assert_series_equal(
            result.histogram[valid_indices], expected_histogram, check_names=False
        )

    def test_rsi_calculation(self, technical_indicators, sample_data):
        """测试RSI"""
        period = 14
        result = technical_indicators.rsi(sample_data["close"], period)

        assert isinstance(result, RSIResult)
        assert len(result.values) == len(sample_data)
        assert result.period == period

        # 验证RSI值在0-100范围内
        valid_values = result.values[~pd.isna(result.values)]
        assert (valid_values >= 0).all()
        assert (valid_values <= 100).all()

    def test_stochastic_calculation(self, technical_indicators, sample_data):
        """测试随机指标"""
        k_period = 14
        d_period = 3
        result = technical_indicators.stochastic(
            sample_data["high"],
            sample_data["low"],
            sample_data["close"],
            k_period,
            d_period,
        )

        assert isinstance(result, StochasticResult)
        assert len(result.k) == len(sample_data)
        assert len(result.d) == len(sample_data)

        # 验证K和D值在0-100范围内
        valid_k = result.k[~pd.isna(result.k)]
        valid_d = result.d[~pd.isna(result.d)]
        assert (valid_k >= 0).all() and (valid_k <= 100).all()
        assert (valid_d >= 0).all() and (valid_d <= 100).all()

    def test_williams_r_calculation(self, technical_indicators, sample_data):
        """测试威廉指标"""
        period = 14
        result = technical_indicators.williams_r(
            sample_data["high"], sample_data["low"], sample_data["close"], period
        )

        assert isinstance(result, WilliamsRResult)
        assert len(result.values) == len(sample_data)

        # 验证威廉指标值在-100到0范围内
        valid_values = result.values[~pd.isna(result.values)]
        assert (valid_values >= -100).all()
        assert (valid_values <= 0).all()

    def test_atr_calculation(self, technical_indicators, sample_data):
        """测试真实波动幅度"""
        period = 14
        result = technical_indicators.atr(
            sample_data["high"], sample_data["low"], sample_data["close"], period
        )

        assert isinstance(result, ATRResult)
        assert len(result.values) == len(sample_data)
        assert result.period == period

        # 验证ATR值为正
        valid_values = result.values[~pd.isna(result.values)]
        assert (valid_values > 0).all()

    def test_adx_calculation(self, technical_indicators, sample_data):
        """测试平均趋向指数"""
        period = 14
        result = technical_indicators.adx(
            sample_data["high"], sample_data["low"], sample_data["close"], period
        )

        assert isinstance(result, ADXResult)
        assert len(result.adx) == len(sample_data)
        assert len(result.plus_di) == len(sample_data)
        assert len(result.minus_di) == len(sample_data)

        # 验证ADX值在0-100范围内
        valid_adx = result.adx[~pd.isna(result.adx)]
        assert (valid_adx >= 0).all()
        assert (valid_adx <= 100).all()

    def test_cci_calculation(self, technical_indicators, sample_data):
        """测试顺势指标"""
        period = 20
        result = technical_indicators.cci(
            sample_data["high"], sample_data["low"], sample_data["close"], period
        )

        assert isinstance(result, CCIResult)
        assert len(result.values) == len(sample_data)
        assert result.period == period

    def test_roc_calculation(self, technical_indicators, sample_data):
        """测试变动率指标"""
        period = 12
        result = technical_indicators.roc(sample_data["close"], period)

        assert isinstance(result, ROCResult)
        assert len(result.values) == len(sample_data)
        assert result.period == period

        # 验证前period个值为NaN
        assert pd.isna(result.values[:period]).all()

    def test_obv_calculation(self, technical_indicators, sample_data):
        """测试能量潮指标"""
        result = technical_indicators.obv(sample_data["close"], sample_data["volume"])

        assert isinstance(result, OBVResult)
        assert len(result.values) == len(sample_data)

        # 验证OBV是累积的
        assert not pd.isna(result.values).any()

    def test_vwap_calculation(self, technical_indicators, sample_data):
        """测试成交量加权平均价格"""
        result = technical_indicators.vwap(
            sample_data["high"],
            sample_data["low"],
            sample_data["close"],
            sample_data["volume"],
        )

        assert isinstance(result, VWAPResult)
        assert len(result.values) == len(sample_data)

        # 验证VWAP值不为空
        assert not pd.isna(result.values).any()

    def test_ichimoku_calculation(self, technical_indicators, sample_data):
        """测试一目均衡表"""
        result = technical_indicators.ichimoku(
            sample_data["high"], sample_data["low"], sample_data["close"]
        )

        assert isinstance(result, IchimokuResult)
        assert len(result.tenkan_sen) == len(sample_data)
        assert len(result.kijun_sen) == len(sample_data)
        assert len(result.senkou_span_a) == len(sample_data)
        assert len(result.senkou_span_b) == len(sample_data)
        assert len(result.chikou_span) == len(sample_data)

    def test_parabolic_sar_calculation(self, technical_indicators, sample_data):
        """测试抛物线SAR"""
        result = technical_indicators.parabolic_sar(
            sample_data["high"], sample_data["low"]
        )

        assert isinstance(result, ParabolicSARResult)
        assert len(result.values) == len(sample_data)
        assert len(result.trend) == len(sample_data)

        # 验证趋势值只有1和-1
        valid_trend = result.trend[~pd.isna(result.trend)]
        assert set(valid_trend.unique()).issubset({1, -1})

    def test_aroon_calculation(self, technical_indicators, sample_data):
        """测试阿隆指标"""
        period = 14
        result = technical_indicators.aroon(
            sample_data["high"], sample_data["low"], period
        )

        assert isinstance(result, AroonResult)
        assert len(result.aroon_up) == len(sample_data)
        assert len(result.aroon_down) == len(sample_data)
        assert len(result.aroon_oscillator) == len(sample_data)

        # 验证Aroon值在0-100范围内
        valid_up = result.aroon_up[~pd.isna(result.aroon_up)]
        valid_down = result.aroon_down[~pd.isna(result.aroon_down)]
        assert (valid_up >= 0).all() and (valid_up <= 100).all()
        assert (valid_down >= 0).all() and (valid_down <= 100).all()

    def test_ultimate_oscillator_calculation(self, technical_indicators, sample_data):
        """测试终极振荡器"""
        result = technical_indicators.ultimate_oscillator(
            sample_data["high"], sample_data["low"], sample_data["close"]
        )

        assert isinstance(result, UltimateOscillatorResult)
        assert len(result.values) == len(sample_data)

        # 验证值在0-100范围内
        valid_values = result.values[~pd.isna(result.values)]
        assert (valid_values >= 0).all()
        assert (valid_values <= 100).all()

    def test_trix_calculation(self, technical_indicators, sample_data):
        """测试TRIX指标"""
        period = 14
        result = technical_indicators.trix(sample_data["close"], period)

        assert isinstance(result, TrixResult)
        assert len(result.values) == len(sample_data)
        assert len(result.signal) == len(sample_data)
        assert result.period == period

    def test_keltner_channel_calculation(self, technical_indicators, sample_data):
        """测试肯特纳通道"""
        period = 20
        multiplier = 2.0
        result = technical_indicators.keltner_channel(
            sample_data["high"],
            sample_data["low"],
            sample_data["close"],
            period,
            multiplier,
        )

        assert isinstance(result, KeltnerChannelResult)
        assert len(result.upper) == len(sample_data)
        assert len(result.middle) == len(sample_data)
        assert len(result.lower) == len(sample_data)

        # 验证上轨大于中轨大于下轨
        valid_indices = ~pd.isna(result.middle)
        assert (result.upper[valid_indices] >= result.middle[valid_indices]).all()
        assert (result.middle[valid_indices] >= result.lower[valid_indices]).all()

    def test_donchian_channel_calculation(self, technical_indicators, sample_data):
        """测试唐奇安通道"""
        period = 20
        result = technical_indicators.donchian_channel(
            sample_data["high"], sample_data["low"], period
        )

        assert isinstance(result, DonchianChannelResult)
        assert len(result.upper) == len(sample_data)
        assert len(result.middle) == len(sample_data)
        assert len(result.lower) == len(sample_data)

        # 验证上轨大于等于中轨大于等于下轨
        valid_indices = ~pd.isna(result.middle)
        assert (result.upper[valid_indices] >= result.middle[valid_indices]).all()
        assert (result.middle[valid_indices] >= result.lower[valid_indices]).all()

    def test_moving_average_crossover_strategy(self, technical_indicators, sample_data):
        """测试移动平均交叉策略"""
        short_period = 10
        long_period = 20
        signals = technical_indicators.moving_average_crossover(
            sample_data["close"], short_period, long_period
        )

        assert isinstance(signals, list)
        for signal in signals:
            assert isinstance(signal, StrategySignal)
            assert signal.signal_type in [SignalType.BUY, SignalType.SELL]
            assert isinstance(signal.timestamp, datetime)
            assert isinstance(signal.price, (int, float))
            assert isinstance(signal.strength, (int, float))
            assert 0 <= signal.strength <= 1

    def test_macd_signal_strategy(self, technical_indicators, sample_data):
        """测试MACD信号策略"""
        signals = technical_indicators.macd_signal_strategy(sample_data["close"])

        assert isinstance(signals, list)
        for signal in signals:
            assert isinstance(signal, StrategySignal)
            assert signal.signal_type in [SignalType.BUY, SignalType.SELL]

    def test_rsi_divergence_strategy(self, technical_indicators, sample_data):
        """测试RSI背离策略"""
        signals = technical_indicators.rsi_divergence_strategy(
            sample_data["close"], sample_data["high"], sample_data["low"]
        )

        assert isinstance(signals, list)
        for signal in signals:
            assert isinstance(signal, StrategySignal)
            assert signal.signal_type in [SignalType.BUY, SignalType.SELL]

    def test_bollinger_squeeze_strategy(self, technical_indicators, sample_data):
        """测试布林带挤压策略"""
        signals = technical_indicators.bollinger_squeeze_strategy(
            sample_data["high"], sample_data["low"], sample_data["close"]
        )

        assert isinstance(signals, list)
        for signal in signals:
            assert isinstance(signal, StrategySignal)
            assert signal.signal_type in [SignalType.BUY, SignalType.SELL]

    def test_multi_timeframe_analysis(self, technical_indicators, sample_data):
        """测试多时间框架分析"""
        # 创建不同时间框架的数据
        daily_data = sample_data.copy()
        weekly_data = sample_data.iloc[::7].copy()  # 每7天取一个点模拟周线

        analysis = technical_indicators.multi_timeframe_analysis(
            daily_data["close"], weekly_data["close"]
        )

        assert isinstance(analysis, dict)
        assert "daily_trend" in analysis
        assert "weekly_trend" in analysis
        assert "alignment" in analysis
        assert "signals" in analysis

    def test_batch_calculate_indicators(self, technical_indicators, sample_data):
        """测试批量计算指标"""
        indicators = ["sma_20", "ema_12", "rsi_14", "macd"]
        results = technical_indicators.batch_calculate(sample_data, indicators)

        assert isinstance(results, dict)
        assert len(results) == len(indicators)

        for indicator in indicators:
            assert indicator in results
            assert hasattr(results[indicator], "values") or hasattr(
                results[indicator], "macd"
            )

    def test_indicator_correlation_analysis(self, technical_indicators, sample_data):
        """测试指标相关性分析"""
        # 计算多个指标
        sma_20 = technical_indicators.sma(sample_data["close"], 20)
        ema_12 = technical_indicators.ema(sample_data["close"], 12)
        rsi_14 = technical_indicators.rsi(sample_data["close"], 14)

        correlation_matrix = technical_indicators.calculate_correlation_matrix(
            {"sma_20": sma_20.values, "ema_12": ema_12.values, "rsi_14": rsi_14.values}
        )

        assert isinstance(correlation_matrix, pd.DataFrame)
        assert correlation_matrix.shape == (3, 3)

        # 验证对角线为1
        np.testing.assert_array_almost_equal(np.diag(correlation_matrix), [1, 1, 1])

    def test_performance_optimization(self, technical_indicators):
        """测试性能优化"""
        # 生成大量数据测试性能
        large_data = pd.Series(np.random.randn(10000))

        import time

        start_time = time.time()

        # 计算多个指标
        sma_result = technical_indicators.sma(large_data, 20)
        ema_result = technical_indicators.ema(large_data, 12)
        rsi_result = technical_indicators.rsi(large_data, 14)

        end_time = time.time()
        execution_time = end_time - start_time

        # 验证计算时间合理（应该在1秒内完成）
        assert execution_time < 1.0

        # 验证结果正确
        assert len(sma_result.values) == len(large_data)
        assert len(ema_result.values) == len(large_data)
        assert len(rsi_result.values) == len(large_data)

    def test_edge_cases(self, technical_indicators):
        """测试边界情况"""
        # 测试空数据
        empty_data = pd.Series([])
        result = technical_indicators.sma(empty_data, 10)
        assert len(result.values) == 0

        # 测试单个数据点
        single_data = pd.Series([100.0])
        result = technical_indicators.sma(single_data, 10)
        assert len(result.values) == 1
        assert pd.isna(result.values[0])

        # 测试包含NaN的数据
        nan_data = pd.Series([100.0, np.nan, 102.0, 103.0])
        result = technical_indicators.sma(nan_data, 2)
        assert len(result.values) == 4

        # 测试所有值相同的数据
        constant_data = pd.Series([100.0] * 50)
        result = technical_indicators.rsi(constant_data, 14)
        # RSI应该为50（中性）
        valid_rsi = result.values[~pd.isna(result.values)]
        assert np.allclose(valid_rsi, 50.0, atol=1e-6)

    def test_parameter_validation(self, technical_indicators, sample_data):
        """测试参数验证"""
        # 测试无效的周期参数
        with pytest.raises(ValueError, match="Period must be positive"):
            technical_indicators.sma(sample_data["close"], 0)

        with pytest.raises(ValueError, match="Period must be positive"):
            technical_indicators.sma(sample_data["close"], -1)

        # 测试周期大于数据长度
        with pytest.raises(
            ValueError, match="Period cannot be larger than data length"
        ):
            technical_indicators.sma(sample_data["close"], len(sample_data) + 1)

        # 测试无效的标准差参数
        with pytest.raises(ValueError, match="Standard deviation must be positive"):
            technical_indicators.bollinger_bands(sample_data["close"], 20, 0)

    def test_data_type_handling(self, technical_indicators):
        """测试数据类型处理"""
        # 测试不同数据类型
        int_data = pd.Series([100, 101, 102, 103, 104])
        float_data = pd.Series([100.0, 101.0, 102.0, 103.0, 104.0])
        decimal_data = pd.Series([Decimal("100.0"), Decimal("101.0"), Decimal("102.0")])

        # 所有数据类型都应该能正常处理
        int_result = technical_indicators.sma(int_data, 3)
        float_result = technical_indicators.sma(float_data, 3)
        decimal_result = technical_indicators.sma(decimal_data, 3)

        assert len(int_result.values) == len(int_data)
        assert len(float_result.values) == len(float_data)
        assert len(decimal_result.values) == len(decimal_data)

    def test_memory_efficiency(self, technical_indicators):
        """测试内存效率"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 处理大量数据
        large_data = pd.Series(np.random.randn(100000))

        # 计算多个指标
        for _ in range(10):
            technical_indicators.sma(large_data, 20)
            technical_indicators.ema(large_data, 12)
            technical_indicators.rsi(large_data, 14)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该控制在合理范围内（小于100MB）
        assert memory_increase < 100

    def test_thread_safety(self, technical_indicators, sample_data):
        """测试线程安全性"""
        import threading
        import concurrent.futures

        results = []

        def calculate_indicator():
            result = technical_indicators.sma(sample_data["close"], 20)
            results.append(result)

        # 并发执行多个计算
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(calculate_indicator) for _ in range(10)]
            concurrent.futures.wait(futures)

        # 验证所有结果都正确
        assert len(results) == 10
        for result in results:
            assert isinstance(result, IndicatorResult)
            assert len(result.values) == len(sample_data)
