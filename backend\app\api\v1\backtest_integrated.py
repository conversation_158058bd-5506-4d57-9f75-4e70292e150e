"""
集成回测API
提供完整的回测功能，整合数据获取、策略执行、分析和可视化
"""
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import json
from pydantic import BaseModel

from ...services.backtest_engine_integrated import get_integrated_backtest_engine, IntegratedBacktestEngine
from ...core.dependencies import get_current_user
from ...db.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/backtest-integrated", tags=["集成回测系统"])


class BacktestRequest(BaseModel):
    """回测请求模型"""
    strategy_config: Dict[str, Any]
    data_config: Dict[str, Any]
    backtest_config: Optional[Dict[str, Any]] = None


class BatchBacktestRequest(BaseModel):
    """批量回测请求模型"""
    strategies: List[Dict[str, Any]]
    data_config: Dict[str, Any]
    backtest_config: Optional[Dict[str, Any]] = None


@router.post("/run")
async def run_integrated_backtest(
    request: BacktestRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    engine: IntegratedBacktestEngine = Depends(get_integrated_backtest_engine)
):
    """运行集成回测"""
    try:
        logger.info(f"用户 {current_user.username} 开始运行集成回测")
        
        # 验证请求参数
        _validate_backtest_request(request)
        
        # 执行回测
        result = await engine.run_comprehensive_backtest(
            strategy_config=request.strategy_config,
            data_config=request.data_config,
            backtest_config=request.backtest_config
        )
        
        return {
            "success": True,
            "message": "集成回测完成",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"集成回测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"集成回测失败: {str(e)}")


@router.post("/quick-start")
async def quick_start_backtest(
    symbols: List[str],
    strategy_type: str = "simple_ma",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    initial_capital: float = 100000.0,
    current_user: User = Depends(get_current_user),
    engine: IntegratedBacktestEngine = Depends(get_integrated_backtest_engine)
):
    """快速开始回测"""
    try:
        # 设置默认日期
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        
        # 构建请求配置
        strategy_config = {
            "type": strategy_type,
            "parameters": {
                "short_window": 5,
                "long_window": 20
            }
        }
        
        data_config = {
            "symbols": symbols,
            "start_date": start_date,
            "end_date": end_date,
            "source": "akshare"
        }
        
        backtest_config = {
            "initial_capital": initial_capital,
            "commission_rate": 0.0003,
            "stamp_duty_rate": 0.001,
            "slippage_rate": 0.0001
        }
        
        # 执行回测
        result = await engine.run_comprehensive_backtest(
            strategy_config=strategy_config,
            data_config=data_config,
            backtest_config=backtest_config
        )
        
        return {
            "success": True,
            "message": "快速回测完成",
            "data": result,
            "config": {
                "strategy": strategy_config,
                "data": data_config,
                "backtest": backtest_config
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"快速回测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"快速回测失败: {str(e)}")


@router.post("/batch")
async def run_batch_backtest(
    request: BatchBacktestRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    engine: IntegratedBacktestEngine = Depends(get_integrated_backtest_engine)
):
    """运行批量回测"""
    try:
        logger.info(f"用户 {current_user.username} 开始运行批量回测")
        
        batch_results = []
        failed_strategies = []
        
        for i, strategy_config in enumerate(request.strategies):
            try:
                result = await engine.run_comprehensive_backtest(
                    strategy_config=strategy_config,
                    data_config=request.data_config,
                    backtest_config=request.backtest_config
                )
                
                batch_results.append({
                    "strategy_index": i,
                    "strategy_name": strategy_config.get("name", f"Strategy_{i+1}"),
                    "success": True,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"策略 {i} 回测失败: {str(e)}")
                failed_strategies.append({
                    "strategy_index": i,
                    "strategy_name": strategy_config.get("name", f"Strategy_{i+1}"),
                    "error": str(e)
                })
                
                batch_results.append({
                    "strategy_index": i,
                    "strategy_name": strategy_config.get("name", f"Strategy_{i+1}"),
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"批量回测完成，成功 {len(batch_results) - len(failed_strategies)} 个，失败 {len(failed_strategies)} 个",
            "data": {
                "results": batch_results,
                "summary": {
                    "total_strategies": len(request.strategies),
                    "successful": len(batch_results) - len(failed_strategies),
                    "failed": len(failed_strategies),
                    "failed_strategies": failed_strategies
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"批量回测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量回测失败: {str(e)}")


@router.get("/strategies/templates")
async def get_strategy_templates(
    current_user: User = Depends(get_current_user)
):
    """获取策略模板"""
    templates = {
        "simple_ma": {
            "name": "简单移动平均策略",
            "description": "基于短期和长期移动平均线的交叉信号进行交易",
            "parameters": {
                "short_window": {
                    "type": "int",
                    "default": 5,
                    "min": 1,
                    "max": 50,
                    "description": "短期移动平均窗口"
                },
                "long_window": {
                    "type": "int", 
                    "default": 20,
                    "min": 5,
                    "max": 200,
                    "description": "长期移动平均窗口"
                }
            }
        },
        "rsi": {
            "name": "RSI相对强弱指标策略",
            "description": "基于RSI指标的超买超卖信号进行交易",
            "parameters": {
                "rsi_period": {
                    "type": "int",
                    "default": 14,
                    "min": 5,
                    "max": 50,
                    "description": "RSI计算周期"
                },
                "oversold": {
                    "type": "float",
                    "default": 30,
                    "min": 10,
                    "max": 40,
                    "description": "超卖阈值"
                },
                "overbought": {
                    "type": "float",
                    "default": 70,
                    "min": 60,
                    "max": 90,
                    "description": "超买阈值"
                }
            }
        },
        "bollinger_bands": {
            "name": "布林带策略",
            "description": "基于布林带指标的均值回归策略",
            "parameters": {
                "window": {
                    "type": "int",
                    "default": 20,
                    "min": 10,
                    "max": 50,
                    "description": "移动平均窗口"
                },
                "std_multiplier": {
                    "type": "float",
                    "default": 2.0,
                    "min": 1.0,
                    "max": 3.0,
                    "description": "标准差倍数"
                }
            }
        },
        "momentum": {
            "name": "动量策略",
            "description": "基于价格动量的趋势跟随策略",
            "parameters": {
                "lookback_period": {
                    "type": "int",
                    "default": 10,
                    "min": 5,
                    "max": 30,
                    "description": "回看周期"
                },
                "threshold": {
                    "type": "float",
                    "default": 0.02,
                    "min": 0.01,
                    "max": 0.1,
                    "description": "动量阈值"
                }
            }
        }
    }
    
    return {
        "success": True,
        "data": templates,
        "total": len(templates),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/data-sources")
async def get_data_sources(
    current_user: User = Depends(get_current_user)
):
    """获取可用数据源"""
    data_sources = {
        "akshare": {
            "name": "AkShare",
            "description": "开源的金融数据接口库，提供股票、指数、期货等数据",
            "supported_markets": ["A股", "港股", "美股"],
            "supported_frequencies": ["daily"],
            "features": ["实时数据", "历史数据", "财务数据", "新闻数据"],
            "availability": True
        },
        "tushare": {
            "name": "Tushare",
            "description": "财经数据接口包，需要token",
            "supported_markets": ["A股"],
            "supported_frequencies": ["daily", "weekly", "monthly"],
            "features": ["历史数据", "财务数据", "基本面数据"],
            "availability": False,
            "note": "需要配置token"
        },
        "local_csv": {
            "name": "本地CSV文件",
            "description": "从本地CSV文件读取数据",
            "supported_markets": ["自定义"],
            "supported_frequencies": ["自定义"],
            "features": ["历史数据"],
            "availability": True
        }
    }
    
    return {
        "success": True,
        "data": data_sources,
        "total": len(data_sources),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/results/{backtest_id}/visualization")
async def get_backtest_visualization(
    backtest_id: str,
    chart_type: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """获取回测结果可视化（此端点需要结合数据库存储实现）"""
    # 这里应该从数据库中获取回测结果
    # 目前返回示例数据
    return {
        "success": True,
        "message": "此功能需要配合数据库存储实现",
        "data": {
            "backtest_id": backtest_id,
            "chart_type": chart_type,
            "available_charts": [
                "equity_curve",
                "drawdown_chart", 
                "return_distribution",
                "monthly_returns_heatmap",
                "rolling_performance",
                "trade_analysis",
                "risk_radar",
                "position_allocation"
            ]
        },
        "timestamp": datetime.now().isoformat()
    }


@router.get("/system/status")
async def get_system_status(
    current_user: User = Depends(get_current_user),
    engine: IntegratedBacktestEngine = Depends(get_integrated_backtest_engine)
):
    """获取系统状态"""
    try:
        # 检查各组件状态
        from ...services.akshare_data_source import get_akshare_client
        
        akshare_client = await get_akshare_client()
        akshare_available = await akshare_client.is_available()
        
        system_status = {
            "engine_status": "ready",
            "akshare_available": akshare_available,
            "visualization_available": True,  # pyecharts 可用性已在可视化器中检查
            "analyzer_available": True,
            "components": {
                "backtest_engine": "ready",
                "data_source": "ready" if akshare_available else "limited",
                "visualizer": "ready",
                "analyzer": "ready"
            },
            "features": {
                "real_data": akshare_available,
                "mock_data": True,
                "visualization": True,
                "advanced_analysis": True,
                "batch_backtest": True
            }
        }
        
        return {
            "success": True,
            "data": system_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")


def _validate_backtest_request(request: BacktestRequest):
    """验证回测请求参数"""
    # 验证策略配置
    if not request.strategy_config:
        raise ValueError("策略配置不能为空")
    
    if "type" not in request.strategy_config:
        raise ValueError("策略类型不能为空")
    
    # 验证数据配置
    if not request.data_config:
        raise ValueError("数据配置不能为空")
    
    if "symbols" not in request.data_config or not request.data_config["symbols"]:
        raise ValueError("股票代码列表不能为空")
    
    # 验证日期格式
    try:
        if "start_date" in request.data_config:
            datetime.strptime(request.data_config["start_date"], "%Y-%m-%d")
        if "end_date" in request.data_config:
            datetime.strptime(request.data_config["end_date"], "%Y-%m-%d")
    except ValueError:
        raise ValueError("日期格式应为 YYYY-MM-DD")
    
    # 验证回测配置
    if request.backtest_config:
        if "initial_capital" in request.backtest_config:
            if request.backtest_config["initial_capital"] <= 0:
                raise ValueError("初始资金必须大于0")