/**
 * 量化投资平台全面功能检查脚本
 * 检查所有页面、按钮、跳转和功能
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class PlatformChecker {
    constructor() {
        this.baseUrl = 'http://localhost:5174';
        this.results = {
            pages: {},
            buttons: {},
            navigation: {},
            forms: {},
            api_calls: {},
            errors: [],
            summary: {}
        };
        this.screenshots = [];
    }

    async init() {
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        
        // 设置用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
        
        // 监听控制台错误
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.results.errors.push({
                    type: 'console_error',
                    message: msg.text(),
                    timestamp: new Date().toISOString()
                });
            }
        });

        // 监听页面错误
        this.page.on('pageerror', error => {
            this.results.errors.push({
                type: 'page_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        });
    }

    async checkPage(pageName, url, expectedElements = []) {
        console.log(`\n📊 检查页面: ${pageName}`);
        
        try {
            await this.page.goto(url, { waitUntil: 'networkidle0', timeout: 10000 });
            await this.page.waitForTimeout(2000);

            const pageInfo = {
                url: url,
                title: await this.page.title(),
                loaded: true,
                elements: {},
                buttons: [],
                forms: [],
                navigation: [],
                timestamp: new Date().toISOString()
            };

            // 截图
            const screenshotPath = `screenshots/${pageName.replace(/[^a-zA-Z0-9]/g, '_')}.png`;
            await this.page.screenshot({ path: screenshotPath, fullPage: true });
            this.screenshots.push(screenshotPath);

            // 检查页面基本元素
            const h1Exists = await this.page.$('h1') !== null;
            const navExists = await this.page.$('nav, .nav, .navbar') !== null;
            
            pageInfo.elements.hasTitle = h1Exists;
            pageInfo.elements.hasNavigation = navExists;

            // 检查所有按钮
            const buttons = await this.page.$$eval('button, .el-button, .btn, [role="button"]', buttons => 
                buttons.map((btn, index) => ({
                    index,
                    text: btn.textContent?.trim() || '',
                    className: btn.className || '',
                    disabled: btn.disabled,
                    visible: btn.offsetParent !== null,
                    id: btn.id || `button_${index}`
                }))
            );

            pageInfo.buttons = buttons;

            // 检查表单
            const forms = await this.page.$$eval('form, .el-form', forms =>
                forms.map((form, index) => ({
                    index,
                    className: form.className || '',
                    inputs: form.querySelectorAll('input, select, textarea').length,
                    id: form.id || `form_${index}`
                }))
            );

            pageInfo.forms = forms;

            // 检查导航链接
            const navLinks = await this.page.$$eval('a, .router-link', links =>
                links.map((link, index) => ({
                    index,
                    text: link.textContent?.trim() || '',
                    href: link.href || link.getAttribute('to') || '',
                    visible: link.offsetParent !== null
                }))
            );

            pageInfo.navigation = navLinks;

            // 检查预期元素
            for (const selector of expectedElements) {
                const exists = await this.page.$(selector) !== null;
                pageInfo.elements[selector] = exists;
            }

            this.results.pages[pageName] = pageInfo;
            console.log(`✅ ${pageName} 页面检查完成 - 找到 ${buttons.length} 个按钮, ${forms.length} 个表单`);

            return pageInfo;

        } catch (error) {
            console.log(`❌ ${pageName} 页面检查失败: ${error.message}`);
            this.results.pages[pageName] = {
                url: url,
                loaded: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
            return null;
        }
    }

    async testButtonFunctionality(pageName, buttonSelector, expectedAction = null) {
        console.log(`🔘 测试按钮: ${buttonSelector} (${pageName})`);
        
        try {
            await this.page.waitForSelector(buttonSelector, { timeout: 5000 });
            
            const buttonInfo = await this.page.$eval(buttonSelector, btn => ({
                text: btn.textContent?.trim() || '',
                disabled: btn.disabled,
                visible: btn.offsetParent !== null,
                className: btn.className || ''
            }));

            if (buttonInfo.disabled) {
                console.log(`⚠️  按钮被禁用: ${buttonSelector}`);
                return { success: false, reason: 'disabled' };
            }

            // 点击按钮
            await this.page.click(buttonSelector);
            await this.page.waitForTimeout(1000);

            // 检查是否有模态框或弹出窗口出现
            const modalExists = await this.page.$('.el-dialog, .modal, .popup') !== null;
            const alertExists = await this.page.$('.el-message, .alert, .notification') !== null;

            const result = {
                success: true,
                buttonInfo,
                modalAppeared: modalExists,
                alertAppeared: alertExists,
                timestamp: new Date().toISOString()
            };

            if (!this.results.buttons[pageName]) {
                this.results.buttons[pageName] = [];
            }
            this.results.buttons[pageName].push({
                selector: buttonSelector,
                result: result
            });

            console.log(`✅ 按钮点击成功 - 模态框: ${modalExists}, 提示: ${alertExists}`);
            return result;

        } catch (error) {
            console.log(`❌ 按钮测试失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    async testFormSubmission(pageName, formSelector, formData = {}) {
        console.log(`📝 测试表单: ${formSelector} (${pageName})`);
        
        try {
            await this.page.waitForSelector(formSelector, { timeout: 5000 });

            // 填充表单数据
            for (const [fieldName, value] of Object.entries(formData)) {
                const inputSelector = `${formSelector} input[name="${fieldName}"], ${formSelector} #${fieldName}`;
                const inputExists = await this.page.$(inputSelector) !== null;
                
                if (inputExists) {
                    await this.page.type(inputSelector, value);
                }
            }

            // 查找提交按钮
            const submitButton = await this.page.$(`${formSelector} button[type="submit"], ${formSelector} .submit-btn`);
            
            if (submitButton) {
                await submitButton.click();
                await this.page.waitForTimeout(2000);

                const result = {
                    success: true,
                    formData,
                    submitted: true,
                    timestamp: new Date().toISOString()
                };

                if (!this.results.forms[pageName]) {
                    this.results.forms[pageName] = [];
                }
                this.results.forms[pageName].push({
                    selector: formSelector,
                    result: result
                });

                console.log(`✅ 表单提交测试完成`);
                return result;
            } else {
                console.log(`⚠️  未找到提交按钮`);
                return { success: false, reason: 'no_submit_button' };
            }

        } catch (error) {
            console.log(`❌ 表单测试失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    async testNavigation(fromPage, toPage, linkSelector) {
        console.log(`🔗 测试导航: ${fromPage} -> ${toPage}`);
        
        try {
            await this.page.click(linkSelector);
            await this.page.waitForTimeout(2000);

            const currentUrl = this.page.url();
            const navigationSuccess = currentUrl.includes(toPage.toLowerCase()) || 
                                    await this.page.$(`[data-page="${toPage}"]`) !== null;

            const result = {
                success: navigationSuccess,
                fromPage,
                toPage,
                linkSelector,
                finalUrl: currentUrl,
                timestamp: new Date().toISOString()
            };

            if (!this.results.navigation[fromPage]) {
                this.results.navigation[fromPage] = [];
            }
            this.results.navigation[fromPage].push(result);

            console.log(`${navigationSuccess ? '✅' : '❌'} 导航测试: ${fromPage} -> ${toPage}`);
            return result;

        } catch (error) {
            console.log(`❌ 导航测试失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    async runComprehensiveCheck() {
        console.log('🚀 开始量化投资平台全面功能检查...\n');

        // 创建截图目录
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        // 定义要检查的页面
        const pagesToCheck = [
            {
                name: '首页/仪表盘',
                path: '/',
                expectedElements: ['.dashboard', '.metric-card', '.chart-container']
            },
            {
                name: '登录页面',
                path: '/login',
                expectedElements: ['.login-form', 'input[name="username"]', 'input[name="password"]']
            },
            {
                name: '市场数据',
                path: '/market',
                expectedElements: ['.market-table', '.stock-list', '.price-column']
            },
            {
                name: '交易中心',
                path: '/trading',
                expectedElements: ['.trading-panel', '.order-form', '.position-list']
            },
            {
                name: '订单管理',
                path: '/trading/orders',
                expectedElements: ['.order-table', '.order-filters', '.export-btn']
            },
            {
                name: '持仓管理',
                path: '/trading/positions',
                expectedElements: ['.position-table', '.position-summary']
            },
            {
                name: '策略中心',
                path: '/strategy',
                expectedElements: ['.strategy-list', '.create-strategy-btn']
            },
            {
                name: '策略开发',
                path: '/strategy/develop',
                expectedElements: ['.code-editor', '.strategy-form']
            },
            {
                name: '策略监控',
                path: '/strategy/monitor',
                expectedElements: ['.strategy-monitor', '.performance-chart']
            },
            {
                name: '回测分析',
                path: '/backtest',
                expectedElements: ['.backtest-form', '.result-chart']
            },
            {
                name: '投资组合',
                path: '/portfolio',
                expectedElements: ['.portfolio-overview', '.asset-allocation']
            },
            {
                name: '风险管理',
                path: '/risk',
                expectedElements: ['.risk-metrics', '.risk-alerts']
            },
            {
                name: '设置页面',
                path: '/settings',
                expectedElements: ['.settings-form', '.user-profile']
            }
        ];

        // 检查每个页面
        for (const pageConfig of pagesToCheck) {
            const url = `${this.baseUrl}${pageConfig.path}`;
            await this.checkPage(pageConfig.name, url, pageConfig.expectedElements);
            
            // 稍作等待
            await this.page.waitForTimeout(1000);
        }

        // 专项功能测试
        await this.testSpecificFunctionality();

        // 生成测试报告
        await this.generateReport();
    }

    async testSpecificFunctionality() {
        console.log('\n🔧 开始专项功能测试...\n');

        // 测试登录功能
        try {
            await this.page.goto(`${this.baseUrl}/login`);
            await this.testFormSubmission('登录页面', '.login-form', {
                username: 'testuser',
                password: 'test123456'
            });
        } catch (error) {
            console.log(`登录测试失败: ${error.message}`);
        }

        // 测试主要按钮功能
        const buttonTests = [
            { page: '交易中心', selector: '.buy-btn', url: '/trading' },
            { page: '交易中心', selector: '.sell-btn', url: '/trading' },
            { page: '策略中心', selector: '.create-strategy-btn', url: '/strategy' },
            { page: '订单管理', selector: '.export-btn', url: '/trading/orders' },
            { page: '风险管理', selector: '.set-limits-btn', url: '/risk' }
        ];

        for (const test of buttonTests) {
            try {
                await this.page.goto(`${this.baseUrl}${test.url}`);
                await this.testButtonFunctionality(test.page, test.selector);
            } catch (error) {
                console.log(`按钮测试失败 (${test.page}): ${error.message}`);
            }
        }

        // 测试页面导航
        const navigationTests = [
            { from: '首页', to: '市场数据', selector: 'a[href="/market"]' },
            { from: '首页', to: '交易中心', selector: 'a[href="/trading"]' },
            { from: '首页', to: '策略中心', selector: 'a[href="/strategy"]' }
        ];

        for (const test of navigationTests) {
            try {
                await this.page.goto(`${this.baseUrl}/`);
                await this.testNavigation(test.from, test.to, test.selector);
            } catch (error) {
                console.log(`导航测试失败: ${error.message}`);
            }
        }
    }

    async generateReport() {
        // 计算统计信息
        const totalPages = Object.keys(this.results.pages).length;
        const loadedPages = Object.values(this.results.pages).filter(p => p.loaded).length;
        const totalButtons = Object.values(this.results.buttons).reduce((sum, buttons) => sum + buttons.length, 0);
        const totalForms = Object.values(this.results.forms).reduce((sum, forms) => sum + forms.length, 0);
        const totalNavigation = Object.values(this.results.navigation).reduce((sum, nav) => sum + nav.length, 0);
        const totalErrors = this.results.errors.length;

        this.results.summary = {
            totalPages,
            loadedPages,
            pageLoadRate: `${((loadedPages / totalPages) * 100).toFixed(1)}%`,
            totalButtons,
            totalForms,
            totalNavigation,
            totalErrors,
            screenshotCount: this.screenshots.length,
            checkCompleted: new Date().toISOString()
        };

        // 生成HTML报告
        const reportHtml = this.generateHtmlReport();
        fs.writeFileSync('platform_check_report.html', reportHtml);

        // 生成JSON报告
        fs.writeFileSync('platform_check_results.json', JSON.stringify(this.results, null, 2));

        console.log('\n📊 检查完成统计:');
        console.log(`✅ 页面检查: ${loadedPages}/${totalPages} (${this.results.summary.pageLoadRate})`);
        console.log(`🔘 按钮测试: ${totalButtons} 个`);
        console.log(`📝 表单测试: ${totalForms} 个`);
        console.log(`🔗 导航测试: ${totalNavigation} 个`);
        console.log(`❌ 错误数量: ${totalErrors} 个`);
        console.log(`📸 截图数量: ${this.screenshots.length} 张`);
        console.log('\n📄 报告已生成:');
        console.log('- platform_check_report.html (HTML格式)');
        console.log('- platform_check_results.json (JSON格式)');
    }

    generateHtmlReport() {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台功能检查报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; }
        .stat-label { opacity: 0.9; margin-top: 5px; }
        .page-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .page-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #fafafa; }
        .page-card.success { border-left: 4px solid #27ae60; }
        .page-card.error { border-left: 4px solid #e74c3c; }
        .button-list, .form-list { margin-top: 10px; }
        .button-item, .form-item { background: white; margin: 5px 0; padding: 8px; border-radius: 4px; font-size: 0.9em; }
        .error-list { background: #fee; border: 1px solid #fcc; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .error-item { margin: 5px 0; padding: 5px; background: white; border-radius: 3px; }
        .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .screenshot-item { text-align: center; }
        .screenshot-item img { max-width: 100%; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 量化投资平台功能检查报告</h1>
        <p><strong>检查时间:</strong> ${this.results.summary.checkCompleted}</p>
        
        <div class="summary">
            <div class="stat-card">
                <div class="stat-value">${this.results.summary.loadedPages}/${this.results.summary.totalPages}</div>
                <div class="stat-label">页面加载成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${this.results.summary.totalButtons}</div>
                <div class="stat-label">按钮测试数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${this.results.summary.totalForms}</div>
                <div class="stat-label">表单测试数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${this.results.summary.totalErrors}</div>
                <div class="stat-label">发现错误数量</div>
            </div>
        </div>

        <h2>📊 页面检查结果</h2>
        <div class="page-grid">
            ${Object.entries(this.results.pages).map(([name, info]) => `
                <div class="page-card ${info.loaded ? 'success' : 'error'}">
                    <h3>${name}</h3>
                    <p><strong>URL:</strong> ${info.url}</p>
                    <p><strong>状态:</strong> ${info.loaded ? '✅ 加载成功' : '❌ 加载失败'}</p>
                    ${info.loaded ? `
                        <p><strong>标题:</strong> ${info.title || '无'}</p>
                        <p><strong>按钮数量:</strong> ${info.buttons?.length || 0}</p>
                        <p><strong>表单数量:</strong> ${info.forms?.length || 0}</p>
                        <p><strong>导航链接:</strong> ${info.navigation?.length || 0}</p>
                    ` : `
                        <p><strong>错误:</strong> ${info.error}</p>
                    `}
                </div>
            `).join('')}
        </div>

        ${this.results.errors.length > 0 ? `
            <h2>❌ 错误列表</h2>
            <div class="error-list">
                ${this.results.errors.map(error => `
                    <div class="error-item">
                        <strong>${error.type}:</strong> ${error.message}
                        <br><small>${error.timestamp}</small>
                    </div>
                `).join('')}
            </div>
        ` : ''}

        <h2>📸 页面截图</h2>
        <div class="screenshot-grid">
            ${this.screenshots.map(screenshot => `
                <div class="screenshot-item">
                    <img src="${screenshot}" alt="页面截图">
                    <p>${path.basename(screenshot, '.png').replace(/_/g, ' ')}</p>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// 运行检查
async function main() {
    const checker = new PlatformChecker();
    
    try {
        await checker.init();
        await checker.runComprehensiveCheck();
    } catch (error) {
        console.error('检查过程中发生错误:', error);
    } finally {
        await checker.close();
    }
}

// 检查是否安装了必要的依赖
try {
    require('puppeteer');
    main();
} catch (error) {
    console.log('❌ 缺少依赖包，请先安装:');
    console.log('npm install puppeteer');
    console.log('\n或者使用:');
    console.log('yarn add puppeteer');
}