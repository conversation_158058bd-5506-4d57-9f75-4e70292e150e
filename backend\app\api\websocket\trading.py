"""
交易WebSocket处理器
提供实时订单状态、成交回报和持仓更新
"""

import json
import logging
from typing import Any, Dict

from fastapi import Depends, WebSocket, WebSocketDisconnect
from fastapi.routing import APIRouter

from app.models.user import User
from app.schemas.trading import OrderRequest
from app.services.risk_service import RiskService
from app.services.trading_service import TradingService

from .connection import WebSocketManager

logger = logging.getLogger(__name__)
router = APIRouter()

# WebSocket管理器实例
ws_manager = WebSocketManager()


@router.websocket("/trading/{user_id}")
async def trading_websocket_endpoint(websocket: WebSocket, user_id: int):
    """交易WebSocket端点"""
    try:
        # 建立连接
        await ws_manager.connect(websocket, user_id)

        # 发送初始数据
        await send_initial_trading_data(websocket, user_id, trading_service)

        # 处理消息循环
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                # 处理不同类型的消息
                await handle_trading_message(
                    websocket, user_id, message, trading_service, risk_service
                )

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await ws_manager.send_to_connection(
                    websocket, {"type": "error", "message": "消息格式错误"}
                )
            except Exception as e:
                logger.error(f"处理交易WebSocket消息失败: {e}")
                await ws_manager.send_to_connection(
                    websocket, {"type": "error", "message": f"处理消息失败: {str(e)}"}
                )

    except Exception as e:
        logger.error(f"交易WebSocket连接失败: {e}")
    finally:
        await ws_manager.disconnect(websocket)


async def send_initial_trading_data(
    websocket: WebSocket, user_id: int, trading_service: TradingService
):
    """发送初始交易数据"""
    try:
        # 获取活跃订单
        active_orders = await trading_service.get_active_orders(user_id)
        await ws_manager.send_to_connection(
            websocket,
            {
                "type": "active_orders",
                "data": [order.dict() for order in active_orders],
            },
        )

        # 获取持仓信息
        positions = await trading_service.get_positions(user_id)
        await ws_manager.send_to_connection(
            websocket,
            {"type": "positions", "data": [position.dict() for position in positions]},
        )

        # 获取账户信息
        account_info = await trading_service.get_account_info(user_id)
        await ws_manager.send_to_connection(
            websocket,
            {
                "type": "account_info",
                "data": account_info.dict() if account_info else None,
            },
        )

    except Exception as e:
        logger.error(f"发送初始交易数据失败: {e}")


async def handle_trading_message(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
    risk_service: RiskService,
):
    """处理交易相关消息"""
    message_type = message.get("type")

    if message_type == "subscribe":
        # 订阅交易更新
        topics = message.get("topics", [])
        for topic in topics:
            await ws_manager.subscribe(websocket, f"trading.{topic}.{user_id}")

    elif message_type == "unsubscribe":
        # 取消订阅
        topics = message.get("topics", [])
        for topic in topics:
            await ws_manager.unsubscribe(websocket, f"trading.{topic}.{user_id}")

    elif message_type == "place_order":
        # 下单请求
        await handle_place_order(
            websocket, user_id, message, trading_service, risk_service
        )

    elif message_type == "cancel_order":
        # 撤单请求
        await handle_cancel_order(websocket, user_id, message, trading_service)

    elif message_type == "modify_order":
        # 改单请求
        await handle_modify_order(
            websocket, user_id, message, trading_service, risk_service
        )

    elif message_type == "query_orders":
        # 查询订单
        await handle_query_orders(websocket, user_id, message, trading_service)

    elif message_type == "query_positions":
        # 查询持仓
        await handle_query_positions(websocket, user_id, message, trading_service)

    elif message_type == "query_trades":
        # 查询成交
        await handle_query_trades(websocket, user_id, message, trading_service)

    else:
        await ws_manager.send_to_connection(
            websocket, {"type": "error", "message": f"未知消息类型: {message_type}"}
        )


async def handle_place_order(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
    risk_service: RiskService,
):
    """处理下单请求"""
    try:
        order_data = message.get("data", {})

        # 创建订单请求
        order_request = OrderRequest(**order_data, user_id=user_id)

        # 风险检查
        risk_check = await risk_service.check_order_risk(order_request)
        if not risk_check.passed:
            await ws_manager.send_to_connection(
                websocket,
                {
                    "type": "order_rejected",
                    "data": {
                        "reason": risk_check.reason,
                        "details": risk_check.details,
                    },
                },
            )
            return

        # 提交订单
        order = await trading_service.place_order(order_request)

        # 发送订单确认
        await ws_manager.send_to_connection(
            websocket, {"type": "order_placed", "data": order.dict()}
        )

        # 广播订单更新给用户的所有连接
        await ws_manager.broadcast_to_topic(
            f"trading.orders.{user_id}", {"type": "order_update", "data": order.dict()}
        )

    except Exception as e:
        logger.error(f"下单处理失败: {e}")
        await ws_manager.send_to_connection(
            websocket, {"type": "order_error", "message": f"下单失败: {str(e)}"}
        )


async def handle_cancel_order(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
):
    """处理撤单请求"""
    try:
        order_id = message.get("order_id")
        if not order_id:
            await ws_manager.send_to_connection(
                websocket, {"type": "error", "message": "缺少订单ID"}
            )
            return

        # 撤销订单
        result = await trading_service.cancel_order(user_id, order_id)

        if result:
            await ws_manager.send_to_connection(
                websocket, {"type": "order_cancelled", "data": {"order_id": order_id}}
            )

            # 广播撤单更新
            await ws_manager.broadcast_to_topic(
                f"trading.orders.{user_id}",
                {"type": "order_cancelled", "data": {"order_id": order_id}},
            )
        else:
            await ws_manager.send_to_connection(
                websocket, {"type": "cancel_failed", "message": "撤单失败"}
            )

    except Exception as e:
        logger.error(f"撤单处理失败: {e}")
        await ws_manager.send_to_connection(
            websocket, {"type": "cancel_error", "message": f"撤单失败: {str(e)}"}
        )


async def handle_modify_order(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
    risk_service: RiskService,
):
    """处理改单请求"""
    try:
        order_data = message.get("data", {})
        order_id = message.get("order_id")

        # 创建订单请求
        order_request = OrderRequest(**order_data, user_id=user_id)

        # 风险检查
        risk_check = await risk_service.check_order_risk(order_request)
        if not risk_check.passed:
            await ws_manager.send_to_connection(
                websocket,
                {"type": "modify_rejected", "data": {"reason": risk_check.reason}},
            )
            return

        # 修改订单
        modified_order = await trading_service.modify_order(
            user_id, order_id, order_request
        )

        await ws_manager.send_to_connection(
            websocket, {"type": "order_modified", "data": modified_order.dict()}
        )

    except Exception as e:
        logger.error(f"改单处理失败: {e}")
        await ws_manager.send_to_connection(
            websocket, {"type": "modify_error", "message": f"改单失败: {str(e)}"}
        )


async def handle_query_orders(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
):
    """处理订单查询请求"""
    try:
        status = message.get("status", "active")
        orders = await trading_service.get_orders_by_status(user_id, status)

        await ws_manager.send_to_connection(
            websocket,
            {"type": "order_list", "data": [order.dict() for order in orders]},
        )

    except Exception as e:
        logger.error(f"查询订单失败: {e}")
        await ws_manager.send_to_connection(
            websocket, {"type": "query_error", "message": "查询订单失败"}
        )


async def handle_query_positions(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
):
    """处理持仓查询请求"""
    try:
        positions = await trading_service.get_positions(user_id)

        await ws_manager.send_to_connection(
            websocket, {"type": "position_list", "data": [p.dict() for p in positions]}
        )

    except Exception as e:
        logger.error(f"查询持仓失败: {e}")
        await ws_manager.send_to_connection(
            websocket, {"type": "query_error", "message": "查询持仓失败"}
        )


async def handle_query_trades(
    websocket: WebSocket,
    user_id: int,
    message: Dict[str, Any],
    trading_service: TradingService,
):
    """处理成交查询请求"""
    try:
        limit = message.get("limit", 100)
        trades = await trading_service.get_trades(user_id, limit=limit)

        await ws_manager.send_to_connection(
            websocket, {"type": "trade_list", "data": [t.dict() for t in trades]}
        )

    except Exception as e:
        logger.error(f"查询成交失败: {e}")
        await ws_manager.send_to_connection(
            websocket, {"type": "query_error", "message": "查询成交失败"}
        )


async def push_order_update(user_id: int, order_data: Dict[str, Any]):
    """推送订单更新"""
    await ws_manager.broadcast_to_topic(
        f"trading.orders.{user_id}", {"type": "order_update", "data": order_data}
    )


async def push_trade_update(user_id: int, trade_data: Dict[str, Any]):
    """推送成交更新"""
    await ws_manager.broadcast_to_topic(
        f"trading.trades.{user_id}", {"type": "trade_update", "data": trade_data}
    )


async def push_position_update(user_id: int, position_data: Dict[str, Any]):
    """推送持仓更新"""
    await ws_manager.broadcast_to_topic(
        f"trading.positions.{user_id}",
        {"type": "position_update", "data": position_data},
    )


async def push_account_update(user_id: int, account_data: Dict[str, Any]):
    """推送账户更新"""
    await ws_manager.broadcast_to_topic(
        f"trading.account.{user_id}", {"type": "account_update", "data": account_data}
    )


async def push_risk_alert(user_id: int, alert_data: Dict[str, Any]):
    """推送风控警报"""
    await ws_manager.broadcast_to_topic(
        f"trading.risk.{user_id}", {"type": "risk_alert", "data": alert_data}
    )
