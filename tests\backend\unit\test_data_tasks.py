"""
数据任务单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal
import pandas as pd

from app.tasks.data_tasks import (
    sync_market_data,
    update_historical_data,
    process_realtime_data,
    validate_market_data,
    cleanup_expired_data,
    backup_market_data,
    DataSyncConfig,
    DataSource,
    DataStatus,
    MarketDataProcessor,
    HistoricalDataUpdater,
    RealTimeDataProcessor,
    DataValidator,
    DataBackupManager,
)


@pytest.mark.unit
@pytest.mark.data_tasks
class TestDataTasks:
    """数据任务测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = AsyncMock()
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.close = AsyncMock()
        return session

    @pytest.fixture
    def mock_redis(self):
        """模拟Redis连接"""
        redis = AsyncMock()
        redis.get = AsyncMock(return_value=None)
        redis.set = AsyncMock()
        redis.delete = AsyncMock()
        redis.publish = AsyncMock()
        redis.hset = AsyncMock()
        redis.hget = AsyncMock()
        return redis

    @pytest.fixture
    def sample_tick_data(self):
        """样本Tick数据"""
        return {
            "symbol": "rb2405",
            "exchange": "SHFE",
            "last_price": Decimal("3850.0"),
            "bid_price": Decimal("3849.0"),
            "ask_price": Decimal("3851.0"),
            "volume": 12345,
            "turnover": Decimal("47532750.0"),
            "timestamp": datetime.now(),
        }

    @pytest.fixture
    def sample_kline_data(self):
        """样本K线数据"""
        return pd.DataFrame(
            {
                "symbol": ["rb2405"] * 100,
                "datetime": pd.date_range("2024-01-01", periods=100, freq="1min"),
                "open": [3800 + i for i in range(100)],
                "high": [3805 + i for i in range(100)],
                "low": [3795 + i for i in range(100)],
                "close": [3800 + i for i in range(100)],
                "volume": [1000 + i * 10 for i in range(100)],
            }
        )

    @pytest.fixture
    def data_sync_config(self):
        """数据同步配置"""
        return DataSyncConfig(
            source=DataSource.TUSHARE,
            symbols=["rb2405", "hc2405", "i2405"],
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            frequency="1min",
            batch_size=1000,
            retry_count=3,
            timeout=30,
        )

    @pytest.fixture
    def market_data_processor(self):
        """市场数据处理器"""
        return MarketDataProcessor()

    @pytest.fixture
    def historical_data_updater(self):
        """历史数据更新器"""
        return HistoricalDataUpdater()

    @pytest.fixture
    def realtime_data_processor(self):
        """实时数据处理器"""
        return RealTimeDataProcessor()

    @pytest.fixture
    def data_validator(self):
        """数据验证器"""
        return DataValidator()

    @pytest.fixture
    def backup_manager(self):
        """备份管理器"""
        return DataBackupManager()

    @pytest.mark.asyncio
    async def test_sync_market_data(
        self, mock_db_session, mock_redis, data_sync_config
    ):
        """测试市场数据同步"""
        # 模拟外部数据源
        mock_data_source = AsyncMock()
        mock_data_source.fetch_data.return_value = pd.DataFrame(
            {
                "symbol": ["rb2405"] * 10,
                "datetime": pd.date_range("2024-01-01", periods=10, freq="1min"),
                "close": range(3800, 3810),
            }
        )

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch("app.tasks.data_tasks.get_redis", return_value=mock_redis):
                with patch(
                    "app.tasks.data_tasks.get_data_source",
                    return_value=mock_data_source,
                ):
                    result = await sync_market_data(data_sync_config)

        assert result is not None
        assert "status" in result
        assert result["status"] == DataStatus.COMPLETED
        assert "processed_count" in result

        # 验证数据源被调用
        mock_data_source.fetch_data.assert_called()

    @pytest.mark.asyncio
    async def test_update_historical_data(
        self, mock_db_session, mock_redis, sample_kline_data
    ):
        """测试历史数据更新"""
        symbols = ["rb2405", "hc2405"]
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)

        # 模拟数据源
        mock_data_source = AsyncMock()
        mock_data_source.fetch_historical_data.return_value = sample_kline_data

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch("app.tasks.data_tasks.get_redis", return_value=mock_redis):
                with patch(
                    "app.tasks.data_tasks.get_data_source",
                    return_value=mock_data_source,
                ):
                    result = await update_historical_data(symbols, start_date, end_date)

        assert result is not None
        assert "status" in result
        assert result["status"] == DataStatus.COMPLETED
        assert "updated_symbols" in result
        assert len(result["updated_symbols"]) == len(symbols)

    @pytest.mark.asyncio
    async def test_process_realtime_data(
        self, mock_db_session, mock_redis, sample_tick_data
    ):
        """测试实时数据处理"""
        data_batch = [sample_tick_data] * 10

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch("app.tasks.data_tasks.get_redis", return_value=mock_redis):
                result = await process_realtime_data(data_batch)

        assert result is not None
        assert "status" in result
        assert result["status"] == DataStatus.COMPLETED
        assert "processed_count" in result
        assert result["processed_count"] == len(data_batch)

    @pytest.mark.asyncio
    async def test_validate_market_data(self, mock_db_session, sample_kline_data):
        """测试市场数据验证"""
        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            result = await validate_market_data(sample_kline_data)

        assert result is not None
        assert "status" in result
        assert "validation_errors" in result
        assert "valid_count" in result
        assert "invalid_count" in result

    @pytest.mark.asyncio
    async def test_cleanup_expired_data(self, mock_db_session):
        """测试清理过期数据"""
        retention_days = 90

        # 模拟删除结果
        mock_result = Mock()
        mock_result.rowcount = 1000
        mock_db_session.execute.return_value = mock_result

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            result = await cleanup_expired_data(retention_days)

        assert result is not None
        assert "status" in result
        assert result["status"] == DataStatus.COMPLETED
        assert "deleted_count" in result
        assert result["deleted_count"] == 1000

    @pytest.mark.asyncio
    async def test_backup_market_data(self, mock_db_session, backup_manager):
        """测试市场数据备份"""
        backup_date = datetime(2024, 1, 15)

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch.object(backup_manager, "create_backup", return_value=True):
                result = await backup_market_data(backup_date)

        assert result is not None
        assert "status" in result
        assert result["status"] == DataStatus.COMPLETED
        assert "backup_file" in result

    def test_market_data_processor_process_tick(
        self, market_data_processor, sample_tick_data
    ):
        """测试市场数据处理器处理Tick数据"""
        processed_data = market_data_processor.process_tick_data(sample_tick_data)

        assert processed_data is not None
        assert "symbol" in processed_data
        assert "last_price" in processed_data
        assert "timestamp" in processed_data
        assert processed_data["symbol"] == sample_tick_data["symbol"]

    def test_market_data_processor_process_kline(
        self, market_data_processor, sample_kline_data
    ):
        """测试市场数据处理器处理K线数据"""
        processed_data = market_data_processor.process_kline_data(sample_kline_data)

        assert processed_data is not None
        assert isinstance(processed_data, pd.DataFrame)
        assert len(processed_data) == len(sample_kline_data)
        assert "symbol" in processed_data.columns
        assert "close" in processed_data.columns

    def test_market_data_processor_calculate_indicators(
        self, market_data_processor, sample_kline_data
    ):
        """测试市场数据处理器计算指标"""
        indicators = market_data_processor.calculate_technical_indicators(
            sample_kline_data
        )

        assert indicators is not None
        assert isinstance(indicators, dict)
        assert "sma_20" in indicators
        assert "ema_12" in indicators
        assert "rsi_14" in indicators

    def test_historical_data_updater_fetch_data(self, historical_data_updater):
        """测试历史数据更新器获取数据"""
        symbol = "rb2405"
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)

        with patch.object(historical_data_updater, "_fetch_from_source") as mock_fetch:
            mock_fetch.return_value = pd.DataFrame(
                {
                    "datetime": pd.date_range(start_date, end_date, freq="1D"),
                    "close": range(100),
                }
            )

            data = historical_data_updater.fetch_historical_data(
                symbol, start_date, end_date
            )

        assert data is not None
        assert isinstance(data, pd.DataFrame)
        assert len(data) > 0

    def test_historical_data_updater_merge_data(self, historical_data_updater):
        """测试历史数据更新器合并数据"""
        existing_data = pd.DataFrame(
            {
                "datetime": pd.date_range("2024-01-01", periods=10, freq="1D"),
                "close": range(100, 110),
            }
        )

        new_data = pd.DataFrame(
            {
                "datetime": pd.date_range("2024-01-06", periods=10, freq="1D"),
                "close": range(105, 115),
            }
        )

        merged_data = historical_data_updater.merge_data(existing_data, new_data)

        assert merged_data is not None
        assert isinstance(merged_data, pd.DataFrame)
        assert len(merged_data) >= len(existing_data)

    def test_realtime_data_processor_process_batch(
        self, realtime_data_processor, sample_tick_data
    ):
        """测试实时数据处理器批量处理"""
        data_batch = [sample_tick_data] * 100

        processed_batch = realtime_data_processor.process_batch(data_batch)

        assert processed_batch is not None
        assert isinstance(processed_batch, list)
        assert len(processed_batch) == len(data_batch)

    def test_realtime_data_processor_filter_data(
        self, realtime_data_processor, sample_tick_data
    ):
        """测试实时数据处理器过滤数据"""
        # 创建包含有效和无效数据的批次
        data_batch = [sample_tick_data] * 5
        invalid_data = sample_tick_data.copy()
        invalid_data["last_price"] = Decimal("0")  # 无效价格
        data_batch.append(invalid_data)

        filtered_data = realtime_data_processor.filter_valid_data(data_batch)

        assert filtered_data is not None
        assert isinstance(filtered_data, list)
        assert len(filtered_data) == 5  # 只有有效数据

    def test_realtime_data_processor_aggregate_data(
        self, realtime_data_processor, sample_tick_data
    ):
        """测试实时数据处理器聚合数据"""
        # 创建同一合约的多个Tick数据
        data_batch = []
        for i in range(10):
            tick = sample_tick_data.copy()
            tick["last_price"] = Decimal(str(3850 + i))
            tick["volume"] = 1000 + i * 100
            data_batch.append(tick)

        aggregated_data = realtime_data_processor.aggregate_data(data_batch, "1min")

        assert aggregated_data is not None
        assert "symbol" in aggregated_data
        assert "open" in aggregated_data
        assert "high" in aggregated_data
        assert "low" in aggregated_data
        assert "close" in aggregated_data
        assert "volume" in aggregated_data

    def test_data_validator_validate_tick_data(self, data_validator, sample_tick_data):
        """测试数据验证器验证Tick数据"""
        # 测试有效数据
        is_valid, errors = data_validator.validate_tick_data(sample_tick_data)
        assert is_valid is True
        assert len(errors) == 0

        # 测试无效数据
        invalid_data = sample_tick_data.copy()
        invalid_data["last_price"] = Decimal("-100")  # 负价格

        is_valid, errors = data_validator.validate_tick_data(invalid_data)
        assert is_valid is False
        assert len(errors) > 0

    def test_data_validator_validate_kline_data(
        self, data_validator, sample_kline_data
    ):
        """测试数据验证器验证K线数据"""
        validation_result = data_validator.validate_kline_data(sample_kline_data)

        assert validation_result is not None
        assert "valid_count" in validation_result
        assert "invalid_count" in validation_result
        assert "errors" in validation_result

    def test_data_validator_check_data_integrity(
        self, data_validator, sample_kline_data
    ):
        """测试数据验证器检查数据完整性"""
        integrity_result = data_validator.check_data_integrity(sample_kline_data)

        assert integrity_result is not None
        assert "missing_data_points" in integrity_result
        assert "duplicate_data_points" in integrity_result
        assert "data_gaps" in integrity_result

    def test_backup_manager_create_backup(self, backup_manager, sample_kline_data):
        """测试备份管理器创建备份"""
        backup_path = "/tmp/backup_test"

        with patch("os.makedirs"):
            with patch("pandas.DataFrame.to_parquet") as mock_to_parquet:
                result = backup_manager.create_data_backup(
                    sample_kline_data, backup_path
                )

        assert result is not None
        assert "backup_file" in result
        assert "backup_size" in result
        mock_to_parquet.assert_called_once()

    def test_backup_manager_restore_backup(self, backup_manager):
        """测试备份管理器恢复备份"""
        backup_file = "/tmp/backup_test.parquet"

        with patch("pandas.read_parquet") as mock_read_parquet:
            mock_read_parquet.return_value = pd.DataFrame({"test": [1, 2, 3]})

            restored_data = backup_manager.restore_from_backup(backup_file)

        assert restored_data is not None
        assert isinstance(restored_data, pd.DataFrame)
        mock_read_parquet.assert_called_once_with(backup_file)

    def test_backup_manager_verify_backup(self, backup_manager):
        """测试备份管理器验证备份"""
        backup_file = "/tmp/backup_test.parquet"

        with patch("os.path.exists", return_value=True):
            with patch("os.path.getsize", return_value=1024):
                with patch("pandas.read_parquet") as mock_read_parquet:
                    mock_read_parquet.return_value = pd.DataFrame({"test": [1, 2, 3]})

                    is_valid = backup_manager.verify_backup(backup_file)

        assert is_valid is True

    @pytest.mark.asyncio
    async def test_data_sync_with_retry(
        self, mock_db_session, mock_redis, data_sync_config
    ):
        """测试数据同步重试机制"""
        # 模拟第一次失败，第二次成功
        mock_data_source = AsyncMock()
        mock_data_source.fetch_data.side_effect = [
            Exception("Network error"),
            pd.DataFrame({"symbol": ["rb2405"], "close": [3800]}),
        ]

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch("app.tasks.data_tasks.get_redis", return_value=mock_redis):
                with patch(
                    "app.tasks.data_tasks.get_data_source",
                    return_value=mock_data_source,
                ):
                    result = await sync_market_data(data_sync_config)

        assert result is not None
        assert result["status"] == DataStatus.COMPLETED
        assert mock_data_source.fetch_data.call_count == 2

    @pytest.mark.asyncio
    async def test_data_sync_with_timeout(
        self, mock_db_session, mock_redis, data_sync_config
    ):
        """测试数据同步超时处理"""
        # 模拟超时
        mock_data_source = AsyncMock()
        mock_data_source.fetch_data.side_effect = asyncio.TimeoutError()

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch("app.tasks.data_tasks.get_redis", return_value=mock_redis):
                with patch(
                    "app.tasks.data_tasks.get_data_source",
                    return_value=mock_data_source,
                ):
                    result = await sync_market_data(data_sync_config)

        assert result is not None
        assert result["status"] == DataStatus.FAILED
        assert "timeout" in result["error"].lower()

    @pytest.mark.asyncio
    async def test_concurrent_data_processing(
        self, mock_db_session, mock_redis, sample_tick_data
    ):
        """测试并发数据处理"""
        # 创建多个数据批次
        data_batches = [[sample_tick_data] * 10 for _ in range(5)]

        with patch("app.tasks.data_tasks.get_db_session", return_value=mock_db_session):
            with patch("app.tasks.data_tasks.get_redis", return_value=mock_redis):
                # 并发处理多个批次
                tasks = [process_realtime_data(batch) for batch in data_batches]
                results = await asyncio.gather(*tasks)

        assert len(results) == 5
        for result in results:
            assert result["status"] == DataStatus.COMPLETED

    def test_data_compression(self, market_data_processor, sample_kline_data):
        """测试数据压缩"""
        compressed_data = market_data_processor.compress_data(sample_kline_data)

        assert compressed_data is not None
        assert len(compressed_data) < len(sample_kline_data.to_json())

    def test_data_decompression(self, market_data_processor, sample_kline_data):
        """测试数据解压缩"""
        compressed_data = market_data_processor.compress_data(sample_kline_data)
        decompressed_data = market_data_processor.decompress_data(compressed_data)

        assert decompressed_data is not None
        assert isinstance(decompressed_data, pd.DataFrame)
        assert len(decompressed_data) == len(sample_kline_data)

    def test_data_transformation(self, market_data_processor, sample_kline_data):
        """测试数据转换"""
        # 测试数据格式转换
        transformed_data = market_data_processor.transform_data_format(
            sample_kline_data, from_format="dataframe", to_format="dict"
        )

        assert transformed_data is not None
        assert isinstance(transformed_data, dict)

    def test_data_quality_check(self, data_validator, sample_kline_data):
        """测试数据质量检查"""
        quality_report = data_validator.check_data_quality(sample_kline_data)

        assert quality_report is not None
        assert "completeness" in quality_report
        assert "accuracy" in quality_report
        assert "consistency" in quality_report
        assert "timeliness" in quality_report

    def test_data_anomaly_detection(self, data_validator, sample_kline_data):
        """测试数据异常检测"""
        # 添加异常数据
        anomaly_data = sample_kline_data.copy()
        anomaly_data.loc[50, "close"] = 99999  # 异常价格

        anomalies = data_validator.detect_anomalies(anomaly_data)

        assert anomalies is not None
        assert len(anomalies) > 0
        assert 50 in anomalies  # 应该检测到第50行的异常

    def test_data_normalization(self, market_data_processor, sample_kline_data):
        """测试数据标准化"""
        normalized_data = market_data_processor.normalize_data(sample_kline_data)

        assert normalized_data is not None
        assert isinstance(normalized_data, pd.DataFrame)
        assert len(normalized_data) == len(sample_kline_data)

        # 检查数据是否标准化
        assert normalized_data["close"].mean() < 1.0
        assert normalized_data["close"].std() < 1.0

    def test_data_sampling(self, market_data_processor, sample_kline_data):
        """测试数据采样"""
        # 测试下采样
        downsampled_data = market_data_processor.downsample_data(
            sample_kline_data, "5min"
        )

        assert downsampled_data is not None
        assert isinstance(downsampled_data, pd.DataFrame)
        assert len(downsampled_data) < len(sample_kline_data)

    def test_data_interpolation(self, market_data_processor):
        """测试数据插值"""
        # 创建带缺失值的数据
        data_with_gaps = pd.DataFrame(
            {
                "datetime": pd.date_range("2024-01-01", periods=10, freq="1min"),
                "close": [100, 101, None, 103, None, 105, 106, None, 108, 109],
            }
        )

        interpolated_data = market_data_processor.interpolate_missing_data(
            data_with_gaps
        )

        assert interpolated_data is not None
        assert interpolated_data["close"].isna().sum() == 0

    def test_memory_optimization(self, market_data_processor):
        """测试内存优化"""
        # 生成大量数据
        large_data = pd.DataFrame(
            {
                "datetime": pd.date_range("2024-01-01", periods=100000, freq="1min"),
                "close": range(100000),
            }
        )

        # 测试内存使用
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 处理数据
        processed_data = market_data_processor.process_large_dataset(large_data)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该控制在合理范围内
        assert memory_increase < 200  # 少于200MB
        assert processed_data is not None

    def test_data_pipeline_integration(
        self, market_data_processor, data_validator, backup_manager
    ):
        """测试数据管道集成"""
        # 模拟完整的数据处理管道
        raw_data = pd.DataFrame(
            {
                "symbol": ["rb2405"] * 100,
                "datetime": pd.date_range("2024-01-01", periods=100, freq="1min"),
                "close": range(3800, 3900),
            }
        )

        # 步骤1：数据处理
        processed_data = market_data_processor.process_kline_data(raw_data)

        # 步骤2：数据验证
        validation_result = data_validator.validate_kline_data(processed_data)

        # 步骤3：数据备份
        backup_result = backup_manager.create_data_backup(
            processed_data, "/tmp/pipeline_test"
        )

        assert processed_data is not None
        assert validation_result["valid_count"] > 0
        assert backup_result is not None
