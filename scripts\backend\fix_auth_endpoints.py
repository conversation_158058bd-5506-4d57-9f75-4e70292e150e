#!/usr/bin/env python3
"""
修复认证端点脚本
确保登录API能正确工作
"""

import sys
import os
import sqlite3
import bcrypt
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_password_bcrypt(password: str, hashed: str) -> bool:
    """验证bcrypt密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def test_database_auth():
    """测试数据库认证"""
    print("🧪 测试数据库认证...")
    
    try:
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        # 获取admin用户
        cursor.execute('SELECT username, email, hashed_password, is_active FROM users WHERE username = ?', ('admin',))
        user_data = cursor.fetchone()
        
        if not user_data:
            print("❌ admin用户不存在")
            return False
        
        username, email, hashed_password, is_active = user_data
        print(f"📋 用户信息: {username} ({email}), 激活状态: {is_active}")
        
        # 测试密码验证
        test_passwords = ["admin123", "admin", "admin123456"]
        
        for password in test_passwords:
            if verify_password_bcrypt(password, hashed_password):
                print(f"✅ 密码验证成功: {password}")
                conn.close()
                return True
            else:
                print(f"❌ 密码验证失败: {password}")
        
        conn.close()
        return False
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def create_auth_patch():
    """创建认证补丁"""
    print("🔧 创建认证补丁...")
    
    patch_code = '''"""
认证补丁 - 修复登录问题
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel
import sqlite3
import bcrypt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# 创建路由
auth_patch_router = APIRouter(prefix="/api/v1/auth", tags=["auth-patch"])

# 安全认证
security = HTTPBearer(auto_error=False)

class LoginRequest(BaseModel):
    username: str
    password: str
    remember_me: bool = False

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: Dict[str, Any]

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def get_user_from_db(username: str) -> Optional[Dict[str, Any]]:
    """从数据库获取用户"""
    try:
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, hashed_password, is_active, is_superuser
            FROM users WHERE username = ? OR email = ?
        ''', (username, username))
        
        user_data = cursor.fetchone()
        conn.close()
        
        if user_data:
            user_id, username, email, hashed_password, is_active, is_superuser = user_data
            return {
                "id": user_id,
                "username": username,
                "email": email,
                "hashed_password": hashed_password,
                "is_active": bool(is_active),
                "is_superuser": bool(is_superuser)
            }
        
        return None
        
    except Exception as e:
        print(f"数据库查询失败: {e}")
        return None

def create_access_token(user_data: Dict[str, Any]) -> str:
    """创建访问令牌"""
    return f"token_{secrets.token_hex(16)}_{user_data['id']}"

@auth_patch_router.post("/login", response_model=LoginResponse)
async def login_patch(request: LoginRequest):
    """修复版登录接口"""
    print(f"🔐 登录请求: {request.username}")
    
    # 获取用户
    user = get_user_from_db(request.username)
    if not user:
        print(f"❌ 用户不存在: {request.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 检查用户状态
    if not user["is_active"]:
        print(f"❌ 用户未激活: {request.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用"
        )
    
    # 验证密码
    if not verify_password(request.password, user["hashed_password"]):
        print(f"❌ 密码错误: {request.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 创建令牌
    access_token = create_access_token(user)
    
    print(f"✅ 登录成功: {request.username}")
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user={
            "id": user["id"],
            "username": user["username"],
            "email": user["email"],
            "is_superuser": user["is_superuser"]
        }
    )

def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """获取当前用户"""
    if not credentials:
        return None
    
    token = credentials.credentials
    
    # 解析简单token
    if token.startswith("token_"):
        try:
            parts = token.split("_")
            if len(parts) >= 3:
                user_id = parts[-1]
                return get_user_by_id(user_id)
        except:
            pass
    
    return None

def get_user_by_id(user_id: str) -> Optional[Dict[str, Any]]:
    """根据ID获取用户"""
    try:
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, is_active, is_superuser
            FROM users WHERE id = ?
        ''', (user_id,))
        
        user_data = cursor.fetchone()
        conn.close()
        
        if user_data:
            user_id, username, email, is_active, is_superuser = user_data
            return {
                "id": user_id,
                "username": username,
                "email": email,
                "is_active": bool(is_active),
                "is_superuser": bool(is_superuser)
            }
        
        return None
        
    except Exception as e:
        print(f"获取用户失败: {e}")
        return None

@auth_patch_router.get("/me")
async def get_current_user_info(current_user: Optional[Dict[str, Any]] = Depends(get_current_user)):
    """获取当前用户信息"""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    return current_user
'''
    
    with open('auth_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_code)
    
    print("✅ 认证补丁创建完成")

def main():
    """主修复流程"""
    print("🚀 修复认证端点...")
    print("=" * 40)
    
    # 1. 测试数据库认证
    if not test_database_auth():
        print("❌ 数据库认证测试失败")
        return False
    
    # 2. 创建认证补丁
    create_auth_patch()
    
    print("\n✅ 认证端点修复完成！")
    print("📋 下一步:")
    print("   1. 重启后端服务")
    print("   2. 使用 admin / admin123 登录")
    print("   3. 测试API访问权限")
    
    return True

if __name__ == "__main__":
    main()
