#!/usr/bin/env python3
"""
调试登录响应和跳转逻辑
"""

import asyncio
import json
from datetime import datetime
from puppeteer import <PERSON>rowserManager

async def debug_login_response():
    """调试登录响应和跳转逻辑"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 调试登录响应和跳转逻辑...")
        
        # 监听所有网络请求和响应
        requests_responses = []
        
        async def handle_request(request):
            requests_responses.append({
                "type": "request",
                "url": request.url,
                "method": request.method,
                "timestamp": datetime.now().isoformat()
            })
            print(f"📡 请求: {request.method} {request.url}")
        
        async def handle_response(response):
            requests_responses.append({
                "type": "response",
                "url": response.url,
                "status": response.status,
                "timestamp": datetime.now().isoformat()
            })
            print(f"📥 响应: {response.status} {response.url}")
            
            # 如果是登录API响应，获取详细信息
            if '/auth/login' in response.url:
                try:
                    response_text = await response.text()
                    print(f"📄 登录响应内容: {response_text}")
                except Exception as e:
                    print(f"❌ 无法读取响应内容: {e}")
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        # 监听控制台消息
        console_messages = []
        
        def handle_console(msg):
            console_messages.append({
                "type": msg.type,
                "text": msg.text,
                "timestamp": datetime.now().isoformat()
            })
            print(f"🖥️ 控制台[{msg.type}]: {msg.text}")
        
        page.on('console', handle_console)
        
        # 监听页面错误
        page_errors = []
        
        def handle_page_error(error):
            page_errors.append({
                "error": str(error),
                "timestamp": datetime.now().isoformat()
            })
            print(f"💥 页面错误: {error}")
        
        page.on('pageerror', handle_page_error)
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 等待演示登录按钮并点击
        await page.wait_for_selector('button:has-text("演示登录")', timeout=10000)
        
        print("✅ 找到演示登录按钮，准备点击...")
        
        # 在点击前检查页面状态
        current_url_before = page.url
        print(f"📍 点击前URL: {current_url_before}")
        
        # 点击演示登录按钮
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        print("🖱️ 已点击演示登录按钮")
        
        # 等待一段时间让登录处理完成
        await page.wait_for_timeout(8000)
        
        # 检查最终状态
        current_url_after = page.url
        print(f"📍 点击后URL: {current_url_after}")
        
        # 检查是否有JavaScript错误
        js_errors = await page.evaluate('''
            () => {
                const errors = [];
                const originalError = console.error;
                console.error = (...args) => {
                    errors.push(args.join(' '));
                    originalError(...args);
                };
                return window.jsErrors || [];
            }
        ''')
        
        # 检查用户状态
        user_state = await page.evaluate('''
            () => {
                try {
                    // 检查Pinia store状态
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    if (pinia && pinia._s) {
                        const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                        if (userStore) {
                            return {
                                isLoggedIn: userStore.isLoggedIn,
                                userInfo: userStore.userInfo,
                                token: userStore.token ? 'exists' : 'missing'
                            };
                        }
                    }
                    return { error: 'User store not found' };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f"👤 用户状态: {user_state}")
        
        # 生成调试报告
        debug_report = {
            "test_time": datetime.now().isoformat(),
            "initial_url": current_url_before,
            "final_url": current_url_after,
            "url_changed": current_url_before != current_url_after,
            "requests_responses": requests_responses,
            "console_messages": console_messages,
            "page_errors": page_errors,
            "js_errors": js_errors,
            "user_state": user_state
        }
        
        # 保存调试报告
        with open('login_debug_report.json', 'w', encoding='utf-8') as f:
            json.dump(debug_report, f, ensure_ascii=False, indent=2)
        
        print("📄 调试报告已保存: login_debug_report.json")
        
        # 截图
        screenshot_name = f"login_debug_{datetime.now().strftime('%H%M%S')}.png"
        await page.screenshot(path=screenshot_name, full_page=True)
        print(f"📸 调试截图已保存: {screenshot_name}")
        
        # 分析结果
        login_responses = [r for r in requests_responses if r.get('type') == 'response' and '/auth/login' in r.get('url', '')]
        
        if login_responses:
            login_response = login_responses[-1]  # 最后一个登录响应
            if login_response.get('status') == 200:
                print("✅ 登录API响应成功")
                if current_url_after == current_url_before:
                    print("❌ 但页面没有跳转，可能是前端路由逻辑问题")
                else:
                    print("✅ 页面已跳转")
            else:
                print(f"❌ 登录API响应失败: {login_response.get('status')}")
        else:
            print("❌ 没有找到登录API响应")
            
    except Exception as e:
        print(f"💥 调试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(debug_login_response())
