# 项目文件结构说明

## 整理后的项目结构（2025-08-01更新）

```
quant012/
├── backend/                    # 后端服务
│   ├── app/                   # 主应用代码
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── db/                # 数据库相关
│   │   ├── models/            # 数据模型
│   │   ├── schemas/           # Pydantic模式
│   │   ├── services/          # 业务逻辑
│   │   └── main.py            # 应用入口
│   ├── venv/                  # Python虚拟环境
│   ├── requirements*.txt      # 依赖文件
│   ├── start_windows.bat      # Windows启动脚本
│   ├── start_windows.ps1      # PowerShell启动脚本
│   └── README_WINDOWS.md      # Windows环境说明
├── frontend/                   # 前端应用
│   ├── src/                   # 源代码
│   ├── public/                # 静态资源
│   ├── dist/                  # 构建输出
│   └── package.json           # 依赖配置
├── docs/                      # 项目文档
│   ├── api/                   # API文档
│   ├── deployment/            # 部署文档
│   ├── fixes/                 # 修复记录
│   ├── reports/               # 各类报告
│   └── guides/                # 使用指南
├── tests/                     # 测试文件
│   ├── api/                   # API测试
│   ├── backend/               # 后端测试
│   ├── frontend/              # 前端测试
│   └── automation/            # 自动化测试
├── scripts/                   # 脚本文件
│   ├── backend/               # 后端脚本
│   ├── database/              # 数据库脚本
│   ├── deployment/            # 部署脚本
│   └── automation/            # 自动化脚本
├── config/                    # 配置文件
│   ├── docker/                # Docker配置
│   ├── environment/           # 环境配置
│   ├── k8s/                   # Kubernetes配置
│   └── nginx/                 # Nginx配置
├── data/                      # 统一数据目录
│   ├── historical/            # 历史数据
│   │   ├── stocks/           # 股票数据
│   │   ├── futures/          # 期货数据
│   │   └── indexes/          # 指数数据
│   ├── realtime/              # 实时数据
│   ├── reports/               # 报告数据
│   ├── uploads/               # 上传文件
│   ├── strategies/            # 策略文件（统一管理）
│   │   ├── library/          # 策略库（按年份分类）
│   │   │   ├── 2019/
│   │   │   ├── 2020/
│   │   │   ├── 2021/
│   │   │   ├── 2022/
│   │   │   ├── 2023/
│   │   │   ├── 2024/
│   │   │   └── 2025/
│   │   ├── templates/        # 策略模板
│   │   └── user/             # 用户策略
│   ├── index/                # 数据索引
│   └── quantplatform.db      # 主数据库
├── logs/                      # 日志文件
├── reports/                   # 报告文件
├── deployment/                # 部署相关
├── k8s/                       # Kubernetes配置
├── puppeteer/                 # 自动化测试工具
└── archive/                   # 归档文件
```

## 文件整理说明（2025-08-01更新）

### 🔧 主要修复内容

#### 1. 路径不一致问题修复
- ✅ 修复了 `backend/data/index/metadata.json` 中的错误路径引用
- ✅ 更新了 `backend/app/api/v1/strategy_files.py` 中的路径计算逻辑
- ✅ 修正了配置文件中的相对路径引用

#### 2. 重复数据目录整理
- ✅ 合并了根目录 `data/` 和 `backend/data/` 目录
- ✅ 统一数据存储结构，避免路径混乱
- ✅ 保留了所有重要数据文件和索引

#### 3. 策略文件目录统一
- ✅ 合并了分散的策略目录：`strategy/`、`data/Strategy/`、`backend/strategy_templates/`、`backend/user_strategies/`
- ✅ 建立了统一的策略文件结构：`data/strategies/`
- ✅ 按功能分类：library（策略库）、templates（模板）、user（用户策略）

#### 4. 文件命名错误修复
- ✅ 修复了 `strategy/2024/` 目录下文件命名错误（2014 → 2024）
- ✅ 批量重命名了101个策略文件

#### 5. 重复目录结构清理
- ✅ 合并了 `backend/scripts/` 到根目录 `scripts/`
- ✅ 合并了 `backend/tests/` 到根目录 `tests/`
- ✅ 合并了 `backend/docs/` 到根目录 `docs/backend/`
- ✅ 删除了重复的目录结构

#### 6. 前端临时文件清理
- ✅ 清理了日志文件：`frontend.log`、`vite.log`
- ✅ 移动了测试HTML文件到 `tests/e2e/` 目录
- ✅ 移动了Python测试文件到 `tests/backend/` 目录

#### 7. 配置文件路径更新
- ✅ 更新了 `backend/.env.development` 中的路径引用
- ✅ 确保所有路径指向正确的统一目录结构

### 已清理的文件
- 删除了backend目录下的错误文件（以=开头的文件）
- 清理了临时日志文件和数据库文件
- 移除了重复的测试报告和状态文件
- 清理了重复的数据目录和策略目录
- 移除了前端临时测试文件

### 文档归类
- 所有报告文档移动到 `docs/reports/`
- API相关文档移动到 `docs/api/`
- 部署文档移动到 `docs/deployment/`
- 修复记录移动到 `docs/fixes/`

### 测试文件归类
- API测试文件移动到 `tests/api/`
- 后端测试文件移动到 `tests/backend/`
- 前端测试文件移动到 `tests/frontend/`
- 自动化测试移动到 `tests/automation/`

### 脚本文件归类
- 后端相关脚本移动到 `scripts/backend/`
- 数据库脚本移动到 `scripts/database/`
- 部署脚本移动到 `scripts/deployment/`

### 配置文件归类
- Docker配置文件移动到 `config/docker/`
- 其他配置文件移动到 `config/`

## Windows环境优化

项目已针对Windows 10环境进行优化：

1. **虚拟环境重建**: 解决了macOS兼容性问题
2. **依赖优化**: 使用Windows兼容的包版本
3. **TA-Lib安装**: 使用预编译wheel包
4. **启动脚本**: 提供Windows批处理和PowerShell脚本

## 快速启动

### Windows环境（推荐）
```cmd
# 启动后端服务
backend\start_windows.bat

# 启动前端服务
cd frontend
npm install
npm run dev
```

### 验证安装
```cmd
backend\venv\Scripts\activate.bat
python -c "import fastapi, pandas, numpy, talib; print('所有主要依赖包导入成功！')"
```

## 注意事项

1. 虚拟环境位于 `backend/venv/`，已针对Windows优化
2. 所有临时文件和重复文件已清理
3. 文档和测试文件已按功能分类整理
4. 配置文件已集中管理
5. 项目结构更加清晰，便于维护和开发
