#\!/usr/bin/env python3
"""
数据库初始化脚本
创建所有表结构和基础数据
"""

import asyncio
import logging
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from app.core.config import get_settings
from app.core.database import Base, get_engine
from app.db.models import *  # 导入所有模型

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def create_tables():
    """创建所有数据表"""
    settings = get_settings()
    engine = get_engine()
    
    try:
        logger.info("开始创建数据库表...")
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库表创建完成")
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise
    finally:
        await engine.dispose()


async def init_basic_data():
    """初始化基础数据"""
    settings = get_settings()
    engine = get_engine()
    
    try:
        logger.info("开始初始化基础数据...")
        
        async with AsyncSession(engine) as session:
            # 创建默认管理员用户
            from app.db.models.user import User, UserRole, UserStatus
            from app.core.security import SecurityManager
            
            security_manager = SecurityManager()
            
            # 检查是否已存在管理员用户
            result = await session.execute(
                text("SELECT COUNT(*) as count FROM users WHERE is_superuser = true")
            )
            admin_count = result.scalar()
            
            if admin_count == 0:
                # 创建默认管理员
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    full_name="系统管理员",
                    hashed_password=security_manager.get_password_hash("admin123"),
                    is_active=True,
                    is_superuser=True,
                    is_admin=True,
                    role=UserRole.ADMIN,
                    status=UserStatus.ACTIVE,
                    created_at=datetime.utcnow()
                )
                session.add(admin_user)
                logger.info("已创建默认管理员用户: admin/admin123")
            
            # 创建默认测试用户
            result = await session.execute(
                text("SELECT COUNT(*) as count FROM users WHERE username = 'testuser'")
            )
            test_count = result.scalar()
            
            if test_count == 0:
                test_user = User(
                    username="testuser",
                    email="<EMAIL>", 
                    full_name="测试用户",
                    hashed_password=security_manager.get_password_hash("test123"),
                    is_active=True,
                    is_superuser=False,
                    is_admin=False,
                    role=UserRole.USER,
                    status=UserStatus.ACTIVE,
                    created_at=datetime.utcnow()
                )
                session.add(test_user)
                logger.info("已创建默认测试用户: testuser/test123")
            
            await session.commit()
        
        logger.info("基础数据初始化完成")
        
    except Exception as e:
        logger.error(f"初始化基础数据失败: {e}")
        raise
    finally:
        await engine.dispose()


async def main():
    """主函数"""
    try:
        logger.info("=== 开始数据库初始化 ===")
        
        # 创建表结构
        await create_tables()
        
        # 初始化基础数据
        await init_basic_data()
        
        logger.info("=== 数据库初始化完成 ===")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
EOF < /dev/null
