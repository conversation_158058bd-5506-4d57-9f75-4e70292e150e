#!/usr/bin/env python3
"""
快速深度交易中心测试 - 使用Puppeteer MCP作为真实用户
测试目标: http://localhost:5173
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from pathlib import Path
import traceback

try:
    from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
except ImportError:
    print("请安装playwright: pip install playwright")
    print("然后运行: python -m playwright install")
    exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickTradingCenterTester:
    def __init__(self):
        self.session_id = f"quick_test_{int(time.time())}"
        self.start_time = datetime.now()
        self.test_results = {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "test_type": "Quick Deep Trading Center Test",
            "platform_url": "http://localhost:5173",
            "test_scenarios": [],
            "discovered_issues": [],
            "console_errors": [],
            "network_issues": [],
            "screenshots": [],
            "recommendations": []
        }
        self.screenshot_counter = 1
        
    async def take_screenshot(self, page, description):
        """截图并记录"""
        try:
            timestamp = int(time.time())
            filename = f"{self.session_id}_{self.screenshot_counter:03d}_{description}_{timestamp}.png"
            filepath = Path(__file__).parent / filename
            
            await page.screenshot(path=str(filepath), full_page=True)
            
            self.test_results["screenshots"].append({
                "filename": filename,
                "description": description,
                "timestamp": datetime.now().isoformat()
            })
            self.screenshot_counter += 1
            
            logger.info(f"截图保存: {filename}")
            return filename
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None
    
    async def record_console_errors(self, page):
        """监听控制台错误"""
        def handle_console(msg):
            if msg.type in ['error', 'warning']:
                self.test_results["console_errors"].append({
                    "type": msg.type,
                    "text": msg.text,
                    "timestamp": datetime.now().isoformat()
                })
                logger.warning(f"控制台{msg.type}: {msg.text}")
        
        page.on("console", handle_console)
        
        def handle_page_error(error):
            self.test_results["console_errors"].append({
                "type": "page_error",
                "text": str(error),
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"页面错误: {error}")
        
        page.on("pageerror", handle_page_error)
    
    async def test_platform_access(self, page):
        """测试平台访问"""
        scenario = {
            "name": "平台访问测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": []
        }
        
        try:
            # 访问主页
            load_start = time.time()
            await page.goto("http://localhost:5173", wait_until="networkidle", timeout=30000)
            load_time = time.time() - load_start
            
            scenario["steps"].append(f"成功访问平台，加载时间: {load_time:.2f}秒")
            
            # 检查页面标题
            title = await page.title()
            scenario["steps"].append(f"页面标题: {title}")
            
            # 等待页面完全加载
            await page.wait_for_timeout(3000)
            
            # 截图
            await self.take_screenshot(page, "platform_access")
            
            # 检查页面内容
            body_text = await page.text_content("body")
            if len(body_text.strip()) < 100:
                scenario["issues"].append("页面内容过少，可能加载不完整")
            
            # 检查是否有错误信息
            error_elements = await page.query_selector_all(".error, .alert-danger, [class*='error']")
            if error_elements:
                scenario["issues"].append(f"页面显示{len(error_elements)}个错误信息")
            
            # 用户体验反馈
            if load_time < 3:
                scenario["user_feedback"].append("页面加载速度良好")
            else:
                scenario["user_feedback"].append("页面加载速度较慢")
                scenario["issues"].append("页面加载时间超过3秒")
            
        except Exception as e:
            scenario["issues"].append(f"平台访问失败: {str(e)}")
            logger.error(f"平台访问测试失败: {e}")
        
        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)
        
    async def test_navigation_and_pages(self, page):
        """测试导航和页面"""
        scenario = {
            "name": "导航和页面测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": []
        }
        
        try:
            # 测试主要导航项
            nav_items = ["仪表盘", "市场数据", "交易终端", "投资组合", "策略中心", "风险管理"]
            
            for nav_item in nav_items:
                try:
                    # 尝试多种选择器
                    selectors = [
                        f"text=\"{nav_item}\"",
                        f"a:has-text(\"{nav_item}\")",
                        f"button:has-text(\"{nav_item}\")",
                        f"[title=\"{nav_item}\"]"
                    ]
                    
                    clicked = False
                    for selector in selectors:
                        try:
                            element = await page.wait_for_selector(selector, timeout=3000)
                            if element:
                                await element.click()
                                await page.wait_for_timeout(2000)
                                
                                scenario["steps"].append(f"成功点击导航项: {nav_item}")
                                await self.take_screenshot(page, f"nav_{nav_item}")
                                
                                clicked = True
                                break
                        except:
                            continue
                    
                    if not clicked:
                        scenario["issues"].append(f"无法找到或点击导航项: {nav_item}")
                        
                except Exception as e:
                    scenario["issues"].append(f"测试导航项{nav_item}时出错: {str(e)}")
            
            # 用户体验反馈
            successful_nav = len([step for step in scenario["steps"] if "成功点击导航项" in step])
            if successful_nav >= 4:
                scenario["user_feedback"].append("导航系统基本完整，用户可以访问主要功能")
            else:
                scenario["user_feedback"].append("导航系统存在问题，部分功能无法访问")
                
        except Exception as e:
            scenario["issues"].append(f"导航测试失败: {str(e)}")
            logger.error(f"导航测试失败: {e}")
        
        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)
    
    async def test_trading_features(self, page):
        """测试交易功能"""
        scenario = {
            "name": "交易功能测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": []
        }
        
        try:
            # 进入交易页面
            trading_selectors = ["text=\"交易终端\"", "text=\"交易\"", "a:has-text(\"交易\")"]
            
            for selector in trading_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await element.click()
                        await page.wait_for_timeout(3000)
                        scenario["steps"].append("成功进入交易页面")
                        break
                except:
                    continue
            
            # 截图交易页面
            await self.take_screenshot(page, "trading_page")
            
            # 检查交易功能元素
            trading_elements = {
                "搜索框": ["input[placeholder*='搜索']", "input[placeholder*='股票']"],
                "买入按钮": ["button:has-text('买入')", ".buy-button"],
                "卖出按钮": ["button:has-text('卖出')", ".sell-button"],
                "价格输入": ["input[placeholder*='价格']", "input[type='number']"],
                "图表": ["canvas", ".chart", "#chart"]
            }
            
            found_elements = 0
            for element_name, selectors in trading_elements.items():
                found = False
                for selector in selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            found = True
                            found_elements += 1
                            scenario["steps"].append(f"发现{element_name}")
                            break
                    except:
                        continue
                
                if not found:
                    scenario["issues"].append(f"缺少{element_name}")
            
            # 用户体验反馈
            if found_elements >= 3:
                scenario["user_feedback"].append("交易功能基本完整")
            else:
                scenario["user_feedback"].append("交易功能不完整，影响用户体验")
            
        except Exception as e:
            scenario["issues"].append(f"交易功能测试失败: {str(e)}")
            logger.error(f"交易功能测试失败: {e}")
        
        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)
    
    async def test_responsive_design(self, page):
        """测试响应式设计"""
        scenario = {
            "name": "响应式设计测试",
            "start_time": time.time(),
            "steps": [],
            "issues": [],
            "user_feedback": []
        }
        
        try:
            # 测试不同屏幕尺寸
            screen_sizes = [
                {"name": "桌面", "width": 1920, "height": 1080},
                {"name": "平板", "width": 768, "height": 1024},
                {"name": "手机", "width": 375, "height": 667}
            ]
            
            for size in screen_sizes:
                try:
                    await page.set_viewport_size({"width": size["width"], "height": size["height"]})
                    await page.wait_for_timeout(1000)
                    
                    scenario["steps"].append(f"测试{size['name']}尺寸: {size['width']}x{size['height']}")
                    await self.take_screenshot(page, f"responsive_{size['name']}")
                    
                except Exception as e:
                    scenario["issues"].append(f"响应式测试失败: {str(e)}")
            
            scenario["user_feedback"].append("响应式设计测试完成")
            
        except Exception as e:
            scenario["issues"].append(f"响应式设计测试失败: {str(e)}")
            logger.error(f"响应式设计测试失败: {e}")
        
        scenario["end_time"] = time.time()
        scenario["duration"] = scenario["end_time"] - scenario["start_time"]
        self.test_results["test_scenarios"].append(scenario)
    
    async def generate_final_report(self):
        """生成最终报告"""
        try:
            # 计算统计
            total_issues = sum(len(scenario.get("issues", [])) for scenario in self.test_results["test_scenarios"])
            total_scenarios = len(self.test_results["test_scenarios"])
            console_errors = len(self.test_results["console_errors"])
            
            # 生成建议
            if total_issues == 0 and console_errors == 0:
                grade = "优秀"
                self.test_results["recommendations"].append("平台功能完整，用户体验优秀")
            elif total_issues < 5 and console_errors < 3:
                grade = "良好"
                self.test_results["recommendations"].append("发现少量问题，建议优化")
            else:
                grade = "需要改进"
                self.test_results["recommendations"].append("发现较多问题，建议及时修复")
            
            if console_errors > 0:
                self.test_results["recommendations"].append("发现控制台错误，建议修复JavaScript问题")
            
            # 完成测试
            self.test_results["end_time"] = datetime.now().isoformat()
            self.test_results["total_duration"] = (datetime.now() - self.start_time).total_seconds()
            
            # 总体评估
            self.test_results["overall_assessment"] = {
                "grade": grade,
                "total_scenarios": total_scenarios,
                "total_issues": total_issues,
                "console_errors": console_errors,
                "issue_rate": total_issues / total_scenarios if total_scenarios > 0 else 0
            }
            
            # 保存报告
            report_filename = f"quick_trading_center_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_path = Path(__file__).parent / report_filename
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
            # 打印报告
            print("\n" + "="*80)
            print("快速交易中心深度测试完成")
            print("="*80)
            print(f"总体评估: {grade}")
            print(f"测试场景: {total_scenarios}个")
            print(f"发现问题: {total_issues}个")
            print(f"控制台错误: {console_errors}个")
            print(f"测试时长: {self.test_results['total_duration']:.2f}秒")
            print(f"截图数量: {len(self.test_results['screenshots'])}张")
            print(f"报告文件: {report_filename}")
            print("\n主要建议:")
            for rec in self.test_results["recommendations"]:
                print(f"- {rec}")
            print("\n发现的问题:")
            for scenario in self.test_results["test_scenarios"]:
                if scenario.get("issues"):
                    print(f"\n{scenario['name']}:")
                    for issue in scenario["issues"]:
                        print(f"  - {issue}")
            print("="*80)
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return None

async def main():
    """主函数"""
    tester = QuickTradingCenterTester()
    
    try:
        logger.info(f"开始快速交易中心深度测试 - 会话ID: {tester.session_id}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            
            try:
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                )
                
                page = await context.new_page()
                await tester.record_console_errors(page)
                
                # 运行测试场景
                test_scenarios = [
                    tester.test_platform_access,
                    tester.test_navigation_and_pages,
                    tester.test_trading_features,
                    tester.test_responsive_design
                ]
                
                for test_func in test_scenarios:
                    try:
                        logger.info(f"执行测试: {test_func.__name__}")
                        await test_func(page)
                        await page.wait_for_timeout(1000)
                    except Exception as e:
                        logger.error(f"测试{test_func.__name__}失败: {e}")
                
                # 生成报告
                await tester.generate_final_report()
                
            finally:
                await browser.close()
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
