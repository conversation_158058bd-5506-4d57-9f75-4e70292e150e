#!/usr/bin/env python3
"""
测试改进后的拼图验证算法
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_improved_puzzle():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    try:
        # 监听控制台日志以获取调试信息
        def handle_console(msg):
            if '拼图验证调试信息' in msg.text:
                logger.info(f"🔍 {msg.text}")
        
        page.on('console', handle_console)
        
        # 登录并跳转到拼图验证页面
        logger.info("🚀 开始测试改进后的拼图验证...")
        await page.goto("http://localhost:5173/login")
        await page.wait_for_load_state('networkidle')
        
        # 清除存储
        await page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
        await page.reload()
        await page.wait_for_load_state('networkidle')
        
        # 点击演示登录
        demo_btn = await page.query_selector('button:has-text("演示登录")')
        if demo_btn:
            await demo_btn.click()
            await asyncio.sleep(3)
            
            if 'puzzle-verify' in page.url:
                logger.info("✅ 成功跳转到拼图验证页面")
            else:
                logger.error("❌ 登录失败")
                return
        
        # 等待页面加载
        await asyncio.sleep(2)
        
        # 进行多次验证测试
        success_count = 0
        total_attempts = 5
        
        for attempt in range(total_attempts):
            logger.info(f"\n🎯 第 {attempt + 1} 次验证尝试...")
            
            # 如果不是第一次，刷新拼图
            if attempt > 0:
                refresh_btn = await page.query_selector('.refresh-btn')
                if refresh_btn:
                    await refresh_btn.click()
                    await asyncio.sleep(1)
            
            # 获取滑动元素
            slider_btn = await page.query_selector('.slider-btn')
            track = await page.query_selector('.slider-track')
            
            if not slider_btn or not track:
                logger.error("❌ 找不到滑动元素")
                continue
            
            # 获取位置信息
            btn_box = await slider_btn.bounding_box()
            track_box = await track.bounding_box()
            
            if btn_box and track_box:
                start_x = btn_box['x'] + btn_box['width'] / 2
                start_y = btn_box['y'] + btn_box['height'] / 2
                
                # 尝试不同的滑动策略
                strategies = [
                    0.6,   # 60%位置
                    0.7,   # 70%位置
                    0.8,   # 80%位置
                    0.65,  # 65%位置
                    0.75   # 75%位置
                ]
                
                target_ratio = strategies[attempt % len(strategies)]
                available_width = track_box['width'] - btn_box['width']
                end_x = track_box['x'] + (available_width * target_ratio) + btn_box['width'] / 2
                
                logger.info(f"   滑动策略: {target_ratio*100:.0f}% 位置")
                logger.info(f"   从 ({start_x:.1f}, {start_y:.1f}) 到 ({end_x:.1f}, {start_y:.1f})")
                
                # 执行平滑滑动
                await page.mouse.move(start_x, start_y)
                await page.mouse.down()
                
                steps = 25
                for i in range(steps + 1):
                    progress = i / steps
                    # 使用缓动函数使滑动更自然
                    eased_progress = progress * progress * (3 - 2 * progress)  # smoothstep
                    current_x = start_x + (end_x - start_x) * eased_progress
                    await page.mouse.move(current_x, start_y)
                    await asyncio.sleep(0.04)
                
                await page.mouse.up()
                await asyncio.sleep(2)
                
                # 检查验证结果
                success_element = await page.query_selector('.slider-btn-success')
                if success_element:
                    success_count += 1
                    logger.info(f"✅ 第 {attempt + 1} 次验证成功！")
                    
                    # 测试继续访问
                    continue_btn = await page.query_selector('button:has-text("继续访问")')
                    if continue_btn:
                        is_disabled = await continue_btn.get_attribute('disabled')
                        if is_disabled is None:
                            logger.info("✅ 继续访问按钮已启用")
                            break
                        else:
                            logger.warning("⚠️ 继续访问按钮仍然禁用")
                    break
                else:
                    logger.info(f"❌ 第 {attempt + 1} 次验证失败")
        
        # 统计结果
        success_rate = (success_count / total_attempts) * 100
        logger.info(f"\n📊 测试结果统计:")
        logger.info(f"   成功次数: {success_count}/{total_attempts}")
        logger.info(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            logger.info("🎉 验证算法改进成功！稳定性良好")
        elif success_rate >= 60:
            logger.info("✅ 验证算法有所改进，但仍需优化")
        else:
            logger.warning("⚠️ 验证算法仍需进一步改进")
        
        # 截图保存
        await page.screenshot(path="improved_puzzle_test.png")
        logger.info("📸 测试截图已保存: improved_puzzle_test.png")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    finally:
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_improved_puzzle())
