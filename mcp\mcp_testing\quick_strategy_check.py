#!/usr/bin/env python3
"""
快速策略页面检查 - 不截图，只检查页面状态
"""

import asyncio
import json
import logging
from datetime import datetime
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuickStrategyCheck:
    def __init__(self):
        self.browser = None
        self.page = None
        
    async def initialize(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=True)  # 无头模式更快
        self.page = await self.browser.new_page()
        
    async def check_page_status(self, url, expected_title):
        """快速检查页面状态"""
        try:
            # 导航到页面
            response = await self.page.goto(url, wait_until='networkidle', timeout=15000)
            await asyncio.sleep(2)
            
            # 获取页面基本信息
            page_info = await self.page.evaluate("""
                () => {
                    return {
                        title: document.title,
                        url: window.location.href,
                        contentLength: document.body ? document.body.innerHTML.length : 0,
                        hasContent: document.body && document.body.innerHTML.length > 1000,
                        isErrorPage: document.title.includes('页面不存在') || document.title.includes('404')
                    };
                }
            """)
            
            # 分析状态
            if page_info['isErrorPage']:
                status = '❌ 页面不存在'
            elif expected_title in page_info['title']:
                status = '✅ 正常工作'
            elif page_info['hasContent']:
                status = '⚠️ 内容异常'
            else:
                status = '🚨 加载失败'
            
            return {
                'url': url,
                'status': status,
                'title': page_info['title'],
                'content_length': page_info['contentLength'],
                'response_status': response.status if response else 'N/A'
            }
            
        except Exception as e:
            return {
                'url': url,
                'status': '🚨 访问失败',
                'title': 'ERROR',
                'content_length': 0,
                'error': str(e)
            }
    
    async def run_quick_check(self):
        """运行快速检查"""
        logger.info("🚀 开始快速策略页面检查...")
        
        # 要检查的页面
        pages_to_check = [
            ('http://localhost:5173/strategy/center', '策略中心'),
            ('http://localhost:5173/strategy/detail/mock-1', '策略详情'),
            ('http://localhost:5173/strategy/detail/mock-2', '策略详情'),
            ('http://localhost:5173/strategy/detail/test-1', '策略详情'),
            ('http://localhost:5173/strategy/detail/strategy-001', '策略详情'),
            ('http://localhost:5173/strategy/development', '策略开发'),
            ('http://localhost:5173/strategy/monitor', '策略监控'),
            ('http://localhost:5173/strategy/library', '策略文库'),
        ]
        
        results = []
        
        try:
            await self.initialize()
            
            print("\n" + "="*80)
            print("🔍 快速策略页面状态检查")
            print("="*80)
            
            for url, expected_title in pages_to_check:
                result = await self.check_page_status(url, expected_title)
                results.append(result)
                
                print(f"{result['status']} {url}")
                print(f"   标题: {result['title']}")
                print(f"   内容长度: {result['content_length']}")
                if 'error' in result:
                    print(f"   错误: {result['error']}")
                print()
            
            # 统计结果
            working = len([r for r in results if '✅' in r['status']])
            not_found = len([r for r in results if '❌' in r['status']])
            errors = len([r for r in results if '🚨' in r['status']])
            warnings = len([r for r in results if '⚠️' in r['status']])
            
            print("="*80)
            print("📊 检查结果统计:")
            print(f"  ✅ 正常工作: {working}个")
            print(f"  ❌ 页面不存在: {not_found}个")
            print(f"  🚨 访问失败: {errors}个")
            print(f"  ⚠️ 内容异常: {warnings}个")
            print(f"  📈 成功率: {(working/(len(results)))*100:.1f}%")
            
            # 问题分析
            if not_found > 0:
                print(f"\n❌ 页面不存在的链接:")
                for r in results:
                    if '❌' in r['status']:
                        print(f"  - {r['url']}")
            
            if errors > 0:
                print(f"\n🚨 访问失败的链接:")
                for r in results:
                    if '🚨' in r['status']:
                        print(f"  - {r['url']}")
                        if 'error' in r:
                            print(f"    错误: {r['error']}")
            
            print("="*80)
            
        finally:
            if self.browser:
                await self.browser.close()

async def main():
    checker = QuickStrategyCheck()
    await checker.run_quick_check()

if __name__ == "__main__":
    asyncio.run(main())
