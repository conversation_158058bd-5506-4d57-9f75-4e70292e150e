{"test_summary": {"total_tests": 15, "total_issues": 84, "high_severity": 0, "medium_severity": 44, "low_severity": 40, "test_time": "2025-08-03T12:19:30.230950"}, "issues_by_category": {"代码质量": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.754147", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.754557", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.754941", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:19:14.755333", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.757631", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.758026", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.760005", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.760301", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.760608", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.760844", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194714868:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:19:14.761204", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.763522", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:19:14.763843", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:19:14.764082", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.764269", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.764505", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.764741", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.765063", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.765371", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.765615", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.765988", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.767942", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.768348", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.770280", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.770651", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.772597", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.772919", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.773196", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.773430", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.773751", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.774057", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.774293", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.774588", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.774889", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:19:14.775138", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:19:14.775541", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:19:14.775778", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.776012", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.776398", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.776781", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.777163", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.777632", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.778122", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.778569", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.778839", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:19:14.779098", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.781416", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.781844", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.782170", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.782597", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.783019", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.783500", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.783874", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:19:14.784384", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.785043", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.785532", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.788145", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.790704", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.793240", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.795816", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.796077", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.796334", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.796562", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194714868:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:19:14.796785", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.799194", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.799614", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.799932", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.800360", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.800786", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.801333", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.801760", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.802281", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:19:14.802711", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.803378", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.803871", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.806374", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.808946", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.811468", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.813999", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.814496", "url": "http://localhost:5173/risk"}], "API": [{"category": "API", "severity": "中", "title": "API请求失败", "description": "GET http://localhost:8000/api/v1/market/sectors - 状态码: 404", "evidence": null, "timestamp": "2025-08-03T12:19:20.887910", "url": "http://localhost:5173/market"}], "UI/UX": [{"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:19:23.107263", "url": "http://localhost:5173/"}], "可访问性": [{"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:19:23.790255", "url": "http://localhost:5173/"}], "安全": [{"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:19:30.229769", "url": "http://localhost:5173/"}]}, "detailed_issues": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.754147", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.754557", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.754941", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:19:14.755333", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.757631", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.758026", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.760005", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.760301", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.760608", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.760844", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194714868:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:19:14.761204", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.763522", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:19:14.763843", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:19:14.764082", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.764269", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.764505", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.764741", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.765063", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.765371", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.765615", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.765988", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.767942", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.768348", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.770280", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194714868:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:19:14.770651", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.772597", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.772919", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.773196", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.773430", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.773751", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.774057", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:19:14.774293", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.774588", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:19:14.774889", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:19:14.775138", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:19:14.775541", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:19:14.775778", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.776012", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.776398", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:19:14.776781", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.777163", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.777632", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.778122", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.778569", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.778839", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194714868:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:19:14.779098", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.781416", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.781844", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.782170", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.782597", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.783019", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.783500", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.783874", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:19:14.784384", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.785043", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.785532", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.788145", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.790704", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.793240", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.795816", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.796077", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.796334", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:19:14.796562", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194714868:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194714868:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194714868:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:19:14.796785", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.799194", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:19:14.799614", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.799932", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.800360", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.800786", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.801333", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:19:14.801760", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:19:14.802281", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:19:14.802711", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.803378", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.803871", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.806374", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.808946", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:19:14.811468", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.813999", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:19:14.814496", "url": "http://localhost:5173/risk"}, {"category": "API", "severity": "中", "title": "API请求失败", "description": "GET http://localhost:8000/api/v1/market/sectors - 状态码: 404", "evidence": null, "timestamp": "2025-08-03T12:19:20.887910", "url": "http://localhost:5173/market"}, {"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:19:23.107263", "url": "http://localhost:5173/"}, {"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:19:23.790255", "url": "http://localhost:5173/"}, {"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:19:30.229769", "url": "http://localhost:5173/"}], "test_results": [{"test": "首页性能测试", "status": "PASS", "details": "加载时间: 1.36秒", "timestamp": "2025-08-03T12:18:49.674868"}, {"test": "仪表盘性能测试", "status": "PASS", "details": "加载时间: 0.90秒", "timestamp": "2025-08-03T12:18:50.574009"}, {"test": "市场数据性能测试", "status": "PASS", "details": "加载时间: 1.21秒", "timestamp": "2025-08-03T12:18:51.780890"}, {"test": "交易终端性能测试", "status": "PASS", "details": "加载时间: 1.25秒", "timestamp": "2025-08-03T12:18:53.031149"}, {"test": "策略中心性能测试", "status": "PASS", "details": "加载时间: 0.70秒", "timestamp": "2025-08-03T12:18:53.732390"}, {"test": "投资组合性能测试", "status": "PASS", "details": "加载时间: 0.67秒", "timestamp": "2025-08-03T12:18:54.402086"}, {"test": "风险管理性能测试", "status": "PASS", "details": "加载时间: 0.64秒", "timestamp": "2025-08-03T12:18:55.041378"}, {"test": "控制台错误检查", "status": "PASS", "details": "发现 40 个错误, 40 个警告", "timestamp": "2025-08-03T12:19:14.814996"}, {"test": "网络请求分析", "status": "PASS", "details": "发现 1 个失败请求", "timestamp": "2025-08-03T12:19:20.888474"}, {"test": "桌面端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:19:21.676887"}, {"test": "平板端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:19:22.394570"}, {"test": "手机端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:19:23.107639"}, {"test": "可访问性检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:19:23.794472"}, {"test": "数据加载检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:19:29.519711"}, {"test": "安全头检查", "status": "PASS", "details": "缺少 5 个安全头", "timestamp": "2025-08-03T12:19:30.230539"}], "recommendations": [{"priority": "中", "title": "用户体验改进", "description": "提升界面响应性和用户体验", "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]}]}