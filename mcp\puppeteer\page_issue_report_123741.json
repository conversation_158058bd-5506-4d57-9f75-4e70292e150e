{"timestamp": "2025-08-02T12:37:42.018159", "page_title": "实时行情 - 量化投资平台", "html_content_length": 1063192, "app_content_length": 31677, "console_errors": 7, "console_warnings": 15, "total_console_messages": 95, "failed_requests": 0, "total_requests": 258, "api_requests": 15, "js_requests": 0, "css_requests": 19, "current_url": "http://localhost:5173/dashboard", "screenshot": "page_issue_test_123741.png", "console_messages": [{"type": "debug", "text": "[vite] connecting...", "location": "{'url': 'http://localhost:5173/@vite/client', 'lineNumber': 788, 'columnNumber': 8}"}, {"type": "debug", "text": "[vite] connected.", "location": "{'url': 'http://localhost:5173/@vite/client', 'lineNumber': 911, 'columnNumber': 14}"}, {"type": "log", "text": "Security initialization completed", "location": "{'url': 'http://localhost:5173/src/utils/security.ts', 'lineNumber': 377, 'columnNumber': 10}"}, {"type": "log", "text": "✅ 全局错误处理器已初始化", "location": "{'url': 'http://localhost:5173/src/utils/error-handler.ts', 'lineNumber': 432, 'columnNumber': 10}"}, {"type": "log", "text": "🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 180, 'columnNumber': 10}"}, {"type": "log", "text": "📱 应用版本: 1.0.0", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 181, 'columnNumber': 10}"}, {"type": "log", "text": "🌐 API地址: http://localhost:8000/api/v1", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 182, 'columnNumber': 10}"}, {"type": "log", "text": "🔌 WebSocket地址: ws://localhost:8000/api/v1/ws", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 183, 'columnNumber': 10}"}, {"type": "log", "text": "🚀 量化投资平台 v1.0.0 启动成功!", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 195, 'columnNumber': 8}"}, {"type": "log", "text": "🌍 运行环境: 开发", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 196, 'columnNumber': 8}"}, {"type": "log", "text": "⏰ 启动时间: 2025/8/2 12:37:34", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 197, 'columnNumber': 8}"}, {"type": "log", "text": "🔧 开发环境自动登录", "location": "{'url': 'http://localhost:5173/src/router/guards.ts', 'lineNumber': 28, 'columnNumber': 16}"}, {"type": "log", "text": "🔧 开发环境自动登录成功", "location": "{'url': 'http://localhost:5173/src/stores/modules/user.ts', 'lineNumber': 139, 'columnNumber': 14}"}, {"type": "log", "text": "✅ Service Worker 注册成功: http://localhost:5173/", "location": "{'url': 'http://localhost:5173/src/main.ts', 'lineNumber': 188, 'columnNumber': 14}"}, {"type": "log", "text": "🧭 路由跳转: / -> /", "location": "{'url': 'http://localhost:5173/src/router/guards.ts', 'lineNumber': 70, 'columnNumber': 14}"}, {"type": "log", "text": "🚀 开始获取股票列表，参数: {pageSize: 100}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 166, 'columnNumber': 14}"}, {"type": "log", "text": "✅ 账户信息获取成功: Proxy(Object)", "location": "{'url': 'http://localhost:5173/src/stores/modules/trading.ts', 'lineNumber': 95, 'columnNumber': 16}"}, {"type": "log", "text": "✅ 持仓信息获取成功: 2 个持仓", "location": "{'url': 'http://localhost:5173/src/stores/modules/trading.ts', 'lineNumber': 129, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Request] GET /trading/orders {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /trading/trades {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/overview {headers: Object, data: undefined, params: undefined}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/sectors {headers: Object, data: undefined, params: undefined}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/news {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/rankings {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "[HTTP Request] GET /market/rankings {headers: Object, data: undefined, params: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 73, 'columnNumber': 12}"}, {"type": "log", "text": "✅ 成功获取股票数据: 15 只股票", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 168, 'columnNumber': 14}"}, {"type": "log", "text": "📊 前3只股票详情: [Object, Object, Object]", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 170, 'columnNumber': 16}"}, {"type": "log", "text": "💾 股票列表已保存到store，当前数量: 15", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 173, 'columnNumber': 14}"}, {"type": "error", "text": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error during WebSocket handshake: Unexpected response code: 404", "location": "{'url': 'http://localhost:5173/src/utils/websocket.ts', 'lineNumber': 62, 'columnNumber': 0}"}, {"type": "warning", "text": "WebSocket连接暂时不可用: Event", "location": "{'url': 'http://localhost:5173/src/services/websocket.service.ts', 'lineNumber': 72, 'columnNumber': 14}"}, {"type": "error", "text": "WebSocket错误详情: Event", "location": "{'url': 'http://localhost:5173/src/utils/resize-observer-fix.ts', 'lineNumber': 10, 'columnNumber': 25}"}, {"type": "log", "text": "WebSocket连接断开", "location": "{'url': 'http://localhost:5173/src/services/websocket.service.ts', 'lineNumber': 64, 'columnNumber': 14}"}, {"type": "error", "text": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "location": "{'url': 'http://localhost:5173/src/utils/resize-observer-fix.ts', 'lineNumber': 10, 'columnNumber': 25}"}, {"type": "error", "text": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "location": "{'url': 'http://localhost:5173/src/utils/resize-observer-fix.ts', 'lineNumber': 10, 'columnNumber': 25}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/sectors {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "🔍 板块数据API响应: {success: true, data: Array(5)}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 421, 'columnNumber': 14}"}, {"type": "warning", "text": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(5)}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 434, 'columnNumber': 18}"}, {"type": "log", "text": "✅ 从板块数据提取行业信息: 0 个行业", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 443, 'columnNumber': 14}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /trading/orders {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/overview {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "⚠️ 指数数据格式异常或为空: {success: true, data: Object}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 307, 'columnNumber': 18}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /trading/trades {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/rankings {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "🔍 排行榜API响应: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 481, 'columnNumber': 14}"}, {"type": "warning", "text": "⚠️ 排行榜数据格式异常或为空: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 497, 'columnNumber': 18}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/rankings {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "🔍 排行榜API响应: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 481, 'columnNumber': 14}"}, {"type": "warning", "text": "⚠️ 排行榜数据格式异常或为空: {success: true, data: Array(3), total: 3}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 497, 'columnNumber': 18}"}, {"type": "warning", "text": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "location": "{'url': 'http://localhost:5173/src/api/interceptors/security.ts', 'lineNumber': 167, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Response] GET /market/news {status: 200, data: Object}", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 111, 'columnNumber': 12}"}, {"type": "log", "text": "📊 图表容器尺寸检查: 991x300", "location": "{'url': 'http://localhost:5173/src/utils/chartUtils.ts', 'lineNumber': 31, 'columnNumber': 18}"}, {"type": "log", "text": "🎨 开始初始化ECharts实例", "location": "{'url': 'http://localhost:5173/src/utils/chartUtils.ts', 'lineNumber': 51, 'columnNumber': 18}"}, {"type": "log", "text": "✅ 图表初始化成功", "location": "{'url': 'http://localhost:5173/src/utils/chartUtils.ts', 'lineNumber': 56, 'columnNumber': 18}"}, {"type": "log", "text": "投资组合图表初始化成功", "location": "{'url': 'http://localhost:5173/src/views/Dashboard/DashboardView.vue', 'lineNumber': 215, 'columnNumber': 20}"}, {"type": "log", "text": "🧭 路由跳转: / -> /dashboard", "location": "{'url': 'http://localhost:5173/src/router/guards.ts', 'lineNumber': 70, 'columnNumber': 14}"}, {"type": "warning", "text": "[ECharts] Instance ec_1754109454789 has been disposed", "location": "{'url': 'http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=3789ae84', 'lineNumber': 603, 'columnNumber': 17}"}, {"type": "log", "text": "🚀 开始获取股票列表，参数: {pageSize: 100}", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 166, 'columnNumber': 14}"}, {"type": "log", "text": "✅ 账户信息获取成功: Proxy(Object)", "location": "{'url': 'http://localhost:5173/src/stores/modules/trading.ts', 'lineNumber': 95, 'columnNumber': 16}"}, {"type": "log", "text": "✅ 持仓信息获取成功: 2 个持仓", "location": "{'url': 'http://localhost:5173/src/stores/modules/trading.ts', 'lineNumber': 129, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /trading/orders", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /trading/trades", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/overview", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/sectors", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/news", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/rankings", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/rankings", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "✅ 成功获取股票数据: 15 只股票", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 168, 'columnNumber': 14}"}, {"type": "log", "text": "📊 前3只股票详情: [Object, Object, Object]", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 170, 'columnNumber': 16}"}, {"type": "log", "text": "💾 股票列表已保存到store，当前数量: 15", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 173, 'columnNumber': 14}"}, {"type": "error", "text": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error during WebSocket handshake: Unexpected response code: 404", "location": "{'url': 'http://localhost:5173/src/utils/websocket.ts', 'lineNumber': 62, 'columnNumber': 0}"}, {"type": "warning", "text": "WebSocket连接暂时不可用: Event", "location": "{'url': 'http://localhost:5173/src/services/websocket.service.ts', 'lineNumber': 72, 'columnNumber': 14}"}, {"type": "error", "text": "WebSocket错误详情: Event", "location": "{'url': 'http://localhost:5173/src/utils/resize-observer-fix.ts', 'lineNumber': 10, 'columnNumber': 25}"}, {"type": "error", "text": "WebSocket连接失败: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "location": "{'url': 'http://localhost:5173/src/utils/resize-observer-fix.ts', 'lineNumber': 10, 'columnNumber': 25}"}, {"type": "warning", "text": "WebSocket连接失败，将使用轮询模式: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "location": "{'url': 'http://localhost:5173/src/stores/modules/market.ts', 'lineNumber': 651, 'columnNumber': 16}"}, {"type": "log", "text": "WebSocket连接断开", "location": "{'url': 'http://localhost:5173/src/services/websocket.service.ts', 'lineNumber': 64, 'columnNumber': 14}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /trading/orders", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /trading/trades", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/overview", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/sectors", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/news", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/rankings", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/rankings", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /trading/orders", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /trading/trades", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/overview", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/sectors", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/news", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/rankings", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}, {"type": "log", "text": "[HTTP Cache Hit] GET /market/rankings", "location": "{'url': 'http://localhost:5173/src/api/http.ts', 'lineNumber': 55, 'columnNumber': 16}"}], "failed_network_requests": []}