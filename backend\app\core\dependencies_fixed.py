"""
依赖注入模块
提供数据库会话、用户认证、权限验证等依赖
"""
from typing import Optional
from jose import JWTError, jwt

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.security import SecurityManager
from app.core.config import get_settings
from app.db.models.user import User

# 获取配置
settings = get_settings()

# 创建安全实例
security = HTTPBearer()
security_manager = SecurityManager()


async def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """从令牌获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 验证JWT令牌
        payload = security_manager.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception

    # 从数据库查询用户
    result = await db.execute(select(User).where(User.id == int(user_id)))
    user = result.scalar_one_or_none()
    
    if user is None:
        raise credentials_exception
        
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user_from_token)
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="Inactive user"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前管理员用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def rate_limit(calls: int, period: int):
    """速率限制装饰器"""
    def decorator(func):
        # 简化版速率限制，实际应用中需要Redis支持
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_permission(permission: str):
    """权限要求装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 简化版权限检查
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """获取当前用户（可选，不强制认证）"""
    if credentials is None:
        return None
        
    try:
        # 验证JWT令牌
        payload = security_manager.verify_token(credentials.credentials)
        if payload is None:
            return None
        
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
            
        # 从数据库查询用户
        result = await db.execute(select(User).where(User.id == int(user_id)))
        user = result.scalar_one_or_none()
        
        return user if user and user.is_active else None
        
    except (JWTError, ValueError):
        return None


async def get_current_user_from_websocket(websocket, db: AsyncSession = Depends(get_db)) -> Optional[User]:
    """从WebSocket获取当前用户（简化版）"""
    # 简化实现，实际应用中需要从WebSocket headers或query parameters获取token
    return None


# 向后兼容
get_current_user = get_current_user_from_token