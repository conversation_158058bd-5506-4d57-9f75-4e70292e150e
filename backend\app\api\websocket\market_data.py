import asyncio
import json
import logging
from collections import defaultdict
from typing import Any, Dict, Set

import redis.asyncio as redis
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from app.core.config import settings

# 引入全局 ConnectionManager，统一管理所有 WebSocket 连接
from app.core.websocket import Channels, WSMessage, ws_service

logger = logging.getLogger(__name__)

router = APIRouter()


class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        # 活跃的WebSocket连接
        self.active_connections: Dict[str, WebSocket] = {}

        # 每个客户端订阅的股票代码
        self.client_subscriptions: Dict[str, Set[str]] = {}

        # Redis连接
        self.redis = None
        self.pubsub = None

        # 运行状态
        self.is_running = False

    async def connect(self, websocket: WebSocket, client_id: str):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            self.active_connections[client_id] = websocket
            self.client_subscriptions[client_id] = set()

            logger.info(f"✅ WebSocket连接已建立: {client_id}")

            # 发送连接确认消息
            await self.send_to_client(
                client_id,
                {
                    "type": "connection",
                    "status": "connected",
                    "message": "WebSocket连接成功",
                },
            )

        except Exception as e:
            logger.error(f"❌ WebSocket连接失败 {client_id}: {e}")
            raise

    async def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        try:
            # 清理订阅
            if client_id in self.client_subscriptions:
                del self.client_subscriptions[client_id]

            # 清理连接
            if client_id in self.active_connections:
                del self.active_connections[client_id]

            logger.info(f"🔌 WebSocket连接已关闭: {client_id}")

        except Exception as e:
            logger.error(f"❌ 断开WebSocket连接失败 {client_id}: {e}")

    async def send_to_client(self, client_id: str, data: dict):
        """向指定客户端发送数据"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_json(data)
            except Exception as e:
                logger.warning(f"向客户端 {client_id} 发送数据失败: {e}")
                await self.disconnect(client_id)

    async def broadcast(self, data: dict, exclude_client: str = None):
        """广播消息给所有客户端"""
        disconnected_clients = []

        for client_id, websocket in self.active_connections.items():
            if exclude_client and client_id == exclude_client:
                continue

            try:
                await websocket.send_json(data)
            except Exception as e:
                logger.warning(f"向客户端 {client_id} 广播失败: {e}")
                disconnected_clients.append(client_id)

        # 清理断开的连接
        for client_id in disconnected_clients:
            await self.disconnect(client_id)

    async def subscribe_symbol(self, client_id: str, symbol: str):
        """为客户端订阅特定股票"""
        if client_id in self.client_subscriptions:
            self.client_subscriptions[client_id].add(symbol)
            logger.info(f"📡 客户端 {client_id} 订阅股票: {symbol}")

            # 发送订阅确认
            await self.send_to_client(
                client_id,
                {
                    "type": "subscription",
                    "action": "subscribe",
                    "symbol": symbol,
                    "status": "success",
                },
            )

    async def unsubscribe_symbol(self, client_id: str, symbol: str):
        """取消订阅特定股票"""
        if client_id in self.client_subscriptions:
            self.client_subscriptions[client_id].discard(symbol)
            logger.info(f"📡 客户端 {client_id} 取消订阅股票: {symbol}")

            # 发送取消订阅确认
            await self.send_to_client(
                client_id,
                {
                    "type": "subscription",
                    "action": "unsubscribe",
                    "symbol": symbol,
                    "status": "success",
                },
            )

    async def start_redis_listener(self):
        """启动Redis订阅监听"""
        if self.is_running:
            return

        try:
            # 初始化Redis连接
            self.redis = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
            )

            # 测试连接
            await self.redis.ping()
            logger.info("✅ WebSocket Redis连接成功")

            # 创建PubSub对象
            self.pubsub = self.redis.pubsub()

            # 订阅频道
            await self.pubsub.subscribe("market:ticks")  # 股票行情
            await self.pubsub.subscribe("market:indices")  # 指数行情

            logger.info("📡 已订阅Redis行情频道")

            # 启动消息监听循环
            self.is_running = True
            asyncio.create_task(self._listen_redis_messages())

        except Exception as e:
            logger.error(f"❌ 启动Redis监听失败: {e}")
            raise

    async def stop_redis_listener(self):
        """停止Redis监听"""
        self.is_running = False

        if self.pubsub:
            await self.pubsub.unsubscribe()
            await self.pubsub.close()

        if self.redis:
            await self.redis.close()

        logger.info("⛔ Redis监听已停止")

    async def _listen_redis_messages(self):
        """监听Redis消息并分发"""
        logger.info("🎧 开始监听Redis消息...")

        try:
            async for message in self.pubsub.listen():
                if not self.is_running:
                    break

                if message["type"] == "message":
                    try:
                        # 解析行情数据
                        data = json.loads(message["data"])
                        channel = message["channel"]

                        # 根据频道类型处理消息
                        if channel == "market:ticks":
                            await self._handle_stock_tick(data)
                        elif channel == "market:indices":
                            await self._handle_index_tick(data)

                    except Exception as e:
                        logger.error(f"处理Redis消息失败: {e}")

        except Exception as e:
            logger.error(f"Redis消息监听异常: {e}")
        finally:
            logger.info("🔚 Redis消息监听结束")

    async def _handle_stock_tick(self, tick_data: dict):
        """处理股票行情数据"""
        symbol = tick_data.get("symbol", "")

        # 找到订阅了这个股票的客户端
        interested_clients = []
        for client_id, subscriptions in self.client_subscriptions.items():
            if symbol in subscriptions or "*" in subscriptions:  # '*' 表示订阅所有
                interested_clients.append(client_id)

        # 发送给相关客户端
        if interested_clients:
            message = {"type": "tick", "data": tick_data}

            for client_id in interested_clients:
                await self.send_to_client(client_id, message)

    async def _handle_index_tick(self, tick_data: dict):
        """处理指数行情数据"""
        # 指数数据广播给所有客户端
        message = {"type": "index", "data": tick_data}

        await self.broadcast(message)

    async def send_heartbeat(self):
        """发送心跳包"""
        heartbeat_msg = {
            "type": "heartbeat",
            "timestamp": asyncio.get_event_loop().time(),
        }

        await self.broadcast(heartbeat_msg)

    def get_stats(self) -> dict:
        """获取连接统计信息"""
        total_subscriptions = sum(
            len(subs) for subs in self.client_subscriptions.values()
        )

        return {
            "active_connections": len(self.active_connections),
            "total_subscriptions": total_subscriptions,
            "is_running": self.is_running,
            "clients": list(self.active_connections.keys()),
        }


# ---------------------------------------------------------------------------
# Use a unified manager that delegates到核心 ws_service.manager，避免多实例冲突
# ---------------------------------------------------------------------------


class UnifiedWebSocketManager:  # noqa: E302
    """适配旧行情接口的 WebSocketManager，内部委托给核心 ConnectionManager。

    通过频道命名规则 `market.{symbol}` 实现合约级订阅/广播，与旧 `subscribe_symbol` 接口保持兼容。
    """

    def __init__(self):
        self._manager = ws_service.manager  # 全局 ConnectionManager
        # 本地记录客户端订阅关系（仅用于查询统计，无推送逻辑依赖）
        self.client_subscriptions: Dict[str, Set[str]] = defaultdict(set)

    # ------------------------- 连接生命周期 ------------------------- #
    async def connect(self, websocket: WebSocket, client_id: str):
        await self._manager.connect(websocket, client_id=client_id)

    async def disconnect(self, client_id: str):
        await self._manager.disconnect(client_id)

    # --------------------------- 消息发送 --------------------------- #
    async def send_to_client(self, client_id: str, data: Dict[str, Any]):
        msg = WSMessage(type=data.get("type", "market_data"), data=data)
        await self._manager.send_personal_message(msg, client_id)

    async def broadcast(self, data: Dict[str, Any], exclude_client: str = None):
        msg = WSMessage(type=data.get("type", "market_data"), data=data)
        if exclude_client:
            # ConnectionManager 当前不支持排除单客户端广播，这里逐个发送
            for cid in list(self._manager.active_connections.keys()):
                if cid == exclude_client:
                    continue
                await self._manager.send_personal_message(msg, cid)
        else:
            await self._manager.broadcast_to_all(msg)

    # --------------------------- 订阅逻辑 --------------------------- #
    async def subscribe_symbol(self, client_id: str, symbol: str):
        channel = Channels.market_symbol(symbol)
        self._manager.subscribe_channel(client_id, channel)
        self.client_subscriptions[client_id].add(symbol)

    async def unsubscribe_symbol(self, client_id: str, symbol: str):
        channel = Channels.market_symbol(symbol)
        # ConnectionManager 目前无显式取消方法；间接通过元数据移除
        self._manager.channel_subscriptions[channel].discard(client_id)
        self.client_subscriptions[client_id].discard(symbol)

    # --------------------------- 统计辅助 --------------------------- #
    def get_stats(self) -> dict:
        total_subscriptions = sum(len(v) for v in self.client_subscriptions.values())
        return {
            "active_connections": len(self._manager.active_connections),
            "total_subscriptions": total_subscriptions,
            "clients": list(self._manager.active_connections.keys()),
        }


# 用统一管理器替换旧实例
websocket_manager = UnifiedWebSocketManager()


@router.websocket("/realtime")
async def websocket_endpoint(websocket: WebSocket, client_id: str = "default"):
    """
    行情实时数据WebSocket端点
    - client_id: 客户端唯一标识
    """
    await websocket_manager.connect(websocket, client_id)

    try:
        while True:
            message_text = await websocket.receive_text()
            message_data = json.loads(message_text)
            await handle_client_message(client_id, message_data)

    except WebSocketDisconnect:
        await websocket_manager.disconnect(client_id)
        logger.info(f"客户端 {client_id} 已断开连接")
    except Exception as e:
        logger.error(f"与客户端 {client_id} 通信时发生错误: {e}")
        await websocket_manager.disconnect(client_id)


async def handle_client_message(client_id: str, message: dict):
    """处理来自客户端的WebSocket消息"""

    message_type = message.get("type")

    if message_type == "subscribe":
        symbol = message.get("symbol")
        if symbol:
            await websocket_manager.subscribe_symbol(client_id, symbol)

    elif message_type == "unsubscribe":
        symbol = message.get("symbol")
        if symbol:
            await websocket_manager.unsubscribe_symbol(client_id, symbol)

    elif message_type == "ping":
        await websocket_manager.send_to_client(
            client_id, {"type": "pong", "timestamp": asyncio.get_event_loop().time()}
        )
    else:
        logger.warning(f"收到来自 {client_id} 的未知消息类型: {message_type}")
        await websocket_manager.send_to_client(
            client_id, {"type": "error", "message": f"未知消息类型: {message_type}"}
        )


async def heartbeat_task():
    """定期发送心跳任务"""
    while True:
        await asyncio.sleep(settings.WS_HEARTBEAT_INTERVAL)
        await websocket_manager.send_heartbeat()


# 在应用启动时，可以启动心跳任务
# asyncio.create_task(heartbeat_task())
