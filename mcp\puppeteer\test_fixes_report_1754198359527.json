{"timestamp": "2025-08-03T05:19:19.527Z", "tests": {"homepage": {"status": "pass", "message": "首页加载正常"}, "simulatedTrading": {"status": "fail", "message": "现代化组件未加载"}, "liveTrading": {"status": "pass", "message": "JavaScript错误已修复"}, "interaction": {"status": "pass", "message": "基本交互功能正常"}}, "modernComponents": {".simulated-trading-modern": {"exists": false, "visible": false, "text": ""}, ".modern-header": {"exists": false, "visible": false, "text": ""}, ".account-dashboard": {"exists": false, "visible": false, "text": ""}, ".trading-workspace": {"exists": false, "visible": false, "text": ""}, ".stock-card": {"exists": false, "visible": false, "text": ""}, ".trade-form-container": {"exists": false, "visible": false, "text": ""}, ".bottom-data-panel": {"exists": false, "visible": false, "text": ""}}, "errors": [], "consoleMessages": [{"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T05:19:04.503Z"}, {"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T05:19:06.755Z"}, {"type": "error", "text": "Error: JSHandle@object", "timestamp": "2025-08-03T05:19:08.204Z"}, {"type": "error", "text": "🚨 未处理的Promise异常: JSHandle@error", "timestamp": "2025-08-03T05:19:08.205Z"}, {"type": "error", "text": "路由错误: JSHandle@error", "timestamp": "2025-08-03T05:19:08.215Z"}, {"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T05:19:12.060Z"}, {"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T05:19:16.539Z"}, {"type": "error", "text": "路由错误: JSHandle@error", "timestamp": "2025-08-03T05:19:16.800Z"}], "screenshots": ["test_fixes_homepage.png", "test_fixes_simulated.png", "test_fixes_live.png", "test_fixes_interaction.png"]}