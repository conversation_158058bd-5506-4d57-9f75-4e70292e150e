"""
日志轮转和清理机制
提供自动化的日志文件管理功能
"""

import os
import gzip
import shutil
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

from loguru import logger as loguru_logger
from app.core.config import settings


@dataclass
class LogRotationConfig:
    """日志轮转配置"""
    max_size: str = "100MB"  # 单个日志文件最大大小
    max_files: int = 10      # 保留的日志文件数量
    max_age_days: int = 30   # 日志保留天数
    compression: bool = True  # 是否压缩旧日志
    check_interval: int = 3600  # 检查间隔（秒）
    enable_cleanup: bool = True  # 是否启用清理


@dataclass
class LogCleanupStats:
    """日志清理统计"""
    files_deleted: int = 0
    files_compressed: int = 0
    space_freed: int = 0  # 字节
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class LogRotationManager:
    """日志轮转管理器"""
    
    def __init__(self, config: LogRotationConfig = None):
        self.config = config or LogRotationConfig()
        self.log_dir = Path(getattr(settings, "LOG_DIR", "logs"))
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.is_running = False
        self.cleanup_task = None
        
        # 确保日志目录存在
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        loguru_logger.info(f"日志轮转管理器已初始化，配置: {self.config}")

    async def start(self):
        """启动日志轮转管理器"""
        if self.is_running:
            return
            
        self.is_running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        loguru_logger.info(
            f"日志轮转管理器已启动，检查间隔: {self.config.check_interval}秒"
        )

    async def stop(self):
        """停止日志轮转管理器"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        self.executor.shutdown(wait=True)
        loguru_logger.info("日志轮转管理器已停止")

    async def _cleanup_loop(self):
        """清理循环"""
        while self.is_running:
            try:
                await asyncio.sleep(self.config.check_interval)
                
                if self.config.enable_cleanup:
                    stats = await self.cleanup_logs()
                    
                    if stats.files_deleted > 0 or stats.files_compressed > 0:
                        loguru_logger.info(
                            f"日志清理完成: 删除{stats.files_deleted}个文件, "
                            f"压缩{stats.files_compressed}个文件, "
                            f"释放{self._format_size(stats.space_freed)}空间"
                        )
                    
                    if stats.errors:
                        loguru_logger.warning(f"日志清理错误: {stats.errors}")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                loguru_logger.error(f"日志清理循环错误: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试

    async def cleanup_logs(self) -> LogCleanupStats:
        """清理日志文件"""
        stats = LogCleanupStats()
        
        try:
            # 在线程池中执行清理
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor, self._cleanup_logs_sync
            )
            return result
            
        except Exception as e:
            stats.errors.append(str(e))
            loguru_logger.error(f"日志清理失败: {e}")
            return stats

    def _cleanup_logs_sync(self) -> LogCleanupStats:
        """同步清理日志文件"""
        stats = LogCleanupStats()
        
        try:
            # 获取所有日志文件
            log_files = self._get_log_files()
            
            # 按类型分组处理
            for log_type, files in log_files.items():
                type_stats = self._cleanup_log_type(log_type, files)
                stats.files_deleted += type_stats.files_deleted
                stats.files_compressed += type_stats.files_compressed
                stats.space_freed += type_stats.space_freed
                stats.errors.extend(type_stats.errors)
                
        except Exception as e:
            stats.errors.append(f"清理过程异常: {str(e)}")
            
        return stats

    def _get_log_files(self) -> Dict[str, List[Path]]:
        """获取所有日志文件，按类型分组"""
        log_files = {
            'app': [],
            'trading': [],
            'error': [],
            'loguru': [],
            'structured': [],
            'other': []
        }
        
        for file_path in self.log_dir.glob("*.log*"):
            if file_path.is_file():
                if 'app' in file_path.name:
                    log_files['app'].append(file_path)
                elif 'trading' in file_path.name:
                    log_files['trading'].append(file_path)
                elif 'error' in file_path.name:
                    log_files['error'].append(file_path)
                elif 'loguru' in file_path.name:
                    log_files['loguru'].append(file_path)
                elif 'structured' in file_path.name:
                    log_files['structured'].append(file_path)
                else:
                    log_files['other'].append(file_path)
        
        return log_files

    def _cleanup_log_type(self, log_type: str, files: List[Path]) -> LogCleanupStats:
        """清理特定类型的日志文件"""
        stats = LogCleanupStats()
        
        if not files:
            return stats
        
        try:
            # 按修改时间排序（最新的在前）
            files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            cutoff_time = datetime.now() - timedelta(days=self.config.max_age_days)
            cutoff_timestamp = cutoff_time.timestamp()
            
            # 处理文件数量超限
            if len(files) > self.config.max_files:
                excess_files = files[self.config.max_files:]
                for file_path in excess_files:
                    try:
                        size = file_path.stat().st_size
                        file_path.unlink()
                        stats.files_deleted += 1
                        stats.space_freed += size
                        loguru_logger.debug(f"删除超限日志文件: {file_path}")
                    except Exception as e:
                        stats.errors.append(f"删除文件{file_path}失败: {str(e)}")
                
                # 更新文件列表
                files = files[:self.config.max_files]
            
            # 处理过期文件
            for file_path in files:
                try:
                    file_time = file_path.stat().st_mtime
                    
                    if file_time < cutoff_timestamp:
                        size = file_path.stat().st_size
                        file_path.unlink()
                        stats.files_deleted += 1
                        stats.space_freed += size
                        loguru_logger.debug(f"删除过期日志文件: {file_path}")
                    elif self.config.compression and not file_path.name.endswith('.gz'):
                        # 压缩较老的文件（保留最新的几个不压缩）
                        if files.index(file_path) >= 2:  # 保留最新的2个文件不压缩
                            compressed_stats = self._compress_file(file_path)
                            stats.files_compressed += compressed_stats.files_compressed
                            stats.space_freed += compressed_stats.space_freed
                            stats.errors.extend(compressed_stats.errors)
                            
                except Exception as e:
                    stats.errors.append(f"处理文件{file_path}失败: {str(e)}")
                    
        except Exception as e:
            stats.errors.append(f"清理{log_type}日志失败: {str(e)}")
            
        return stats

    def _compress_file(self, file_path: Path) -> LogCleanupStats:
        """压缩单个文件"""
        stats = LogCleanupStats()
        
        try:
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            # 如果压缩文件已存在，跳过
            if compressed_path.exists():
                return stats
            
            original_size = file_path.stat().st_size
            
            # 压缩文件
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除原文件
            file_path.unlink()
            
            compressed_size = compressed_path.stat().st_size
            space_saved = original_size - compressed_size
            
            stats.files_compressed = 1
            stats.space_freed = space_saved
            
            loguru_logger.debug(
                f"压缩日志文件: {file_path} -> {compressed_path}, "
                f"节省{self._format_size(space_saved)}空间"
            )
            
        except Exception as e:
            stats.errors.append(f"压缩文件{file_path}失败: {str(e)}")
            
        return stats

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        size = float(size_bytes)
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f}{units[unit_index]}"

    async def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, self._get_log_statistics_sync
        )

    def _get_log_statistics_sync(self) -> Dict[str, Any]:
        """同步获取日志统计信息"""
        stats = {
            'total_files': 0,
            'total_size': 0,
            'compressed_files': 0,
            'uncompressed_files': 0,
            'oldest_file': None,
            'newest_file': None,
            'by_type': {}
        }
        
        try:
            log_files = self._get_log_files()
            oldest_time = float('inf')
            newest_time = 0
            
            for log_type, files in log_files.items():
                type_stats = {
                    'count': len(files),
                    'size': 0,
                    'compressed': 0,
                    'uncompressed': 0
                }
                
                for file_path in files:
                    try:
                        file_stat = file_path.stat()
                        file_size = file_stat.st_size
                        file_time = file_stat.st_mtime
                        
                        type_stats['size'] += file_size
                        stats['total_size'] += file_size
                        
                        if file_path.name.endswith('.gz'):
                            type_stats['compressed'] += 1
                            stats['compressed_files'] += 1
                        else:
                            type_stats['uncompressed'] += 1
                            stats['uncompressed_files'] += 1
                        
                        if file_time < oldest_time:
                            oldest_time = file_time
                            stats['oldest_file'] = {
                                'path': str(file_path),
                                'time': datetime.fromtimestamp(file_time).isoformat(),
                                'size': file_size
                            }
                        
                        if file_time > newest_time:
                            newest_time = file_time
                            stats['newest_file'] = {
                                'path': str(file_path),
                                'time': datetime.fromtimestamp(file_time).isoformat(),
                                'size': file_size
                            }
                            
                    except Exception as e:
                        loguru_logger.warning(f"获取文件{file_path}统计失败: {e}")
                
                stats['by_type'][log_type] = type_stats
                stats['total_files'] += type_stats['count']
            
            # 格式化大小
            stats['total_size_formatted'] = self._format_size(stats['total_size'])
            for log_type in stats['by_type']:
                stats['by_type'][log_type]['size_formatted'] = self._format_size(
                    stats['by_type'][log_type]['size']
                )
                
        except Exception as e:
            loguru_logger.error(f"获取日志统计失败: {e}")
            
        return stats

    async def force_cleanup(self) -> LogCleanupStats:
        """强制执行日志清理"""
        loguru_logger.info("开始强制日志清理")
        return await self.cleanup_logs()

    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                loguru_logger.info(f"更新日志轮转配置: {key} = {value}")

    async def compress_all_logs(self) -> LogCleanupStats:
        """压缩所有未压缩的日志文件"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, self._compress_all_logs_sync
        )

    def _compress_all_logs_sync(self) -> LogCleanupStats:
        """同步压缩所有日志文件"""
        stats = LogCleanupStats()
        
        try:
            log_files = self._get_log_files()
            
            for log_type, files in log_files.items():
                for file_path in files:
                    if not file_path.name.endswith('.gz'):
                        compress_stats = self._compress_file(file_path)
                        stats.files_compressed += compress_stats.files_compressed
                        stats.space_freed += compress_stats.space_freed
                        stats.errors.extend(compress_stats.errors)
                        
        except Exception as e:
            stats.errors.append(f"批量压缩失败: {str(e)}")
            
        return stats


# 全局日志轮转管理器实例
log_rotation_manager = LogRotationManager()


async def setup_log_rotation():
    """设置日志轮转"""
    await log_rotation_manager.start()
    loguru_logger.info("日志轮转系统已启动")


async def shutdown_log_rotation():
    """关闭日志轮转"""
    await log_rotation_manager.stop()
    loguru_logger.info("日志轮转系统已关闭")


def get_log_rotation_manager() -> LogRotationManager:
    """获取日志轮转管理器实例"""
    return log_rotation_manager