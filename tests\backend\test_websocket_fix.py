#!/usr/bin/env python3
"""
WebSocket功能测试脚本
用于验证修复后的WebSocket系统是否正常工作
"""

import asyncio
import json
import logging
from datetime import datetime

import websockets

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_general_websocket():
    """测试通用WebSocket连接"""
    uri = "ws://localhost:8000/ws/general"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("连接WebSocket成功")
            
            # 发送认证消息
            auth_message = {
                "type": "auth",
                "token": "dev-token-for-testing",
                "client_id": "test-client-1"
            }
            await websocket.send(json.dumps(auth_message))
            
            # 接收认证响应
            response = await websocket.recv()
            auth_response = json.loads(response)
            logger.info(f"认证响应: {auth_response}")
            
            # 订阅频道
            subscribe_message = {
                "type": "subscribe",
                "channel": "test-channel"
            }
            await websocket.send(json.dumps(subscribe_message))
            
            # 接收订阅响应
            response = await websocket.recv()
            subscribe_response = json.loads(response)
            logger.info(f"订阅响应: {subscribe_response}")
            
            # 发送心跳
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_message))
            
            # 接收心跳响应
            response = await websocket.recv()
            pong_response = json.loads(response)
            logger.info(f"心跳响应: {pong_response}")
            
            # 监听一段时间的消息
            try:
                for _ in range(3):
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    message = json.loads(response)
                    logger.info(f"收到消息: {message}")
            except asyncio.TimeoutError:
                logger.info("没有更多消息")
                
    except Exception as e:
        logger.error(f"WebSocket测试失败: {e}")


async def test_simple_websocket():
    """测试简单WebSocket连接"""
    uri = "ws://localhost:8000/ws/simple"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("连接简单WebSocket成功")
            
            # 接收欢迎消息
            response = await websocket.recv()
            welcome_message = json.loads(response)
            logger.info(f"欢迎消息: {welcome_message}")
            
            # 发送ping消息
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_message))
            
            # 接收pong响应
            response = await websocket.recv()
            pong_message = json.loads(response)
            logger.info(f"Pong响应: {pong_message}")
            
            # 发送echo消息
            echo_message = {
                "type": "echo",
                "data": "Hello WebSocket!"
            }
            await websocket.send(json.dumps(echo_message))
            
            # 接收echo响应
            response = await websocket.recv()
            echo_response = json.loads(response)
            logger.info(f"Echo响应: {echo_response}")
            
    except Exception as e:
        logger.error(f"简单WebSocket测试失败: {e}")


async def test_websocket_core():
    """测试核心WebSocket服务"""
    try:
        from app.core.websocket import ws_service, WSMessage, MessageType
        
        # 测试WebSocket服务创建
        logger.info("测试WebSocket服务创建...")
        assert ws_service is not None
        assert ws_service.manager is not None
        logger.info("✓ WebSocket服务创建成功")
        
        # 测试消息创建
        logger.info("测试消息创建...")
        message = WSMessage(
            type=MessageType.HEARTBEAT,
            data={"test": "data"},
            client_id="test-client"
        )
        assert message.type == MessageType.HEARTBEAT
        assert message.data == {"test": "data"}
        assert message.client_id == "test-client"
        logger.info("✓ 消息创建成功")
        
        # 测试连接统计
        logger.info("测试连接统计...")
        stats = ws_service.get_connection_info()
        assert isinstance(stats, dict)
        assert "total_connections" in stats
        logger.info(f"✓ 连接统计获取成功: {stats}")
        
    except ImportError as e:
        logger.error(f"导入WebSocket模块失败: {e}")
    except Exception as e:
        logger.error(f"WebSocket核心测试失败: {e}")


async def test_websocket_manager():
    """测试WebSocket管理器"""
    try:
        from app.api.websocket.connection import websocket_manager
        
        logger.info("测试WebSocket管理器...")
        assert websocket_manager is not None
        assert hasattr(websocket_manager, 'connect')
        assert hasattr(websocket_manager, 'disconnect')
        assert hasattr(websocket_manager, 'subscribe')
        assert hasattr(websocket_manager, 'send_to_connection')
        assert hasattr(websocket_manager, 'broadcast_to_topic')
        logger.info("✓ WebSocket管理器测试成功")
        
        # 测试统计信息
        stats = websocket_manager.get_connection_stats()
        assert isinstance(stats, dict)
        logger.info(f"✓ 管理器统计信息获取成功: {stats}")
        
    except ImportError as e:
        logger.error(f"导入WebSocket管理器失败: {e}")
    except Exception as e:
        logger.error(f"WebSocket管理器测试失败: {e}")


async def main():
    """主测试函数"""
    logger.info("开始WebSocket功能测试...")
    
    # 测试核心模块
    await test_websocket_core()
    await test_websocket_manager()
    
    # 测试实际连接（需要服务器运行）
    logger.info("\n注意: 以下测试需要服务器运行在 localhost:8000")
    logger.info("如果服务器未运行，连接测试将失败")
    
    try:
        await test_simple_websocket()
        await test_general_websocket()
    except Exception as e:
        logger.warning(f"连接测试失败（可能服务器未运行）: {e}")
    
    logger.info("WebSocket功能测试完成")


if __name__ == "__main__":
    asyncio.run(main())