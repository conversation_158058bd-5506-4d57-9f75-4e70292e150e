{"analysis_timestamp": "2025-07-27T15:21:47.631187", "overall_score": 50.0, "detailed_results": {"platform_overview": {"title": "量化交易平台", "main_url": "http://localhost:5173/", "load_time": 1.93, "accessible": true}, "authentication_system": {"elements": {"login_form": true, "username_input": true, "password_input": true, "login_button": true, "demo_login_button": true}, "demo_login_success": true, "redirect_url": "http://localhost:5173/puzzle-verify"}, "puzzle_verification": {"page_title": "量化交易平台", "page_url": "http://localhost:5173/puzzle-verify", "canvas_count": 2, "puzzle_canvas": true, "block_canvas": true, "slider_track": true, "slider_button": true, "slider_text": true, "continue_button": true, "back_button": true, "refresh_button": true}, "backend_services": {"health_check": {"status": "success", "data": {"message": "Welcome to Quant Platform API (Dev)", "version": "1.0.0", "docs": "/docs"}}, "login_api": {"status": "success", "has_token": true}, "endpoint_register": {"status": "accessible", "code": 405}, "endpoint_profile": {"status": "accessible", "code": 404}, "endpoint_accounts": {"status": "accessible", "code": 404}}, "performance_metrics": {"page_load_time": 226.5, "dom_content_loaded": 222.**************, "memory_used": ********, "memory_total": ********, "memory_limit": **********, "total_requests": 242, "avg_response_time": 39.**************}, "security_assessment": {}, "user_experience": {}, "technical_implementation": {}, "issues_found": [{"type": "console_error", "message": "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "timestamp": "2025-07-27T15:21:32.399209"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.757740"}, {"type": "console_error", "message": "获取市场概览失败: Error: API请求失败: 404 Not Found\n    at fetchMarketOverview (http://localhost:5173/src/stores/modules/market.ts?t=*************:292:15)\n    at async Promise.allSettled (index 0)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:32.758741"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.759146"}, {"type": "console_error", "message": "获取板块数据失败: Error: API请求失败: 404 Not Found\n    at fetchSectors (http://localhost:5173/src/stores/modules/market.ts?t=*************:402:15)\n    at async Promise.allSettled (index 2)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:32.759713"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.760069"}, {"type": "console_error", "message": "获取排行榜失败: Error: API请求失败: 404 Not Found\n    at fetchRankings (http://localhost:5173/src/stores/modules/market.ts?t=*************:450:15)\n    at async Promise.allSettled (index 4)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:32.760462"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.760655"}, {"type": "console_error", "message": "获取排行榜失败: Error: API请求失败: 404 Not Found\n    at fetchRankings (http://localhost:5173/src/stores/modules/market.ts?t=*************:450:15)\n    at async Promise.allSettled (index 5)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:32.760996"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:32.762Z}", "timestamp": "2025-07-27T15:21:32.767965"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:351:82\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:32.768888"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:32.765Z}", "timestamp": "2025-07-27T15:21:32.771422"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>aleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:395:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:32.772118"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:32.766Z}", "timestamp": "2025-07-27T15:21:32.772623"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:439:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:32.773093"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:32.767Z}", "timestamp": "2025-07-27T15:21:32.774406"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:351:82\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:32.774861"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:32.768Z}", "timestamp": "2025-07-27T15:21:32.775268"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>aleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:395:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:32.775615"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:32.768Z}", "timestamp": "2025-07-27T15:21:32.775932"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:439:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:32.776303"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.779218"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:21:32.781553"}, {"type": "console_error", "message": "fetchStockList error AxiosError", "timestamp": "2025-07-27T15:21:32.781978"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.782188"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:21:32.782615"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:32.785639"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:21:32.787573"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "timestamp": "2025-07-27T15:21:32.907645"}, {"type": "console_error", "message": "WebSocket错误: Event", "timestamp": "2025-07-27T15:21:32.909033"}, {"type": "console_error", "message": "Error: {type: SY<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket连接错误, details: undefined, context: Object}", "timestamp": "2025-07-27T15:21:32.914669"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接错误\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-07-27T15:21:32.918676"}, {"type": "console_error", "message": "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "timestamp": "2025-07-27T15:21:33.969349"}, {"type": "console_error", "message": "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "timestamp": "2025-07-27T15:21:34.920146"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.206493"}, {"type": "console_error", "message": "获取市场概览失败: Error: API请求失败: 404 Not Found\n    at fetchMarketOverview (http://localhost:5173/src/stores/modules/market.ts?t=*************:292:15)\n    at async Promise.allSettled (index 0)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:46.207339"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.209818"}, {"type": "console_error", "message": "获取板块数据失败: Error: API请求失败: 404 Not Found\n    at fetchSectors (http://localhost:5173/src/stores/modules/market.ts?t=*************:402:15)\n    at async Promise.allSettled (index 2)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:46.213433"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.214107"}, {"type": "console_error", "message": "获取排行榜失败: Error: API请求失败: 404 Not Found\n    at fetchRankings (http://localhost:5173/src/stores/modules/market.ts?t=*************:450:15)\n    at async Promise.allSettled (index 4)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:46.214503"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.214675"}, {"type": "console_error", "message": "获取排行榜失败: Error: API请求失败: 404 Not Found\n    at fetchRankings (http://localhost:5173/src/stores/modules/market.ts?t=*************:450:15)\n    at async Promise.allSettled (index 5)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=*************:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:187:9", "timestamp": "2025-07-27T15:21:46.215134"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:46.198Z}", "timestamp": "2025-07-27T15:21:46.216437"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:351:82\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:46.217061"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:46.199Z}", "timestamp": "2025-07-27T15:21:46.217430"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>aleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:395:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:46.218401"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:46.199Z}", "timestamp": "2025-07-27T15:21:46.218804"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:439:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:46.219119"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:46.202Z}", "timestamp": "2025-07-27T15:21:46.220362"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:351:82\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:46.221243"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:46.202Z}", "timestamp": "2025-07-27T15:21:46.221790"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>aleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:395:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:46.222374"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:21:46.203Z}", "timestamp": "2025-07-27T15:21:46.222896"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toL<PERSON>ale<PERSON>tring')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue?t=*************:439:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:21:46.223451"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.224789"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:21:46.225072"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.225206"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:21:46.225515"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:21:46.225648"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:21:46.225916"}, {"type": "console_error", "message": "fetchStockList error AxiosError", "timestamp": "2025-07-27T15:21:46.226193"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "timestamp": "2025-07-27T15:21:46.253897"}, {"type": "console_error", "message": "WebSocket错误: Event", "timestamp": "2025-07-27T15:21:46.254613"}, {"type": "console_error", "message": "Error: {type: SY<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket连接错误, details: undefined, context: Object}", "timestamp": "2025-07-27T15:21:46.257464"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接错误\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-07-27T15:21:46.264830"}], "recommendations": ["优化拼图验证算法，提高验证成功率", "检查继续访问功能的路由配置", "修复发现的JavaScript错误和网络请求问题"]}}