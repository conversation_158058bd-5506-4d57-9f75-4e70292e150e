#!/usr/bin/env python3
"""
最终MCP真实用户测试报告生成器
汇总所有测试结果，生成综合的用户体验评估报告
"""

import json
import time
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinalMCPTestingReportGenerator:
    def __init__(self):
        self.report_id = f"final_mcp_report_{int(time.time())}"
        self.final_report = {
            'report_id': self.report_id,
            'generation_time': datetime.now().isoformat(),
            'report_type': 'Final MCP Real User Testing Report',
            'executive_summary': {},
            'detailed_findings': {},
            'user_experience_assessment': {},
            'technical_analysis': {},
            'recommendations': {},
            'conclusion': {}
        }
        
    def load_test_reports(self):
        """加载所有测试报告"""
        reports_dir = Path("reports")
        test_reports = []
        
        if reports_dir.exists():
            # 查找所有JSON报告文件
            json_files = list(reports_dir.glob("*.json"))
            
            for file in json_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)
                        report_data['source_file'] = str(file)
                        test_reports.append(report_data)
                        logger.info(f"加载报告: {file.name}")
                except Exception as e:
                    logger.warning(f"无法加载报告 {file}: {str(e)}")
                    
        return test_reports
        
    def analyze_executive_summary(self, reports):
        """生成执行摘要"""
        logger.info("生成执行摘要")
        
        total_tests = len(reports)
        total_scenarios = sum(len(r.get('user_scenarios', [])) for r in reports)
        total_issues = 0
        
        # 统计问题
        for report in reports:
            # 统计用户场景中的问题
            for scenario in report.get('user_scenarios', []):
                total_issues += len(scenario.get('issues', []))
                
            # 统计分析结果中的问题
            for analysis in report.get('analysis_results', []):
                total_issues += len(analysis.get('issues', []))
                
            # 统计发现的问题
            total_issues += len(report.get('discovered_issues', []))
            
        # 计算测试覆盖率
        test_types = set()
        for report in reports:
            test_types.add(report.get('test_type', 'Unknown'))
            
        self.final_report['executive_summary'] = {
            'total_test_sessions': total_tests,
            'total_test_scenarios': total_scenarios,
            'total_issues_found': total_issues,
            'test_types_covered': list(test_types),
            'testing_approach': 'MCP工具组合 + 真实用户模拟',
            'overall_assessment': self._get_overall_assessment(total_issues)
        }
        
    def _get_overall_assessment(self, total_issues):
        """获取总体评估"""
        if total_issues <= 5:
            return {
                'level': '优秀',
                'description': '平台状态良好，用户体验优秀，可以投入使用',
                'confidence': '高'
            }
        elif total_issues <= 15:
            return {
                'level': '良好',
                'description': '平台基本可用，有一些改进空间',
                'confidence': '中高'
            }
        elif total_issues <= 30:
            return {
                'level': '一般',
                'description': '平台需要重要改进才能提供良好的用户体验',
                'confidence': '中'
            }
        else:
            return {
                'level': '需要改进',
                'description': '平台存在较多问题，需要大量改进工作',
                'confidence': '低'
            }
            
    def analyze_detailed_findings(self, reports):
        """分析详细发现"""
        logger.info("分析详细发现")
        
        findings = {
            'platform_accessibility': [],
            'user_interface': [],
            'functionality': [],
            'performance': [],
            'technical_architecture': []
        }
        
        for report in reports:
            # 分析用户场景
            for scenario in report.get('user_scenarios', []):
                scenario_name = scenario.get('name', '')
                
                if '可用性' in scenario_name or '访问' in scenario_name:
                    findings['platform_accessibility'].extend(scenario.get('issues', []))
                elif '界面' in scenario_name or 'UI' in scenario_name or '导航' in scenario_name:
                    findings['user_interface'].extend(scenario.get('issues', []))
                elif '功能' in scenario_name or '交易' in scenario_name or '策略' in scenario_name:
                    findings['functionality'].extend(scenario.get('issues', []))
                elif '性能' in scenario_name or '响应' in scenario_name:
                    findings['performance'].extend(scenario.get('issues', []))
                    
            # 分析技术架构
            for analysis in report.get('analysis_results', []):
                if '架构' in analysis.get('name', ''):
                    findings['technical_architecture'].extend(analysis.get('issues', []))
                    
        # 去重并统计
        for category in findings:
            findings[category] = list(set(findings[category]))
            
        self.final_report['detailed_findings'] = findings
        
    def analyze_user_experience(self, reports):
        """分析用户体验"""
        logger.info("分析用户体验")
        
        user_feedback = []
        user_insights = []
        
        for report in reports:
            # 收集用户反馈
            for scenario in report.get('user_scenarios', []):
                user_feedback.extend(scenario.get('user_feedback', []))
                
            # 收集用户体验洞察
            user_insights.extend(report.get('user_experience_insights', []))
            
            # 收集分析洞察
            for analysis in report.get('analysis_results', []):
                user_insights.extend(analysis.get('insights', []))
                
        # 分析用户体验维度
        ux_dimensions = {
            'usability': {'positive': 0, 'negative': 0, 'comments': []},
            'functionality': {'positive': 0, 'negative': 0, 'comments': []},
            'performance': {'positive': 0, 'negative': 0, 'comments': []},
            'design': {'positive': 0, 'negative': 0, 'comments': []}
        }
        
        # 分类用户反馈
        for feedback in user_feedback:
            feedback_lower = feedback.lower()
            
            # 判断正面或负面
            is_positive = any(word in feedback_lower for word in 
                            ['好', '优秀', '清晰', '快', '友好', '完整', '正常'])
            is_negative = any(word in feedback_lower for word in 
                            ['差', '慢', '缺少', '问题', '错误', '不足', '无法'])
            
            # 分类到维度
            if '导航' in feedback_lower or '界面' in feedback_lower:
                if is_positive:
                    ux_dimensions['usability']['positive'] += 1
                elif is_negative:
                    ux_dimensions['usability']['negative'] += 1
                ux_dimensions['usability']['comments'].append(feedback)
                
            elif '功能' in feedback_lower or '交易' in feedback_lower:
                if is_positive:
                    ux_dimensions['functionality']['positive'] += 1
                elif is_negative:
                    ux_dimensions['functionality']['negative'] += 1
                ux_dimensions['functionality']['comments'].append(feedback)
                
            elif '性能' in feedback_lower or '速度' in feedback_lower:
                if is_positive:
                    ux_dimensions['performance']['positive'] += 1
                elif is_negative:
                    ux_dimensions['performance']['negative'] += 1
                ux_dimensions['performance']['comments'].append(feedback)
                
            elif '设计' in feedback_lower or '样式' in feedback_lower:
                if is_positive:
                    ux_dimensions['design']['positive'] += 1
                elif is_negative:
                    ux_dimensions['design']['negative'] += 1
                ux_dimensions['design']['comments'].append(feedback)
                
        self.final_report['user_experience_assessment'] = {
            'dimensions': ux_dimensions,
            'key_insights': list(set(user_insights)),
            'overall_sentiment': self._calculate_sentiment(ux_dimensions)
        }
        
    def _calculate_sentiment(self, dimensions):
        """计算整体情感倾向"""
        total_positive = sum(d['positive'] for d in dimensions.values())
        total_negative = sum(d['negative'] for d in dimensions.values())
        
        if total_positive > total_negative * 1.5:
            return '积极'
        elif total_negative > total_positive * 1.5:
            return '消极'
        else:
            return '中性'
            
    def analyze_technical_aspects(self, reports):
        """分析技术方面"""
        logger.info("分析技术方面")
        
        technical_findings = {
            'architecture': [],
            'api_integration': [],
            'data_visualization': [],
            'ui_framework': [],
            'performance_metrics': []
        }
        
        for report in reports:
            # 分析技术架构
            for analysis in report.get('analysis_results', []):
                analysis_name = analysis.get('name', '')
                
                if '架构' in analysis_name:
                    technical_findings['architecture'].extend(analysis.get('findings', []))
                elif 'API' in analysis_name:
                    technical_findings['api_integration'].extend(analysis.get('findings', []))
                elif '可视化' in analysis_name or '图表' in analysis_name:
                    technical_findings['data_visualization'].extend(analysis.get('findings', []))
                elif '界面' in analysis_name or 'UI' in analysis_name:
                    technical_findings['ui_framework'].extend(analysis.get('findings', []))
                    
            # 收集性能指标
            technical_findings['performance_metrics'].extend(report.get('performance_metrics', []))
            
        self.final_report['technical_analysis'] = technical_findings
        
    def generate_recommendations(self, reports):
        """生成建议"""
        logger.info("生成建议")
        
        all_recommendations = []
        
        for report in reports:
            all_recommendations.extend(report.get('recommendations', []))
            
        # 去重并分类
        unique_recommendations = list(set(all_recommendations))
        
        categorized_recommendations = {
            'high_priority': [],
            'medium_priority': [],
            'low_priority': []
        }
        
        # 根据关键词分类优先级
        high_priority_keywords = ['修复', '错误', '无法', '失败', '严重']
        medium_priority_keywords = ['改进', '优化', '增加', '完善']
        
        for rec in unique_recommendations:
            rec_lower = rec.lower()
            
            if any(keyword in rec_lower for keyword in high_priority_keywords):
                categorized_recommendations['high_priority'].append(rec)
            elif any(keyword in rec_lower for keyword in medium_priority_keywords):
                categorized_recommendations['medium_priority'].append(rec)
            else:
                categorized_recommendations['low_priority'].append(rec)
                
        self.final_report['recommendations'] = categorized_recommendations
        
    def generate_conclusion(self):
        """生成结论"""
        logger.info("生成结论")
        
        assessment = self.final_report['executive_summary']['overall_assessment']
        total_issues = self.final_report['executive_summary']['total_issues_found']
        
        conclusion = {
            'summary': assessment['description'],
            'readiness_for_production': assessment['level'] in ['优秀', '良好'],
            'key_strengths': [],
            'key_weaknesses': [],
            'next_steps': []
        }
        
        # 分析优势
        technical_findings = self.final_report['technical_analysis']
        if technical_findings['architecture']:
            conclusion['key_strengths'].append("项目架构完整")
        if technical_findings['data_visualization']:
            conclusion['key_strengths'].append("数据可视化能力强")
        if technical_findings['api_integration']:
            conclusion['key_strengths'].append("API集成完善")
            
        # 分析弱点
        detailed_findings = self.final_report['detailed_findings']
        if detailed_findings['user_interface']:
            conclusion['key_weaknesses'].append("用户界面需要改进")
        if detailed_findings['functionality']:
            conclusion['key_weaknesses'].append("功能完整性有待提升")
        if detailed_findings['performance']:
            conclusion['key_weaknesses'].append("性能需要优化")
            
        # 下一步建议
        if total_issues <= 10:
            conclusion['next_steps'] = [
                "进行用户验收测试",
                "准备生产环境部署",
                "制定用户培训计划"
            ]
        else:
            conclusion['next_steps'] = [
                "优先修复高优先级问题",
                "进行下一轮测试",
                "完善文档和用户指南"
            ]
            
        self.final_report['conclusion'] = conclusion
        
    def save_final_report(self):
        """保存最终报告"""
        # 保存JSON格式
        json_file = f"reports/final_mcp_real_user_testing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.final_report, f, ensure_ascii=False, indent=2)
            
        # 生成Markdown格式
        md_file = f"reports/final_mcp_real_user_testing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self._generate_markdown_report(md_file)
        
        logger.info(f"最终报告已保存: {json_file}")
        logger.info(f"Markdown报告已保存: {md_file}")
        
    def _generate_markdown_report(self, filename):
        """生成Markdown格式报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# 量化投资平台MCP真实用户测试最终报告\n\n")
            
            # 执行摘要
            f.write("## 执行摘要\n\n")
            summary = self.final_report['executive_summary']
            f.write(f"- **测试会话数**: {summary['total_test_sessions']}\n")
            f.write(f"- **测试场景数**: {summary['total_test_scenarios']}\n")
            f.write(f"- **发现问题数**: {summary['total_issues_found']}\n")
            f.write(f"- **整体评估**: {summary['overall_assessment']['level']}\n")
            f.write(f"- **评估描述**: {summary['overall_assessment']['description']}\n\n")
            
            # 用户体验评估
            f.write("## 用户体验评估\n\n")
            ux = self.final_report['user_experience_assessment']
            f.write(f"**整体情感倾向**: {ux['overall_sentiment']}\n\n")
            
            # 建议
            f.write("## 改进建议\n\n")
            recommendations = self.final_report['recommendations']
            
            if recommendations['high_priority']:
                f.write("### 高优先级\n")
                for rec in recommendations['high_priority']:
                    f.write(f"- {rec}\n")
                f.write("\n")
                
            if recommendations['medium_priority']:
                f.write("### 中优先级\n")
                for rec in recommendations['medium_priority']:
                    f.write(f"- {rec}\n")
                f.write("\n")
                
            # 结论
            f.write("## 结论\n\n")
            conclusion = self.final_report['conclusion']
            f.write(f"{conclusion['summary']}\n\n")
            
            if conclusion['readiness_for_production']:
                f.write("✅ **平台已准备好投入生产使用**\n\n")
            else:
                f.write("⚠️ **平台需要进一步改进才能投入生产使用**\n\n")
                
    def print_summary(self):
        """打印摘要"""
        print("\n" + "=" * 80)
        print("量化投资平台MCP真实用户测试最终报告")
        print("=" * 80)
        
        summary = self.final_report['executive_summary']
        print(f"报告ID: {self.report_id}")
        print(f"测试会话: {summary['total_test_sessions']}个")
        print(f"测试场景: {summary['total_test_scenarios']}个")
        print(f"发现问题: {summary['total_issues_found']}个")
        print(f"整体评估: {summary['overall_assessment']['level']}")
        
        conclusion = self.final_report['conclusion']
        if conclusion['readiness_for_production']:
            print("\n✅ 平台已准备好投入生产使用")
        else:
            print("\n⚠️ 平台需要进一步改进")
            
        print(f"\n关键优势:")
        for strength in conclusion['key_strengths']:
            print(f"  + {strength}")
            
        print(f"\n需要改进:")
        for weakness in conclusion['key_weaknesses']:
            print(f"  - {weakness}")
            
    def generate_final_report(self):
        """生成最终报告"""
        logger.info("开始生成最终MCP真实用户测试报告")
        
        # 加载所有测试报告
        reports = self.load_test_reports()
        
        if not reports:
            logger.warning("未找到测试报告文件")
            return
            
        # 分析各个方面
        self.analyze_executive_summary(reports)
        self.analyze_detailed_findings(reports)
        self.analyze_user_experience(reports)
        self.analyze_technical_aspects(reports)
        self.generate_recommendations(reports)
        self.generate_conclusion()
        
        # 保存报告
        self.save_final_report()
        self.print_summary()
        
        logger.info("最终MCP真实用户测试报告生成完成")

def main():
    """主函数"""
    generator = FinalMCPTestingReportGenerator()
    generator.generate_final_report()

if __name__ == "__main__":
    main()
