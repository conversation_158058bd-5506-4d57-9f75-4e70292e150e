#!/usr/bin/env python3
"""
功能验证脚本
验证系统各个模块的功能是否正常工作
"""

import asyncio
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

try:
    from app.services.backtest_engine_enhanced import BacktestEngine
    from app.services.backtest_data_analyzer import BacktestDataAnalyzer
    from app.services.backtest_visualizer import BacktestVisualizer
    from app.models.trading import OrderDirection, OrderType, OrderStatus
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

class FunctionValidator:
    """功能验证器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'validations': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
    
    async def validate_backtest_engine(self) -> Dict[str, Any]:
        """验证回测引擎功能"""
        print("验证回测引擎...")
        
        try:
            engine = BacktestEngine()
            
            # 测试参数
            strategy_code = """
def simple_strategy(data):
    # 简单双均线策略
    return 1 if len(data) > 10 else 0
"""
            symbols = ["000001.SZ", "000002.SZ"]
            start_date = datetime.now() - timedelta(days=90)
            end_date = datetime.now() - timedelta(days=1)
            
            # 运行回测
            result = await engine.run_backtest(
                strategy_code=strategy_code,
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                initial_capital=100000
            )
            
            # 验证结果
            required_keys = ['summary', 'performance', 'equity_curve', 'trades']
            has_required_keys = all(key in result for key in required_keys)
            
            return {
                'status': 'PASS' if has_required_keys else 'FAIL',
                'has_required_keys': has_required_keys,
                'result_keys': list(result.keys()),
                'equity_curve_length': len(result.get('equity_curve', [])),
                'trades_count': len(result.get('trades', [])),
                'final_capital': result.get('summary', {}).get('final_capital', 0)
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    async def validate_data_analyzer(self) -> Dict[str, Any]:
        """验证数据分析器功能"""
        print("验证数据分析器...")
        
        try:
            analyzer = BacktestDataAnalyzer()
            
            # 模拟回测结果数据
            mock_result = {
                'equity_curve': [
                    {'date': '2024-01-01', 'value': 100000},
                    {'date': '2024-01-02', 'value': 101000},
                    {'date': '2024-01-03', 'value': 99500},
                    {'date': '2024-01-04', 'value': 102000},
                    {'date': '2024-01-05', 'value': 101500}
                ],
                'trades': [
                    {
                        'symbol': '000001.SZ',
                        'action': '买入',
                        'timestamp': '2024-01-01T09:30:00',
                        'amount': 10000
                    },
                    {
                        'symbol': '000001.SZ',
                        'action': '卖出',
                        'timestamp': '2024-01-03T15:00:00',
                        'amount': 10000,
                        'pnl': 500
                    }
                ]
            }
            
            # 运行分析
            analysis = await analyzer.analyze_backtest_results(mock_result)
            
            # 验证分析结果
            required_sections = [
                'risk_metrics', 'performance_attribution', 'seasonality_analysis',
                'drawdown_analysis', 'trade_analysis', 'statistical_tests'
            ]
            has_required_sections = all(section in analysis for section in required_sections)
            
            return {
                'status': 'PASS' if has_required_sections else 'FAIL',
                'has_required_sections': has_required_sections,
                'analysis_keys': list(analysis.keys()),
                'risk_metrics_count': len(analysis.get('risk_metrics', {})),
                'trade_analysis': analysis.get('trade_analysis', {})
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    async def validate_visualizer(self) -> Dict[str, Any]:
        """验证可视化器功能"""
        print("验证可视化器...")
        
        try:
            visualizer = BacktestVisualizer()
            
            # 模拟回测和分析结果
            mock_backtest_result = {
                'equity_curve': [
                    {'date': '2024-01-01', 'value': 100000, 'return': 0.0},
                    {'date': '2024-01-02', 'value': 101000, 'return': 0.01},
                    {'date': '2024-01-03', 'value': 99500, 'return': -0.015}
                ],
                'trades': [
                    {
                        'symbol': '000001.SZ',
                        'action': '卖出',
                        'pnl': 500,
                        'amount': 10000,
                        'timestamp': '2024-01-02T15:00:00'
                    }
                ],
                'positions': [
                    {
                        'symbol': '000001.SZ',
                        'market_value': 50000
                    },
                    {
                        'symbol': '000002.SZ',
                        'market_value': 30000
                    }
                ]
            }
            
            mock_analysis_result = {
                'risk_metrics': {
                    'var_95': -0.02,
                    'sortino_ratio': 1.5,
                    'downside_deviation': 0.1
                },
                'seasonality_analysis': {
                    'monthly_returns': {'01': 0.015, '02': -0.005}
                },
                'rolling_performance': {
                    '30_days': {
                        'avg_return': 0.12,
                        'avg_volatility': 0.15,
                        'avg_sharpe': 1.2
                    }
                },
                'monte_carlo': {
                    'confidence_intervals': {
                        '5%': -0.1,
                        '50%': 0.05,
                        '95%': 0.2
                    },
                    'expected_return': 0.08,
                    'probability_positive': 0.75
                }
            }
            
            # 生成可视化数据
            viz_data = await visualizer.generate_visualization_data(
                mock_backtest_result,
                mock_analysis_result
            )
            
            # 验证可视化数据
            expected_charts = [
                'equity_curve', 'drawdown_chart', 'return_distribution',
                'monthly_returns_heatmap', 'rolling_performance', 'trade_analysis',
                'risk_radar', 'position_allocation', 'performance_table', 'monte_carlo'
            ]
            has_expected_charts = all(chart in viz_data for chart in expected_charts)
            
            return {
                'status': 'PASS' if has_expected_charts else 'FAIL',
                'has_expected_charts': has_expected_charts,
                'chart_count': len(viz_data),
                'chart_types': list(viz_data.keys()),
                'equity_curve_type': viz_data.get('equity_curve', {}).get('type'),
                'performance_table_sections': len(viz_data.get('performance_table', {}).get('sections', []))
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    async def validate_trading_models(self) -> Dict[str, Any]:
        """验证交易模型"""
        print("验证交易模型...")
        
        try:
            # 测试枚举类
            order_directions = [d for d in OrderDirection]
            order_types = [t for t in OrderType]
            order_statuses = [s for s in OrderStatus]
            
            return {
                'status': 'PASS',
                'order_directions': [d.value for d in order_directions],
                'order_types': [t.value for t in order_types],
                'order_statuses': [s.value for s in order_statuses],
                'total_enums': len(order_directions) + len(order_types) + len(order_statuses)
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    async def run_all_validations(self) -> Dict[str, Any]:
        """运行所有验证"""
        validations = [
            ('backtest_engine', self.validate_backtest_engine),
            ('data_analyzer', self.validate_data_analyzer),
            ('visualizer', self.validate_visualizer),
            ('trading_models', self.validate_trading_models)
        ]
        
        for validation_name, validation_func in validations:
            print(f"运行验证: {validation_name}")
            try:
                result = await validation_func()
                self.results['validations'][validation_name] = result
                self.results['summary']['total_tests'] += 1
                
                if result['status'] == 'PASS':
                    self.results['summary']['passed'] += 1
                else:
                    self.results['summary']['failed'] += 1
                    if 'error' in result:
                        self.results['summary']['errors'].append({
                            'validation': validation_name,
                            'error': result['error']
                        })
                        
            except Exception as e:
                self.results['validations'][validation_name] = {
                    'status': 'FAIL',
                    'error': str(e),
                    'error_type': type(e).__name__
                }
                self.results['summary']['failed'] += 1
                self.results['summary']['total_tests'] += 1
                self.results['summary']['errors'].append({
                    'validation': validation_name,
                    'error': str(e)
                })
        
        return self.results
    
    def generate_report(self) -> str:
        """生成验证报告"""
        report = []
        report.append("=" * 60)
        report.append("功能验证报告")
        report.append("=" * 60)
        report.append(f"验证时间: {self.results['timestamp']}")
        report.append(f"总验证项: {self.results['summary']['total_tests']}")
        report.append(f"通过: {self.results['summary']['passed']}")
        report.append(f"失败: {self.results['summary']['failed']}")
        report.append("")
        
        for validation_name, validation_result in self.results['validations'].items():
            report.append(f"验证项: {validation_name}")
            report.append(f"状态: {validation_result['status']}")
            
            if validation_result['status'] == 'PASS':
                # 显示成功的关键信息
                if validation_name == 'backtest_engine':
                    report.append(f"  权益曲线长度: {validation_result.get('equity_curve_length', 0)}")
                    report.append(f"  交易次数: {validation_result.get('trades_count', 0)}")
                    report.append(f"  最终资金: {validation_result.get('final_capital', 0)}")
                elif validation_name == 'data_analyzer':
                    report.append(f"  分析模块数: {len(validation_result.get('analysis_keys', []))}")
                    report.append(f"  风险指标数: {validation_result.get('risk_metrics_count', 0)}")
                elif validation_name == 'visualizer':
                    report.append(f"  图表类型数: {validation_result.get('chart_count', 0)}")
                    report.append(f"  权益曲线类型: {validation_result.get('equity_curve_type', 'N/A')}")
                elif validation_name == 'trading_models':
                    report.append(f"  枚举类型总数: {validation_result.get('total_enums', 0)}")
            else:
                # 显示错误信息
                report.append(f"  错误: {validation_result.get('error', 'Unknown error')}")
                report.append(f"  错误类型: {validation_result.get('error_type', 'Unknown')}")
            
            report.append("-" * 40)
        
        # 显示错误汇总
        if self.results['summary']['errors']:
            report.append("错误汇总:")
            for error in self.results['summary']['errors']:
                report.append(f"  {error['validation']}: {error['error']}")
        
        return '\n'.join(report)

async def main():
    """主函数"""
    validator = FunctionValidator()
    
    print("开始功能验证...")
    results = await validator.run_all_validations()
    
    # 生成报告
    report = validator.generate_report()
    print(report)
    
    # 保存结果到文件
    project_root = Path(__file__).parent
    results_file = project_root / 'function_validation_results.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    report_file = project_root / 'function_validation_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n验证结果已保存到: {results_file}")
    print(f"验证报告已保存到: {report_file}")
    
    # 返回验证状态
    if results['summary']['failed'] > 0:
        return 1
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)