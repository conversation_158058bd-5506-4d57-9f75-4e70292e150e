#!/usr/bin/env python3
"""
简单的服务状态检查脚本
"""
import urllib.request
import urllib.error
import time


def check_service():
    """检查服务状态"""
    try:
        print("🔍 检查服务状态...")

        # 检查健康端点
        req = urllib.request.Request("http://localhost:8000/health")
        with urllib.request.urlopen(req, timeout=5) as response:
            status_code = response.getcode()
            content = response.read().decode("utf-8")

        print(f"健康检查响应: {status_code}")
        if status_code == 200:
            print("✅ 服务正常运行")
            print(f"响应内容: {content}")
            return True
        else:
            print(f"❌ 服务响应异常: {content}")
            return False

    except urllib.error.URLError as e:
        print(f"❌ 无法连接到服务: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查服务时出错: {e}")
        return False


if __name__ == "__main__":
    check_service()
