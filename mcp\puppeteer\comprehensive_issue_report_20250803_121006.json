{"test_summary": {"total_tests": 15, "total_issues": 78, "high_severity": 0, "medium_severity": 44, "low_severity": 34, "test_time": "2025-08-03T12:10:06.158347"}, "issues_by_category": {"代码质量": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.671686", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.672384", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.673065", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:35.100Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.673672", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.674828", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:09:50.677681", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.680011", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.680327", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.680623", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.680833", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:38.352Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.681188", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.681843", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:38.352Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.683775", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.684432", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:38.354Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.686343", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.686995", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194134902:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:09:50.688893", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.691083", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:09:50.691375", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:09:50.691542", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.691692", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.691857", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.692047", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.692378", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.692635", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.692806", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.693058", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.693283", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.693490", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.693767", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.694016", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.694184", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.694456", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.694683", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:09:50.694873", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:09:50.695228", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:09:50.695392", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.695586", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.695920", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.696253", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.696584", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.697023", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.697437", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:09:50.697845", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.700124", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.700539", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.700848", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.701255", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.701666", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.702162", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.702587", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:09:50.703074", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.703694", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.704154", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.706655", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.709158", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.711688", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194134902:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:09:50.714214", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.716531", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.716949", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.717258", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.717642", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.718052", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.718538", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.718968", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.719623", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:09:50.720027", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.720653", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.721136", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.723944", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.726429", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.728896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.731389", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.731837", "url": "http://localhost:5173/risk"}], "API": [{"category": "API", "severity": "中", "title": "API请求失败", "description": "GET http://localhost:8000/api/v1/market/sectors - 状态码: 404", "evidence": null, "timestamp": "2025-08-03T12:09:56.841159", "url": "http://localhost:5173/market"}], "UI/UX": [{"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:09:59.021016", "url": "http://localhost:5173/"}], "可访问性": [{"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:09:59.703879", "url": "http://localhost:5173/"}], "安全": [{"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:10:06.157302", "url": "http://localhost:5173/"}]}, "detailed_issues": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.671686", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.672384", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.673065", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:35.100Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.673672", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.674828", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:09:50.677681", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.680011", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.680327", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.680623", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.680833", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:38.352Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.681188", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.681843", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:38.352Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.683775", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.684432", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect …, errorInfo: mounted hook, instance: Unknown Component, timestamp: 2025-08-03T04:09:38.354Z}", "evidence": null, "timestamp": "2025-08-03T12:09:50.686343", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194134902:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:09:50.686995", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194134902:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:09:50.688893", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.691083", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:09:50.691375", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:09:50.691542", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.691692", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.691857", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.692047", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.692378", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.692635", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.692806", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.693058", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.693283", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.693490", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.693767", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.694016", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:09:50.694184", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.694456", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:09:50.694683", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:09:50.694873", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:09:50.695228", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:09:50.695392", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.695586", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.695920", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:09:50.696253", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.696584", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.697023", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.697437", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194134902:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:09:50.697845", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.700124", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.700539", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.700848", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.701255", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.701666", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.702162", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.702587", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:09:50.703074", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.703694", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.704154", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.706655", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.709158", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.711688", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: TypeError: Cannot read properties of undefined (reading 'connect')\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194134902:126:27)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194134902:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194134902:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:09:50.714214", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.716531", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:09:50.716949", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.717258", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.717642", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.718052", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.718538", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:09:50.718968", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:09:50.719623", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:09:50.720027", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.720653", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.721136", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.723944", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.726429", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:09:50.728896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.731389", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:09:50.731837", "url": "http://localhost:5173/risk"}, {"category": "API", "severity": "中", "title": "API请求失败", "description": "GET http://localhost:8000/api/v1/market/sectors - 状态码: 404", "evidence": null, "timestamp": "2025-08-03T12:09:56.841159", "url": "http://localhost:5173/market"}, {"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:09:59.021016", "url": "http://localhost:5173/"}, {"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:09:59.703879", "url": "http://localhost:5173/"}, {"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:10:06.157302", "url": "http://localhost:5173/"}], "test_results": [{"test": "首页性能测试", "status": "PASS", "details": "加载时间: 1.44秒", "timestamp": "2025-08-03T12:09:23.952683"}, {"test": "仪表盘性能测试", "status": "PASS", "details": "加载时间: 0.88秒", "timestamp": "2025-08-03T12:09:24.836494"}, {"test": "市场数据性能测试", "status": "PASS", "details": "加载时间: 1.37秒", "timestamp": "2025-08-03T12:09:26.211622"}, {"test": "交易终端性能测试", "status": "PASS", "details": "加载时间: 1.24秒", "timestamp": "2025-08-03T12:09:27.455689"}, {"test": "策略中心性能测试", "status": "PASS", "details": "加载时间: 0.67秒", "timestamp": "2025-08-03T12:09:28.127666"}, {"test": "投资组合性能测试", "status": "PASS", "details": "加载时间: 0.78秒", "timestamp": "2025-08-03T12:09:28.906949"}, {"test": "风险管理性能测试", "status": "PASS", "details": "加载时间: 0.64秒", "timestamp": "2025-08-03T12:09:29.549016"}, {"test": "控制台错误检查", "status": "PASS", "details": "发现 40 个错误, 34 个警告", "timestamp": "2025-08-03T12:09:50.732277"}, {"test": "网络请求分析", "status": "PASS", "details": "发现 1 个失败请求", "timestamp": "2025-08-03T12:09:56.841475"}, {"test": "桌面端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:09:57.597241"}, {"test": "平板端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:09:58.280707"}, {"test": "手机端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:09:59.021595"}, {"test": "可访问性检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:09:59.708696"}, {"test": "数据加载检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:10:05.463344"}, {"test": "安全头检查", "status": "PASS", "details": "缺少 5 个安全头", "timestamp": "2025-08-03T12:10:06.157936"}], "recommendations": [{"priority": "中", "title": "用户体验改进", "description": "提升界面响应性和用户体验", "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]}]}