"""
直接测试认证API
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

app = FastAPI(title="认证测试应用", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟用户数据库
fake_users_db = {
    "test_user": {
        "id": 1,
        "username": "test_user",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
        "full_name": "测试用户",
        "is_active": True,
    }
}

# 请求和响应模型
class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    full_name: Optional[str] = None

class LoginRequest(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: UserResponse

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """简化的密码验证"""
    return plain_password == "secret" or plain_password == "test123456"

def get_password_hash(password: str) -> str:
    """简化的密码哈希"""
    return f"hashed_{password}"

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """简化的令牌创建"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    return f"fake_token_{data['sub']}"

@app.get("/")
async def root():
    return {"message": "认证测试应用", "status": "running"}

@app.post("/api/v1/auth/register", response_model=UserResponse, status_code=201)
async def register(user_data: UserCreate):
    """用户注册"""
    # 检查用户是否已存在
    if user_data.username in fake_users_db:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    # 创建新用户
    user_id = len(fake_users_db) + 1
    hashed_password = get_password_hash(user_data.password)
    
    fake_users_db[user_data.username] = {
        "id": user_id,
        "username": user_data.username,
        "email": user_data.email,
        "hashed_password": hashed_password,
        "full_name": user_data.full_name,
        "is_active": True,
    }
    
    return UserResponse(
        id=user_id,
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        is_active=True
    )

@app.post("/api/v1/auth/login", response_model=Token)
async def login(credentials: LoginRequest):
    """用户登录"""
    user = fake_users_db.get(credentials.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not verify_password(credentials.password, user["hashed_password"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user["is_active"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(user["id"]), "username": user["username"]},
        expires_delta=access_token_expires
    )
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=1800,  # 30分钟
        user=UserResponse(
            id=user["id"],
            username=user["username"],
            email=user["email"],
            full_name=user["full_name"],
            is_active=user["is_active"]
        )
    )

@app.get("/health")
async def health():
    return {"status": "healthy", "users_count": len(fake_users_db)}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)