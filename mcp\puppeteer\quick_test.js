const puppeteer = require('puppeteer');

async function quickTest() {
    console.log('🔍 快速检查页面状态...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 }
    });
    
    try {
        const page = await browser.newPage();
        
        // 监听控制台错误
        const errors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                errors.push(msg.text());
            }
        });
        
        console.log('📱 访问页面...');
        await page.goto('http://localhost:5175/trading/simulated', {
            waitUntil: 'networkidle2',
            timeout: 15000
        });
        
        await page.waitForTimeout(2000);
        
        // 检查页面内容
        const pageText = await page.evaluate(() => document.body.innerText);
        console.log('📄 页面内容预览:', pageText.substring(0, 200) + '...');
        
        // 检查关键元素
        const simulationBadge = await page.$('.simulation-badge');
        const accountOverview = await page.$('.account-overview');
        const tradingForm = await page.$('.trading-form');
        
        console.log(`🏷️ 模拟交易标识: ${simulationBadge ? '✅' : '❌'}`);
        console.log(`💰 账户信息: ${accountOverview ? '✅' : '❌'}`);
        console.log(`📋 交易表单: ${tradingForm ? '✅' : '❌'}`);
        
        if (errors.length > 0) {
            console.log('❌ 控制台错误:');
            errors.forEach(error => console.log(`  - ${error}`));
        }
        
        // 截图
        await page.screenshot({ 
            path: 'puppeteer/quick_test_result.png',
            fullPage: true 
        });
        
        const isWorking = simulationBadge && accountOverview && tradingForm;
        console.log(`\n${isWorking ? '✅ 页面正常工作' : '❌ 页面有问题'}`);
        
        return isWorking;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    } finally {
        await browser.close();
    }
}

quickTest()
    .then(result => {
        console.log('\n测试完成');
        process.exit(result ? 0 : 1);
    })
    .catch(error => {
        console.error('测试异常:', error);
        process.exit(1);
    });
