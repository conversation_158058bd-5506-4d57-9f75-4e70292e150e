#!/usr/bin/env python3
"""
简单的认证修复脚本
"""

import sqlite3
import bcrypt
import hashlib

def hash_password_bcrypt(password: str) -> str:
    """使用bcrypt哈希密码"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def main():
    print("🔧 修复认证系统...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        # 检查当前用户
        cursor.execute('SELECT username, email FROM users')
        users = cursor.fetchall()
        print(f"当前用户: {users}")
        
        # 更新admin用户密码
        admin_password = "admin123"
        admin_hash = hash_password_bcrypt(admin_password)
        
        cursor.execute('''
            UPDATE users
            SET hashed_password = ?, is_active = 1
            WHERE username = 'admin'
        ''', (admin_hash,))
        
        print(f"✅ 更新admin密码为: {admin_password}")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("✅ 修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
