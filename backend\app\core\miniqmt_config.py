"""
MiniQMT交易接口配置
"""

import os
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field

from app.core.config import settings


class MiniQMTConfig(BaseModel):
    """MiniQMT配置类"""

    # 基础连接配置
    host: str = Field(default=os.getenv("MINIQMT_HOST", "localhost"), description="MiniQMT服务器地址")
    port: int = Field(default=int(os.getenv("MINIQMT_PORT", "58609")), description="MiniQMT API端口")
    
    # 账户配置
    account_id: str = Field(default=os.getenv("MINIQMT_ACCOUNT", ""), description="交易账户编号")
    
    # 连接配置
    timeout: int = Field(default=10, description="连接超时时间(秒)")
    ssl_enabled: bool = Field(default=False, description="是否启用SSL")
    retry_count: int = Field(default=3, description="重试次数")
    retry_interval: int = Field(default=2, description="重试间隔(秒)")
    
    # API配置
    api_version: str = Field(default="v1", description="API版本")
    
    # 交易配置
    order_timeout: int = Field(default=30, description="订单超时时间(秒)")
    max_order_size: int = Field(default=1000000, description="单笔最大下单量")
    
    # 风控配置
    enable_risk_control: bool = Field(default=True, description="是否启用风控")
    max_position_ratio: float = Field(default=0.8, description="最大持仓比例")
    max_daily_loss: float = Field(default=0.05, description="日最大亏损比例")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="logs/miniqmt.log", description="日志文件路径")

    class Config:
        env_prefix = "MINIQMT_"


class MiniQMTOrderType:
    """MiniQMT订单类型"""
    
    # 基础订单类型
    LIMIT = "11"          # 限价单
    MARKET = "12"         # 市价单
    STOP_LIMIT = "41"     # 止损限价单
    STOP_MARKET = "42"    # 止损市价单
    
    # 算法订单类型
    TWAP = "91"           # 时间加权平均价格
    VWAP = "92"           # 成交量加权平均价格
    
    @classmethod
    def get_type_name(cls, order_type: str) -> str:
        """获取订单类型名称"""
        type_map = {
            cls.LIMIT: "限价单",
            cls.MARKET: "市价单", 
            cls.STOP_LIMIT: "止损限价单",
            cls.STOP_MARKET: "止损市价单",
            cls.TWAP: "TWAP算法单",
            cls.VWAP: "VWAP算法单"
        }
        return type_map.get(order_type, "未知类型")


class MiniQMTOrderSide:
    """MiniQMT订单方向"""
    
    BUY = "1"     # 买入
    SELL = "2"    # 卖出
    
    @classmethod
    def get_side_name(cls, side: str) -> str:
        """获取订单方向名称"""
        side_map = {
            cls.BUY: "买入",
            cls.SELL: "卖出"
        }
        return side_map.get(side, "未知方向")


class MiniQMTOrderStatus:
    """MiniQMT订单状态"""
    
    NEW = "0"             # 新建
    PARTIALLY_FILLED = "1"  # 部分成交
    FILLED = "2"          # 完全成交
    CANCELLED = "4"       # 已撤销
    REJECTED = "8"        # 已拒绝
    PENDING_CANCEL = "6"  # 待撤销
    
    @classmethod
    def get_status_name(cls, status: str) -> str:
        """获取订单状态名称"""
        status_map = {
            cls.NEW: "新建",
            cls.PARTIALLY_FILLED: "部分成交",
            cls.FILLED: "完全成交",
            cls.CANCELLED: "已撤销",
            cls.REJECTED: "已拒绝",
            cls.PENDING_CANCEL: "待撤销"
        }
        return status_map.get(status, "未知状态")


class MiniQMTMarket:
    """MiniQMT市场代码"""
    
    # 股票市场
    SH = "SH"     # 上海证券交易所
    SZ = "SZ"     # 深圳证券交易所
    BJ = "BJ"     # 北京证券交易所
    
    # 期货市场
    SHFE = "SHFE"   # 上海期货交易所
    DCE = "DCE"     # 大连商品交易所
    CZCE = "CZCE"   # 郑州商品交易所
    CFFEX = "CFFEX" # 中国金融期货交易所
    INE = "INE"     # 上海国际能源交易中心
    
    @classmethod
    def get_market_name(cls, market: str) -> str:
        """获取市场名称"""
        market_map = {
            cls.SH: "上海证券交易所",
            cls.SZ: "深圳证券交易所", 
            cls.BJ: "北京证券交易所",
            cls.SHFE: "上海期货交易所",
            cls.DCE: "大连商品交易所",
            cls.CZCE: "郑州商品交易所",
            cls.CFFEX: "中国金融期货交易所",
            cls.INE: "上海国际能源交易中心"
        }
        return market_map.get(market, "未知市场")


class MiniQMTInstrumentType:
    """MiniQMT证券类型"""
    
    STOCK = "CS"          # 普通股票
    INDEX = "IDX"         # 指数
    FUND = "FD"           # 基金
    BOND = "BD"           # 债券
    OPTION = "OP"         # 期权
    FUTURE = "FT"         # 期货
    
    @classmethod
    def get_type_name(cls, inst_type: str) -> str:
        """获取证券类型名称"""
        type_map = {
            cls.STOCK: "股票",
            cls.INDEX: "指数",
            cls.FUND: "基金", 
            cls.BOND: "债券",
            cls.OPTION: "期权",
            cls.FUTURE: "期货"
        }
        return type_map.get(inst_type, "未知类型")


# 默认配置实例
miniqmt_config = MiniQMTConfig()


def get_miniqmt_config() -> MiniQMTConfig:
    """获取MiniQMT配置"""
    return miniqmt_config


def update_miniqmt_config(config_dict: Dict[str, Any]) -> None:
    """更新MiniQMT配置"""
    global miniqmt_config
    for key, value in config_dict.items():
        if hasattr(miniqmt_config, key):
            setattr(miniqmt_config, key, value)


class MiniQMTConnectionPool:
    """MiniQMT连接池"""
    
    def __init__(self, config: MiniQMTConfig):
        self.config = config
        self.connections = {}
        self.max_connections = 10
        
    def get_connection(self, account_id: Optional[str] = None):
        """获取连接"""
        account = account_id or self.config.account_id
        if account not in self.connections:
            # 这里应该创建实际的MiniQMT连接
            self.connections[account] = self._create_connection(account)
        return self.connections[account]
        
    def _create_connection(self, account_id: str):
        """创建MiniQMT连接"""
        # 实际实现中，这里应该创建到MiniQMT的连接
        # 这里返回一个模拟的连接对象
        return {
            "account_id": account_id,
            "host": self.config.host,
            "port": self.config.port,
            "connected": False
        }
        
    def close_connection(self, account_id: str):
        """关闭连接"""
        if account_id in self.connections:
            # 关闭实际连接
            del self.connections[account_id]


# 全局连接池实例
connection_pool = MiniQMTConnectionPool(miniqmt_config)


def get_connection_pool() -> MiniQMTConnectionPool:
    """获取连接池"""
    return connection_pool