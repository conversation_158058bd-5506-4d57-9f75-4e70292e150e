"""
市场数据服务单元测试
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal
import asyncio
from typing import List, Dict, Any

from app.services.market_data_service import MarketDataService
from app.schemas.market_data import TickData, KlineData, MarketDepth
from app.core.exceptions import DataNotFoundError, ServiceError


@pytest.mark.unit
@pytest.mark.market
@pytest.mark.asyncio
class TestMarketDataService:
    """市场数据服务测试类"""

    @pytest.fixture
    def market_service(self):
        """创建市场数据服务实例"""
        return MarketDataService()

    @pytest.fixture
    def sample_tick_data(self):
        """示例Tick数据"""
        return TickData(
            symbol="000001",
            timestamp=datetime.now(),
            price=Decimal("10.50"),
            volume=1000,
            bid_price=Decimal("10.49"),
            ask_price=Decimal("10.51"),
            bid_volume=500,
            ask_volume=600,
        )

    @pytest.fixture
    def sample_kline_data(self):
        """示例K线数据"""
        return KlineData(
            symbol="000001",
            timestamp=datetime.now(),
            interval="1m",
            open_price=Decimal("10.45"),
            high_price=Decimal("10.55"),
            low_price=Decimal("10.40"),
            close_price=Decimal("10.50"),
            volume=50000,
            amount=Decimal("525000.00"),
        )

    @pytest.fixture
    def sample_depth_data(self):
        """示例深度数据"""
        return MarketDepth(
            symbol="000001",
            timestamp=datetime.now(),
            bids=[
                [Decimal("10.49"), Decimal("500")],
                [Decimal("10.48"), Decimal("800")],
                [Decimal("10.47"), Decimal("600")],
            ],
            asks=[
                [Decimal("10.51"), Decimal("600")],
                [Decimal("10.52"), Decimal("700")],
                [Decimal("10.53"), Decimal("400")],
            ],
        )

    async def test_subscribe_tick_data_success(self, market_service):
        """测试订阅Tick数据成功"""
        symbol = "000001"
        callback = AsyncMock()

        # 执行测试
        result = await market_service.subscribe_tick_data(symbol, callback)

        # 验证结果
        assert result is True
        assert symbol in market_service.subscribed_symbols
        assert symbol in market_service.tick_callbacks
        assert callback in market_service.tick_callbacks[symbol]

    async def test_subscribe_tick_data_duplicate(self, market_service):
        """测试重复订阅Tick数据"""
        symbol = "000001"
        callback = AsyncMock()

        # 第一次订阅
        await market_service.subscribe_tick_data(symbol, callback)
        
        # 第二次订阅相同回调
        result = await market_service.subscribe_tick_data(symbol, callback)

        # 验证结果
        assert result is True
        assert len(market_service.tick_callbacks[symbol]) == 1

    async def test_unsubscribe_tick_data_success(self, market_service):
        """测试取消订阅Tick数据成功"""
        symbol = "000001"
        callback = AsyncMock()

        # 先订阅
        await market_service.subscribe_tick_data(symbol, callback)
        
        # 取消订阅
        result = await market_service.unsubscribe_tick_data(symbol, callback)

        # 验证结果
        assert result is True
        if symbol in market_service.tick_callbacks:
            assert callback not in market_service.tick_callbacks[symbol]

    async def test_subscribe_kline_data_success(self, market_service):
        """测试订阅K线数据成功"""
        symbol = "000001"
        interval = "1m"
        callback = AsyncMock()

        # 执行测试
        result = await market_service.subscribe_kline_data(symbol, interval, callback)

        # 验证结果
        assert result is True
        key = f"{symbol}_{interval}"
        assert key in market_service.kline_callbacks
        assert callback in market_service.kline_callbacks[key]

    async def test_subscribe_depth_data_success(self, market_service):
        """测试订阅深度数据成功"""
        symbol = "000001"
        callback = AsyncMock()

        # 执行测试
        result = await market_service.subscribe_depth_data(symbol, callback)

        # 验证结果
        assert result is True
        assert symbol in market_service.depth_callbacks
        assert callback in market_service.depth_callbacks[symbol]

    async def test_publish_tick_data_success(self, market_service, sample_tick_data):
        """测试发布Tick数据成功"""
        callback = AsyncMock()
        
        # 订阅数据
        await market_service.subscribe_tick_data(sample_tick_data.symbol, callback)
        
        # 发布数据
        await market_service.publish_tick_data(sample_tick_data)

        # 验证回调被调用
        callback.assert_called_once_with(sample_tick_data)
        
        # 验证缓存
        assert sample_tick_data.symbol in market_service.tick_cache
        assert market_service.tick_cache[sample_tick_data.symbol] == sample_tick_data

    async def test_publish_kline_data_success(self, market_service, sample_kline_data):
        """测试发布K线数据成功"""
        callback = AsyncMock()
        
        # 订阅数据
        await market_service.subscribe_kline_data(
            sample_kline_data.symbol, sample_kline_data.interval, callback
        )
        
        # 发布数据
        await market_service.publish_kline_data(sample_kline_data)

        # 验证回调被调用
        callback.assert_called_once_with(sample_kline_data)
        
        # 验证缓存
        key = f"{sample_kline_data.symbol}_{sample_kline_data.interval}"
        assert key in market_service.kline_cache

    async def test_publish_depth_data_success(self, market_service, sample_depth_data):
        """测试发布深度数据成功"""
        callback = AsyncMock()
        
        # 订阅数据
        await market_service.subscribe_depth_data(sample_depth_data.symbol, callback)
        
        # 发布数据
        await market_service.publish_depth_data(sample_depth_data)

        # 验证回调被调用
        callback.assert_called_once_with(sample_depth_data)

    async def test_get_latest_tick_success(self, market_service, sample_tick_data):
        """测试获取最新Tick数据成功"""
        # 发布数据到缓存
        await market_service.publish_tick_data(sample_tick_data)
        
        # 获取最新数据
        result = await market_service.get_latest_tick(sample_tick_data.symbol)

        # 验证结果
        assert result == sample_tick_data

    async def test_get_latest_tick_not_found(self, market_service):
        """测试获取不存在的Tick数据"""
        result = await market_service.get_latest_tick("NONEXISTENT")
        assert result is None

    async def test_get_kline_history_success(self, market_service):
        """测试获取K线历史数据成功"""
        symbol = "000001"
        interval = "1d"
        start_time = datetime.now() - timedelta(days=30)
        end_time = datetime.now()

        # Mock数据源
        mock_klines = [
            KlineData(
                symbol=symbol,
                timestamp=start_time + timedelta(days=i),
                interval=interval,
                open_price=Decimal("10.00"),
                high_price=Decimal("10.50"),
                low_price=Decimal("9.80"),
                close_price=Decimal("10.20"),
                volume=100000,
                amount=Decimal("1020000.00"),
            )
            for i in range(30)
        ]

        with patch.object(market_service, '_fetch_kline_data') as mock_fetch:
            mock_fetch.return_value = mock_klines
            
            result = await market_service.get_kline_history(
                symbol, interval, start_time, end_time
            )

            assert len(result) == 30
            assert all(isinstance(k, KlineData) for k in result)

    async def test_get_symbol_info_success(self, market_service):
        """测试获取合约信息成功"""
        symbol = "000001"
        
        mock_info = {
            "symbol": symbol,
            "name": "平安银行",
            "exchange": "SZSE",
            "lot_size": 100,
            "tick_size": Decimal("0.01"),
            "trading_hours": "09:30-15:00",
        }

        with patch.object(market_service, '_fetch_symbol_info') as mock_fetch:
            mock_fetch.return_value = mock_info
            
            result = await market_service.get_symbol_info(symbol)
            
            assert result == mock_info

    async def test_batch_subscribe_success(self, market_service):
        """测试批量订阅成功"""
        symbols = ["000001", "000002", "000300"]
        callback = AsyncMock()

        # 执行批量订阅
        result = await market_service.batch_subscribe_tick(symbols, callback)

        # 验证结果
        assert result["success_count"] == 3
        assert result["failed_count"] == 0
        
        for symbol in symbols:
            assert symbol in market_service.subscribed_symbols

    async def test_batch_subscribe_partial_failure(self, market_service):
        """测试批量订阅部分失败"""
        symbols = ["000001", "INVALID", "000300"]
        callback = AsyncMock()

        # Mock部分符号订阅失败
        with patch.object(market_service, 'subscribe_tick_data') as mock_sub:
            mock_sub.side_effect = [True, False, True]
            
            result = await market_service.batch_subscribe_tick(symbols, callback)

            assert result["success_count"] == 2
            assert result["failed_count"] == 1

    async def test_websocket_connection_management(self, market_service):
        """测试WebSocket连接管理"""
        mock_ws = AsyncMock()
        client_id = "test-client"

        # 添加客户端
        await market_service.add_websocket_client(client_id, mock_ws)
        
        assert client_id in market_service.clients
        assert market_service.clients[client_id] == mock_ws

        # 移除客户端
        await market_service.remove_websocket_client(client_id)
        
        assert client_id not in market_service.clients

    async def test_websocket_broadcast_success(self, market_service, sample_tick_data):
        """测试WebSocket广播成功"""
        mock_ws1 = AsyncMock()
        mock_ws2 = AsyncMock()
        
        # 添加多个客户端
        await market_service.add_websocket_client("client1", mock_ws1)
        await market_service.add_websocket_client("client2", mock_ws2)
        
        # 广播数据
        await market_service.broadcast_to_websockets(sample_tick_data)

        # 验证所有客户端都收到数据
        mock_ws1.send_json.assert_called_once()
        mock_ws2.send_json.assert_called_once()

    async def test_websocket_broadcast_with_failed_client(self, market_service, sample_tick_data):
        """测试WebSocket广播时客户端连接失败"""
        mock_ws_good = AsyncMock()
        mock_ws_bad = AsyncMock()
        mock_ws_bad.send_json = AsyncMock(side_effect=Exception("Connection lost"))
        
        # 添加客户端
        await market_service.add_websocket_client("good_client", mock_ws_good)
        await market_service.add_websocket_client("bad_client", mock_ws_bad)
        
        # 广播数据
        await market_service.broadcast_to_websockets(sample_tick_data)

        # 验证好的客户端收到数据，坏的客户端被移除
        mock_ws_good.send_json.assert_called_once()
        assert "bad_client" not in market_service.clients

    async def test_data_validation_invalid_tick(self, market_service):
        """测试无效Tick数据验证"""
        invalid_tick = TickData(
            symbol="",  # 空符号
            timestamp=datetime.now(),
            price=Decimal("-10.50"),  # 负价格
            volume=-1000,  # 负成交量
            bid_price=Decimal("10.49"),
            ask_price=Decimal("10.51"),
            bid_volume=500,
            ask_volume=600,
        )

        with pytest.raises(ValueError):
            await market_service.publish_tick_data(invalid_tick)

    async def test_data_validation_invalid_kline(self, market_service):
        """测试无效K线数据验证"""
        invalid_kline = KlineData(
            symbol="000001",
            timestamp=datetime.now(),
            interval="",  # 空时间间隔
            open_price=Decimal("10.00"),
            high_price=Decimal("9.50"),  # 最高价小于开盘价
            low_price=Decimal("10.50"),  # 最低价大于开盘价
            close_price=Decimal("10.20"),
            volume=0,
            amount=Decimal("0"),
        )

        with pytest.raises(ValueError):
            await market_service.publish_kline_data(invalid_kline)

    async def test_performance_metrics_collection(self, market_service, sample_tick_data):
        """测试性能指标收集"""
        # 发布一些数据
        for i in range(100):
            tick_data = TickData(
                symbol="000001",
                timestamp=datetime.now(),
                price=Decimal("10.50") + Decimal(str(i * 0.01)),
                volume=1000,
                bid_price=Decimal("10.49"),
                ask_price=Decimal("10.51"),
                bid_volume=500,
                ask_volume=600,
            )
            await market_service.publish_tick_data(tick_data)

        # 获取统计信息
        stats = await market_service.get_statistics()

        assert stats["tick_count"] == 100
        assert stats["last_update"] is not None
        assert "processing_speed" in stats

    async def test_memory_management_cache_cleanup(self, market_service):
        """测试内存管理和缓存清理"""
        # 填充大量数据
        for i in range(1000):
            tick_data = TickData(
                symbol=f"SYMBOL{i}",
                timestamp=datetime.now(),
                price=Decimal("10.50"),
                volume=1000,
                bid_price=Decimal("10.49"),
                ask_price=Decimal("10.51"),
                bid_volume=500,
                ask_volume=600,
            )
            await market_service.publish_tick_data(tick_data)

        # 执行缓存清理
        await market_service.cleanup_old_cache()

        # 验证缓存大小被控制
        assert len(market_service.tick_cache) <= market_service.max_cache_size

    async def test_error_handling_network_timeout(self, market_service):
        """测试网络超时错误处理"""
        symbol = "000001"
        
        # Mock网络超时
        with patch.object(market_service, '_fetch_symbol_info') as mock_fetch:
            mock_fetch.side_effect = asyncio.TimeoutError("Network timeout")
            
            with pytest.raises(ServiceError, match="网络超时"):
                await market_service.get_symbol_info(symbol)

    async def test_concurrent_data_processing(self, market_service):
        """测试并发数据处理"""
        symbols = [f"SYMBOL{i}" for i in range(50)]
        callbacks = [AsyncMock() for _ in range(50)]

        # 并发订阅
        tasks = []
        for symbol, callback in zip(symbols, callbacks):
            task = market_service.subscribe_tick_data(symbol, callback)
            tasks.append(task)

        results = await asyncio.gather(*tasks)
        
        # 验证所有订阅都成功
        assert all(results)
        assert len(market_service.subscribed_symbols) == 50

    async def test_rate_limiting(self, market_service):
        """测试频率限制"""
        symbol = "000001"
        
        # 快速发送大量请求
        start_time = datetime.now()
        requests_made = 0
        
        for i in range(100):
            try:
                await market_service.get_latest_tick(symbol)
                requests_made += 1
            except Exception as e:
                if "rate limit" in str(e).lower():
                    break

        # 验证频率限制生效
        elapsed = (datetime.now() - start_time).total_seconds()
        if elapsed < 1.0:  # 如果在1秒内完成
            assert requests_made < 100  # 应该被限制

    async def test_data_integrity_check(self, market_service, sample_tick_data):
        """测试数据完整性检查"""
        # 发布数据
        await market_service.publish_tick_data(sample_tick_data)
        
        # 获取数据并验证完整性
        retrieved_data = await market_service.get_latest_tick(sample_tick_data.symbol)
        
        assert retrieved_data.symbol == sample_tick_data.symbol
        assert retrieved_data.price == sample_tick_data.price
        assert retrieved_data.volume == sample_tick_data.volume
        assert retrieved_data.timestamp == sample_tick_data.timestamp

    async def test_subscription_cleanup_on_client_disconnect(self, market_service):
        """测试客户端断开时的订阅清理"""
        client_id = "test-client"
        symbol = "000001"
        callback = AsyncMock()
        mock_ws = AsyncMock()

        # 添加客户端和订阅
        await market_service.add_websocket_client(client_id, mock_ws)
        await market_service.subscribe_tick_data(symbol, callback)
        market_service.client_subscriptions[client_id] = [symbol]

        # 模拟客户端断开
        await market_service.remove_websocket_client(client_id)

        # 验证订阅被清理
        assert client_id not in market_service.client_subscriptions
        assert client_id not in market_service.clients

    async def test_heartbeat_mechanism(self, market_service):
        """测试心跳机制"""
        client_id = "test-client"
        mock_ws = AsyncMock()

        # 添加客户端
        await market_service.add_websocket_client(client_id, mock_ws)
        
        # 发送心跳
        await market_service.send_heartbeat(client_id)

        # 验证心跳消息被发送
        mock_ws.send_json.assert_called_once()
        call_args = mock_ws.send_json.call_args[0][0]
        assert call_args["type"] == "heartbeat"

    async def test_data_compression_optimization(self, market_service, sample_tick_data):
        """测试数据压缩优化"""
        # 启用压缩
        market_service.enable_compression = True
        
        # 发布数据
        await market_service.publish_tick_data(sample_tick_data)
        
        # 验证数据被压缩存储
        # 这里只是示例，实际实现会根据具体压缩算法调整
        assert sample_tick_data.symbol in market_service.tick_cache

    async def test_service_health_check(self, market_service):
        """测试服务健康检查"""
        # 执行健康检查
        health_status = await market_service.health_check()

        # 验证健康状态
        assert "status" in health_status
        assert "uptime" in health_status
        assert "memory_usage" in health_status
        assert "active_subscriptions" in health_status
        assert "cache_size" in health_status