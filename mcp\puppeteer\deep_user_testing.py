#!/usr/bin/env python3
"""
深度用户测试脚本
模拟真实用户使用量化投资平台的完整流程，发现潜在问题
"""

import asyncio
import json
import time
import random
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeepUserTesting:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'user_scenarios': [],
            'discovered_issues': [],
            'performance_data': {},
            'user_experience_problems': [],
            'functional_bugs': [],
            'ui_inconsistencies': []
        }
        self.screenshot_counter = 0
        
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器以便观察用户行为
            args=[
                '--no-sandbox', 
                '--disable-dev-shm-usage',
                '--disable-web-security',  # 允许跨域测试
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建新页面，模拟真实用户环境
        context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
        # 监听所有事件
        self.page.on('console', self.handle_console_message)
        self.page.on('pageerror', self.handle_page_error)
        self.page.on('requestfailed', self.handle_request_failed)
        self.page.on('response', self.handle_response)
        
        logger.info("🚀 深度用户测试环境初始化完成")

    async def handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type in ['error', 'warning']:
            self.test_results['discovered_issues'].append({
                'type': 'console_' + msg.type,
                'message': msg.text,
                'timestamp': datetime.now().isoformat(),
                'url': self.page.url
            })

    async def handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['functional_bugs'].append({
            'type': 'page_error',
            'message': str(error),
            'timestamp': datetime.now().isoformat(),
            'url': self.page.url
        })

    async def handle_request_failed(self, request):
        """处理请求失败"""
        self.test_results['functional_bugs'].append({
            'type': 'request_failed',
            'url': request.url,
            'method': request.method,
            'timestamp': datetime.now().isoformat()
        })

    async def handle_response(self, response):
        """处理响应"""
        if response.status >= 400:
            self.test_results['functional_bugs'].append({
                'type': 'http_error',
                'url': response.url,
                'status': response.status,
                'timestamp': datetime.now().isoformat()
            })

    async def take_screenshot(self, name: str):
        """截图记录"""
        self.screenshot_counter += 1
        screenshot_path = f"screenshots/deep_test_{self.screenshot_counter:03d}_{name}.png"
        Path("screenshots").mkdir(exist_ok=True)
        await self.page.screenshot(path=screenshot_path)
        logger.info(f"📸 截图保存: {screenshot_path}")
        return screenshot_path

    async def simulate_human_behavior(self):
        """模拟人类行为"""
        # 随机等待时间，模拟用户思考
        await asyncio.sleep(random.uniform(0.5, 2.0))
        
        # 随机鼠标移动
        await self.page.mouse.move(
            random.randint(100, 1800),
            random.randint(100, 900)
        )

    async def scenario_new_user_exploration(self):
        """场景1: 新用户探索平台"""
        logger.info("👤 开始场景1: 新用户探索平台")
        scenario = {
            'name': '新用户探索平台',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_experience_notes': []
        }
        
        try:
            # 步骤1: 首次访问首页
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            await self.take_screenshot('01_homepage_first_visit')
            scenario['steps'].append('访问首页')
            
            # 检查首页加载时间
            load_time = await self.page.evaluate('''() => {
                return performance.timing.loadEventEnd - performance.timing.navigationStart;
            }''')
            
            if load_time > 3000:
                scenario['issues_found'].append(f'首页加载时间过长: {load_time}ms')
            
            await self.simulate_human_behavior()
            
            # 步骤2: 探索导航菜单
            nav_elements = await self.page.query_selector_all('nav a, .nav-item, .menu-item')
            if not nav_elements:
                scenario['issues_found'].append('未找到明显的导航菜单')
            else:
                scenario['steps'].append(f'发现{len(nav_elements)}个导航元素')
            
            # 步骤3: 尝试理解平台功能
            # 查找关键词来理解平台用途
            page_text = await self.page.text_content('body')
            key_terms = ['量化', '投资', '策略', '交易', '市场', '数据']
            found_terms = [term for term in key_terms if term in page_text]
            
            if len(found_terms) < 3:
                scenario['user_experience_notes'].append('平台功能不够明确，缺少关键词说明')
            
            await self.simulate_human_behavior()
            
            # 步骤4: 查找帮助或引导信息
            help_elements = await self.page.query_selector_all('[title*="帮助"], [aria-label*="帮助"], .help, .guide, .tutorial')
            if not help_elements:
                scenario['user_experience_notes'].append('缺少新用户引导或帮助信息')
            
            await self.take_screenshot('02_homepage_explored')
            
        except Exception as e:
            scenario['issues_found'].append(f'场景执行异常: {str(e)}')
            logger.error(f"场景1执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景1完成，耗时: {scenario['duration']:.2f}秒")

    async def scenario_market_data_interaction(self):
        """场景2: 市场数据交互测试"""
        logger.info("📊 开始场景2: 市场数据交互测试")
        scenario = {
            'name': '市场数据交互测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_experience_notes': []
        }
        
        try:
            # 导航到市场页面
            await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
            await self.take_screenshot('03_market_page_loaded')
            scenario['steps'].append('导航到市场页面')
            
            await self.simulate_human_behavior()
            
            # 等待数据加载
            await asyncio.sleep(3)
            
            # 检查数据表格
            tables = await self.page.query_selector_all('table, .el-table')
            if tables:
                scenario['steps'].append(f'发现{len(tables)}个数据表格')
                
                # 测试表格交互
                for i, table in enumerate(tables[:2]):  # 只测试前2个表格
                    try:
                        # 尝试点击表头排序
                        headers = await table.query_selector_all('th, .el-table__header th')
                        if headers:
                            await headers[0].click()
                            await asyncio.sleep(1)
                            scenario['steps'].append(f'测试表格{i+1}排序功能')
                    except Exception as e:
                        scenario['issues_found'].append(f'表格{i+1}排序功能异常: {str(e)}')
            else:
                scenario['issues_found'].append('未发现数据表格')
            
            # 检查图表
            charts = await self.page.query_selector_all('canvas, .chart-container, [id*="chart"]')
            if charts:
                scenario['steps'].append(f'发现{len(charts)}个图表元素')
                
                # 测试图表交互
                for i, chart in enumerate(charts[:2]):
                    try:
                        # 尝试鼠标悬停
                        await chart.hover()
                        await asyncio.sleep(0.5)
                        scenario['steps'].append(f'测试图表{i+1}悬停交互')
                    except Exception as e:
                        scenario['issues_found'].append(f'图表{i+1}交互异常: {str(e)}')
            else:
                scenario['issues_found'].append('未发现图表元素')
            
            # 测试搜索功能
            search_inputs = await self.page.query_selector_all('input[placeholder*="搜索"], input[placeholder*="查找"], .search-input')
            if search_inputs:
                search_input = search_inputs[0]
                await search_input.fill('000001')
                await self.page.keyboard.press('Enter')
                await asyncio.sleep(2)
                scenario['steps'].append('测试搜索功能')
                await self.take_screenshot('04_search_test')
            else:
                scenario['user_experience_notes'].append('未找到搜索功能')
            
            await self.simulate_human_behavior()
            
        except Exception as e:
            scenario['issues_found'].append(f'场景执行异常: {str(e)}')
            logger.error(f"场景2执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景2完成，耗时: {scenario['duration']:.2f}秒")

    async def scenario_strategy_workflow(self):
        """场景3: 策略工作流测试"""
        logger.info("🧠 开始场景3: 策略工作流测试")
        scenario = {
            'name': '策略工作流测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_experience_notes': []
        }
        
        try:
            # 导航到策略页面
            await self.page.goto('http://localhost:5173/strategy', wait_until='networkidle')
            await self.take_screenshot('05_strategy_page')
            scenario['steps'].append('导航到策略页面')
            
            await self.simulate_human_behavior()
            
            # 查找策略列表
            strategy_cards = await self.page.query_selector_all('.strategy-card, .el-card, .strategy-item')
            if strategy_cards:
                scenario['steps'].append(f'发现{len(strategy_cards)}个策略卡片')
                
                # 测试策略卡片交互
                if len(strategy_cards) > 0:
                    await strategy_cards[0].click()
                    await asyncio.sleep(2)
                    scenario['steps'].append('点击第一个策略卡片')
                    await self.take_screenshot('06_strategy_detail')
            else:
                scenario['issues_found'].append('未发现策略列表')
            
            # 查找创建策略按钮
            create_buttons = await self.page.query_selector_all('button:has-text("创建"), button:has-text("新建"), .create-btn')
            if create_buttons:
                scenario['steps'].append('发现创建策略按钮')
                
                # 测试创建流程
                try:
                    await create_buttons[0].click()
                    await asyncio.sleep(2)
                    scenario['steps'].append('点击创建策略按钮')
                    await self.take_screenshot('07_create_strategy')
                    
                    # 查找表单元素
                    form_inputs = await self.page.query_selector_all('input, textarea, select')
                    if form_inputs:
                        scenario['steps'].append(f'发现{len(form_inputs)}个表单元素')
                        
                        # 测试表单填写
                        for i, input_elem in enumerate(form_inputs[:3]):  # 只测试前3个
                            try:
                                input_type = await input_elem.get_attribute('type')
                                if input_type in ['text', 'email', None]:
                                    await input_elem.fill(f'测试数据{i+1}')
                                    scenario['steps'].append(f'填写表单字段{i+1}')
                            except Exception as e:
                                scenario['issues_found'].append(f'表单字段{i+1}填写失败: {str(e)}')
                    
                except Exception as e:
                    scenario['issues_found'].append(f'创建策略流程异常: {str(e)}')
            else:
                scenario['user_experience_notes'].append('未找到创建策略功能')
            
            await self.simulate_human_behavior()
            
        except Exception as e:
            scenario['issues_found'].append(f'场景执行异常: {str(e)}')
            logger.error(f"场景3执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景3完成，耗时: {scenario['duration']:.2f}秒")

    async def scenario_responsive_testing(self):
        """场景4: 响应式设计测试"""
        logger.info("📱 开始场景4: 响应式设计测试")
        scenario = {
            'name': '响应式设计测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_experience_notes': []
        }
        
        viewports = [
            {'width': 1920, 'height': 1080, 'name': '桌面端'},
            {'width': 1024, 'height': 768, 'name': '平板横屏'},
            {'width': 768, 'height': 1024, 'name': '平板竖屏'},
            {'width': 414, 'height': 896, 'name': '手机大屏'},
            {'width': 375, 'height': 667, 'name': '手机标准'}
        ]
        
        try:
            for viewport in viewports:
                await self.page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
                await asyncio.sleep(1)
                
                # 重新加载页面以触发响应式
                await self.page.reload(wait_until='networkidle')
                await asyncio.sleep(2)
                
                await self.take_screenshot(f'08_responsive_{viewport["name"]}')
                scenario['steps'].append(f'{viewport["name"]}视口测试')
                
                # 检查元素是否溢出
                overflow_elements = await self.page.evaluate('''() => {
                    const elements = document.querySelectorAll('*');
                    let overflowCount = 0;
                    elements.forEach(el => {
                        const rect = el.getBoundingClientRect();
                        if (rect.width > window.innerWidth) {
                            overflowCount++;
                        }
                    });
                    return overflowCount;
                }''')
                
                if overflow_elements > 0:
                    scenario['issues_found'].append(f'{viewport["name"]}发现{overflow_elements}个溢出元素')
                
                # 检查文字大小
                if viewport['width'] <= 768:  # 移动端
                    small_text_count = await self.page.evaluate('''() => {
                        const elements = document.querySelectorAll('*');
                        let count = 0;
                        elements.forEach(el => {
                            const style = window.getComputedStyle(el);
                            const fontSize = parseFloat(style.fontSize);
                            if (fontSize < 14 && el.textContent.trim()) {
                                count++;
                            }
                        });
                        return count;
                    }''')
                    
                    if small_text_count > 10:
                        scenario['issues_found'].append(f'{viewport["name"]}发现{small_text_count}个小文字元素')
                
                await self.simulate_human_behavior()
            
        except Exception as e:
            scenario['issues_found'].append(f'响应式测试异常: {str(e)}')
            logger.error(f"场景4执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景4完成，耗时: {scenario['duration']:.2f}秒")

    async def scenario_stress_testing(self):
        """场景5: 压力测试"""
        logger.info("⚡ 开始场景5: 压力测试")
        scenario = {
            'name': '压力测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_experience_notes': []
        }
        
        try:
            # 快速页面切换测试
            pages = ['/market', '/strategy', '/trading', '/portfolio', '/settings']
            
            for i in range(3):  # 重复3次
                for page in pages:
                    try:
                        await self.page.goto(f'http://localhost:5173{page}', wait_until='domcontentloaded', timeout=5000)
                        await asyncio.sleep(0.5)  # 短暂等待
                        scenario['steps'].append(f'快速访问{page}')
                    except Exception as e:
                        scenario['issues_found'].append(f'快速访问{page}失败: {str(e)}')
            
            # 内存使用检查
            memory_info = await self.page.evaluate('''() => {
                if (performance.memory) {
                    return {
                        usedJSHeapSize: performance.memory.usedJSHeapSize,
                        totalJSHeapSize: performance.memory.totalJSHeapSize,
                        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                    };
                }
                return null;
            }''')
            
            if memory_info:
                memory_usage_mb = memory_info['usedJSHeapSize'] / 1024 / 1024
                if memory_usage_mb > 100:  # 超过100MB
                    scenario['issues_found'].append(f'内存使用过高: {memory_usage_mb:.2f}MB')
                scenario['steps'].append(f'内存使用: {memory_usage_mb:.2f}MB')
            
            await self.take_screenshot('09_stress_test_final')
            
        except Exception as e:
            scenario['issues_found'].append(f'压力测试异常: {str(e)}')
            logger.error(f"场景5执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景5完成，耗时: {scenario['duration']:.2f}秒")

    async def analyze_results(self):
        """分析测试结果"""
        logger.info("📊 分析测试结果...")
        
        # 统计问题
        total_issues = len(self.test_results['discovered_issues'])
        total_bugs = len(self.test_results['functional_bugs'])
        total_ux_issues = sum(len(s['user_experience_notes']) for s in self.test_results['user_scenarios'])
        
        # 计算成功率
        total_scenarios = len(self.test_results['user_scenarios'])
        successful_scenarios = sum(1 for s in self.test_results['user_scenarios'] if not s['issues_found'])
        success_rate = successful_scenarios / total_scenarios if total_scenarios > 0 else 0
        
        # 生成分析报告
        analysis = {
            'summary': {
                'total_scenarios': total_scenarios,
                'successful_scenarios': successful_scenarios,
                'success_rate': success_rate,
                'total_issues': total_issues,
                'functional_bugs': total_bugs,
                'ux_issues': total_ux_issues
            },
            'critical_issues': [],
            'recommendations': []
        }
        
        # 识别关键问题
        for scenario in self.test_results['user_scenarios']:
            for issue in scenario['issues_found']:
                if any(keyword in issue.lower() for keyword in ['异常', '失败', '错误', '无法']):
                    analysis['critical_issues'].append({
                        'scenario': scenario['name'],
                        'issue': issue
                    })
        
        # 生成建议
        if total_ux_issues > 5:
            analysis['recommendations'].append('用户体验需要改进，建议增加用户引导和帮助信息')
        
        if total_bugs > 3:
            analysis['recommendations'].append('发现多个功能性问题，建议进行代码审查和测试')
        
        if success_rate < 0.8:
            analysis['recommendations'].append('场景成功率较低，建议优化核心用户流程')
        
        self.test_results['analysis'] = analysis
        
        return analysis

    async def generate_report(self):
        """生成详细测试报告"""
        analysis = await self.analyze_results()
        
        report_file = Path(f'deep_user_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 保存详细报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📋 详细测试报告已保存: {report_file}")
        
        # 输出摘要
        print("\n" + "="*80)
        print("🔍 深度用户测试报告")
        print("="*80)
        print(f"测试时间: {self.test_results['timestamp']}")
        print(f"总场景数: {analysis['summary']['total_scenarios']}")
        print(f"成功场景: {analysis['summary']['successful_scenarios']}")
        print(f"成功率: {analysis['summary']['success_rate']:.1%}")
        print(f"发现问题: {analysis['summary']['total_issues']}")
        print(f"功能性错误: {analysis['summary']['functional_bugs']}")
        print(f"用户体验问题: {analysis['summary']['ux_issues']}")
        
        print("\n📋 场景执行结果:")
        for scenario in self.test_results['user_scenarios']:
            status = "✅" if not scenario['issues_found'] else "❌"
            print(f"{status} {scenario['name']}: {scenario['duration']:.2f}秒")
            
            if scenario['issues_found']:
                for issue in scenario['issues_found']:
                    print(f"   🐛 {issue}")
            
            if scenario['user_experience_notes']:
                for note in scenario['user_experience_notes']:
                    print(f"   💡 {note}")
        
        if analysis['critical_issues']:
            print("\n🚨 关键问题:")
            for issue in analysis['critical_issues']:
                print(f"   ❗ [{issue['scenario']}] {issue['issue']}")
        
        if analysis['recommendations']:
            print("\n💡 改进建议:")
            for rec in analysis['recommendations']:
                print(f"   📝 {rec}")
        
        return report_file

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    test = DeepUserTesting()
    
    try:
        await test.setup()
        
        # 执行所有测试场景
        await test.scenario_new_user_exploration()
        await test.scenario_market_data_interaction()
        await test.scenario_strategy_workflow()
        await test.scenario_responsive_testing()
        await test.scenario_stress_testing()
        
        # 生成报告
        report_file = await test.generate_report()
        
        print(f"\n🎉 深度用户测试完成！详细报告: {report_file}")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
