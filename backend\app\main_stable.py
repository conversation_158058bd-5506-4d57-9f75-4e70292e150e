#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quantitative Investment Platform Backend - Stable Version
Designed for production environment and user-friendly experience
"""

import logging
import os
import sys
import time
import traceback
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from fastapi import FastAPI, Request, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import J<PERSON>NResponse, HTMLResponse
    from fastapi.staticfiles import StaticFiles
    from pydantic import BaseModel
    import uvicorn
except ImportError as e:
    print(f"❌ 缺少必要依赖: {e}")
    print("📦 请运行: pip install fastapi uvicorn pydantic")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/backend.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data', exist_ok=True)

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用启动和关闭时的生命周期管理"""
    # 启动时执行
    logger.info("🚀 量化投资平台后端启动中...")
    
    try:
        # 初始化数据库连接
        logger.info("📊 初始化数据库...")
        
        # 初始化缓存
        logger.info("🔄 初始化缓存...")
        
        # 初始化市场数据服务
        logger.info("📈 初始化市场数据服务...")
        
        logger.info("✅ 后端服务启动完成")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise
    finally:
        # 关闭时执行
        logger.info("🛑 量化投资平台后端关闭中...")
        logger.info("✅ 后端服务已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="量化投资平台API",
    description="""
    ## 专业的量化投资后端服务
    
    提供以下核心功能：
    - 🔐 用户认证与授权
    - 📊 实时市场数据
    - 💹 交易执行与管理
    - 🎯 策略开发与回测
    - 📈 风险管理与监控
    
    ### 快速开始
    1. 访问 `/docs` 查看API文档
    2. 使用 `/health` 检查服务状态
    3. 通过 `/api/v1/auth/login` 进行用户认证
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    services: dict

class ErrorResponse(BaseModel):
    error: str
    message: str
    timestamp: str

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="服务器内部错误，请稍后重试",
            timestamp=datetime.now().isoformat()
        ).dict()
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=f"HTTP {exc.status_code}",
            message=exc.detail,
            timestamp=datetime.now().isoformat()
        ).dict()
    )

# 基础路由
@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 显示欢迎页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>量化投资平台API</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
            .status { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 20px 0; }
            .links { display: flex; gap: 20px; margin: 30px 0; }
            .link { background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; }
            .link:hover { background: #2980b9; }
            .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
            .feature { background: #f8f9fa; padding: 20px; border-radius: 4px; border-left: 4px solid #3498db; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 量化投资平台API</h1>
            <div class="status">
                ✅ 服务运行正常 | 启动时间: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """
            </div>
            
            <div class="links">
                <a href="/docs" class="link">📚 API文档</a>
                <a href="/health" class="link">🔍 健康检查</a>
                <a href="/redoc" class="link">📖 ReDoc文档</a>
            </div>
            
            <div class="features">
                <div class="feature">
                    <h3>🔐 用户认证</h3>
                    <p>JWT令牌认证、权限管理</p>
                </div>
                <div class="feature">
                    <h3>📊 市场数据</h3>
                    <p>实时行情、历史数据</p>
                </div>
                <div class="feature">
                    <h3>💹 交易系统</h3>
                    <p>订单管理、持仓查询</p>
                </div>
                <div class="feature">
                    <h3>🎯 策略回测</h3>
                    <p>策略开发、回测分析</p>
                </div>
            </div>
            
            <p><strong>前端地址:</strong> <a href="http://localhost:5173">http://localhost:5173</a></p>
        </div>
    </body>
    </html>
    """

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    try:
        services = {
            "database": "healthy",
            "cache": "healthy", 
            "market_data": "healthy",
            "trading": "healthy"
        }
        
        return HealthResponse(
            status="healthy",
            timestamp=datetime.now().isoformat(),
            version="1.0.0",
            services=services
        )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail="服务不可用")

# 简化的API端点
@app.get("/api/v1/auth/test")
async def auth_test():
    """认证测试端点"""
    return {"message": "认证服务正常", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/market/stocks")
async def get_stocks():
    """获取股票列表"""
    # 模拟数据
    stocks = [
        {"symbol": "000001.SZ", "name": "平安银行", "price": 12.50, "change": 0.05},
        {"symbol": "000002.SZ", "name": "万科A", "price": 18.30, "change": -0.12},
        {"symbol": "600000.SH", "name": "浦发银行", "price": 8.90, "change": 0.08},
        {"symbol": "600036.SH", "name": "招商银行", "price": 42.15, "change": 0.25},
        {"symbol": "600519.SH", "name": "贵州茅台", "price": 1680.00, "change": 15.50}
    ]
    return {"data": stocks, "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/trading/orders")
async def get_orders():
    """获取订单列表"""
    return {"data": [], "message": "交易服务正常", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/portfolio/summary")
async def get_portfolio():
    """获取投资组合摘要"""
    return {
        "total_value": 100000.00,
        "available_cash": 20000.00,
        "positions": [],
        "timestamp": datetime.now().isoformat()
    }

# 启动函数
def start_server():
    """启动服务器"""
    try:
        logger.info("🚀 启动量化投资平台后端服务...")
        
        # 检查端口是否被占用
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 8000))
        sock.close()
        
        if result == 0:
            logger.warning("⚠️ 端口8000已被占用，尝试使用其他端口...")
            port = 8001
        else:
            port = 8000
            
        logger.info(f"🌐 服务将在端口 {port} 启动")
        logger.info(f"📚 API文档: http://localhost:{port}/docs")
        logger.info(f"🔍 健康检查: http://localhost:{port}/health")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info",
            access_log=True,
            reload=False  # 生产环境建议关闭
        )
        
    except KeyboardInterrupt:
        logger.info("👋 用户中断，正在关闭服务...")
    except Exception as e:
        logger.error(f"❌ 启动失败: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    start_server()
