"""
性能优化配置模块
包含应用程序性能优化相关的配置和工具
"""

import asyncio
import time
import psutil
import logging
from typing import Dict, Any, Optional
from functools import wraps
from contextlib import asynccontextmanager

from app.core.config import settings

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics = {
            "request_count": 0,
            "error_count": 0,
            "avg_response_time": 0.0,
            "total_response_time": 0.0,
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "active_connections": 0,
        }
        self.start_time = time.time()

    def record_request(self, response_time: float, success: bool = True):
        """记录请求性能"""
        self.metrics["request_count"] += 1
        self.metrics["total_response_time"] += response_time
        self.metrics["avg_response_time"] = (
            self.metrics["total_response_time"] / self.metrics["request_count"]
        )

        if not success:
            self.metrics["error_count"] += 1

    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            self.metrics["cpu_usage"] = psutil.cpu_percent(interval=1)
            self.metrics["memory_usage"] = psutil.virtual_memory().percent

            return {
                "uptime": time.time() - self.start_time,
                "request_count": self.metrics["request_count"],
                "error_count": self.metrics["error_count"],
                "error_rate": (
                    self.metrics["error_count"] / max(self.metrics["request_count"], 1)
                )
                * 100,
                "avg_response_time": self.metrics["avg_response_time"],
                "cpu_usage": self.metrics["cpu_usage"],
                "memory_usage": self.metrics["memory_usage"],
                "active_connections": self.metrics["active_connections"],
            }
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return self.metrics


# 全局性能监控器
performance_monitor = PerformanceMonitor()


def performance_timer(func):
    """性能计时装饰器"""

    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            raise e
        finally:
            end_time = time.time()
            response_time = end_time - start_time
            performance_monitor.record_request(response_time, success)

    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            raise e
        finally:
            end_time = time.time()
            response_time = end_time - start_time
            performance_monitor.record_request(response_time, success)

    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


@asynccontextmanager
async def async_performance_context(operation_name: str = "operation"):
    """异步性能上下文管理器"""
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"{operation_name} 耗时: {duration:.4f}秒")


class DatabaseOptimizer:
    """数据库优化器"""

    @staticmethod
    def get_connection_pool_config():
        """获取优化的数据库连接池配置"""
        return {
            "pool_size": settings.DB_POOL_SIZE,
            "max_overflow": settings.DB_MAX_OVERFLOW,
            "pool_recycle": settings.DB_POOL_RECYCLE,
            "pool_pre_ping": True,
            "echo": settings.DB_ECHO_LOG,
        }

    @staticmethod
    def get_query_optimization_hints():
        """获取查询优化提示"""
        return {
            "use_indexes": True,
            "limit_results": True,
            "avoid_n_plus_1": True,
            "use_bulk_operations": True,
            "cache_frequently_accessed_data": True,
        }


class CacheOptimizer:
    """缓存优化器"""

    @staticmethod
    def get_cache_config():
        """获取缓存配置"""
        return {
            "default_ttl": settings.REDIS_CACHE_TTL,
            "max_connections": 100,
            "connection_pool_size": 50,
            "socket_timeout": 5,
            "socket_connect_timeout": 5,
        }

    @staticmethod
    def get_cache_strategies():
        """获取缓存策略"""
        return {
            "market_data": 30,  # 市场数据缓存30秒
            "user_session": 3600,  # 用户会话缓存1小时
            "static_data": 86400,  # 静态数据缓存24小时
            "trading_data": 60,  # 交易数据缓存1分钟
            "strategy_results": 300,  # 策略结果缓存5分钟
        }


class ApplicationOptimizer:
    """应用程序优化器"""

    @staticmethod
    def get_uvicorn_config():
        """获取Uvicorn优化配置"""
        return {
            "host": settings.HOST,
            "port": settings.PORT,
            "workers": 4,  # 根据CPU核心数调整
            "worker_class": "uvicorn.workers.UvicornWorker",
            "max_requests": 1000,
            "max_requests_jitter": 50,
            "timeout": 30,
            "keep_alive": 2,
            "loop": "auto",
        }

    @staticmethod
    def get_middleware_config():
        """获取中间件配置"""
        return {
            "cors_max_age": 3600,
            "compression_level": 6,
            "gzip_minimum_size": 1024,
            "rate_limit_per_minute": settings.RATE_LIMIT_PER_MINUTE,
            "rate_limit_burst": settings.RATE_LIMIT_BURST,
        }


class MemoryOptimizer:
    """内存优化器"""

    @staticmethod
    def get_memory_config():
        """获取内存配置"""
        return {
            "max_memory_usage": "80%",
            "gc_threshold": (700, 10, 10),
            "object_pool_size": 1000,
            "buffer_size": 8192,
        }

    @staticmethod
    def optimize_garbage_collection():
        """优化垃圾收集"""
        import gc

        # 设置垃圾收集阈值
        gc.set_threshold(700, 10, 10)

        # 启用垃圾收集调试
        if settings.DEBUG:
            gc.set_debug(gc.DEBUG_STATS)

        # 立即执行垃圾收集
        collected = gc.collect()
        logger.info(f"垃圾收集完成，回收对象数量: {collected}")


# 性能优化配置
PERFORMANCE_CONFIG = {
    "database": DatabaseOptimizer.get_connection_pool_config(),
    "cache": CacheOptimizer.get_cache_config(),
    "application": ApplicationOptimizer.get_uvicorn_config(),
    "middleware": ApplicationOptimizer.get_middleware_config(),
    "memory": MemoryOptimizer.get_memory_config(),
}


# 导出主要组件
__all__ = [
    "PerformanceMonitor",
    "performance_monitor",
    "performance_timer",
    "async_performance_context",
    "DatabaseOptimizer",
    "CacheOptimizer",
    "ApplicationOptimizer",
    "MemoryOptimizer",
    "PERFORMANCE_CONFIG",
]
