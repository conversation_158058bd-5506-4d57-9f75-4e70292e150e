"""
服务基类
为所有服务提供一致的错误处理、日志记录和通用功能
"""

import asyncio
import functools
import traceback
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union

from app.core.logging_config import get_contextual_logger, log_performance_metric
from app.core.exceptions import (
    BaseCustomException,
    DatabaseError,
    ExternalServiceError,
    NetworkError,
    ValidationError,
    BusinessLogicError,
    ServiceUnavailableError,
    TimeoutError,
    ConfigurationError
)

T = TypeVar('T')


class ServiceBase(ABC):
    """服务基类 - 提供通用的服务功能"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = get_contextual_logger(f"service.{service_name}")
        self._initialized = False
        self._metrics = {}
        
    async def initialize(self) -> None:
        """初始化服务"""
        if self._initialized:
            self.logger.warning("Service already initialized")
            return
            
        try:
            self.logger.info("Initializing service")
            await self._initialize_service()
            self._initialized = True
            self.logger.info("Service initialized successfully")
        except Exception as e:
            self.logger.error("Service initialization failed", 
                            error=str(e), exc_info=True)
            raise ConfigurationError(
                f"Failed to initialize {self.service_name} service: {str(e)}",
                details={"service": self.service_name, "error": str(e)}
            )
    
    async def cleanup(self) -> None:
        """清理服务资源"""
        if not self._initialized:
            return
            
        try:
            self.logger.info("Cleaning up service")
            await self._cleanup_service()
            self._initialized = False
            self.logger.info("Service cleanup completed")
        except Exception as e:
            self.logger.error("Service cleanup failed", 
                            error=str(e), exc_info=True)
    
    def is_initialized(self) -> bool:
        """检查服务是否已初始化"""
        return self._initialized
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        return {
            "service_name": self.service_name,
            "initialized": self._initialized,
            "metrics": self._metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    @abstractmethod
    async def _initialize_service(self) -> None:
        """子类实现的初始化逻辑"""
        pass
    
    async def _cleanup_service(self) -> None:
        """子类实现的清理逻辑（可选）"""
        pass
    
    def _update_metric(self, name: str, value: Any) -> None:
        """更新服务指标"""
        self._metrics[name] = {
            "value": value,
            "timestamp": datetime.utcnow().isoformat()
        }


def service_method(
    timeout: Optional[float] = None,
    retry_count: int = 0,
    retry_delay: float = 1.0,
    log_performance: bool = True,
    validate_initialized: bool = True
):
    """服务方法装饰器 - 提供统一的错误处理和性能监控"""
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def async_wrapper(self: ServiceBase, *args, **kwargs) -> T:
            # 检查服务是否已初始化
            if validate_initialized and not self.is_initialized():
                raise ServiceUnavailableError(
                    f"{self.service_name} service not initialized",
                    details={"service": self.service_name, "method": func.__name__}
                )
            
            method_name = f"{self.service_name}.{func.__name__}"
            start_time = datetime.utcnow()
            
            for attempt in range(retry_count + 1):
                try:
                    # 应用超时
                    if timeout:
                        result = await asyncio.wait_for(
                            func(self, *args, **kwargs),
                            timeout=timeout
                        )
                    else:
                        result = await func(self, *args, **kwargs)
                    
                    # 记录性能指标
                    if log_performance:
                        duration = (datetime.utcnow() - start_time).total_seconds()
                        log_performance_metric(
                            operation=method_name,
                            duration=duration,
                            metadata={
                                "service": self.service_name,
                                "method": func.__name__,
                                "attempt": attempt + 1,
                                "success": True
                            }
                        )
                        
                        # 更新服务内部指标
                        self._update_metric(f"{func.__name__}_last_duration", duration)
                        self._update_metric(f"{func.__name__}_success_count", 
                                          self._metrics.get(f"{func.__name__}_success_count", {}).get("value", 0) + 1)
                    
                    self.logger.debug(f"Service method completed successfully", 
                                    method=func.__name__, 
                                    attempt=attempt + 1)
                    
                    return result
                    
                except asyncio.TimeoutError as e:
                    error = TimeoutError(
                        f"{method_name} timed out after {timeout} seconds",
                        details={
                            "service": self.service_name,
                            "method": func.__name__,
                            "timeout": timeout,
                            "attempt": attempt + 1
                        }
                    )
                    await self._handle_method_error(error, func.__name__, attempt, retry_count)
                    if attempt == retry_count:
                        raise error
                        
                except BaseCustomException as e:
                    # 自定义异常直接抛出，不重试
                    await self._handle_method_error(e, func.__name__, attempt, 0)
                    raise
                    
                except Exception as e:
                    # 包装未知异常
                    wrapped_error = self._wrap_unknown_exception(e, func.__name__)
                    await self._handle_method_error(wrapped_error, func.__name__, attempt, retry_count)
                    
                    if attempt == retry_count:
                        raise wrapped_error
                
                # 重试延迟
                if attempt < retry_count:
                    await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
        
        @functools.wraps(func)
        def sync_wrapper(self: ServiceBase, *args, **kwargs) -> T:
            # 同步方法的简化处理
            if validate_initialized and not self.is_initialized():
                raise ServiceUnavailableError(
                    f"{self.service_name} service not initialized",
                    details={"service": self.service_name, "method": func.__name__}
                )
            
            method_name = f"{self.service_name}.{func.__name__}"
            start_time = datetime.utcnow()
            
            try:
                result = func(self, *args, **kwargs)
                
                # 记录性能指标
                if log_performance:
                    duration = (datetime.utcnow() - start_time).total_seconds()
                    log_performance_metric(
                        operation=method_name,
                        duration=duration,
                        metadata={
                            "service": self.service_name,
                            "method": func.__name__,
                            "success": True
                        }
                    )
                
                return result
                
            except BaseCustomException:
                raise
            except Exception as e:
                raise self._wrap_unknown_exception(e, func.__name__)
        
        # 根据函数是否为协程选择合适的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ServiceBase(ServiceBase):
    """扩展服务基类，添加错误处理方法"""
    
    async def _handle_method_error(
        self, 
        error: BaseCustomException, 
        method_name: str, 
        attempt: int, 
        max_attempts: int
    ) -> None:
        """处理方法执行错误"""
        if attempt < max_attempts:
            self.logger.warning(f"Service method failed, will retry", 
                              method=method_name,
                              attempt=attempt + 1,
                              max_attempts=max_attempts + 1,
                              error=str(error))
        else:
            self.logger.error(f"Service method failed after all retries", 
                            method=method_name,
                            attempts=attempt + 1,
                            error=str(error),
                            error_type=type(error).__name__)
        
        # 更新错误指标
        error_count_key = f"{method_name}_error_count"
        current_count = self._metrics.get(error_count_key, {}).get("value", 0)
        self._update_metric(error_count_key, current_count + 1)
    
    def _wrap_unknown_exception(self, exc: Exception, method_name: str) -> BaseCustomException:
        """包装未知异常为服务异常"""
        error_message = str(exc)
        
        # 根据异常类型选择合适的包装
        if "database" in error_message.lower() or "sql" in error_message.lower():
            return DatabaseError(
                f"Database error in {self.service_name}.{method_name}: {error_message}",
                details={
                    "service": self.service_name,
                    "method": method_name,
                    "original_error": error_message,
                    "original_type": type(exc).__name__
                },
                causes=[exc]
            )
        
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            return NetworkError(
                f"Network error in {self.service_name}.{method_name}: {error_message}",
                details={
                    "service": self.service_name,
                    "method": method_name,
                    "original_error": error_message,
                    "original_type": type(exc).__name__
                },
                causes=[exc]
            )
        
        elif "http" in error_message.lower() or "api" in error_message.lower():
            return ExternalServiceError(
                f"External service error in {self.service_name}.{method_name}: {error_message}",
                details={
                    "service": self.service_name,
                    "method": method_name,
                    "original_error": error_message,
                    "original_type": type(exc).__name__
                },
                causes=[exc]
            )
        
        else:
            return BusinessLogicError(
                f"Unexpected error in {self.service_name}.{method_name}: {error_message}",
                details={
                    "service": self.service_name,
                    "method": method_name,
                    "original_error": error_message,
                    "original_type": type(exc).__name__,
                    "traceback": traceback.format_exc()
                },
                causes=[exc]
            )


# 便捷装饰器
def database_operation(timeout: float = 30.0, retry_count: int = 2):
    """数据库操作装饰器"""
    return service_method(
        timeout=timeout,
        retry_count=retry_count,
        retry_delay=1.0,
        log_performance=True
    )


def external_api_call(timeout: float = 10.0, retry_count: int = 3):
    """外部API调用装饰器"""
    return service_method(
        timeout=timeout,
        retry_count=retry_count,
        retry_delay=2.0,
        log_performance=True
    )


def cache_operation(timeout: float = 5.0, retry_count: int = 1):
    """缓存操作装饰器"""
    return service_method(
        timeout=timeout,
        retry_count=retry_count,
        retry_delay=0.5,
        log_performance=False
    )


def critical_operation(timeout: float = 60.0, retry_count: int = 0):
    """关键操作装饰器（不重试）"""
    return service_method(
        timeout=timeout,
        retry_count=retry_count,
        log_performance=True
    )


# 工具函数
def validate_service_input(data: Any, schema: Type, field_name: str = "input") -> Any:
    """验证服务输入数据"""
    try:
        if hasattr(schema, 'parse_obj'):
            # Pydantic model
            return schema.parse_obj(data)
        elif hasattr(schema, '__call__'):
            # Callable validator
            return schema(data)
        else:
            return data
    except Exception as e:
        raise ValidationError(
            f"Invalid {field_name}: {str(e)}",
            details={
                "field": field_name,
                "provided_data": str(data)[:200],  # 截断长数据
                "expected_schema": str(schema),
                "validation_error": str(e)
            }
        )


def ensure_service_initialized(service: ServiceBase) -> None:
    """确保服务已初始化"""
    if not service.is_initialized():
        raise ServiceUnavailableError(
            f"Service {service.service_name} is not initialized",
            details={"service": service.service_name},
            recovery_hint="Please initialize the service before using it"
        )