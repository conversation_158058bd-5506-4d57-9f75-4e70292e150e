"""
数据库性能优化集成配置
统一管理所有数据库优化组件的初始化和配置
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional

from app.core.database import db_manager, init_db, cleanup_db
from app.core.query_cache import cache_manager, init_cache, cleanup_cache
from app.core.connection_pool_optimizer import pool_optimizer, init_optimized_database, cleanup_optimized_database
from app.monitoring.slow_query_analyzer import slow_query_analyzer, init_slow_query_monitoring
from app.monitoring.db_performance_monitor import db_performance_monitor, init_performance_monitoring


logger = logging.getLogger(__name__)


class DatabasePerformanceManager:
    """数据库性能管理器"""
    
    def __init__(self):
        self.initialized = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        self.config = {
            'enable_query_cache': True,
            'enable_connection_pool_optimization': True,
            'enable_slow_query_analysis': True,
            'enable_performance_monitoring': True,
            'enable_pagination_optimization': True,
            
            # 缓存配置
            'cache_redis_url': 'redis://localhost:6379/0',
            'cache_default_ttl': 300,
            
            # 连接池配置
            'pool_size': 20,
            'max_overflow': 30,
            'pool_timeout': 30,
            'pool_recycle': 3600,
            
            # 慢查询分析配置
            'slow_query_threshold': 1.0,
            'max_slow_query_events': 10000,
            
            # 性能监控配置
            'metrics_collection_interval': 60,
            'metrics_retention_hours': 24,
            'enable_alerting': True,
            
            # 分页优化配置
            'pagination_cache_ttl': 60,
            'max_page_size': 1000,
        }
    
    async def initialize(self, database_url: str = None, config_overrides: Dict[str, Any] = None):
        """初始化所有性能优化组件"""
        if self.initialized:
            logger.warning("数据库性能管理器已经初始化")
            return
        
        try:
            # 更新配置
            if config_overrides:
                self.config.update(config_overrides)
            
            logger.info("开始初始化数据库性能优化组件...")
            
            # 1. 初始化数据库连接
            if not database_url:
                database_url = "sqlite+aiosqlite:///./quant_dev.db"
            
            # 使用优化的数据库初始化
            if self.config['enable_connection_pool_optimization']:
                await init_optimized_database(database_url)
                logger.info("✓ 数据库连接池优化已启用")
            else:
                db_manager.initialize(database_url)
                await db_manager.create_tables()
                logger.info("✓ 标准数据库连接已初始化")
            
            # 2. 初始化查询缓存
            if self.config['enable_query_cache']:
                await init_cache()
                logger.info("✓ 查询缓存系统已启用")
            
            # 3. 初始化慢查询分析
            if self.config['enable_slow_query_analysis']:
                await init_slow_query_monitoring()
                # 配置慢查询阈值
                slow_query_analyzer.slow_threshold = self.config['slow_query_threshold']
                slow_query_analyzer.max_events = self.config['max_slow_query_events']
                logger.info("✓ 慢查询分析已启用")
            
            # 4. 初始化性能监控
            if self.config['enable_performance_monitoring']:
                await init_performance_monitoring()
                # 配置监控参数
                db_performance_monitor.config['collection_interval'] = self.config['metrics_collection_interval']
                db_performance_monitor.config['enable_alerting'] = self.config['enable_alerting']
                logger.info("✓ 数据库性能监控已启用")
            
            # 5. 启动监控任务
            if self.config['enable_performance_monitoring']:
                self.monitoring_task = asyncio.create_task(self._monitoring_loop())
                logger.info("✓ 性能监控循环已启动")
            
            self.initialized = True
            logger.info("数据库性能优化组件初始化完成")
            
            # 执行初始优化
            await self.run_initial_optimization()
            
        except Exception as e:
            logger.error(f"数据库性能管理器初始化失败: {e}")
            await self.cleanup()
            raise
    
    async def run_initial_optimization(self):
        """执行初始优化操作"""
        try:
            logger.info("执行初始数据库优化...")
            
            # 运行索引优化
            from scripts.db_index_optimizer import DatabaseIndexOptimizer
            async with DatabaseIndexOptimizer() as optimizer:
                report = await optimizer.generate_optimization_report()
                logger.info(f"索引优化完成: 创建 {len(report.get('created_indexes', []))} 个索引")
            
            # 预热缓存
            if self.config['enable_query_cache']:
                from app.core.query_cache import warm_up_cache
                await warm_up_cache()
                logger.info("缓存预热完成")
            
            logger.info("初始优化操作完成")
            
        except Exception as e:
            logger.warning(f"初始优化操作失败: {e}")
    
    async def _monitoring_loop(self):
        """性能监控循环"""
        while True:
            try:
                if self.config['enable_performance_monitoring']:
                    await db_performance_monitor.collect_metrics()
                
                await asyncio.sleep(self.config['metrics_collection_interval'])
                
            except asyncio.CancelledError:
                logger.info("性能监控循环已停止")
                break
            except Exception as e:
                logger.error(f"性能监控循环错误: {e}")
                await asyncio.sleep(60)  # 出错时等待60秒
    
    async def get_performance_status(self) -> Dict[str, Any]:
        """获取性能状态总览"""
        if not self.initialized:
            return {'status': 'not_initialized'}
        
        status = {
            'initialized': True,
            'timestamp': asyncio.get_event_loop().time(),
            'components': {}
        }
        
        try:
            # 查询缓存状态
            if self.config['enable_query_cache']:
                cache_stats = await cache_manager.get_stats()
                status['components']['query_cache'] = {
                    'enabled': True,
                    'hit_ratio': cache_stats.get('hit_ratio', 0),
                    'memory_usage': cache_stats.get('memory_cache', {}).get('total_items', 0)
                }
            
            # 连接池状态
            if self.config['enable_connection_pool_optimization']:
                pool_stats = await pool_optimizer.get_connection_pool_stats()
                if pool_stats:
                    status['components']['connection_pool'] = {
                        'enabled': True,
                        'utilization': pool_stats.pool_utilization,
                        'active_connections': pool_stats.checked_out,
                        'total_connections': pool_stats.total_connections
                    }
            
            # 慢查询分析状态
            if self.config['enable_slow_query_analysis']:
                status['components']['slow_query_analyzer'] = {
                    'enabled': True,
                    'total_queries': slow_query_analyzer.total_queries,
                    'slow_queries': slow_query_analyzer.slow_queries,
                    'slow_ratio': slow_query_analyzer.slow_queries / max(1, slow_query_analyzer.total_queries)
                }
            
            # 性能监控状态
            if self.config['enable_performance_monitoring']:
                recent_metrics = list(db_performance_monitor.metrics_history)[-1:] if db_performance_monitor.metrics_history else []
                if recent_metrics:
                    latest = recent_metrics[0]
                    status['components']['performance_monitor'] = {
                        'enabled': True,
                        'health_status': db_performance_monitor._get_health_status(latest),
                        'qps': latest.queries_per_second,
                        'avg_query_time': latest.avg_query_time,
                        'cpu_usage': latest.cpu_usage,
                        'memory_usage': latest.memory_usage
                    }
            
        except Exception as e:
            logger.error(f"获取性能状态失败: {e}")
            status['error'] = str(e)
        
        return status
    
    async def generate_comprehensive_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成综合性能报告"""
        if not self.initialized:
            return {'error': 'Manager not initialized'}
        
        report = {
            'report_type': 'comprehensive_performance',
            'period_hours': hours,
            'generated_at': asyncio.get_event_loop().time(),
            'config': self.config.copy(),
            'components': {}
        }
        
        try:
            # 性能监控报告
            if self.config['enable_performance_monitoring']:
                perf_report = await db_performance_monitor.get_performance_report(hours)
                report['components']['performance_monitoring'] = perf_report
            
            # 慢查询分析报告
            if self.config['enable_slow_query_analysis']:
                slow_query_report = await slow_query_analyzer.get_slow_query_report(hours)
                report['components']['slow_query_analysis'] = slow_query_report
            
            # 连接池优化报告
            if self.config['enable_connection_pool_optimization']:
                pool_report = await pool_optimizer.get_performance_report()
                report['components']['connection_pool'] = pool_report
            
            # 缓存统计报告
            if self.config['enable_query_cache']:
                cache_stats = await cache_manager.get_stats()
                report['components']['query_cache'] = cache_stats
            
            # 生成优化建议
            report['optimization_recommendations'] = await self._generate_optimization_recommendations(report)
            
        except Exception as e:
            logger.error(f"生成综合报告失败: {e}")
            report['error'] = str(e)
        
        return report
    
    async def _generate_optimization_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 基于性能监控的建议
            perf_data = report.get('components', {}).get('performance_monitoring', {})
            if perf_data:
                summary = perf_data.get('performance_summary', {})
                
                # 查询性能建议
                queries = summary.get('queries', {})
                if queries.get('avg_query_time', 0) > 1.0:
                    recommendations.append("平均查询时间过长，建议优化索引或查询逻辑")
                
                if queries.get('total_slow_queries', 0) > 100:
                    recommendations.append("慢查询数量过多，建议分析慢查询报告并优化")
                
                # 连接池建议
                connections = summary.get('connections', {})
                if connections.get('max_pool_utilization', 0) > 0.8:
                    recommendations.append("连接池利用率过高，建议增加连接池大小")
                
                # 资源使用建议
                resources = summary.get('resources', {})
                if resources.get('max_cpu_usage', 0) > 80:
                    recommendations.append("CPU使用率过高，建议优化查询性能或增加资源")
                
                if resources.get('max_memory_usage', 0) > 85:
                    recommendations.append("内存使用率过高，建议优化缓存策略或增加内存")
            
            # 基于缓存的建议
            cache_data = report.get('components', {}).get('query_cache', {})
            if cache_data and cache_data.get('hit_ratio', 0) < 0.7:
                recommendations.append("查询缓存命中率较低，建议调整缓存策略或TTL设置")
            
            # 基于慢查询的建议
            slow_query_data = report.get('components', {}).get('slow_query_analysis', {})
            if slow_query_data:
                if slow_query_data.get('summary', {}).get('slow_query_ratio', 0) > 0.1:
                    recommendations.append("慢查询比例过高，建议重点优化Top慢查询")
            
            # 通用建议
            recommendations.extend([
                "定期运行数据库索引优化脚本",
                "监控数据库增长情况，及时清理历史数据",
                "考虑使用读写分离或分片策略处理大数据量",
                "定期备份数据库并测试恢复流程"
            ])
            
        except Exception as e:
            logger.error(f"生成优化建议失败: {e}")
            recommendations.append("建议检查系统日志以获取详细的优化建议")
        
        return recommendations[:10]  # 限制建议数量
    
    async def cleanup(self):
        """清理所有组件"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        try:
            if self.config['enable_query_cache']:
                await cleanup_cache()
            
            if self.config['enable_connection_pool_optimization']:
                await cleanup_optimized_database()
            else:
                await cleanup_db()
            
            logger.info("数据库性能管理器已清理")
            
        except Exception as e:
            logger.error(f"清理数据库性能管理器失败: {e}")
        
        self.initialized = False
    
    @asynccontextmanager
    async def performance_context(self, database_url: str = None, config_overrides: Dict[str, Any] = None):
        """性能优化上下文管理器"""
        try:
            await self.initialize(database_url, config_overrides)
            yield self
        finally:
            await self.cleanup()


# 全局性能管理器实例
db_performance_manager = DatabasePerformanceManager()


# 便利函数
async def init_database_performance(database_url: str = None, **config_overrides):
    """初始化数据库性能优化"""
    await db_performance_manager.initialize(database_url, config_overrides)


async def cleanup_database_performance():
    """清理数据库性能优化"""
    await db_performance_manager.cleanup()


async def get_database_performance_status():
    """获取数据库性能状态"""
    return await db_performance_manager.get_performance_status()


async def generate_database_performance_report(hours: int = 24):
    """生成数据库性能报告"""
    return await db_performance_manager.generate_comprehensive_report(hours)


if __name__ == "__main__":
    async def test_performance_manager():
        """测试性能管理器"""
        print("测试数据库性能管理器...")
        
        async with db_performance_manager.performance_context() as manager:
            # 获取状态
            status = await manager.get_performance_status()
            print(f"初始化状态: {status['initialized']}")
            print(f"启用的组件: {list(status.get('components', {}).keys())}")
            
            # 等待一些数据收集
            await asyncio.sleep(2)
            
            # 生成报告
            report = await manager.generate_comprehensive_report(hours=1)
            print(f"报告组件: {list(report.get('components', {}).keys())}")
            
            recommendations = report.get('optimization_recommendations', [])
            if recommendations:
                print("优化建议:")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"  {i}. {rec}")
        
        print("测试完成")
    
    asyncio.run(test_performance_manager())