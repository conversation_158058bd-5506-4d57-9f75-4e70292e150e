name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'

jobs:
  # 后端测试和构建
  backend:
    name: Backend CI
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: quant_user
          POSTGRES_PASSWORD: quant_password
          POSTGRES_DB: quant_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio flake8 mypy
    
    - name: Lint with flake8
      run: |
        cd backend
        flake8 app --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 app --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Type check with mypy
      run: |
        cd backend
        mypy app --ignore-missing-imports || true
    
    - name: Run tests
      env:
        DATABASE_URL: postgresql://quant_user:quant_password@localhost:5432/quant_test
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        cd backend
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage
    
    - name: Build Docker image
      run: |
        docker build -f Dockerfile.backend -t quant-backend:test .
    
    - name: Run security scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'quant-backend:test'
        format: 'table'
        exit-code: '0'
        ignore-unfixed: true
        vuln-type: 'os,library'
        severity: 'CRITICAL,HIGH'

  # 前端测试和构建
  frontend:
    name: Frontend CI
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install pnpm
      run: npm install -g pnpm
    
    - name: Install dependencies
      run: |
        cd frontend
        pnpm install --frozen-lockfile
    
    - name: Lint
      run: |
        cd frontend
        pnpm lint:check
    
    - name: Type check
      run: |
        cd frontend
        pnpm type-check
    
    - name: Unit tests
      run: |
        cd frontend
        pnpm test:run
    
    - name: Build
      run: |
        cd frontend
        pnpm build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist
    
    - name: Build Docker image
      run: |
        docker build -f Dockerfile.frontend -t quant-frontend:test .
    
    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v9
      with:
        configPath: './frontend/.lighthouserc.js'
        uploadArtifacts: true
        temporaryPublicStorage: true

  # E2E测试
  e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [backend, frontend]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Start services with Docker Compose
      run: |
        docker-compose -f docker-compose.yml up -d
        sleep 30  # 等待服务启动
    
    - name: Check services health
      run: |
        curl -f http://localhost:8000/health || exit 1
        curl -f http://localhost || exit 1
    
    - name: Run E2E tests
      run: |
        cd frontend
        pnpm install --frozen-lockfile
        pnpm test:e2e
    
    - name: Upload E2E test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: e2e-test-results
        path: frontend/test-results
    
    - name: Stop services
      if: always()
      run: docker-compose down

  # 代码质量检查
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    
    - name: Check for secrets
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: ${{ github.event.repository.default_branch }}
        head: HEAD

  # 发布Docker镜像
  publish:
    name: Publish Docker Images
    runs-on: ubuntu-latest
    needs: [backend, frontend, e2e]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Log in to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push backend
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.backend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/quant-backend:latest
          ${{ secrets.DOCKER_USERNAME }}/quant-backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push frontend
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Dockerfile.frontend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/quant-frontend:latest
          ${{ secrets.DOCKER_USERNAME }}/quant-frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max