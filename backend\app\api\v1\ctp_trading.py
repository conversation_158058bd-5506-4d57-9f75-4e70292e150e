"""
CTP交易API接口
集成增强版CTP服务，提供真实交易功能
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import logging
import json

from app.services.enhanced_ctp_service import (
    get_ctp_service, 
    OrderRequest, 
    OrderResponse,
    OrderDirection,
    OrderType,
    TradeData,
    PositionData,
    AccountData,
    OrderStatus
)
from app.core.auth import get_current_user
from app.db.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ctp", tags=["CTP交易"])

# 请求模型
class SubmitOrderRequest(BaseModel):
    """提交订单请求"""
    symbol: str
    direction: str  # "BUY" or "SELL"
    order_type: str = "LIMIT"  # "LIMIT" or "MARKET"
    volume: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"

class CancelOrderRequest(BaseModel):
    """撤销订单请求"""
    order_id: str

class OrderQueryRequest(BaseModel):
    """订单查询请求"""
    symbol: Optional[str] = None
    status: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[int, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: int):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.user_connections[user_id] = websocket

    def disconnect(self, websocket: WebSocket, user_id: int):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id in self.user_connections:
            del self.user_connections[user_id]

    async def send_personal_message(self, message: str, user_id: int):
        if user_id in self.user_connections:
            try:
                await self.user_connections[user_id].send_text(message)
            except:
                # 连接已断开，清理
                self.disconnect(self.user_connections[user_id], user_id)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，从列表中移除
                if connection in self.active_connections:
                    self.active_connections.remove(connection)

manager = ConnectionManager()

# 初始化CTP服务回调
def setup_ctp_callbacks():
    """设置CTP服务回调"""
    ctp_service = get_ctp_service()
    
    async def on_order_update(order_data: Dict):
        """订单更新回调"""
        message = {
            "type": "order_update",
            "data": order_data
        }
        await manager.broadcast(json.dumps(message))
    
    async def on_trade(trade_data: Dict):
        """成交回调"""
        message = {
            "type": "trade",
            "data": trade_data
        }
        await manager.broadcast(json.dumps(message))
    
    async def on_position_update(position_data: Dict):
        """持仓更新回调"""
        message = {
            "type": "position_update",
            "data": position_data
        }
        await manager.broadcast(json.dumps(message))
    
    async def on_account_update(account_data: Dict):
        """账户更新回调"""
        message = {
            "type": "account_update",
            "data": account_data
        }
        await manager.broadcast(json.dumps(message))
    
    # 注册回调
    ctp_service.register_callback("on_order_update", on_order_update)
    ctp_service.register_callback("on_trade", on_trade)
    ctp_service.register_callback("on_position_update", on_position_update)
    ctp_service.register_callback("on_account_update", on_account_update)

# 在模块加载时设置回调
setup_ctp_callbacks()

@router.post("/orders", response_model=OrderResponse)
async def submit_order(
    request: SubmitOrderRequest,
    current_user: User = Depends(get_current_user)
):
    """提交订单"""
    try:
        ctp_service = get_ctp_service()
        
        # 转换订单请求
        order_request = OrderRequest(
            symbol=request.symbol,
            direction=OrderDirection(request.direction),
            order_type=OrderType(request.order_type),
            volume=request.volume,
            price=request.price,
            stop_price=request.stop_price,
            time_in_force=request.time_in_force
        )
        
        # 提交订单
        response = await ctp_service.submit_order(order_request, current_user.id)
        
        logger.info(f"Order submitted by user {current_user.id}: {response.order_id}")
        
        return response
        
    except Exception as e:
        logger.error(f"Submit order error: {e}")
        raise HTTPException(status_code=500, detail=f"订单提交失败: {str(e)}")

@router.delete("/orders/{order_id}", response_model=OrderResponse)
async def cancel_order(
    order_id: str,
    current_user: User = Depends(get_current_user)
):
    """撤销订单"""
    try:
        ctp_service = get_ctp_service()
        response = await ctp_service.cancel_order(order_id)
        
        logger.info(f"Order cancelled by user {current_user.id}: {order_id}")
        
        return response
        
    except Exception as e:
        logger.error(f"Cancel order error: {e}")
        raise HTTPException(status_code=500, detail=f"撤单失败: {str(e)}")

@router.get("/orders")
async def query_orders(
    symbol: Optional[str] = None,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """查询订单"""
    try:
        ctp_service = get_ctp_service()
        orders = await ctp_service.query_orders(current_user.id)
        
        # 应用筛选条件
        if symbol:
            orders = [order for order in orders if order["symbol"] == symbol]
        if status:
            orders = [order for order in orders if order["status"] == status]
        
        return {
            "success": True,
            "data": orders,
            "total": len(orders)
        }
        
    except Exception as e:
        logger.error(f"Query orders error: {e}")
        raise HTTPException(status_code=500, detail=f"查询订单失败: {str(e)}")

@router.get("/positions")
async def query_positions(
    current_user: User = Depends(get_current_user)
):
    """查询持仓"""
    try:
        ctp_service = get_ctp_service()
        positions = await ctp_service.query_positions(current_user.id)
        
        # 转换为字典格式
        positions_data = [pos.dict() if hasattr(pos, 'dict') else pos for pos in positions]
        
        return {
            "success": True,
            "data": positions_data,
            "total": len(positions_data)
        }
        
    except Exception as e:
        logger.error(f"Query positions error: {e}")
        raise HTTPException(status_code=500, detail=f"查询持仓失败: {str(e)}")

@router.get("/account")
async def query_account(
    current_user: User = Depends(get_current_user)
):
    """查询账户"""
    try:
        ctp_service = get_ctp_service()
        account = await ctp_service.query_account(current_user.id)
        
        if not account:
            raise HTTPException(status_code=404, detail="账户信息不存在")
        
        account_data = account.dict() if hasattr(account, 'dict') else account
        
        return {
            "success": True,
            "data": account_data
        }
        
    except Exception as e:
        logger.error(f"Query account error: {e}")
        raise HTTPException(status_code=500, detail=f"查询账户失败: {str(e)}")

@router.get("/status")
async def get_ctp_status():
    """获取CTP连接状态"""
    try:
        ctp_service = get_ctp_service()
        
        return {
            "success": True,
            "data": {
                "connected": ctp_service.is_connected,
                "logged_in": ctp_service.is_logged_in,
                "broker_id": ctp_service.config.get("broker_id"),
                "user_id": ctp_service.config.get("user_id"),
                "simulation_mode": ctp_service.config.get("simulation_mode", True)
            }
        }
        
    except Exception as e:
        logger.error(f"Get CTP status error: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/initialize")
async def initialize_ctp(
    config: Optional[Dict] = None,
    current_user: User = Depends(get_current_user)
):
    """初始化CTP连接"""
    try:
        ctp_service = get_ctp_service()
        
        if config:
            ctp_service.config.update(config)
        
        success = await ctp_service.initialize()
        
        if success:
            logger.info(f"CTP initialized by user {current_user.id}")
            return {
                "success": True,
                "message": "CTP初始化成功"
            }
        else:
            raise HTTPException(status_code=500, detail="CTP初始化失败")
            
    except Exception as e:
        logger.error(f"Initialize CTP error: {e}")
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = None
):
    """WebSocket连接端点"""
    try:
        # 这里应该验证token并获取用户ID
        # 简化处理，使用默认用户ID
        user_id = 1
        
        await manager.connect(websocket, user_id)
        
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理ping消息
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                
        except WebSocketDisconnect:
            manager.disconnect(websocket, user_id)
            logger.info(f"WebSocket disconnected for user {user_id}")
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        if websocket in manager.active_connections:
            await websocket.close()

# 批量订单接口
@router.post("/orders/batch")
async def submit_batch_orders(
    orders: List[SubmitOrderRequest],
    current_user: User = Depends(get_current_user)
):
    """批量提交订单"""
    try:
        ctp_service = get_ctp_service()
        results = []
        
        for order_request in orders:
            try:
                order_req = OrderRequest(
                    symbol=order_request.symbol,
                    direction=OrderDirection(order_request.direction),
                    order_type=OrderType(order_request.order_type),
                    volume=order_request.volume,
                    price=order_request.price,
                    stop_price=order_request.stop_price,
                    time_in_force=order_request.time_in_force
                )
                
                response = await ctp_service.submit_order(order_req, current_user.id)
                results.append(response.dict())
                
            except Exception as e:
                results.append({
                    "success": False,
                    "message": f"订单提交失败: {str(e)}",
                    "order_data": order_request.dict()
                })
        
        return {
            "success": True,
            "data": results,
            "total": len(results)
        }
        
    except Exception as e:
        logger.error(f"Batch submit orders error: {e}")
        raise HTTPException(status_code=500, detail=f"批量提交失败: {str(e)}")
