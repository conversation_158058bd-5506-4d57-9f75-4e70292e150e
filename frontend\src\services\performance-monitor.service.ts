/**
 * 性能监控服务
 * 监控页面加载时间、API响应时间、用户交互性能等
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  url?: string
  userAgent?: string
}

interface PageLoadMetrics {
  domContentLoaded: number
  loadComplete: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
}

interface APIMetrics {
  url: string
  method: string
  duration: number
  status: number
  timestamp: number
}

class PerformanceMonitorService {
  private metrics: PerformanceMetric[] = []
  private apiMetrics: APIMetrics[] = []
  private isEnabled = true
  private reportInterval = 30000 // 30秒上报一次

  constructor() {
    this.init()
  }

  /**
   * 初始化性能监控
   */
  private init(): void {
    if (!this.isEnabled || typeof window === 'undefined') return

    // 监控页面加载性能
    this.monitorPageLoad()
    
    // 监控Core Web Vitals
    this.monitorWebVitals()
    
    // 监控资源加载
    this.monitorResourceLoad()
    
    // 定期上报数据
    this.startReporting()

    console.log('📊 性能监控服务已启动')
  }

  /**
   * 监控页面加载性能
   */
  private monitorPageLoad(): void {
    if (document.readyState === 'complete') {
      this.collectPageLoadMetrics()
    } else {
      window.addEventListener('load', () => {
        // 延迟收集，确保所有指标都可用
        setTimeout(() => this.collectPageLoadMetrics(), 1000)
      })
    }
  }

  /**
   * 收集页面加载指标
   */
  private collectPageLoadMetrics(): void {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (!navigation) return

    const metrics: PageLoadMetrics = {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0
    }

    // 收集Paint Timing
    const paintEntries = performance.getEntriesByType('paint')
    paintEntries.forEach(entry => {
      if (entry.name === 'first-contentful-paint') {
        metrics.firstContentfulPaint = entry.startTime
      }
    })

    this.recordMetric('page_load_dom_content_loaded', metrics.domContentLoaded)
    this.recordMetric('page_load_complete', metrics.loadComplete)
    this.recordMetric('page_load_fcp', metrics.firstContentfulPaint)

    console.log('📊 页面加载性能指标:', metrics)
  }

  /**
   * 监控Core Web Vitals
   */
  private monitorWebVitals(): void {
    // LCP (Largest Contentful Paint)
    this.observePerformanceEntry('largest-contentful-paint', (entry) => {
      this.recordMetric('web_vitals_lcp', entry.startTime)
    })

    // FID (First Input Delay) - 通过事件监听器模拟
    let firstInputProcessed = false
    const firstInputHandler = (event: Event) => {
      if (firstInputProcessed) return
      firstInputProcessed = true

      const now = performance.now()
      this.recordMetric('web_vitals_fid', now)
      
      // 移除监听器
      ['mousedown', 'keydown', 'touchstart', 'pointerdown'].forEach(type => {
        document.removeEventListener(type, firstInputHandler, true)
      })
    }

    // 添加首次输入监听器
    ['mousedown', 'keydown', 'touchstart', 'pointerdown'].forEach(type => {
      document.addEventListener(type, firstInputHandler, true)
    })

    // CLS (Cumulative Layout Shift)
    this.observePerformanceEntry('layout-shift', (entry: any) => {
      if (!entry.hadRecentInput) {
        this.recordMetric('web_vitals_cls', entry.value)
      }
    })
  }

  /**
   * 监控资源加载
   */
  private monitorResourceLoad(): void {
    // 监控现有资源
    const resources = performance.getEntriesByType('resource')
    resources.forEach(resource => {
      this.analyzeResourceTiming(resource as PerformanceResourceTiming)
    })

    // 监控新资源
    this.observePerformanceEntry('resource', (entry) => {
      this.analyzeResourceTiming(entry as PerformanceResourceTiming)
    })
  }

  /**
   * 分析资源加载时间
   */
  private analyzeResourceTiming(resource: PerformanceResourceTiming): void {
    const duration = resource.responseEnd - resource.startTime
    const resourceType = this.getResourceType(resource.name)

    this.recordMetric(`resource_load_${resourceType}`, duration, resource.name)

    // 记录慢资源
    if (duration > 3000) { // 超过3秒的资源
      console.warn(`🐌 慢资源加载: ${resource.name} (${duration.toFixed(2)}ms)`)
    }
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script'
    if (url.includes('.css')) return 'stylesheet'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image'
    if (url.includes('/api/')) return 'api'
    return 'other'
  }

  /**
   * 观察性能条目
   */
  private observePerformanceEntry(type: string, callback: (entry: PerformanceEntry) => void): void {
    if (!('PerformanceObserver' in window)) return

    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback)
      })
      observer.observe({ entryTypes: [type] })
    } catch (error) {
      console.warn(`无法观察性能条目类型: ${type}`, error)
    }
  }

  /**
   * 记录API性能指标
   */
  recordAPIMetric(url: string, method: string, duration: number, status: number): void {
    const metric: APIMetrics = {
      url,
      method,
      duration,
      status,
      timestamp: Date.now()
    }

    this.apiMetrics.push(metric)
    this.recordMetric('api_response_time', duration, url)

    // 记录慢API
    if (duration > 5000) { // 超过5秒的API
      console.warn(`🐌 慢API请求: ${method} ${url} (${duration.toFixed(2)}ms)`)
    }

    // 记录错误API
    if (status >= 400) {
      console.error(`❌ API错误: ${method} ${url} (状态码: ${status})`)
    }
  }

  /**
   * 记录自定义性能指标
   */
  recordMetric(name: string, value: number, url?: string): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url,
      userAgent: navigator.userAgent
    }

    this.metrics.push(metric)

    // 限制内存中的指标数量
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500)
    }
  }

  /**
   * 开始性能数据上报
   */
  private startReporting(): void {
    setInterval(() => {
      this.reportMetrics()
    }, this.reportInterval)
  }

  /**
   * 上报性能指标
   */
  private reportMetrics(): void {
    if (this.metrics.length === 0 && this.apiMetrics.length === 0) return

    const report = {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.metrics.splice(0), // 清空已上报的指标
      apiMetrics: this.apiMetrics.splice(0),
      memoryUsage: this.getMemoryUsage(),
      connectionInfo: this.getConnectionInfo()
    }

    // 在开发环境中输出到控制台
    if (import.meta.env.DEV) {
      console.log('📊 性能报告:', report)
    }

    // 在生产环境中可以发送到监控服务
    // this.sendToMonitoringService(report)
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): any {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }
    return null
  }

  /**
   * 获取网络连接信息
   */
  private getConnectionInfo(): any {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      }
    }
    return null
  }

  /**
   * 获取性能摘要
   */
  getPerformanceSummary(): any {
    const now = Date.now()
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 300000) // 最近5分钟

    const summary = {
      totalMetrics: recentMetrics.length,
      avgPageLoadTime: this.calculateAverage(recentMetrics, 'page_load_complete'),
      avgAPIResponseTime: this.calculateAverage(this.apiMetrics, 'duration'),
      slowAPIs: this.apiMetrics.filter(api => api.duration > 5000).length,
      errorAPIs: this.apiMetrics.filter(api => api.status >= 400).length
    }

    return summary
  }

  /**
   * 计算平均值
   */
  private calculateAverage(data: any[], field: string): number {
    if (data.length === 0) return 0
    const sum = data.reduce((acc, item) => acc + (item[field] || 0), 0)
    return sum / data.length
  }

  /**
   * 启用/禁用性能监控
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    console.log(`📊 性能监控已${enabled ? '启用' : '禁用'}`)
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitorService()

// 导出类型
export type { PerformanceMetric, PageLoadMetrics, APIMetrics }
