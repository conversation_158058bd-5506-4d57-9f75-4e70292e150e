const puppeteer = require('./puppeteer/node_modules/puppeteer');
const fs = require('fs');
const path = require('path');

class ComprehensiveUITester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        warnings: 0
      },
      pages: {},
      components: {},
      interactions: {},
      accessibility: {},
      performance: {},
      screenshots: [],
      issues: []
    };
    this.baseUrl = 'http://localhost:5173';
    this.apiUrl = 'http://localhost:8000';
  }

  async init() {
    console.log('🚀 启动浏览器...');
    this.browser = await puppeteer.launch({
      headless: false, // 显示浏览器以便观察
      defaultViewport: { width: 1920, height: 1080 },
      executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    
    // 设置用户代理
    await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
    
    // 监听控制台错误
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.results.issues.push({
          type: 'console_error',
          message: msg.text(),
          location: msg.location()
        });
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      this.results.issues.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack
      });
    });
  }

  async testPageLoad(pageName, url) {
    console.log(`📄 测试页面: ${pageName} (${url})`);
    this.results.summary.totalTests++;
    
    try {
      const startTime = Date.now();
      await this.page.goto(url, { 
        waitUntil: 'networkidle2', 
        timeout: 30000 
      });
      const loadTime = Date.now() - startTime;

      // 截图
      const screenshotPath = `screenshots/${pageName.replace(/[^a-zA-Z0-9]/g, '_')}.png`;
      await this.page.screenshot({ path: screenshotPath, fullPage: true });
      this.results.screenshots.push(screenshotPath);

      // 检查页面基本元素
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          hasNavigation: !!document.querySelector('nav, .nav, .navigation'),
          hasHeader: !!document.querySelector('header, .header'),
          hasMain: !!document.querySelector('main, .main, .content'),
          hasFooter: !!document.querySelector('footer, .footer'),
          elementCounts: {
            buttons: document.querySelectorAll('button, .el-button').length,
            inputs: document.querySelectorAll('input, .el-input').length,
            cards: document.querySelectorAll('.el-card, .card').length,
            tables: document.querySelectorAll('table, .el-table').length,
            forms: document.querySelectorAll('form, .el-form').length
          },
          hasErrors: !!document.querySelector('.error, .el-message--error'),
          hasLoading: !!document.querySelector('.loading, .el-loading')
        };
      });

      this.results.pages[pageName] = {
        status: 'success',
        loadTime,
        url,
        ...pageInfo
      };
      
      this.results.summary.passedTests++;
      console.log(`✅ ${pageName} 加载成功 (${loadTime}ms)`);
      
    } catch (error) {
      this.results.pages[pageName] = {
        status: 'failed',
        error: error.message,
        url
      };
      this.results.summary.failedTests++;
      console.log(`❌ ${pageName} 加载失败: ${error.message}`);
    }
  }

  async testButtons(pageName) {
    console.log(`🔘 测试 ${pageName} 页面的按钮...`);
    
    try {
      // 获取所有按钮
      const buttons = await this.page.$$('button, .el-button');
      const buttonResults = [];

      for (let i = 0; i < Math.min(buttons.length, 20); i++) { // 限制测试前20个按钮
        const button = buttons[i];
        
        try {
          // 获取按钮信息
          const buttonInfo = await button.evaluate(el => ({
            text: el.textContent?.trim() || '',
            className: el.className || '',
            disabled: el.disabled || el.classList.contains('is-disabled'),
            visible: el.offsetParent !== null,
            type: el.type || 'button'
          }));

          if (buttonInfo.visible && !buttonInfo.disabled) {
            // 尝试点击按钮
            await button.click();
            await this.page.waitForTimeout(1000); // 等待响应
            
            buttonResults.push({
              index: i,
              ...buttonInfo,
              clickable: true,
              status: 'success'
            });
          } else {
            buttonResults.push({
              index: i,
              ...buttonInfo,
              clickable: false,
              status: 'skipped',
              reason: buttonInfo.disabled ? 'disabled' : 'not visible'
            });
          }
        } catch (error) {
          buttonResults.push({
            index: i,
            status: 'failed',
            error: error.message
          });
        }
      }

      this.results.components[`${pageName}_buttons`] = {
        total: buttons.length,
        tested: buttonResults.length,
        results: buttonResults
      };

    } catch (error) {
      console.log(`❌ 测试 ${pageName} 按钮时出错: ${error.message}`);
    }
  }

  async testForms(pageName) {
    console.log(`📝 测试 ${pageName} 页面的表单...`);
    
    try {
      const forms = await this.page.$$('form, .el-form');
      const formResults = [];

      for (let i = 0; i < forms.length; i++) {
        const form = forms[i];
        
        try {
          const formInfo = await form.evaluate(el => {
            const inputs = el.querySelectorAll('input, textarea, select');
            return {
              inputCount: inputs.length,
              hasSubmitButton: !!el.querySelector('button[type="submit"], .submit-btn'),
              visible: el.offsetParent !== null
            };
          });

          // 测试表单输入
          const inputs = await form.$$('input[type="text"], input[type="email"], textarea');
          for (const input of inputs.slice(0, 3)) { // 测试前3个输入框
            await input.click();
            await input.type('测试数据');
            await this.page.waitForTimeout(500);
          }

          formResults.push({
            index: i,
            ...formInfo,
            status: 'success'
          });

        } catch (error) {
          formResults.push({
            index: i,
            status: 'failed',
            error: error.message
          });
        }
      }

      this.results.components[`${pageName}_forms`] = {
        total: forms.length,
        results: formResults
      };

    } catch (error) {
      console.log(`❌ 测试 ${pageName} 表单时出错: ${error.message}`);
    }
  }

  async testNavigation() {
    console.log('🧭 测试导航功能...');
    
    const routes = [
      { name: '首页', path: '/' },
      { name: '仪表板', path: '/dashboard' },
      { name: '市场数据', path: '/market' },
      { name: '交易', path: '/trading' },
      { name: '策略', path: '/strategy' },
      { name: '回测', path: '/backtest' },
      { name: '投资组合', path: '/portfolio' },
      { name: '风险管理', path: '/risk' },
      { name: '组件展示', path: '/demo' },
      { name: 'API测试', path: '/api-test' }
    ];

    for (const route of routes) {
      await this.testPageLoad(route.name, `${this.baseUrl}${route.path}`);
      await this.testButtons(route.name);
      await this.testForms(route.name);
      await this.page.waitForTimeout(2000); // 页面间等待
    }
  }

  async testSpecialFeatures() {
    console.log('🎯 测试特殊功能...');
    
    // 测试验证码功能
    try {
      await this.page.goto(`${this.baseUrl}/test-captcha`);
      await this.page.waitForTimeout(2000);
      
      const captchaExists = await this.page.$('.slider-captcha, .captcha-container');
      if (captchaExists) {
        console.log('✅ 验证码组件存在');
        // 尝试拖拽验证码
        const slider = await this.page.$('.slider-handle, .captcha-slider');
        if (slider) {
          const box = await slider.boundingBox();
          await this.page.mouse.move(box.x + box.width/2, box.y + box.height/2);
          await this.page.mouse.down();
          await this.page.mouse.move(box.x + 200, box.y + box.height/2);
          await this.page.mouse.up();
          console.log('✅ 验证码拖拽测试完成');
        }
      }
    } catch (error) {
      console.log(`❌ 验证码测试失败: ${error.message}`);
    }
  }

  async generateReport() {
    console.log('📊 生成测试报告...');
    
    // 创建截图目录
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }

    // 计算成功率
    const successRate = this.results.summary.totalTests > 0 
      ? (this.results.summary.passedTests / this.results.summary.totalTests * 100).toFixed(2)
      : 0;

    // 生成HTML报告
    const htmlReport = this.generateHTMLReport(successRate);
    fs.writeFileSync('test_report.html', htmlReport);

    // 生成JSON报告
    fs.writeFileSync('test_results.json', JSON.stringify(this.results, null, 2));

    console.log(`📋 测试完成! 成功率: ${successRate}%`);
    console.log(`📁 报告已保存: test_report.html`);
    console.log(`📁 详细数据: test_results.json`);
  }

  generateHTMLReport(successRate) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易平台 - UI测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .success-rate { font-size: 48px; font-weight: bold; color: ${successRate >= 80 ? '#67C23A' : successRate >= 60 ? '#E6A23C' : '#F56C6C'}; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #409EFF; }
        .section { margin-bottom: 30px; }
        .section h3 { color: #303133; border-bottom: 2px solid #409EFF; padding-bottom: 10px; }
        .page-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .page-card { border: 1px solid #EBEEF5; border-radius: 6px; padding: 15px; }
        .status-success { border-left: 4px solid #67C23A; }
        .status-failed { border-left: 4px solid #F56C6C; }
        .issues { background: #FEF0F0; border: 1px solid #FBC4C4; border-radius: 6px; padding: 15px; }
        .issue { margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 量化交易平台 UI测试报告</h1>
            <div class="success-rate">${successRate}%</div>
            <p>测试时间: ${this.results.timestamp}</p>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value">${this.results.summary.totalTests}</div>
                <div>总测试数</div>
            </div>
            <div class="metric">
                <div class="metric-value">${this.results.summary.passedTests}</div>
                <div>通过测试</div>
            </div>
            <div class="metric">
                <div class="metric-value">${this.results.summary.failedTests}</div>
                <div>失败测试</div>
            </div>
            <div class="metric">
                <div class="metric-value">${this.results.issues.length}</div>
                <div>发现问题</div>
            </div>
        </div>

        <div class="section">
            <h3>📄 页面测试结果</h3>
            <div class="page-grid">
                ${Object.entries(this.results.pages).map(([name, data]) => `
                    <div class="page-card status-${data.status}">
                        <h4>${name}</h4>
                        <p><strong>状态:</strong> ${data.status === 'success' ? '✅ 成功' : '❌ 失败'}</p>
                        ${data.loadTime ? `<p><strong>加载时间:</strong> ${data.loadTime}ms</p>` : ''}
                        ${data.elementCounts ? `
                            <p><strong>页面元素:</strong></p>
                            <ul>
                                <li>按钮: ${data.elementCounts.buttons}</li>
                                <li>输入框: ${data.elementCounts.inputs}</li>
                                <li>卡片: ${data.elementCounts.cards}</li>
                                <li>表格: ${data.elementCounts.tables}</li>
                            </ul>
                        ` : ''}
                        ${data.error ? `<p style="color: #F56C6C;"><strong>错误:</strong> ${data.error}</p>` : ''}
                    </div>
                `).join('')}
            </div>
        </div>

        ${this.results.issues.length > 0 ? `
        <div class="section">
            <h3>⚠️ 发现的问题</h3>
            <div class="issues">
                ${this.results.issues.map(issue => `
                    <div class="issue">
                        <strong>${issue.type}:</strong> ${issue.message}
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        <div class="section">
            <h3>📸 页面截图</h3>
            <p>共生成 ${this.results.screenshots.length} 张截图，保存在 screenshots/ 目录中</p>
        </div>
    </div>
</body>
</html>`;
  }

  async run() {
    try {
      await this.init();
      await this.testNavigation();
      await this.testSpecialFeatures();
      await this.generateReport();
    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行测试
const tester = new ComprehensiveUITester();
tester.run().catch(console.error);
