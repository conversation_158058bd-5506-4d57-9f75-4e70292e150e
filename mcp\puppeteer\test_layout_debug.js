const puppeteer = require('puppeteer-core');

async function testLayout() {
  const browser = await puppeteer.launch({
    executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    if (type === 'error' || text.includes('error') || text.includes('Error')) {
      console.log(`🚨 [${type}]: ${text}`);
    }
  });

  try {
    console.log('🚀 访问实时行情页面...');
    await page.goto('http://localhost:5173/market/realtime', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // 等待页面完全加载
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 检查布局结构
    const layoutInfo = await page.evaluate(() => {
      const getElementInfo = (selector) => {
        const element = document.querySelector(selector);
        if (!element) return { exists: false };
        
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return {
          exists: true,
          visible: rect.width > 0 && rect.height > 0,
          display: style.display,
          visibility: style.visibility,
          position: style.position,
          width: rect.width,
          height: rect.height,
          left: rect.left,
          top: rect.top,
          zIndex: style.zIndex,
          className: element.className,
          innerHTML: element.innerHTML.length
        };
      };

      return {
        defaultLayout: getElementInfo('.default-layout'),
        layoutSidebar: getElementInfo('.layout-sidebar'),
        layoutMain: getElementInfo('.layout-main'),
        layoutHeader: getElementInfo('.layout-header'),
        layoutContent: getElementInfo('.layout-content'),
        sidebarNav: getElementInfo('.sidebar-nav'),
        elMenu: getElementInfo('.el-menu'),
        routerView: getElementInfo('router-view'),
        marketView: getElementInfo('.market-view-optimized'),
        
        // 检查CSS变量
        cssVars: {
          bgColor: getComputedStyle(document.documentElement).getPropertyValue('--el-bg-color'),
          bgColorPage: getComputedStyle(document.documentElement).getPropertyValue('--el-bg-color-page'),
          borderColor: getComputedStyle(document.documentElement).getPropertyValue('--el-border-color-light')
        },
        
        // 检查body样式
        bodyStyle: {
          margin: getComputedStyle(document.body).margin,
          padding: getComputedStyle(document.body).padding,
          overflow: getComputedStyle(document.body).overflow
        }
      };
    });

    console.log('\n📋 布局结构分析:');
    console.log('='.repeat(60));
    
    Object.entries(layoutInfo).forEach(([key, value]) => {
      if (value && typeof value === 'object' && value.exists !== undefined) {
        const status = value.exists ? (value.visible ? '✅ 可见' : '⚠️ 隐藏') : '❌ 不存在';
        console.log(`${key}: ${status}`);
        if (value.exists) {
          console.log(`  尺寸: ${value.width}x${value.height}`);
          console.log(`  位置: (${value.left}, ${value.top})`);
          console.log(`  样式: display=${value.display}, visibility=${value.visibility}`);
          if (value.className) {
            console.log(`  类名: ${value.className}`);
          }
        }
        console.log('');
      }
    });

    console.log('🎨 CSS变量:');
    console.log(JSON.stringify(layoutInfo.cssVars, null, 2));
    
    console.log('\n📄 Body样式:');
    console.log(JSON.stringify(layoutInfo.bodyStyle, null, 2));

    // 尝试强制显示侧边栏
    console.log('\n🔧 尝试修复布局...');
    await page.evaluate(() => {
      // 强制设置布局样式
      const defaultLayout = document.querySelector('.default-layout');
      if (defaultLayout) {
        defaultLayout.style.display = 'flex';
        defaultLayout.style.height = '100vh';
        defaultLayout.style.width = '100%';
      }

      const sidebar = document.querySelector('.layout-sidebar');
      if (sidebar) {
        sidebar.style.display = 'block';
        sidebar.style.width = '256px';
        sidebar.style.height = '100vh';
        sidebar.style.position = 'relative';
        sidebar.style.zIndex = '1000';
        sidebar.style.backgroundColor = '#ffffff';
        sidebar.style.borderRight = '1px solid #e4e7ed';
      }

      const main = document.querySelector('.layout-main');
      if (main) {
        main.style.display = 'flex';
        main.style.flexDirection = 'column';
        main.style.flex = '1';
        main.style.minWidth = '0';
      }

      return 'Layout styles applied';
    });

    await new Promise(resolve => setTimeout(resolve, 2000));

    // 再次检查布局
    const finalCheck = await page.evaluate(() => {
      const getElementInfo = (selector) => {
        const element = document.querySelector(selector);
        if (!element) return { exists: false };
        
        const rect = element.getBoundingClientRect();
        return {
          exists: true,
          visible: rect.width > 0 && rect.height > 0,
          width: rect.width,
          height: rect.height
        };
      };

      return {
        defaultLayout: getElementInfo('.default-layout'),
        layoutSidebar: getElementInfo('.layout-sidebar'),
        layoutMain: getElementInfo('.layout-main'),
        marketView: getElementInfo('.market-view-optimized')
      };
    });

    console.log('\n🔍 修复后检查:');
    Object.entries(finalCheck).forEach(([key, value]) => {
      if (value && typeof value === 'object' && value.exists !== undefined) {
        const status = value.exists ? (value.visible ? '✅ 可见' : '⚠️ 隐藏') : '❌ 不存在';
        console.log(`${key}: ${status} (${value.width || 0}x${value.height || 0})`);
      }
    });

    // 保持浏览器打开以便查看
    console.log('\n✅ 测试完成，浏览器保持打开状态以便查看...');
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

testLayout();
