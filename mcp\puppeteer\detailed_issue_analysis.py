#!/usr/bin/env python3
"""
量化投资平台深度问题分析
基于Puppeteer测试结果进行详细的问题诊断和分析
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

class PlatformIssueAnalyzer:
    def __init__(self):
        self.manager = BrowserManager()
        self.page = None
        self.issues = []
        self.performance_metrics = []
        
    async def setup(self):
        """初始化浏览器"""
        self.page = await self.manager.ensure_browser()
        print("🔍 开始深度问题分析...")
        
    async def teardown(self):
        """清理资源"""
        if self.manager.browser:
            await self.manager.browser.close()
            
    async def log_issue(self, category, severity, title, description, solution=""):
        """记录问题"""
        issue = {
            "category": category,  # "performance", "functionality", "ui", "api", "websocket"
            "severity": severity,  # "critical", "high", "medium", "low"
            "title": title,
            "description": description,
            "solution": solution,
            "timestamp": datetime.now().isoformat()
        }
        self.issues.append(issue)
        
        severity_icon = {"critical": "🚨", "high": "⚠️", "medium": "⚡", "low": "💡"}
        print(f"{severity_icon.get(severity, '📝')} [{category.upper()}] {title}")
        
    async def analyze_network_issues(self):
        """分析网络和API问题"""
        print("\n🌐 分析网络和API问题...")
        
        try:
            # 访问首页并监控网络请求
            await self.page.goto('http://localhost:5173', wait_until='domcontentloaded')
            await self.page.wait_for_timeout(5000)
            
            # 检查控制台错误
            console_errors = await self.page.evaluate('''() => {
                return window.consoleErrors || [];
            }''')
            
            # 分析WebSocket连接问题
            websocket_status = await self.page.evaluate('''() => {
                return {
                    websocketErrors: window.websocketErrors || [],
                    connectionAttempts: window.connectionAttempts || 0,
                    lastError: window.lastWebSocketError || null
                };
            }''')
            
            if websocket_status['websocketErrors']:
                await self.log_issue(
                    "websocket", "high",
                    "WebSocket连接失败",
                    f"WebSocket连接到 ws://localhost:8000/api/v1/ws 失败，错误次数: {len(websocket_status['websocketErrors'])}",
                    "检查后端WebSocket服务是否正常启动，确认端口8000可访问"
                )
                
            # 检查API响应
            api_status = await self.page.evaluate('''() => {
                return {
                    successfulRequests: window.successfulApiRequests || 0,
                    failedRequests: window.failedApiRequests || 0,
                    slowRequests: window.slowApiRequests || 0
                };
            }''')
            
            if api_status['failedRequests'] > 0:
                await self.log_issue(
                    "api", "medium",
                    "API请求失败",
                    f"发现 {api_status['failedRequests']} 个失败的API请求",
                    "检查后端API服务状态和网络连接"
                )
                
        except Exception as e:
            await self.log_issue(
                "performance", "critical",
                "页面加载超时",
                f"首页加载超时: {str(e)}",
                "优化页面加载性能，减少初始化时间"
            )
            
    async def analyze_performance_issues(self):
        """分析性能问题"""
        print("\n⚡ 分析性能问题...")
        
        try:
            # 测试页面加载性能
            start_time = datetime.now()
            await self.page.goto('http://localhost:5173', wait_until='domcontentloaded')
            load_time = (datetime.now() - start_time).total_seconds()
            
            self.performance_metrics.append({
                "metric": "page_load_time",
                "value": load_time,
                "unit": "seconds"
            })
            
            if load_time > 5:
                await self.log_issue(
                    "performance", "medium",
                    "页面加载缓慢",
                    f"首页加载时间: {load_time:.2f}秒，超过5秒阈值",
                    "优化资源加载，使用懒加载，压缩静态资源"
                )
                
            # 检查资源加载
            resource_info = await self.page.evaluate('''() => {
                const resources = performance.getEntriesByType('resource');
                return {
                    totalResources: resources.length,
                    slowResources: resources.filter(r => r.duration > 1000).length,
                    failedResources: resources.filter(r => r.transferSize === 0).length
                };
            }''')
            
            if resource_info['slowResources'] > 0:
                await self.log_issue(
                    "performance", "medium",
                    "资源加载缓慢",
                    f"发现 {resource_info['slowResources']} 个加载超过1秒的资源",
                    "优化图片和脚本文件大小，使用CDN"
                )
                
        except Exception as e:
            await self.log_issue(
                "performance", "high",
                "性能分析失败",
                f"无法完成性能分析: {str(e)}",
                "检查页面是否能正常访问"
            )
            
    async def analyze_ui_issues(self):
        """分析UI和交互问题"""
        print("\n🎨 分析UI和交互问题...")
        
        try:
            await self.page.goto('http://localhost:5173', wait_until='domcontentloaded')
            await self.page.wait_for_timeout(3000)
            
            # 检查页面元素
            page_elements = await self.page.evaluate('''() => {
                return {
                    buttons: document.querySelectorAll('button, .btn').length,
                    inputs: document.querySelectorAll('input, textarea').length,
                    links: document.querySelectorAll('a').length,
                    images: document.querySelectorAll('img').length,
                    brokenImages: Array.from(document.querySelectorAll('img')).filter(img => !img.complete || img.naturalHeight === 0).length
                };
            }''')
            
            if page_elements['brokenImages'] > 0:
                await self.log_issue(
                    "ui", "medium",
                    "图片加载失败",
                    f"发现 {page_elements['brokenImages']} 个无法加载的图片",
                    "检查图片路径和服务器配置"
                )
                
            # 检查响应式设计
            viewport_sizes = [(1920, 1080), (768, 1024), (375, 667)]
            for width, height in viewport_sizes:
                await self.page.set_viewport_size({"width": width, "height": height})
                await self.page.wait_for_timeout(1000)
                
                # 检查是否有横向滚动条
                has_horizontal_scroll = await self.page.evaluate('''() => {
                    return document.body.scrollWidth > window.innerWidth;
                }''')
                
                if has_horizontal_scroll:
                    await self.log_issue(
                        "ui", "low",
                        f"响应式设计问题 ({width}x{height})",
                        "页面在该尺寸下出现横向滚动条",
                        "调整CSS媒体查询和布局"
                    )
                    
        except Exception as e:
            await self.log_issue(
                "ui", "medium",
                "UI分析失败",
                f"UI分析过程中出错: {str(e)}",
                "检查页面DOM结构"
            )
            
    async def analyze_functionality_issues(self):
        """分析功能问题"""
        print("\n⚙️ 分析功能问题...")
        
        try:
            # 测试主要页面访问
            pages_to_test = [
                ('/dashboard', '仪表盘'),
                ('/market', '市场数据'),
                ('/trading', '交易终端'),
                ('/strategy', '策略中心'),
                ('/portfolio', '投资组合'),
                ('/risk', '风险管理')
            ]
            
            working_pages = 0
            for path, name in pages_to_test:
                try:
                    await self.page.goto(f'http://localhost:5173{path}', wait_until='domcontentloaded', timeout=10000)
                    await self.page.wait_for_timeout(2000)
                    working_pages += 1
                except Exception as e:
                    await self.log_issue(
                        "functionality", "high",
                        f"{name}页面无法访问",
                        f"访问 {path} 时出错: {str(e)}",
                        "检查路由配置和页面组件"
                    )
                    
            if working_pages < len(pages_to_test):
                await self.log_issue(
                    "functionality", "high",
                    "页面路由问题",
                    f"只有 {working_pages}/{len(pages_to_test)} 个页面可以正常访问",
                    "检查Vue Router配置和组件导入"
                )
                
            # 测试登录功能
            try:
                await self.page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
                await self.page.wait_for_timeout(2000)
                
                demo_button = await self.page.query_selector('.demo-login, .demo-btn, button:has-text("演示登录")')
                if demo_button:
                    await demo_button.click()
                    await self.page.wait_for_timeout(2000)
                    
                    # 检查是否成功跳转
                    current_url = self.page.url
                    if 'login' not in current_url:
                        print("✅ 演示登录功能正常")
                    else:
                        await self.log_issue(
                            "functionality", "medium",
                            "登录后未跳转",
                            "演示登录后仍停留在登录页面",
                            "检查登录成功后的路由跳转逻辑"
                        )
                else:
                    await self.log_issue(
                        "functionality", "medium",
                        "演示登录按钮缺失",
                        "未找到演示登录按钮",
                        "确认登录页面UI组件完整性"
                    )
                    
            except Exception as e:
                await self.log_issue(
                    "functionality", "high",
                    "登录功能异常",
                    f"登录功能测试失败: {str(e)}",
                    "检查登录页面和认证逻辑"
                )
                
        except Exception as e:
            await self.log_issue(
                "functionality", "critical",
                "功能分析失败",
                f"功能分析过程中出现严重错误: {str(e)}",
                "检查应用基础架构"
            )
            
    async def generate_comprehensive_report(self):
        """生成综合分析报告"""
        report = {
            "analysis_summary": {
                "total_issues": len(self.issues),
                "critical_issues": len([i for i in self.issues if i["severity"] == "critical"]),
                "high_issues": len([i for i in self.issues if i["severity"] == "high"]),
                "medium_issues": len([i for i in self.issues if i["severity"] == "medium"]),
                "low_issues": len([i for i in self.issues if i["severity"] == "low"]),
                "categories": {
                    "performance": len([i for i in self.issues if i["category"] == "performance"]),
                    "functionality": len([i for i in self.issues if i["category"] == "functionality"]),
                    "ui": len([i for i in self.issues if i["category"] == "ui"]),
                    "api": len([i for i in self.issues if i["category"] == "api"]),
                    "websocket": len([i for i in self.issues if i["category"] == "websocket"])
                },
                "timestamp": datetime.now().isoformat()
            },
            "issues": self.issues,
            "performance_metrics": self.performance_metrics,
            "recommendations": [
                "优先解决WebSocket连接问题，这影响实时数据更新",
                "优化页面加载性能，减少初始化时间",
                "修复页面路由问题，确保所有页面可访问",
                "改进错误处理机制，提供更好的用户反馈",
                "添加加载状态指示器，改善用户体验"
            ]
        }
        
        # 保存报告
        filename = f"platform_issue_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 打印总结
        print("\n" + "="*80)
        print("🔍 量化投资平台问题分析报告")
        print("="*80)
        print(f"📊 问题总数: {report['analysis_summary']['total_issues']}")
        print(f"🚨 严重问题: {report['analysis_summary']['critical_issues']}")
        print(f"⚠️ 高优先级: {report['analysis_summary']['high_issues']}")
        print(f"⚡ 中优先级: {report['analysis_summary']['medium_issues']}")
        print(f"💡 低优先级: {report['analysis_summary']['low_issues']}")
        print("\n📋 问题分类:")
        for category, count in report['analysis_summary']['categories'].items():
            if count > 0:
                print(f"  {category}: {count} 个问题")
        print(f"\n📄 详细报告已保存: {filename}")
        print("="*80)
        
        return report

async def main():
    """主分析函数"""
    analyzer = PlatformIssueAnalyzer()
    
    try:
        await analyzer.setup()
        
        # 执行各项分析
        await analyzer.analyze_network_issues()
        await analyzer.analyze_performance_issues()
        await analyzer.analyze_ui_issues()
        await analyzer.analyze_functionality_issues()
        
        # 生成综合报告
        report = await analyzer.generate_comprehensive_report()
        
    except Exception as e:
        print(f"💥 分析过程中发生严重错误: {e}")
    finally:
        await analyzer.teardown()

if __name__ == "__main__":
    asyncio.run(main())
