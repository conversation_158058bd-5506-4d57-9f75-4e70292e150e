#!/usr/bin/env python3
"""
API连接测试
验证前端是否能正确连接到后端API
"""

import asyncio
import json
from datetime import datetime
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class APIConnectionTest:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'api_tests': [],
            'frontend_tests': [],
            'connection_status': {}
        }
        
    async def setup(self):
        """初始化测试环境"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await context.new_page()
        
        # 监听网络请求
        self.page.on('request', self.handle_request)
        self.page.on('response', self.handle_response)
        
        logger.info("🔗 API连接测试环境初始化完成")

    async def handle_request(self, request):
        """处理请求"""
        if 'api/v1' in request.url:
            logger.info(f"📤 API请求: {request.method} {request.url}")

    async def handle_response(self, response):
        """处理响应"""
        if 'api/v1' in response.url:
            status_icon = "✅" if response.status < 400 else "❌"
            logger.info(f"📥 API响应: {status_icon} {response.status} {response.url}")
            
            self.test_results['api_tests'].append({
                'url': response.url,
                'method': response.request.method,
                'status': response.status,
                'success': response.status < 400,
                'timestamp': datetime.now().isoformat()
            })

    async def test_market_page_data_loading(self):
        """测试市场页面数据加载"""
        logger.info("📊 测试市场页面数据加载...")
        
        await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
        
        # 等待数据加载
        await asyncio.sleep(5)
        
        # 检查是否有数据表格
        tables = await self.page.query_selector_all('table, .el-table, .data-table')
        table_count = len(tables)
        
        # 检查是否有图表
        charts = await self.page.query_selector_all('canvas, .chart-container, [id*="chart"]')
        chart_count = len(charts)
        
        # 检查页面内容
        page_text = await self.page.text_content('body')
        has_stock_data = any(symbol in page_text for symbol in ['000001', '000002', '600000'])
        
        self.test_results['frontend_tests'].append({
            'page': 'market',
            'tables_found': table_count,
            'charts_found': chart_count,
            'has_stock_data': has_stock_data,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"📊 市场页面检查结果: 表格={table_count}, 图表={chart_count}, 股票数据={has_stock_data}")

    async def test_strategy_page_functionality(self):
        """测试策略页面功能"""
        logger.info("🧠 测试策略页面功能...")
        
        await self.page.goto('http://localhost:5173/strategy', wait_until='networkidle')
        await asyncio.sleep(3)
        
        # 检查策略卡片
        strategy_cards = await self.page.query_selector_all('.strategy-card, .el-card')
        card_count = len(strategy_cards)
        
        # 检查创建按钮
        create_buttons = await self.page.query_selector_all('button:has-text("创建"), button:has-text("新建")')
        has_create_button = len(create_buttons) > 0
        
        self.test_results['frontend_tests'].append({
            'page': 'strategy',
            'strategy_cards': card_count,
            'has_create_button': has_create_button,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"🧠 策略页面检查结果: 策略卡片={card_count}, 创建按钮={has_create_button}")

    async def test_trading_page_access(self):
        """测试交易页面访问"""
        logger.info("💰 测试交易页面访问...")
        
        try:
            await self.page.goto('http://localhost:5173/trading', wait_until='networkidle', timeout=10000)
            
            # 检查页面是否正常加载
            title = await self.page.text_content('h1, .page-title, .title')
            
            # 检查交易表单
            trading_forms = await self.page.query_selector_all('form, .trading-form, .order-form')
            form_count = len(trading_forms)
            
            # 检查买卖按钮
            buy_buttons = await self.page.query_selector_all('button:has-text("买入"), button:has-text("买")')
            sell_buttons = await self.page.query_selector_all('button:has-text("卖出"), button:has-text("卖")')
            
            self.test_results['frontend_tests'].append({
                'page': 'trading',
                'page_loaded': True,
                'title': title,
                'trading_forms': form_count,
                'buy_buttons': len(buy_buttons),
                'sell_buttons': len(sell_buttons),
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"💰 交易页面检查结果: 表单={form_count}, 买入按钮={len(buy_buttons)}, 卖出按钮={len(sell_buttons)}")
            
        except Exception as e:
            self.test_results['frontend_tests'].append({
                'page': 'trading',
                'page_loaded': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"💰 交易页面访问失败: {e}")

    async def analyze_connection_status(self):
        """分析连接状态"""
        logger.info("🔍 分析API连接状态...")
        
        # 统计API请求结果
        total_requests = len(self.test_results['api_tests'])
        successful_requests = sum(1 for test in self.test_results['api_tests'] if test['success'])
        
        # 分析前端功能状态
        market_test = next((test for test in self.test_results['frontend_tests'] if test['page'] == 'market'), None)
        strategy_test = next((test for test in self.test_results['frontend_tests'] if test['page'] == 'strategy'), None)
        trading_test = next((test for test in self.test_results['frontend_tests'] if test['page'] == 'trading'), None)
        
        self.test_results['connection_status'] = {
            'api_connection': {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
                'status': 'good' if successful_requests / total_requests > 0.8 else 'poor' if total_requests > 0 else 'no_requests'
            },
            'frontend_functionality': {
                'market_data_loading': market_test['has_stock_data'] if market_test else False,
                'market_tables': market_test['tables_found'] if market_test else 0,
                'market_charts': market_test['charts_found'] if market_test else 0,
                'strategy_cards': strategy_test['strategy_cards'] if strategy_test else 0,
                'trading_page_accessible': trading_test['page_loaded'] if trading_test else False,
                'trading_forms': trading_test.get('trading_forms', 0) if trading_test else 0
            }
        }

    async def generate_report(self):
        """生成测试报告"""
        await self.analyze_connection_status()
        
        print("\n" + "="*80)
        print("🔗 API连接测试报告")
        print("="*80)
        print(f"测试时间: {self.test_results['timestamp']}")
        
        # API连接状态
        api_status = self.test_results['connection_status']['api_connection']
        print(f"\n📡 API连接状态:")
        print(f"   总请求数: {api_status['total_requests']}")
        print(f"   成功请求: {api_status['successful_requests']}")
        print(f"   成功率: {api_status['success_rate']:.1%}")
        print(f"   连接状态: {api_status['status']}")
        
        # 前端功能状态
        frontend_status = self.test_results['connection_status']['frontend_functionality']
        print(f"\n🖥️ 前端功能状态:")
        print(f"   市场数据加载: {'✅' if frontend_status['market_data_loading'] else '❌'}")
        print(f"   市场数据表格: {frontend_status['market_tables']}个")
        print(f"   市场图表: {frontend_status['market_charts']}个")
        print(f"   策略卡片: {frontend_status['strategy_cards']}个")
        print(f"   交易页面访问: {'✅' if frontend_status['trading_page_accessible'] else '❌'}")
        print(f"   交易表单: {frontend_status['trading_forms']}个")
        
        # 详细API请求记录
        if self.test_results['api_tests']:
            print(f"\n📋 API请求详情:")
            for test in self.test_results['api_tests']:
                status_icon = "✅" if test['success'] else "❌"
                print(f"   {status_icon} {test['method']} {test['url']} - {test['status']}")
        
        # 问题诊断
        print(f"\n🔍 问题诊断:")
        
        if api_status['total_requests'] == 0:
            print("   ⚠️ 前端没有发起任何API请求")
            print("   💡 建议: 检查前端API配置和网络请求代码")
        elif api_status['success_rate'] < 0.5:
            print("   ❌ API请求失败率过高")
            print("   💡 建议: 检查后端服务状态和API端点")
        
        if not frontend_status['market_data_loading']:
            print("   ❌ 市场数据未正确加载")
            print("   💡 建议: 检查数据绑定和组件渲染逻辑")
        
        if frontend_status['market_tables'] == 0:
            print("   ❌ 市场页面缺少数据表格")
            print("   💡 建议: 检查表格组件的条件渲染")
        
        if frontend_status['market_charts'] == 0:
            print("   ❌ 市场页面缺少图表")
            print("   💡 建议: 检查图表组件的初始化和容器引用")
        
        if not frontend_status['trading_page_accessible']:
            print("   ❌ 交易页面无法访问")
            print("   💡 建议: 检查路由配置和页面组件")
        
        if frontend_status['trading_forms'] == 0:
            print("   ❌ 交易页面缺少交易表单")
            print("   💡 建议: 添加买卖交易表单组件")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    test = APIConnectionTest()
    
    try:
        await test.setup()
        
        # 执行测试
        await test.test_market_page_data_loading()
        await test.test_strategy_page_functionality()
        await test.test_trading_page_access()
        
        # 生成报告
        await test.generate_report()
        
        print(f"\n🎉 API连接测试完成！")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
