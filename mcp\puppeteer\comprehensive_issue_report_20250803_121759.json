{"test_summary": {"total_tests": 15, "total_issues": 84, "high_severity": 0, "medium_severity": 44, "low_severity": 40, "test_time": "2025-08-03T12:17:59.673491"}, "issues_by_category": {"代码质量": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.092436", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.094026", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.095528", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:17:44.098050", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.106594", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.107889", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.112176", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.112869", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.113431", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.113894", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194318023:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:17:44.114680", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.118337", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:17:44.118660", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:17:44.118896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.119109", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.119359", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.119573", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.119868", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.120143", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.120385", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.120796", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.122713", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.123079", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.124913", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.125284", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.127224", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.127553", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.127855", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.128090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.128410", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.128712", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.128929", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.129433", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.129678", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:17:44.129854", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:17:44.130259", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:17:44.130500", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.130715", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.131057", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.131414", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.131798", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.132291", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.132784", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.133281", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.133508", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:17:44.133735", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.135962", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.136389", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.136710", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.137130", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.137564", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.138070", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.138497", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:17:44.138969", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.139621", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.140107", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.142612", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.145156", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.147621", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150105", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150375", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150639", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194318023:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:17:44.151150", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.153411", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.153830", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.154146", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.154556", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.154978", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.155625", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.156044", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.156543", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:17:44.156957", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.157604", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.158080", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.160560", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.163026", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.165557", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.168087", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.168567", "url": "http://localhost:5173/risk"}], "API": [{"category": "API", "severity": "中", "title": "API请求失败", "description": "GET http://localhost:8000/api/v1/market/sectors - 状态码: 404", "evidence": null, "timestamp": "2025-08-03T12:17:50.272491", "url": "http://localhost:5173/market"}], "UI/UX": [{"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:17:52.498371", "url": "http://localhost:5173/"}], "可访问性": [{"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:17:53.194531", "url": "http://localhost:5173/"}], "安全": [{"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:17:59.671230", "url": "http://localhost:5173/"}]}, "detailed_issues": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.092436", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.094026", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.095528", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:17:44.098050", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.106594", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.107889", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.112176", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.112869", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.113431", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.113894", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194318023:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:17:44.114680", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.118337", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T12:17:44.118660", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T12:17:44.118896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.119109", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.119359", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.119573", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.119868", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取板块数据失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.120143", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.120385", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.120796", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.122713", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.123079", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket manager not initialized, details: undefined, context: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.124913", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "🚨 未处理的Promise异常: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at http://localhost:5173/src/composables/useWebSocket.ts?t=1754194318023:22:24\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)\n    at flushPostFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2474:28)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2516:5)", "evidence": null, "timestamp": "2025-08-03T12:17:44.125284", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.127224", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.127553", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.127855", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.128090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.128410", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.128712", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T12:17:44.128929", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.129433", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T12:17:44.129678", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T12:17:44.129854", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T12:17:44.130259", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T12:17:44.130500", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.130715", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.131057", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T12:17:44.131414", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.131798", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.132291", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.132784", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.133281", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.133508", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at loadData (http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:465:27)\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue?t=1754194318023:577:7\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)", "evidence": null, "timestamp": "2025-08-03T12:17:44.133735", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.135962", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.136389", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.136710", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.137130", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.137564", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.138070", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.138497", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:17:44.138969", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.139621", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.140107", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.142612", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.145156", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.147621", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150105", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150375", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150639", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket管理器尚未初始化，连接请求被忽略", "evidence": null, "timestamp": "2025-08-03T12:17:44.150896", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "WebSocket连接失败，将使用轮询模式: Error: WebSocket manager not initialized\n    at WebSocketService.connect (http://localhost:5173/src/services/websocket.service.ts?t=1754194318023:128:29)\n    at connectWebSocket (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:600:40)\n    at Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts?t=1754194318023:685:7)\n    at Proxy.wrappedAction (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:1107:18)\n    at store.<computed> (http://localhost:5173/node_modules/.vite/deps/pinia.js?v=dd06b969:785:44)\n    at http://localhost:5173/src/views/Trading/TradingTerminal.vue?t=1754194318023:367:29\n    at http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4948:40\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at hook.__weh.hook.__weh (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:4928:19)", "evidence": null, "timestamp": "2025-08-03T12:17:44.151150", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.153411", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T12:17:44.153830", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.154146", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.154556", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.154978", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.155625", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T12:17:44.156044", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T12:17:44.156543", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T12:17:44.156957", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.157604", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.158080", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.160560", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.163026", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T12:17:44.165557", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.168087", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T12:17:44.168567", "url": "http://localhost:5173/risk"}, {"category": "API", "severity": "中", "title": "API请求失败", "description": "GET http://localhost:8000/api/v1/market/sectors - 状态码: 404", "evidence": null, "timestamp": "2025-08-03T12:17:50.272491", "url": "http://localhost:5173/market"}, {"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T12:17:52.498371", "url": "http://localhost:5173/"}, {"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T12:17:53.194531", "url": "http://localhost:5173/"}, {"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T12:17:59.671230", "url": "http://localhost:5173/"}], "test_results": [{"test": "首页性能测试", "status": "PASS", "details": "加载时间: 1.45秒", "timestamp": "2025-08-03T12:17:19.119763"}, {"test": "仪表盘性能测试", "status": "PASS", "details": "加载时间: 0.85秒", "timestamp": "2025-08-03T12:17:19.968053"}, {"test": "市场数据性能测试", "status": "PASS", "details": "加载时间: 1.20秒", "timestamp": "2025-08-03T12:17:21.170873"}, {"test": "交易终端性能测试", "status": "PASS", "details": "加载时间: 1.25秒", "timestamp": "2025-08-03T12:17:22.416862"}, {"test": "策略中心性能测试", "status": "PASS", "details": "加载时间: 0.69秒", "timestamp": "2025-08-03T12:17:23.104911"}, {"test": "投资组合性能测试", "status": "PASS", "details": "加载时间: 0.68秒", "timestamp": "2025-08-03T12:17:23.790031"}, {"test": "风险管理性能测试", "status": "PASS", "details": "加载时间: 0.66秒", "timestamp": "2025-08-03T12:17:24.445640"}, {"test": "控制台错误检查", "status": "PASS", "details": "发现 40 个错误, 40 个警告", "timestamp": "2025-08-03T12:17:44.169050"}, {"test": "网络请求分析", "status": "PASS", "details": "发现 1 个失败请求", "timestamp": "2025-08-03T12:17:50.272824"}, {"test": "桌面端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:17:51.074095"}, {"test": "平板端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:17:51.775414"}, {"test": "手机端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:17:52.498689"}, {"test": "可访问性检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:17:53.199337"}, {"test": "数据加载检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T12:17:58.947410"}, {"test": "安全头检查", "status": "PASS", "details": "缺少 5 个安全头", "timestamp": "2025-08-03T12:17:59.672778"}], "recommendations": [{"priority": "中", "title": "用户体验改进", "description": "提升界面响应性和用户体验", "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]}]}