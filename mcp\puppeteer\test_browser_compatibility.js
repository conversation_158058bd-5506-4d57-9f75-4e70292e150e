const puppeteer = require('puppeteer');

async function testBrowserCompatibility() {
    console.log('🔍 开始浏览器兼容性测试...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 }
    });
    
    try {
        const page = await browser.newPage();
        
        // 监听控制台错误
        const consoleErrors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
            }
        });
        
        // 监听页面错误
        const pageErrors = [];
        page.on('pageerror', error => {
            pageErrors.push(error.message);
        });
        
        console.log('📱 访问模拟交易页面...');
        await page.goto('http://localhost:5175/trading/simulated', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        // 检查页面标题
        const title = await page.title();
        console.log(`📄 页面标题: ${title}`);
        
        // 检查是否有模拟交易标识
        const simulationBadge = await page.$('.simulation-badge');
        console.log(`🏷️ 模拟交易标识: ${simulationBadge ? '✅ 存在' : '❌ 不存在'}`);
        
        // 检查是否有账户信息
        const accountOverview = await page.$('.account-overview');
        console.log(`💰 账户信息: ${accountOverview ? '✅ 存在' : '❌ 不存在'}`);
        
        // 检查是否有股票搜索框
        const searchInput = await page.$('.search-input input');
        console.log(`🔍 搜索框: ${searchInput ? '✅ 存在' : '❌ 不存在'}`);
        
        // 检查是否有交易表单
        const tradingForm = await page.$('.trading-form');
        console.log(`📋 交易表单: ${tradingForm ? '✅ 存在' : '❌ 不存在'}`);
        
        // 检查是否有底部面板
        const bottomPanel = await page.$('.bottom-panel');
        console.log(`📊 底部面板: ${bottomPanel ? '✅ 存在' : '❌ 不存在'}`);
        
        // 测试搜索功能
        if (searchInput) {
            console.log('🧪 测试搜索功能...');
            await searchInput.type('000001');
            await page.waitForTimeout(1000);
            
            const searchResults = await page.$('.search-dropdown');
            console.log(`🔍 搜索结果: ${searchResults ? '✅ 显示' : '❌ 未显示'}`);
        }
        
        // 截图
        await page.screenshot({ 
            path: 'puppeteer/browser_compatibility_test.png',
            fullPage: true 
        });
        console.log('📸 截图已保存: browser_compatibility_test.png');
        
        // 报告错误
        if (consoleErrors.length > 0) {
            console.log('\n❌ 控制台错误:');
            consoleErrors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        if (pageErrors.length > 0) {
            console.log('\n❌ 页面错误:');
            pageErrors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        // 计算完整度分数
        let score = 0;
        if (simulationBadge) score += 2;
        if (accountOverview) score += 2;
        if (searchInput) score += 2;
        if (tradingForm) score += 2;
        if (bottomPanel) score += 2;
        
        console.log(`\n🎯 界面完整度评分: ${score}/10 (${score * 10}%)`);
        
        if (score >= 8) {
            console.log('🎉 界面状态: 优秀');
        } else if (score >= 6) {
            console.log('⚠️ 界面状态: 良好');
        } else {
            console.log('❌ 界面状态: 需要修复');
        }
        
        return {
            score,
            consoleErrors,
            pageErrors,
            components: {
                simulationBadge: !!simulationBadge,
                accountOverview: !!accountOverview,
                searchInput: !!searchInput,
                tradingForm: !!tradingForm,
                bottomPanel: !!bottomPanel
            }
        };
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return { score: 0, error: error.message };
    } finally {
        await browser.close();
    }
}

// 运行测试
testBrowserCompatibility()
    .then(result => {
        console.log('\n✅ 测试完成');
        if (result.score >= 8) {
            process.exit(0);
        } else {
            process.exit(1);
        }
    })
    .catch(error => {
        console.error('❌ 测试异常:', error);
        process.exit(1);
    });
