"""
策略开发模块API
提供策略编辑器、回测、优化、监控等完整功能
"""
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import uuid
import json
import random

from fastapi import APIRouter, Depends, HTTPException, Query, Body, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.db.models.user import User

router = APIRouter()

# 模拟数据存储
strategies_db = {}
backtest_results_db = {}
strategy_signals_db = {}


@router.post("/strategies/create")
async def create_strategy(
    name: str = Body(..., description="策略名称"),
    description: str = Body("", description="策略描述"),
    code: str = Body(..., description="策略代码"),
    language: str = Body("python", description="编程语言"),
    parameters: Dict[str, Any] = Body({}, description="策略参数"),
    tags: List[str] = Body([], description="标签"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新策略
    
    - **name**: 策略名称
    - **description**: 策略描述
    - **code**: 策略代码
    - **language**: 编程语言(python/javascript)
    - **parameters**: 策略参数配置
    - **tags**: 标签列表
    """
    strategy_id = f"STG{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
    
    strategy = {
        "strategy_id": strategy_id,
        "user_id": current_user.id,
        "name": name,
        "description": description,
        "code": code,
        "language": language,
        "parameters": parameters,
        "tags": tags,
        "status": "draft",  # draft/testing/active/stopped
        "version": 1,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "performance": {
            "total_return": 0,
            "annual_return": 0,
            "sharpe_ratio": 0,
            "max_drawdown": 0,
            "win_rate": 0
        },
        "backtest_count": 0,
        "live_trading": False
    }
    
    strategies_db[strategy_id] = strategy
    
    return {
        "success": True,
        "message": "策略创建成功",
        "data": {
            "strategy_id": strategy_id,
            "name": name,
            "status": "draft"
        }
    }


@router.get("/strategies/template/list")
async def get_strategy_templates():
    """
    获取策略模板列表
    
    提供常用策略模板供用户参考
    """
    templates = [
        {
            "template_id": "T001",
            "name": "双均线策略",
            "description": "基于MA5和MA20的经典交叉策略",
            "category": "趋势跟踪",
            "difficulty": "初级",
            "code_snippet": """
# 双均线策略示例
def initialize(context):
    context.s1 = '000001.SZ'
    context.short_window = 5
    context.long_window = 20

def handle_data(context, data):
    # 计算均线
    short_ma = data.history(context.s1, 'close', context.short_window, '1d').mean()
    long_ma = data.history(context.s1, 'close', context.long_window, '1d').mean()
    
    # 交易信号
    if short_ma > long_ma:
        order_target_percent(context.s1, 1.0)
    else:
        order_target_percent(context.s1, 0.0)
"""
        },
        {
            "template_id": "T002",
            "name": "MACD策略",
            "description": "基于MACD指标的趋势策略",
            "category": "技术指标",
            "difficulty": "中级",
            "code_snippet": """
# MACD策略示例
import talib

def initialize(context):
    context.s1 = '000001.SZ'
    
def handle_data(context, data):
    # 获取历史数据
    prices = data.history(context.s1, 'close', 100, '1d')
    
    # 计算MACD
    macd, signal, hist = talib.MACD(prices)
    
    # 交易信号
    if hist[-1] > 0 and hist[-2] <= 0:
        order_target_percent(context.s1, 1.0)
    elif hist[-1] < 0 and hist[-2] >= 0:
        order_target_percent(context.s1, 0.0)
"""
        },
        {
            "template_id": "T003",
            "name": "网格交易策略",
            "description": "固定价格区间的网格交易",
            "category": "套利策略",
            "difficulty": "中级",
            "code_snippet": """
# 网格交易策略示例
def initialize(context):
    context.s1 = '000001.SZ'
    context.grid_size = 0.02  # 2%网格
    context.grid_count = 10
    context.base_price = None
    
def handle_data(context, data):
    current_price = data.current(context.s1, 'price')
    
    if context.base_price is None:
        context.base_price = current_price
        
    # 计算网格位置
    grid_level = int((current_price - context.base_price) / (context.base_price * context.grid_size))
    
    # 根据网格位置调整仓位
    target_position = 1.0 - (grid_level + context.grid_count/2) / context.grid_count
    target_position = max(0, min(1, target_position))
    
    order_target_percent(context.s1, target_position)
"""
        }
    ]
    
    return {
        "success": True,
        "data": templates,
        "count": len(templates)
    }


@router.post("/strategies/{strategy_id}/backtest")
async def run_strategy_backtest(
    strategy_id: str,
    start_date: str = Body(..., description="开始日期"),
    end_date: str = Body(..., description="结束日期"),
    initial_capital: float = Body(1000000, description="初始资金"),
    benchmark: str = Body("000300.SH", description="基准指数"),
    symbols: List[str] = Body(..., description="交易标的"),
    commission_rate: float = Body(0.0003, description="手续费率"),
    slippage: float = Body(0.001, description="滑点"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    运行策略回测
    
    - **start_date**: 回测开始日期(YYYY-MM-DD)
    - **end_date**: 回测结束日期(YYYY-MM-DD)
    - **initial_capital**: 初始资金
    - **benchmark**: 基准指数代码
    - **symbols**: 交易标的列表
    - **commission_rate**: 手续费率
    - **slippage**: 滑点设置
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此策略")
    
    # 生成回测ID
    backtest_id = f"BT{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
    
    # 模拟回测过程（实际应调用回测引擎）
    # 这里生成模拟数据
    trading_days = 252
    
    # 生成收益曲线
    equity_curve = []
    current_value = initial_capital
    
    for i in range(trading_days):
        # 模拟每日收益
        daily_return = random.uniform(-0.03, 0.04)
        current_value *= (1 + daily_return)
        
        date = datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=i)
        
        equity_curve.append({
            "date": date.strftime("%Y-%m-%d"),
            "value": round(current_value, 2),
            "return": round((current_value / initial_capital - 1) * 100, 2)
        })
    
    # 计算绩效指标
    final_value = current_value
    total_return = (final_value / initial_capital - 1) * 100
    annual_return = total_return / (trading_days / 252)
    
    # 计算每日收益率
    daily_returns = []
    for i in range(1, len(equity_curve)):
        prev_value = equity_curve[i-1]["value"]
        curr_value = equity_curve[i]["value"]
        daily_ret = (curr_value - prev_value) / prev_value
        daily_returns.append(daily_ret)
    
    # 计算关键指标
    import numpy as np
    
    # 夏普比率 (假设无风险利率为3%)
    risk_free_rate = 0.03
    if len(daily_returns) > 0:
        avg_daily_return = np.mean(daily_returns)
        std_daily_return = np.std(daily_returns)
        if std_daily_return > 0:
            sharpe_ratio = round((avg_daily_return * 252 - risk_free_rate) / (std_daily_return * np.sqrt(252)), 2)
        else:
            sharpe_ratio = 0.0
    else:
        sharpe_ratio = 0.0
    
    # 最大回撤
    peak = initial_capital
    max_drawdown_value = 0
    for point in equity_curve:
        value = point["value"]
        if value > peak:
            peak = value
        drawdown = (peak - value) / peak
        if drawdown > max_drawdown_value:
            max_drawdown_value = drawdown
    
    max_drawdown = round(-max_drawdown_value, 4)
    
    # 索提诺比率 (Sortino Ratio)
    downside_returns = [r for r in daily_returns if r < 0]
    if len(downside_returns) > 0:
        downside_deviation = np.std(downside_returns) * np.sqrt(252)
        if downside_deviation > 0:
            sortino_ratio = round((avg_daily_return * 252 - risk_free_rate) / downside_deviation, 2)
        else:
            sortino_ratio = 0.0
    else:
        sortino_ratio = sharpe_ratio  # 没有负收益时，等于夏普比率
    
    # 卡尔马比率 (Calmar Ratio)
    if abs(max_drawdown) > 0:
        calmar_ratio = round(annual_return / abs(max_drawdown * 100), 2)
    else:
        calmar_ratio = 0.0
    
    # 波动率
    volatility = round(std_daily_return * np.sqrt(252) * 100, 2) if len(daily_returns) > 0 else 0.0
    
    # VaR (95%置信水平的风险价值)
    if len(daily_returns) >= 20:
        var_95 = round(np.percentile(daily_returns, 5) * 100, 2)
    else:
        var_95 = round(random.uniform(-3.0, -0.5), 2)
    
    # 胜率和盈亏比（基于交易数据计算）
    win_rate = round(random.uniform(0.45, 0.65), 2)  # 将在交易生成后重新计算
    
    # 生成交易记录并计算实际的胜率和盈亏比
    trades = []
    total_trades = random.randint(50, 200)
    winning_trades = 0
    total_profit = 0
    total_loss = 0
    profit_trades = []
    loss_trades = []
    
    for i in range(total_trades):
        trade_date = datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=random.randint(0, trading_days))
        
        # 生成更真实的PnL分布
        is_win = random.random() < 0.55  # 55%胜率基础
        if is_win:
            pnl = round(random.uniform(100, 2000), 2)
            winning_trades += 1
            total_profit += pnl
            profit_trades.append(pnl)
        else:
            pnl = round(random.uniform(-1500, -50), 2)
            total_loss += abs(pnl)
            loss_trades.append(abs(pnl))
        
        trades.append({
            "date": trade_date.strftime("%Y-%m-%d"),
            "symbol": random.choice(symbols),
            "side": random.choice(["BUY", "SELL"]),
            "price": round(random.uniform(10, 100), 2),
            "quantity": random.randint(1, 10) * 100,
            "pnl": pnl
        })
    
    # 基于实际交易计算指标
    win_rate = round(winning_trades / total_trades, 4) if total_trades > 0 else 0.0
    profit_factor = round(total_profit / total_loss, 2) if total_loss > 0 else 0.0
    avg_win = round(np.mean(profit_trades), 2) if profit_trades else 0.0
    avg_loss = round(-np.mean(loss_trades), 2) if loss_trades else 0.0
    
    # 盈亏比
    profit_loss_ratio = round(avg_win / abs(avg_loss), 2) if avg_loss != 0 else 0.0
    
    # 保存回测结果
    backtest_result = {
        "backtest_id": backtest_id,
        "strategy_id": strategy_id,
        "status": "completed",
        "config": {
            "start_date": start_date,
            "end_date": end_date,
            "initial_capital": initial_capital,
            "benchmark": benchmark,
            "symbols": symbols,
            "commission_rate": commission_rate,
            "slippage": slippage
        },
        "performance": {
            # 基础收益指标
            "total_return": round(total_return, 2),
            "annual_return": round(annual_return, 2),
            "final_value": round(final_value, 2),
            "initial_capital": initial_capital,
            
            # 风险调整收益指标
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "calmar_ratio": calmar_ratio,
            
            # 风险指标
            "max_drawdown": max_drawdown,
            "volatility": volatility,
            "var_95": var_95,
            
            # 交易统计
            "total_trades": len(trades),
            "win_rate": win_rate,
            "winning_trades": winning_trades,
            "losing_trades": total_trades - winning_trades,
            
            # 盈亏分析
            "profit_factor": profit_factor,
            "profit_loss_ratio": profit_loss_ratio,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "total_profit": round(total_profit, 2),
            "total_loss": round(-total_loss, 2),
            
            # 其他统计
            "trading_days": trading_days,
            "avg_trades_per_day": round(len(trades) / trading_days, 2) if trading_days > 0 else 0,
            
            # 基准比较（模拟）
            "benchmark_return": round(random.uniform(5, 15), 2),
            "alpha": round(annual_return - random.uniform(5, 15), 2),
            "beta": round(random.uniform(0.8, 1.2), 2)
        },
        "equity_curve": equity_curve,
        "trades": trades,
        "created_at": datetime.now()
    }
    
    backtest_results_db[backtest_id] = backtest_result
    
    # 更新策略回测次数
    strategy["backtest_count"] += 1
    
    return {
        "success": True,
        "message": "回测完成",
        "data": {
            "backtest_id": backtest_id,
            "performance": backtest_result["performance"]
        }
    }


@router.get("/strategies/{strategy_id}/backtest/history")
async def get_backtest_history(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取策略回测历史
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权查看此策略")
    
    # 获取该策略的所有回测结果
    history = []
    
    for backtest in backtest_results_db.values():
        if backtest["strategy_id"] == strategy_id:
            history.append({
                "backtest_id": backtest["backtest_id"],
                "created_at": backtest["created_at"].isoformat(),
                "config": backtest["config"],
                "performance": backtest["performance"]
            })
    
    # 按时间倒序排列
    history.sort(key=lambda x: x["created_at"], reverse=True)
    
    return {
        "success": True,
        "data": history,
        "count": len(history)
    }


@router.get("/strategies/{strategy_id}/signals")
async def get_strategy_signals(
    strategy_id: str,
    days: int = Query(7, description="最近N天"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取策略信号
    
    - **days**: 获取最近N天的信号
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权查看此策略")
    
    # 生成模拟信号
    signals = []
    
    for i in range(days):
        date = datetime.now() - timedelta(days=i)
        
        # 每天可能有多个信号
        for j in range(random.randint(0, 5)):
            signal = {
                "signal_id": f"SIG{date.strftime('%Y%m%d')}{j:03d}",
                "strategy_id": strategy_id,
                "timestamp": (date + timedelta(hours=random.randint(9, 15))).isoformat(),
                "symbol": random.choice(["000001", "000002", "600036", "600519"]),
                "action": random.choice(["BUY", "SELL", "HOLD"]),
                "strength": random.uniform(0.5, 1.0),  # 信号强度
                "price": round(random.uniform(10, 100), 2),
                "reason": random.choice([
                    "MA金叉",
                    "MACD底背离",
                    "突破阻力位",
                    "RSI超卖",
                    "放量突破"
                ]),
                "executed": random.choice([True, False])
            }
            
            signals.append(signal)
    
    # 按时间倒序
    signals.sort(key=lambda x: x["timestamp"], reverse=True)
    
    return {
        "success": True,
        "data": signals,
        "count": len(signals)
    }


@router.post("/strategies/{strategy_id}/optimize")
async def optimize_strategy(
    strategy_id: str,
    parameters: Dict[str, Dict[str, Any]] = Body(..., description="参数范围"),
    optimization_target: str = Body("sharpe_ratio", description="优化目标"),
    method: str = Body("grid", description="优化方法"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    策略参数优化
    
    - **parameters**: 参数优化范围，如 {"ma_period": {"min": 5, "max": 60, "step": 5}}
    - **optimization_target**: 优化目标(sharpe_ratio/total_return/win_rate)
    - **method**: 优化方法(grid/random/genetic)
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此策略")
    
    # 模拟优化过程
    optimization_id = f"OPT{uuid.uuid4().hex[:8].upper()}"
    
    # 生成优化结果
    results = []
    
    # 网格搜索示例
    if method == "grid":
        # 简化示例：只优化一个参数
        for param_name, param_range in parameters.items():
            min_val = param_range.get("min", 5)
            max_val = param_range.get("max", 60)
            step = param_range.get("step", 5)
            
            current = min_val
            while current <= max_val:
                # 模拟该参数下的表现
                performance = {
                    param_name: current,
                    "sharpe_ratio": round(random.uniform(0.5, 2.5), 2),
                    "total_return": round(random.uniform(-10, 50), 2),
                    "max_drawdown": round(random.uniform(-0.3, -0.05), 2),
                    "win_rate": round(random.uniform(0.4, 0.7), 2)
                }
                
                results.append(performance)
                current += step
    
    # 找出最优参数
    best_result = max(results, key=lambda x: x[optimization_target])
    
    return {
        "success": True,
        "message": "优化完成",
        "data": {
            "optimization_id": optimization_id,
            "method": method,
            "target": optimization_target,
            "best_parameters": best_result,
            "all_results": results[:10],  # 返回前10个结果
            "total_combinations": len(results)
        }
    }


@router.post("/strategies/{strategy_id}/start")
async def start_strategy(
    strategy_id: str,
    capital: float = Body(..., description="分配资金"),
    symbols: List[str] = Body(..., description="交易标的"),
    mode: str = Body("paper", description="运行模式(paper/live)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    启动策略
    
    - **capital**: 分配给策略的资金
    - **symbols**: 允许交易的标的
    - **mode**: 运行模式(paper模拟/live实盘)
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此策略")
    
    if strategy["status"] == "active":
        raise HTTPException(status_code=400, detail="策略已在运行中")
    
    # 更新策略状态
    strategy["status"] = "active"
    strategy["live_trading"] = (mode == "live")
    strategy["start_time"] = datetime.now()
    strategy["config"] = {
        "capital": capital,
        "symbols": symbols,
        "mode": mode
    }
    
    return {
        "success": True,
        "message": f"策略已启动({mode}模式)",
        "data": {
            "strategy_id": strategy_id,
            "status": "active",
            "mode": mode,
            "start_time": strategy["start_time"].isoformat()
        }
    }


@router.post("/strategies/{strategy_id}/stop")
async def stop_strategy(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    停止策略
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此策略")
    
    if strategy["status"] != "active":
        raise HTTPException(status_code=400, detail="策略未在运行中")
    
    # 更新策略状态
    strategy["status"] = "stopped"
    strategy["stop_time"] = datetime.now()
    strategy["live_trading"] = False
    
    return {
        "success": True,
        "message": "策略已停止",
        "data": {
            "strategy_id": strategy_id,
            "status": "stopped",
            "stop_time": strategy["stop_time"].isoformat()
        }
    }


@router.get("/strategies/{strategy_id}/monitor")
async def monitor_strategy(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    监控策略运行状态
    
    返回策略实时运行数据
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权查看此策略")
    
    # 模拟监控数据
    monitor_data = {
        "strategy_id": strategy_id,
        "status": strategy["status"],
        "mode": strategy.get("config", {}).get("mode", "paper"),
        "runtime": {
            "start_time": strategy.get("start_time", datetime.now()).isoformat(),
            "uptime_hours": random.randint(1, 168),
            "last_heartbeat": datetime.now().isoformat()
        },
        "performance": {
            "total_return": round(random.uniform(-5, 15), 2),
            "today_return": round(random.uniform(-2, 3), 2),
            "sharpe_ratio": round(random.uniform(0.5, 2.0), 2),
            "max_drawdown": round(random.uniform(-0.15, -0.02), 2),
            "win_rate": round(random.uniform(0.45, 0.65), 2)
        },
        "positions": {
            "total_positions": random.randint(0, 10),
            "long_positions": random.randint(0, 8),
            "short_positions": random.randint(0, 2),
            "cash_ratio": round(random.uniform(0.2, 0.8), 2)
        },
        "orders": {
            "today_orders": random.randint(0, 50),
            "pending_orders": random.randint(0, 5),
            "filled_orders": random.randint(0, 45),
            "rejected_orders": random.randint(0, 2)
        },
        "risk": {
            "current_exposure": round(random.uniform(0.3, 0.8), 2),
            "var_95": round(random.uniform(0.01, 0.05), 2),
            "leverage": round(random.uniform(0.8, 1.2), 2),
            "risk_score": random.randint(60, 90)
        },
        "alerts": []
    }
    
    # 添加告警信息
    if monitor_data["risk"]["risk_score"] > 80:
        monitor_data["alerts"].append({
            "level": "warning",
            "message": "风险评分较高，请注意控制风险",
            "time": datetime.now().isoformat()
        })
    
    return {
        "success": True,
        "data": monitor_data
    }


@router.get("/strategies/{strategy_id}/code")
async def get_strategy_code(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取策略代码
    
    支持代码高亮和版本历史
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权查看此策略")
    
    return {
        "success": True,
        "data": {
            "strategy_id": strategy_id,
            "name": strategy["name"],
            "language": strategy["language"],
            "code": strategy["code"],
            "version": strategy["version"],
            "parameters": strategy["parameters"],
            "last_modified": strategy["updated_at"].isoformat()
        }
    }


@router.put("/strategies/{strategy_id}/code")
async def update_strategy_code(
    strategy_id: str,
    code: str = Body(..., description="新的策略代码"),
    parameters: Optional[Dict[str, Any]] = Body(None, description="策略参数"),
    commit_message: str = Body("", description="提交说明"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新策略代码
    
    - **code**: 新的策略代码
    - **parameters**: 策略参数（可选）
    - **commit_message**: 版本提交说明
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此策略")
    
    if strategy["status"] == "active":
        raise HTTPException(status_code=400, detail="运行中的策略不能修改代码")
    
    # 保存代码历史（实际应保存到版本控制系统）
    old_version = strategy["version"]
    
    # 更新策略
    strategy["code"] = code
    if parameters is not None:
        strategy["parameters"] = parameters
    strategy["version"] += 1
    strategy["updated_at"] = datetime.now()
    
    return {
        "success": True,
        "message": "策略代码已更新",
        "data": {
            "strategy_id": strategy_id,
            "old_version": old_version,
            "new_version": strategy["version"],
            "commit_message": commit_message or f"Update strategy code to version {strategy['version']}"
        }
    }


@router.get("/strategies/market")
async def get_strategy_market(
    category: Optional[str] = Query(None, description="策略分类"),
    sort_by: str = Query("popularity", description="排序方式"),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100)
):
    """
    策略市场
    
    浏览公开的优秀策略
    
    - **category**: 策略分类(trend/arbitrage/market_making/high_frequency)
    - **sort_by**: 排序方式(popularity/performance/recent)
    """
    # 模拟策略市场数据
    market_strategies = [
        {
            "strategy_id": f"MKT{i:03d}",
            "name": f"优秀策略{i}",
            "author": f"作者{i}",
            "description": "这是一个优秀的量化策略",
            "category": random.choice(["trend", "arbitrage", "market_making"]),
            "tags": random.sample(["高频", "低风险", "稳定收益", "趋势跟踪"], 2),
            "performance": {
                "annual_return": round(random.uniform(10, 50), 2),
                "sharpe_ratio": round(random.uniform(1.0, 3.0), 2),
                "max_drawdown": round(random.uniform(-0.2, -0.05), 2),
                "win_rate": round(random.uniform(0.5, 0.7), 2)
            },
            "subscribers": random.randint(100, 5000),
            "rating": round(random.uniform(4.0, 5.0), 1),
            "price": random.choice([0, 99, 199, 499]),  # 0表示免费
            "created_at": (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat()
        }
        for i in range(50)
    ]
    
    # 筛选
    if category:
        market_strategies = [s for s in market_strategies if s["category"] == category]
    
    # 排序
    if sort_by == "performance":
        market_strategies.sort(key=lambda x: x["performance"]["annual_return"], reverse=True)
    elif sort_by == "recent":
        market_strategies.sort(key=lambda x: x["created_at"], reverse=True)
    else:  # popularity
        market_strategies.sort(key=lambda x: x["subscribers"], reverse=True)
    
    # 分页
    total = len(market_strategies)
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    
    return {
        "success": True,
        "data": market_strategies[start_idx:end_idx],
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total": total,
            "total_pages": (total + page_size - 1) // page_size
        }
    }


# 回测状态数据库（模拟）
backtest_status_db = {}

@router.get("/backtest/{backtest_id}/status")
async def get_backtest_status(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    查询回测状态
    
    - **backtest_id**: 回测任务ID
    
    返回回测状态信息，包括：
    - 当前状态（运行中、已完成、失败）
    - 进度百分比
    - 预计完成时间
    - 错误信息（如果有）
    """
    # 检查回测任务是否存在
    if backtest_id not in backtest_results_db and backtest_id not in backtest_status_db:
        raise HTTPException(status_code=404, detail="回测任务不存在")
    
    # 如果在结果数据库中，说明已完成
    if backtest_id in backtest_results_db:
        backtest_result = backtest_results_db[backtest_id]
        
        # 验证用户权限
        strategy = strategies_db.get(backtest_result["strategy_id"])
        if not strategy or strategy["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权查看此回测任务")
        
        return {
            "success": True,
            "data": {
                "backtest_id": backtest_id,
                "status": "completed",
                "progress": 100,
                "start_time": backtest_result["created_at"].isoformat(),
                "end_time": backtest_result["created_at"].isoformat(),
                "duration": "00:02:30",
                "error_message": None,
                "result_summary": {
                    "total_return": backtest_result["performance"]["total_return"],
                    "annual_return": backtest_result["performance"]["annual_return"],
                    "sharpe_ratio": backtest_result["performance"]["sharpe_ratio"],
                    "max_drawdown": backtest_result["performance"]["max_drawdown"]
                }
            }
        }
    
    # 如果在状态数据库中，返回当前状态
    status_info = backtest_status_db.get(backtest_id)
    if status_info:
        # 验证用户权限
        strategy = strategies_db.get(status_info["strategy_id"])
        if not strategy or strategy["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权查看此回测任务")
        
        return {
            "success": True,
            "data": status_info
        }
    
    raise HTTPException(status_code=404, detail="回测任务不存在")


@router.get("/backtest/status/list")
async def list_backtest_status(
    status: str = Query(None, description="按状态筛选: running/completed/failed"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户的回测状态列表
    
    - **status**: 筛选状态（可选）
    - **page**: 页码
    - **page_size**: 每页数量
    """
    user_backtests = []
    
    # 获取运行中的回测任务
    for backtest_id, status_info in backtest_status_db.items():
        strategy = strategies_db.get(status_info["strategy_id"])
        if strategy and strategy["user_id"] == current_user.id:
            if not status or status_info["status"] == status:
                user_backtests.append({
                    "backtest_id": backtest_id,
                    "strategy_name": strategy["name"],
                    "status": status_info["status"],
                    "progress": status_info["progress"],
                    "start_time": status_info["start_time"],
                    "estimated_end_time": status_info.get("estimated_end_time")
                })
    
    # 获取已完成的回测任务
    for backtest_id, result in backtest_results_db.items():
        strategy = strategies_db.get(result["strategy_id"])
        if strategy and strategy["user_id"] == current_user.id:
            if not status or status == "completed":
                user_backtests.append({
                    "backtest_id": backtest_id,
                    "strategy_name": strategy["name"],
                    "status": "completed",
                    "progress": 100,
                    "start_time": result["created_at"].isoformat(),
                    "end_time": result["created_at"].isoformat(),
                    "performance": result["performance"]
                })
    
    # 排序（按开始时间倒序）
    user_backtests.sort(key=lambda x: x["start_time"], reverse=True)
    
    # 分页
    total = len(user_backtests)
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    
    return {
        "success": True,
        "data": user_backtests[start_idx:end_idx],
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total": total,
            "total_pages": (total + page_size - 1) // page_size
        }
    }


@router.delete("/backtest/{backtest_id}")
async def cancel_backtest(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    取消正在运行的回测任务
    
    - **backtest_id**: 回测任务ID
    """
    # 检查任务是否存在且正在运行
    if backtest_id not in backtest_status_db:
        raise HTTPException(status_code=404, detail="回测任务不存在或已完成")
    
    status_info = backtest_status_db[backtest_id]
    
    # 验证用户权限
    strategy = strategies_db.get(status_info["strategy_id"])
    if not strategy or strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此回测任务")
    
    # 检查任务状态
    if status_info["status"] != "running":
        raise HTTPException(status_code=400, detail="只能取消正在运行的任务")
    
    # 更新状态为已取消
    status_info["status"] = "cancelled"
    status_info["end_time"] = datetime.now().isoformat()
    status_info["error_message"] = "用户取消"
    
    return {
        "success": True,
        "message": "回测任务已取消",
        "data": {
            "backtest_id": backtest_id,
            "status": "cancelled"
        }
    }


@router.get("/health")
async def health_check():
    """
    策略开发模块健康检查
    """
    return {
        "status": "healthy",
        "module": "strategy_development",
        "timestamp": datetime.now().isoformat(),
        "strategies_count": len(strategies_db),
        "active_strategies": len([s for s in strategies_db.values() if s["status"] == "active"]),
        "running_backtests": len([s for s in backtest_status_db.values() if s["status"] == "running"])
    }