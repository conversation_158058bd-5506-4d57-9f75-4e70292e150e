"""
回测功能API实现
提供完整的策略回测功能，包括历史数据获取、策略执行、绩效计算等
"""
import asyncio
import json
import numpy as np
import pandas as pd
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()

# 请求/响应模型
class BacktestRequest(BaseModel):
    name: str
    strategy_id: str = "simple_ma"
    symbol: str
    start_date: str  # YYYY-MM-DD
    end_date: str    # YYYY-MM-DD
    initial_cash: float = 100000
    parameters: Optional[Dict] = None

class BacktestResponse(BaseModel):
    success: bool
    data: Optional[Dict]
    message: str

# 存储回测任务
BACKTEST_TASKS = {}

# 简单的历史数据生成器（模拟真实数据源）
def generate_historical_data(symbol: str, start_date: str, end_date: str, initial_price: float = 100.0):
    """生成模拟历史数据（实际应用中应该从真实数据源获取）"""
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    dates = pd.date_range(start=start, end=end, freq='D')
    
    # 排除周末
    dates = [d for d in dates if d.weekday() < 5]
    
    data = []
    price = initial_price
    
    for date in dates:
        # 模拟价格波动（几何布朗运动）
        daily_return = np.random.normal(0.0005, 0.02)  # 平均日收益率0.05%，波动率2%
        price *= (1 + daily_return)
        
        # 生成OHLC数据
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = price * (1 + np.random.normal(0, 0.005))
        close_price = price
        volume = np.random.randint(100000, 1000000)
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'symbol': symbol,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
    
    return data

# 简单的回测引擎
class SimpleBacktestEngine:
    def __init__(self, initial_cash: float = 100000):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        
    def get_current_value(self, data_point):
        """计算当前组合价值"""
        portfolio_value = self.cash
        for symbol, shares in self.positions.items():
            if shares > 0:
                portfolio_value += shares * data_point['close']
        return portfolio_value
    
    def buy(self, symbol: str, shares: int, price: float, date: str):
        """买入股票"""
        cost = shares * price * 1.0003  # 加入0.03%的交易成本
        if cost <= self.cash:
            self.cash -= cost
            self.positions[symbol] = self.positions.get(symbol, 0) + shares
            self.trades.append({
                'date': date,
                'symbol': symbol,
                'action': 'buy',
                'shares': shares,
                'price': price,
                'amount': cost,
                'commission': shares * price * 0.0003
            })
            return True
        return False
    
    def sell(self, symbol: str, shares: int, price: float, date: str):
        """卖出股票"""
        if self.positions.get(symbol, 0) >= shares:
            proceeds = shares * price * 0.9997  # 扣除0.03%的交易成本
            self.cash += proceeds
            self.positions[symbol] -= shares
            self.trades.append({
                'date': date,
                'symbol': symbol,
                'action': 'sell',
                'shares': shares,
                'price': price,
                'amount': proceeds,
                'commission': shares * price * 0.0003
            })
            return True
        return False
    
    def run_simple_ma_strategy(self, data, short_window=5, long_window=20):
        """运行简单双均线策略"""
        df = pd.DataFrame(data)
        df['short_ma'] = df['close'].rolling(window=short_window).mean()
        df['long_ma'] = df['close'].rolling(window=long_window).mean()
        
        position = 0  # 0: 无持仓, 1: 持仓
        
        for i, row in df.iterrows():
            if i < long_window:  # 等待均线计算完成
                continue
                
            current_value = self.get_current_value(row)
            self.equity_curve.append({
                'date': row['date'],
                'value': round(current_value, 2),
                'return': round((current_value - self.initial_cash) / self.initial_cash * 100, 2),
                'cash': round(self.cash, 2),
                'positions_value': round(current_value - self.cash, 2)
            })
            
            # 交易信号
            if row['short_ma'] > row['long_ma'] and position == 0:
                # 金叉，买入
                shares = int(self.cash / row['close'] / 100) * 100  # 按手买入
                if shares > 0:
                    self.buy(row['symbol'], shares, row['close'], row['date'])
                    position = 1
            elif row['short_ma'] < row['long_ma'] and position == 1:
                # 死叉，卖出
                shares = self.positions.get(row['symbol'], 0)
                if shares > 0:
                    self.sell(row['symbol'], shares, row['close'], row['date'])
                    position = 0
        
        # 计算最终结果
        final_value = self.get_current_value(df.iloc[-1])
        total_return = (final_value - self.initial_cash) / self.initial_cash * 100
        
        return {
            'initial_cash': self.initial_cash,
            'final_value': round(final_value, 2),
            'total_return': round(total_return, 2),
            'total_trades': len(self.trades),
            'trades': self.trades[-10:],  # 返回最后10笔交易
            'equity_curve': self.equity_curve[-30:]  # 返回最后30天数据
        }

def calculate_performance_metrics(results, trading_days):
    """计算绩效指标"""
    equity_curve = results['equity_curve']
    if not equity_curve:
        return {}
    
    returns = [point['return'] for point in equity_curve]
    
    # 计算基本指标
    total_return = results['total_return']
    max_return = max(returns) if returns else 0
    min_return = min(returns) if returns else 0
    
    # 计算最大回撤
    peak = results['initial_cash']
    max_drawdown = 0
    for point in equity_curve:
        if point['value'] > peak:
            peak = point['value']
        drawdown = (peak - point['value']) / peak * 100
        max_drawdown = max(max_drawdown, drawdown)
    
    # 计算年化收益率
    days = trading_days
    annual_return = total_return * (252 / days) if days > 0 else 0
    
    # 计算夏普比率（简化）
    if len(equity_curve) > 1:
        daily_returns = [(equity_curve[i]['value'] - equity_curve[i-1]['value']) / equity_curve[i-1]['value'] 
                        for i in range(1, len(equity_curve))]
        if daily_returns and np.std(daily_returns) > 0:
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0
    else:
        sharpe_ratio = 0
    
    # 计算胜率
    trades = results.get('trades', [])
    if trades:
        winning_trades = sum(1 for t in trades if t['action'] == 'sell' and t['amount'] > 0)
        win_rate = winning_trades / len([t for t in trades if t['action'] == 'sell']) * 100 if trades else 0
    else:
        win_rate = 0
    
    return {
        'total_return': round(total_return, 2),
        'annual_return': round(annual_return, 2),
        'max_return': round(max_return, 2),
        'min_return': round(min_return, 2),
        'max_drawdown': round(max_drawdown, 2),
        'sharpe_ratio': round(sharpe_ratio, 2),
        'total_trades': results['total_trades'],
        'win_rate': round(win_rate, 2),
        'profit_factor': 1.2  # 简化计算
    }

async def run_backtest_async(backtest_id: str, config: BacktestRequest):
    """异步运行回测"""
    try:
        # 更新进度
        BACKTEST_TASKS[backtest_id]['progress'] = 10
        BACKTEST_TASKS[backtest_id]['status'] = 'fetching_data'
        
        # 模拟数据获取延迟
        await asyncio.sleep(1)
        
        # 获取历史数据
        historical_data = generate_historical_data(
            config.symbol, 
            config.start_date, 
            config.end_date
        )
        
        # 更新进度
        BACKTEST_TASKS[backtest_id]['progress'] = 30
        BACKTEST_TASKS[backtest_id]['status'] = 'running_strategy'
        
        # 模拟策略运行延迟
        await asyncio.sleep(2)
        
        # 运行回测引擎
        engine = SimpleBacktestEngine(config.initial_cash)
        
        # 根据策略类型运行不同策略
        if config.strategy_id == "simple_ma":
            params = config.parameters or {}
            results = engine.run_simple_ma_strategy(
                historical_data,
                short_window=params.get('short_window', 5),
                long_window=params.get('long_window', 20)
            )
        else:
            # 默认策略
            results = engine.run_simple_ma_strategy(historical_data)
        
        # 更新进度
        BACKTEST_TASKS[backtest_id]['progress'] = 90
        BACKTEST_TASKS[backtest_id]['status'] = 'calculating_metrics'
        
        # 计算绩效指标
        trading_days = len(historical_data)
        metrics = calculate_performance_metrics(results, trading_days)
        
        # 保存结果
        BACKTEST_TASKS[backtest_id].update({
            'status': 'completed',
            'progress': 100,
            'results': results,
            'metrics': metrics,
            'completed_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        BACKTEST_TASKS[backtest_id].update({
            'status': 'failed',
            'error': str(e),
            'failed_at': datetime.now().isoformat()
        })

# API路由
@router.post("/", response_model=BacktestResponse)
async def create_backtest(config: BacktestRequest):
    """创建并运行回测"""
    try:
        # 生成回测ID
        backtest_id = str(uuid.uuid4())
        
        # 保存任务状态
        BACKTEST_TASKS[backtest_id] = {
            'id': backtest_id,
            'name': config.name,
            'status': 'running',
            'progress': 0,
            'config': config.dict(),
            'created_at': datetime.now().isoformat()
        }
        
        # 异步运行回测
        asyncio.create_task(run_backtest_async(backtest_id, config))
        
        return BacktestResponse(
            success=True,
            data={
                'id': backtest_id,
                'name': config.name,
                'status': 'running',
                'message': '回测已开始运行'
            },
            message='回测创建成功'
        )
    except Exception as e:
        return BacktestResponse(
            success=False,
            data=None,
            message=f'创建回测失败: {str(e)}'
        )

@router.get("/", response_model=BacktestResponse)
async def get_backtests():
    """获取回测列表"""
    backtests = []
    for task_id, task in BACKTEST_TASKS.items():
        backtests.append({
            'id': task['id'],
            'name': task['name'],
            'status': task['status'],
            'progress': task['progress'],
            'created_at': task['created_at'],
            'total_return': task.get('metrics', {}).get('total_return', 0)
        })
    
    return BacktestResponse(
        success=True,
        data=backtests,
        message='获取回测列表成功'
    )

@router.get("/{backtest_id}", response_model=BacktestResponse)
async def get_backtest_detail(backtest_id: str):
    """获取回测详情"""
    if backtest_id not in BACKTEST_TASKS:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    task = BACKTEST_TASKS[backtest_id]
    return BacktestResponse(
        success=True,
        data=task,
        message='获取回测详情成功'
    )

@router.get("/{backtest_id}/results", response_model=BacktestResponse)
async def get_backtest_results(backtest_id: str):
    """获取回测结果"""
    if backtest_id not in BACKTEST_TASKS:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    task = BACKTEST_TASKS[backtest_id]
    if task['status'] != 'completed':
        return BacktestResponse(
            success=False,
            data={'status': task['status'], 'progress': task['progress']},
            message=f'回测尚未完成，当前状态: {task["status"]}'
        )
    
    return BacktestResponse(
        success=True,
        data={
            'results': task['results'],
            'metrics': task['metrics']
        },
        message='获取回测结果成功'
    )

@router.get("/{backtest_id}/progress", response_model=BacktestResponse)
async def get_backtest_progress(backtest_id: str):
    """获取回测进度"""
    if backtest_id not in BACKTEST_TASKS:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    task = BACKTEST_TASKS[backtest_id]
    return BacktestResponse(
        success=True,
        data={
            'status': task['status'],
            'progress': task['progress']
        },
        message='获取回测进度成功'
    )

@router.get("/{backtest_id}/chart", response_model=BacktestResponse)
async def get_backtest_chart_data(backtest_id: str):
    """获取回测图表数据"""
    if backtest_id not in BACKTEST_TASKS:
        raise HTTPException(status_code=404, detail="回测不存在")
    
    task = BACKTEST_TASKS[backtest_id]
    if task['status'] != 'completed':
        return BacktestResponse(
            success=False,
            data=None,
            message='回测尚未完成'
        )
    
    # 准备图表数据
    equity_curve = task['results'].get('equity_curve', [])
    chart_data = {
        'dates': [point['date'] for point in equity_curve],
        'values': [point['value'] for point in equity_curve],
        'returns': [point['return'] for point in equity_curve]
    }
    
    return BacktestResponse(
        success=True,
        data=chart_data,
        message='获取图表数据成功'
    )