#!/usr/bin/env python3
"""
快速API测试脚本
测试修复后的核心API功能
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_api(method, endpoint, data=None, headers=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        return {
            "status_code": response.status_code,
            "success": response.status_code < 400 and response.status_code != 405,
            "response_size": len(response.text),
            "error": f"HTTP {response.status_code}" if response.status_code >= 400 else None
        }
    except Exception as e:
        return {"error": str(e), "success": False}

def run_tests():
    """运行所有测试"""
    print("🚀 开始快速API测试\n")
    print("=" * 50)
    
    # 定义测试用例
    test_cases = [
        # 基础端点
        ("GET", "/", "根路径"),
        ("GET", "/health", "健康检查"),
        
        # 认证相关 (简单版本)
        ("POST", "/api/auth/register", "用户注册", {
            "username": "testuser",
            "email": "<EMAIL>", 
            "password": "123456"
        }),
        ("POST", "/api/auth/login", "用户登录", {
            "username": "testuser",
            "password": "123456"
        }),
        
        # 市场数据
        ("GET", "/api/market/overview", "市场概览"),
        ("GET", "/api/market/stocks", "股票列表"),
        
        # 交易相关
        ("GET", "/api/trading/account", "账户信息"),
        ("GET", "/api/trading/orders", "订单列表"),
        ("GET", "/api/trading/positions", "持仓信息"),
        
        # 策略相关  
        ("GET", "/api/strategy/list", "策略列表"),
        
        # 风控相关
        ("GET", "/api/risk/metrics", "风险指标"),
        
        # WebSocket测试
        ("GET", "/api/ws/test", "WebSocket测试"),
    ]
    
    results = []
    passed = 0
    failed = 0
    
    for method, endpoint, name, *data in test_cases:
        test_data = data[0] if data else None
        result = test_api(method, endpoint, test_data)
        results.append((name, result))
        
        if result.get("success"):
            status = "✅"
            passed += 1
        elif result.get("status_code") == 405:
            status = "❌ 405"
            failed += 1
        elif result.get("status_code") == 401:
            status = "🔐 401"
            passed += 1  # 401是预期的，说明端点存在
        else:
            status = "⚠️"
            failed += 1
        
        print(f"{status} {name}: {result.get('status_code', 'ERROR')} {result.get('error', '')}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试总结:")
    print(f"   总数: {len(test_cases)}")
    print(f"   通过: {passed}")
    print(f"   失败: {failed}")
    print(f"   成功率: {passed/len(test_cases)*100:.1f}%")
    
    # 分析405错误
    errors_405 = [name for name, result in results if result.get("status_code") == 405]
    if errors_405:
        print(f"\n❌ 仍存在405错误的端点 ({len(errors_405)}个):")
        for name in errors_405:
            print(f"   - {name}")
    
    # 检查API文档
    print(f"\n📖 API文档访问: http://127.0.0.1:8000/docs")
    
    return {
        "total": len(test_cases),
        "passed": passed,
        "failed": failed,
        "success_rate": passed/len(test_cases)*100,
        "errors_405": errors_405
    }

if __name__ == "__main__":
    try:
        results = run_tests()
        
        # 保存结果
        with open("quick_api_test_results.json", "w") as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "results": results
            }, f, indent=2)
        
        print(f"\n💾 结果已保存到: quick_api_test_results.json")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")