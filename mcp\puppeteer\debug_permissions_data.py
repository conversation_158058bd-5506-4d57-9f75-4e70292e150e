#!/usr/bin/env python3
"""
调试权限数据传递
"""

import asyncio
from puppeteer import <PERSON><PERSON>erManager

async def debug_permissions():
    manager = BrowserManager()
    try:
        page = await manager.ensure_browser()
        print('🔧 调试权限数据传递...')
        
        # 监听网络请求
        requests = []
        responses = []
        
        def handle_request(request):
            if '/auth/login' in request.url:
                requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'post_data': request.post_data
                })
                print(f'[REQUEST] {request.method} {request.url}')
                if request.post_data:
                    print(f'[REQUEST DATA] {request.post_data}')
        
        def handle_response(response):
            if '/auth/login' in response.url:
                responses.append({
                    'url': response.url,
                    'status': response.status,
                    'headers': dict(response.headers)
                })
                print(f'[RESPONSE] {response.status} {response.url}')
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        await page.goto('http://localhost:5173/login')
        await page.wait_for_timeout(2000)
        
        await page.wait_for_selector('button:has-text("演示登录")')
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        
        await page.wait_for_timeout(3000)
        
        # 获取响应数据
        for response in responses:
            if '/auth/login' in response['url']:
                try:
                    response_data = await page.evaluate(f'''
                        () => {{
                            return fetch('{response["url"]}', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify({{ username: 'admin', password: 'admin123' }})
                            }}).then(r => r.json())
                        }}
                    ''')
                    print(f'[RESPONSE DATA] {response_data}')
                except Exception as e:
                    print(f'[ERROR] 获取响应数据失败: {e}')
        
        # 检查用户store中的数据
        user_data = await page.evaluate('''
            () => {
                try {
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                    return {
                        userInfo: userStore.userInfo,
                        permissions: userStore.permissions,
                        isLoggedIn: userStore.isLoggedIn,
                        token: userStore.token ? 'exists' : 'missing'
                    };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f'👤 用户Store数据: {user_data}')
        
        # 检查localStorage中的数据
        local_storage_data = await page.evaluate('''
            () => {
                try {
                    const userInfo = localStorage.getItem('userInfo');
                    const token = localStorage.getItem('token');
                    return {
                        userInfo: userInfo ? JSON.parse(userInfo) : null,
                        token: token ? 'exists' : 'missing'
                    };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f'💾 LocalStorage数据: {local_storage_data}')
        
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(debug_permissions())
