"""
任务调度器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.job import Job
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from app.tasks.scheduler import (
    TaskScheduler,
    ScheduledTask,
    TaskStatus,
    SchedulerError,
    TaskExecutionError,
    SchedulerConfig,
)


@pytest.mark.unit
@pytest.mark.scheduler
class TestTaskScheduler:
    """任务调度器测试类"""

    @pytest.fixture
    def scheduler_config(self):
        """调度器配置"""
        return SchedulerConfig(
            timezone="Asia/Shanghai",
            max_workers=5,
            job_defaults={
                "coalesce": False,
                "max_instances": 1,
                "misfire_grace_time": 30,
            },
            executors={"default": {"type": "threadpool", "max_workers": 5}},
        )

    @pytest.fixture
    async def task_scheduler(self, scheduler_config):
        """创建任务调度器实例"""
        scheduler = TaskScheduler(scheduler_config)
        yield scheduler
        await scheduler.shutdown()

    @pytest.fixture
    def mock_task_function(self):
        """模拟任务函数"""
        return AsyncMock(return_value="Task completed successfully")

    @pytest.fixture
    def sample_scheduled_task(self, mock_task_function):
        """样本定时任务"""
        return ScheduledTask(
            id="test_task_1",
            name="Test Task",
            description="A test task for unit testing",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 30},
            enabled=True,
            max_instances=1,
            misfire_grace_time=30,
            retry_count=3,
            retry_delay=5,
        )

    async def test_scheduler_initialization(self, task_scheduler, scheduler_config):
        """测试调度器初始化"""
        assert task_scheduler.config == scheduler_config
        assert task_scheduler.scheduler is not None
        assert isinstance(task_scheduler.scheduler, AsyncIOScheduler)
        assert task_scheduler.tasks == {}
        assert task_scheduler.execution_history == []

    async def test_start_scheduler(self, task_scheduler):
        """测试启动调度器"""
        await task_scheduler.start()

        assert task_scheduler.scheduler.running is True
        assert task_scheduler.is_running() is True

    async def test_stop_scheduler(self, task_scheduler):
        """测试停止调度器"""
        await task_scheduler.start()
        await task_scheduler.stop()

        assert task_scheduler.scheduler.running is False
        assert task_scheduler.is_running() is False

    async def test_add_task(self, task_scheduler, sample_scheduled_task):
        """测试添加任务"""
        await task_scheduler.start()

        result = await task_scheduler.add_task(sample_scheduled_task)

        assert result is True
        assert sample_scheduled_task.id in task_scheduler.tasks
        assert task_scheduler.tasks[sample_scheduled_task.id] == sample_scheduled_task

        # 验证任务已添加到调度器中
        job = task_scheduler.scheduler.get_job(sample_scheduled_task.id)
        assert job is not None
        assert job.id == sample_scheduled_task.id

    async def test_add_duplicate_task(self, task_scheduler, sample_scheduled_task):
        """测试添加重复任务"""
        await task_scheduler.start()

        # 添加第一次
        await task_scheduler.add_task(sample_scheduled_task)

        # 添加第二次应该失败
        with pytest.raises(SchedulerError, match="Task with id .* already exists"):
            await task_scheduler.add_task(sample_scheduled_task)

    async def test_remove_task(self, task_scheduler, sample_scheduled_task):
        """测试移除任务"""
        await task_scheduler.start()

        # 先添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 然后移除
        result = await task_scheduler.remove_task(sample_scheduled_task.id)

        assert result is True
        assert sample_scheduled_task.id not in task_scheduler.tasks

        # 验证任务已从调度器中移除
        job = task_scheduler.scheduler.get_job(sample_scheduled_task.id)
        assert job is None

    async def test_remove_nonexistent_task(self, task_scheduler):
        """测试移除不存在的任务"""
        await task_scheduler.start()

        with pytest.raises(SchedulerError, match="Task with id .* not found"):
            await task_scheduler.remove_task("nonexistent_task")

    async def test_update_task(
        self, task_scheduler, sample_scheduled_task, mock_task_function
    ):
        """测试更新任务"""
        await task_scheduler.start()

        # 先添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 更新任务
        updated_task = ScheduledTask(
            id=sample_scheduled_task.id,
            name="Updated Test Task",
            description="Updated description",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 60},  # 修改间隔
            enabled=True,
            max_instances=2,  # 修改最大实例数
            misfire_grace_time=60,
            retry_count=5,
            retry_delay=10,
        )

        result = await task_scheduler.update_task(updated_task)

        assert result is True
        assert (
            task_scheduler.tasks[sample_scheduled_task.id].name == "Updated Test Task"
        )
        assert (
            task_scheduler.tasks[sample_scheduled_task.id].trigger_args["seconds"] == 60
        )

    async def test_enable_task(self, task_scheduler, sample_scheduled_task):
        """测试启用任务"""
        await task_scheduler.start()

        # 添加禁用的任务
        sample_scheduled_task.enabled = False
        await task_scheduler.add_task(sample_scheduled_task)

        # 启用任务
        result = await task_scheduler.enable_task(sample_scheduled_task.id)

        assert result is True
        assert task_scheduler.tasks[sample_scheduled_task.id].enabled is True

        # 验证任务在调度器中已恢复
        job = task_scheduler.scheduler.get_job(sample_scheduled_task.id)
        assert job is not None

    async def test_disable_task(self, task_scheduler, sample_scheduled_task):
        """测试禁用任务"""
        await task_scheduler.start()

        # 添加启用的任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 禁用任务
        result = await task_scheduler.disable_task(sample_scheduled_task.id)

        assert result is True
        assert task_scheduler.tasks[sample_scheduled_task.id].enabled is False

        # 验证任务在调度器中已暂停
        job = task_scheduler.scheduler.get_job(sample_scheduled_task.id)
        assert job is None

    async def test_get_task(self, task_scheduler, sample_scheduled_task):
        """测试获取任务"""
        await task_scheduler.start()

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 获取任务
        task = task_scheduler.get_task(sample_scheduled_task.id)

        assert task is not None
        assert task == sample_scheduled_task

    async def test_get_nonexistent_task(self, task_scheduler):
        """测试获取不存在的任务"""
        task = task_scheduler.get_task("nonexistent_task")
        assert task is None

    async def test_list_tasks(self, task_scheduler, mock_task_function):
        """测试列出所有任务"""
        await task_scheduler.start()

        # 添加多个任务
        task1 = ScheduledTask(
            id="task_1",
            name="Task 1",
            description="First task",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 30},
            enabled=True,
        )

        task2 = ScheduledTask(
            id="task_2",
            name="Task 2",
            description="Second task",
            func=mock_task_function,
            trigger_type="cron",
            trigger_args={"hour": 9, "minute": 0},
            enabled=False,
        )

        await task_scheduler.add_task(task1)
        await task_scheduler.add_task(task2)

        # 列出所有任务
        tasks = task_scheduler.list_tasks()

        assert len(tasks) == 2
        assert task1 in tasks
        assert task2 in tasks

    async def test_list_tasks_with_filter(self, task_scheduler, mock_task_function):
        """测试按条件列出任务"""
        await task_scheduler.start()

        # 添加多个任务
        task1 = ScheduledTask(
            id="task_1",
            name="Task 1",
            description="First task",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 30},
            enabled=True,
        )

        task2 = ScheduledTask(
            id="task_2",
            name="Task 2",
            description="Second task",
            func=mock_task_function,
            trigger_type="cron",
            trigger_args={"hour": 9, "minute": 0},
            enabled=False,
        )

        await task_scheduler.add_task(task1)
        await task_scheduler.add_task(task2)

        # 只列出启用的任务
        enabled_tasks = task_scheduler.list_tasks(enabled_only=True)
        assert len(enabled_tasks) == 1
        assert task1 in enabled_tasks

    async def test_run_task_immediately(self, task_scheduler, sample_scheduled_task):
        """测试立即运行任务"""
        await task_scheduler.start()

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 立即运行任务
        result = await task_scheduler.run_task_now(sample_scheduled_task.id)

        assert result is True
        # 验证任务函数被调用
        sample_scheduled_task.func.assert_called_once()

    async def test_run_nonexistent_task_immediately(self, task_scheduler):
        """测试立即运行不存在的任务"""
        await task_scheduler.start()

        with pytest.raises(SchedulerError, match="Task with id .* not found"):
            await task_scheduler.run_task_now("nonexistent_task")

    async def test_task_execution_with_success(
        self, task_scheduler, sample_scheduled_task
    ):
        """测试任务执行成功"""
        await task_scheduler.start()

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 模拟任务执行
        await task_scheduler._execute_task(sample_scheduled_task)

        # 验证任务函数被调用
        sample_scheduled_task.func.assert_called_once()

        # 验证执行历史记录
        assert len(task_scheduler.execution_history) == 1
        history = task_scheduler.execution_history[0]
        assert history["task_id"] == sample_scheduled_task.id
        assert history["status"] == TaskStatus.SUCCESS

    async def test_task_execution_with_failure(
        self, task_scheduler, sample_scheduled_task
    ):
        """测试任务执行失败"""
        await task_scheduler.start()

        # 设置任务函数抛出异常
        sample_scheduled_task.func.side_effect = Exception("Task failed")

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 模拟任务执行
        await task_scheduler._execute_task(sample_scheduled_task)

        # 验证执行历史记录
        assert len(task_scheduler.execution_history) == 1
        history = task_scheduler.execution_history[0]
        assert history["task_id"] == sample_scheduled_task.id
        assert history["status"] == TaskStatus.FAILED
        assert "Task failed" in history["error"]

    async def test_task_retry_mechanism(self, task_scheduler, sample_scheduled_task):
        """测试任务重试机制"""
        await task_scheduler.start()

        # 设置任务函数前两次失败，第三次成功
        sample_scheduled_task.func.side_effect = [
            Exception("First failure"),
            Exception("Second failure"),
            "Success",
        ]
        sample_scheduled_task.retry_count = 3

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 模拟任务执行
        await task_scheduler._execute_task(sample_scheduled_task)

        # 验证任务函数被调用3次
        assert sample_scheduled_task.func.call_count == 3

    async def test_task_max_instances_limit(
        self, task_scheduler, sample_scheduled_task
    ):
        """测试任务最大实例数限制"""
        await task_scheduler.start()

        # 设置最大实例数为1
        sample_scheduled_task.max_instances = 1

        # 设置任务函数执行时间较长
        async def long_running_task():
            await asyncio.sleep(0.1)
            return "Task completed"

        sample_scheduled_task.func = long_running_task

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 同时启动多个任务实例
        tasks = []
        for _ in range(3):
            task = asyncio.create_task(
                task_scheduler._execute_task(sample_scheduled_task)
            )
            tasks.append(task)

        # 等待所有任务完成
        await asyncio.gather(*tasks)

        # 验证只有一个任务实例真正执行
        running_instances = task_scheduler.get_running_instances(
            sample_scheduled_task.id
        )
        assert len(running_instances) <= 1

    async def test_cron_trigger_creation(self, task_scheduler, mock_task_function):
        """测试Cron触发器创建"""
        await task_scheduler.start()

        # 创建带Cron触发器的任务
        cron_task = ScheduledTask(
            id="cron_task",
            name="Cron Task",
            description="Task with cron trigger",
            func=mock_task_function,
            trigger_type="cron",
            trigger_args={"hour": 9, "minute": 0, "second": 0},
            enabled=True,
        )

        await task_scheduler.add_task(cron_task)

        # 验证任务已添加
        job = task_scheduler.scheduler.get_job(cron_task.id)
        assert job is not None
        assert isinstance(job.trigger, CronTrigger)

    async def test_interval_trigger_creation(self, task_scheduler, mock_task_function):
        """测试间隔触发器创建"""
        await task_scheduler.start()

        # 创建带间隔触发器的任务
        interval_task = ScheduledTask(
            id="interval_task",
            name="Interval Task",
            description="Task with interval trigger",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 30},
            enabled=True,
        )

        await task_scheduler.add_task(interval_task)

        # 验证任务已添加
        job = task_scheduler.scheduler.get_job(interval_task.id)
        assert job is not None
        assert isinstance(job.trigger, IntervalTrigger)

    async def test_invalid_trigger_type(self, task_scheduler, mock_task_function):
        """测试无效触发器类型"""
        await task_scheduler.start()

        # 创建带无效触发器的任务
        invalid_task = ScheduledTask(
            id="invalid_task",
            name="Invalid Task",
            description="Task with invalid trigger",
            func=mock_task_function,
            trigger_type="invalid_type",
            trigger_args={},
            enabled=True,
        )

        with pytest.raises(SchedulerError, match="Unsupported trigger type"):
            await task_scheduler.add_task(invalid_task)

    async def test_get_task_statistics(self, task_scheduler, sample_scheduled_task):
        """测试获取任务统计信息"""
        await task_scheduler.start()

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 模拟多次执行
        for i in range(5):
            if i < 3:
                # 前3次成功
                await task_scheduler._record_execution(
                    sample_scheduled_task.id,
                    TaskStatus.SUCCESS,
                    datetime.now(),
                    datetime.now() + timedelta(seconds=1),
                    None,
                )
            else:
                # 后2次失败
                await task_scheduler._record_execution(
                    sample_scheduled_task.id,
                    TaskStatus.FAILED,
                    datetime.now(),
                    datetime.now() + timedelta(seconds=1),
                    "Task failed",
                )

        # 获取统计信息
        stats = task_scheduler.get_task_statistics(sample_scheduled_task.id)

        assert stats["total_executions"] == 5
        assert stats["successful_executions"] == 3
        assert stats["failed_executions"] == 2
        assert stats["success_rate"] == 0.6

    async def test_get_scheduler_statistics(self, task_scheduler, mock_task_function):
        """测试获取调度器统计信息"""
        await task_scheduler.start()

        # 添加多个任务
        for i in range(3):
            task = ScheduledTask(
                id=f"task_{i}",
                name=f"Task {i}",
                description=f"Task {i} description",
                func=mock_task_function,
                trigger_type="interval",
                trigger_args={"seconds": 30},
                enabled=i < 2,  # 前2个启用，最后1个禁用
            )
            await task_scheduler.add_task(task)

        # 获取调度器统计信息
        stats = task_scheduler.get_scheduler_statistics()

        assert stats["total_tasks"] == 3
        assert stats["enabled_tasks"] == 2
        assert stats["disabled_tasks"] == 1
        assert stats["running_tasks"] == 0  # 没有正在运行的任务
        assert stats["scheduler_uptime"] > 0

    async def test_clear_execution_history(self, task_scheduler, sample_scheduled_task):
        """测试清除执行历史"""
        await task_scheduler.start()

        # 添加任务并执行
        await task_scheduler.add_task(sample_scheduled_task)
        await task_scheduler._execute_task(sample_scheduled_task)

        # 验证历史记录存在
        assert len(task_scheduler.execution_history) == 1

        # 清除历史记录
        task_scheduler.clear_execution_history()

        # 验证历史记录已清除
        assert len(task_scheduler.execution_history) == 0

    async def test_export_tasks_config(self, task_scheduler, mock_task_function):
        """测试导出任务配置"""
        await task_scheduler.start()

        # 添加任务
        task = ScheduledTask(
            id="export_test_task",
            name="Export Test Task",
            description="Task for export testing",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 30},
            enabled=True,
        )
        await task_scheduler.add_task(task)

        # 导出配置
        config = task_scheduler.export_tasks_config()

        assert isinstance(config, dict)
        assert "tasks" in config
        assert len(config["tasks"]) == 1
        assert config["tasks"][0]["id"] == "export_test_task"

    async def test_import_tasks_config(self, task_scheduler, mock_task_function):
        """测试导入任务配置"""
        await task_scheduler.start()

        # 准备配置数据
        config = {
            "tasks": [
                {
                    "id": "imported_task",
                    "name": "Imported Task",
                    "description": "Task imported from config",
                    "trigger_type": "interval",
                    "trigger_args": {"seconds": 60},
                    "enabled": True,
                    "max_instances": 1,
                    "misfire_grace_time": 30,
                    "retry_count": 3,
                    "retry_delay": 5,
                }
            ]
        }

        # 导入配置
        with patch.object(
            task_scheduler, "_get_task_function", return_value=mock_task_function
        ):
            result = await task_scheduler.import_tasks_config(config)

        assert result is True
        assert "imported_task" in task_scheduler.tasks

    async def test_scheduler_error_handling(self, task_scheduler):
        """测试调度器错误处理"""
        # 测试未启动时添加任务
        with pytest.raises(SchedulerError, match="Scheduler is not running"):
            await task_scheduler.add_task(Mock())

    async def test_task_validation(self, task_scheduler, mock_task_function):
        """测试任务验证"""
        await task_scheduler.start()

        # 测试无效任务ID
        invalid_task = ScheduledTask(
            id="",  # 空ID
            name="Invalid Task",
            description="Task with invalid ID",
            func=mock_task_function,
            trigger_type="interval",
            trigger_args={"seconds": 30},
            enabled=True,
        )

        with pytest.raises(SchedulerError, match="Task ID cannot be empty"):
            await task_scheduler.add_task(invalid_task)

    async def test_concurrent_task_operations(self, task_scheduler, mock_task_function):
        """测试并发任务操作"""
        await task_scheduler.start()

        # 并发添加多个任务
        tasks = []
        for i in range(10):
            task = ScheduledTask(
                id=f"concurrent_task_{i}",
                name=f"Concurrent Task {i}",
                description=f"Concurrent task {i}",
                func=mock_task_function,
                trigger_type="interval",
                trigger_args={"seconds": 30},
                enabled=True,
            )
            tasks.append(task)

        # 并发添加任务
        add_tasks = [task_scheduler.add_task(task) for task in tasks]
        results = await asyncio.gather(*add_tasks, return_exceptions=True)

        # 验证所有任务都成功添加
        for result in results:
            assert result is True

        # 验证任务数量
        assert len(task_scheduler.tasks) == 10

    async def test_memory_cleanup(self, task_scheduler, sample_scheduled_task):
        """测试内存清理"""
        await task_scheduler.start()

        # 添加任务并执行多次
        await task_scheduler.add_task(sample_scheduled_task)

        # 模拟大量执行历史
        for i in range(1000):
            await task_scheduler._record_execution(
                sample_scheduled_task.id,
                TaskStatus.SUCCESS,
                datetime.now() - timedelta(hours=i),
                datetime.now() - timedelta(hours=i) + timedelta(seconds=1),
                None,
            )

        # 验证历史记录数量
        assert len(task_scheduler.execution_history) == 1000

        # 清理旧记录
        task_scheduler.cleanup_old_execution_history(max_age_hours=24)

        # 验证旧记录已清理
        assert len(task_scheduler.execution_history) < 1000

    async def test_graceful_shutdown(self, task_scheduler, sample_scheduled_task):
        """测试优雅关闭"""
        await task_scheduler.start()

        # 添加任务
        await task_scheduler.add_task(sample_scheduled_task)

        # 关闭调度器
        await task_scheduler.shutdown()

        # 验证调度器已停止
        assert task_scheduler.scheduler.running is False

        # 验证任务已清理
        assert len(task_scheduler.tasks) == 0
