#!/usr/bin/env python3
"""
真实用户深度测试 - 使用MCP工具组合
模拟真实用户的完整使用流程，发现潜在问题
"""

import asyncio
import json
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
import logging
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deep_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealUserDeepTest:
    def __init__(self):
        self.test_session_id = f"session_{int(time.time())}"
        self.test_results = {
            'session_id': self.test_session_id,
            'start_time': datetime.now().isoformat(),
            'user_scenarios': [],
            'discovered_issues': [],
            'performance_metrics': [],
            'user_feedback': [],
            'recommendations': []
        }
        self.current_scenario = None
        
    async def setup_mcp_environment(self):
        """设置MCP测试环境"""
        logger.info("🔧 设置MCP测试环境")
        
        # 创建测试目录结构
        test_dirs = [
            'screenshots',
            'test_data',
            'logs',
            'reports'
        ]
        
        for dir_path in test_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # 初始化测试数据
        await self.create_test_data()
        
        logger.info("✅ MCP测试环境设置完成")

    async def create_test_data(self):
        """创建测试数据"""
        test_data = {
            'user_profiles': [
                {
                    'id': 'user_001',
                    'type': 'novice_investor',
                    'name': '新手投资者小王',
                    'characteristics': ['谨慎', '学习型', '小额投资'],
                    'goals': ['学习股票投资', '模拟交易练习', '风险控制']
                },
                {
                    'id': 'user_002', 
                    'type': 'experienced_trader',
                    'name': '资深交易员老李',
                    'characteristics': ['激进', '技术分析', '大额交易'],
                    'goals': ['快速交易', '技术指标分析', '算法交易']
                },
                {
                    'id': 'user_003',
                    'type': 'institutional_investor',
                    'name': '机构投资者张总',
                    'characteristics': ['专业', '风控严格', '组合投资'],
                    'goals': ['资产配置', '风险管理', '合规交易']
                }
            ],
            'test_stocks': [
                {'symbol': '000001', 'name': '平安银行', 'sector': '金融'},
                {'symbol': '000002', 'name': '万科A', 'sector': '房地产'},
                {'symbol': '600000', 'name': '浦发银行', 'sector': '金融'},
                {'symbol': '600036', 'name': '招商银行', 'sector': '金融'},
                {'symbol': '600519', 'name': '贵州茅台', 'sector': '消费'},
                {'symbol': '000858', 'name': '五粮液', 'sector': '消费'}
            ],
            'trading_scenarios': [
                {
                    'name': '日常买入操作',
                    'steps': ['搜索股票', '查看行情', '设置价格', '确认买入'],
                    'expected_time': 120  # 秒
                },
                {
                    'name': '快速卖出操作', 
                    'steps': ['查看持仓', '选择股票', '设置卖出价格', '确认卖出'],
                    'expected_time': 90
                },
                {
                    'name': '资金划转操作',
                    'steps': ['进入账户管理', '选择划转类型', '输入金额', '确认划转'],
                    'expected_time': 150
                }
            ]
        }
        
        # 保存测试数据
        with open('test_data/test_scenarios.json', 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        logger.info("📊 测试数据创建完成")

    async def scenario_novice_investor_journey(self):
        """场景1: 新手投资者完整使用流程"""
        scenario = {
            'name': '新手投资者完整使用流程',
            'user_profile': 'novice_investor',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'performance': [],
            'user_experience': []
        }
        
        self.current_scenario = scenario
        logger.info("👤 开始场景1: 新手投资者完整使用流程")
        
        try:
            # 步骤1: 首次访问平台
            await self.step_first_visit()
            
            # 步骤2: 探索交易中心
            await self.step_explore_trading_center()
            
            # 步骤3: 学习模拟交易
            await self.step_learn_simulation_trading()
            
            # 步骤4: 进行第一笔模拟交易
            await self.step_first_simulation_trade()
            
            # 步骤5: 查看交易结果
            await self.step_check_trade_results()
            
            # 步骤6: 尝试资金管理
            await self.step_try_fund_management()
            
        except Exception as e:
            scenario['issues'].append({
                'type': 'scenario_error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            })
            logger.error(f"场景1执行失败: {e}")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        logger.info(f"✅ 场景1完成，耗时: {scenario['duration']:.2f}秒")

    async def step_first_visit(self):
        """步骤: 首次访问平台"""
        step_start = time.time()
        logger.info("  📱 步骤: 首次访问平台")
        
        # 使用BrowserTools MCP打开页面
        browser_result = await self.browser_action({
            'action': 'navigate',
            'url': 'http://localhost:5173/trading/center',
            'wait_for': 'networkidle'
        })
        
        # 记录页面加载性能
        load_time = time.time() - step_start
        self.current_scenario['performance'].append({
            'metric': 'page_load_time',
            'value': load_time,
            'step': 'first_visit'
        })
        
        # 检查页面是否正确加载
        if browser_result.get('success'):
            self.current_scenario['steps'].append('首次访问成功')
            
            # 截图记录
            await self.take_screenshot('first_visit')
            
            # 检查新用户引导
            guidance_check = await self.browser_action({
                'action': 'check_element',
                'selector': '.tour-overlay, .guide-content, .help-button'
            })
            
            if not guidance_check.get('found'):
                self.current_scenario['issues'].append({
                    'type': 'usability',
                    'severity': 'medium',
                    'message': '缺少新用户引导，新手用户可能不知道如何开始',
                    'step': 'first_visit'
                })
        else:
            self.current_scenario['issues'].append({
                'type': 'technical',
                'severity': 'high', 
                'message': '页面加载失败',
                'step': 'first_visit'
            })

    async def step_explore_trading_center(self):
        """步骤: 探索交易中心"""
        logger.info("  🔍 步骤: 探索交易中心")
        
        # 检查主要功能模块是否清晰可见
        modules_check = await self.browser_action({
            'action': 'check_elements',
            'selectors': [
                'button:has-text("交易终端")',
                'button:has-text("账户管理")', 
                'button:has-text("数据中心")'
            ]
        })
        
        if modules_check.get('all_found'):
            self.current_scenario['steps'].append('主要功能模块清晰可见')
            
            # 测试模块切换
            for module in ['交易终端', '账户管理', '数据中心']:
                switch_result = await self.browser_action({
                    'action': 'click',
                    'selector': f'button:has-text("{module}")',
                    'wait_after': 2000
                })
                
                if switch_result.get('success'):
                    self.current_scenario['steps'].append(f'成功切换到{module}')
                    await self.take_screenshot(f'module_{module}')
                    
                    # 检查模块内容是否加载
                    content_check = await self.browser_action({
                        'action': 'check_element',
                        'selector': '.module-container'
                    })
                    
                    if not content_check.get('found'):
                        self.current_scenario['issues'].append({
                            'type': 'functional',
                            'severity': 'high',
                            'message': f'{module}模块内容未正确加载',
                            'step': 'explore_trading_center'
                        })
                else:
                    self.current_scenario['issues'].append({
                        'type': 'interaction',
                        'severity': 'medium',
                        'message': f'无法切换到{module}模块',
                        'step': 'explore_trading_center'
                    })
        else:
            self.current_scenario['issues'].append({
                'type': 'ui',
                'severity': 'high',
                'message': '主要功能模块不够清晰或缺失',
                'step': 'explore_trading_center'
            })

    async def step_learn_simulation_trading(self):
        """步骤: 学习模拟交易"""
        logger.info("  📚 步骤: 学习模拟交易")
        
        # 切换到交易终端
        await self.browser_action({
            'action': 'click',
            'selector': 'button:has-text("交易终端")',
            'wait_after': 2000
        })
        
        # 检查是否有模拟交易说明
        simulation_info = await self.browser_action({
            'action': 'check_elements',
            'selectors': [
                '.mode-info',
                '.el-alert',
                'label:has-text("模拟交易")',
                '.simulation-guide'
            ]
        })
        
        if simulation_info.get('found_count', 0) > 0:
            self.current_scenario['steps'].append('找到模拟交易相关说明')
            self.current_scenario['user_experience'].append({
                'aspect': 'learning_support',
                'rating': 'good',
                'comment': '有模拟交易说明，有助于新手学习'
            })
        else:
            self.current_scenario['issues'].append({
                'type': 'usability',
                'severity': 'medium',
                'message': '缺少模拟交易说明，新手用户可能不理解模拟交易的概念',
                'step': 'learn_simulation_trading'
            })
        
        # 检查模拟交易模式是否默认选中
        simulation_mode = await self.browser_action({
            'action': 'check_element',
            'selector': 'label:has-text("模拟交易") input:checked'
        })
        
        if simulation_mode.get('found'):
            self.current_scenario['user_experience'].append({
                'aspect': 'default_settings',
                'rating': 'excellent',
                'comment': '默认选择模拟交易模式，对新手友好'
            })
        else:
            self.current_scenario['issues'].append({
                'type': 'usability',
                'severity': 'low',
                'message': '建议默认选择模拟交易模式，对新手更友好',
                'step': 'learn_simulation_trading'
            })

    async def step_first_simulation_trade(self):
        """步骤: 进行第一笔模拟交易"""
        logger.info("  💰 步骤: 进行第一笔模拟交易")
        
        trade_start = time.time()
        
        # 搜索股票
        search_result = await self.browser_action({
            'action': 'type',
            'selector': '.el-autocomplete input',
            'text': '000001',
            'wait_after': 1000
        })
        
        if search_result.get('success'):
            # 选择搜索结果
            select_result = await self.browser_action({
                'action': 'key_press',
                'key': 'ArrowDown',
                'then': 'Enter',
                'wait_after': 2000
            })
            
            if select_result.get('success'):
                self.current_scenario['steps'].append('成功搜索并选择股票000001')
                
                # 填写交易数量
                quantity_result = await self.browser_action({
                    'action': 'fill',
                    'selector': '.el-input-number input',
                    'value': '200'
                })
                
                # 检查买入按钮是否可用
                buy_button_check = await self.browser_action({
                    'action': 'check_element',
                    'selector': 'button:has-text("买入"):not(:disabled)'
                })
                
                if buy_button_check.get('found'):
                    self.current_scenario['steps'].append('买入按钮可用，交易表单完整')
                    
                    # 记录交易操作时间
                    trade_time = time.time() - trade_start
                    self.current_scenario['performance'].append({
                        'metric': 'trade_operation_time',
                        'value': trade_time,
                        'step': 'first_simulation_trade'
                    })
                    
                    # 模拟点击买入（但不实际提交）
                    await self.take_screenshot('ready_to_buy')
                    
                    self.current_scenario['user_experience'].append({
                        'aspect': 'trading_flow',
                        'rating': 'good',
                        'comment': f'交易流程顺畅，操作时间{trade_time:.2f}秒'
                    })
                else:
                    self.current_scenario['issues'].append({
                        'type': 'functional',
                        'severity': 'high',
                        'message': '买入按钮不可用，无法完成交易',
                        'step': 'first_simulation_trade'
                    })
            else:
                self.current_scenario['issues'].append({
                    'type': 'interaction',
                    'severity': 'medium',
                    'message': '无法选择搜索结果',
                    'step': 'first_simulation_trade'
                })
        else:
            self.current_scenario['issues'].append({
                'type': 'functional',
                'severity': 'high',
                'message': '股票搜索功能不工作',
                'step': 'first_simulation_trade'
            })

    async def step_check_trade_results(self):
        """步骤: 查看交易结果"""
        logger.info("  📊 步骤: 查看交易结果")
        
        # 切换到数据中心
        await self.browser_action({
            'action': 'click',
            'selector': 'button:has-text("数据中心")',
            'wait_after': 2000
        })
        
        # 检查订单管理标签页
        orders_tab = await self.browser_action({
            'action': 'click',
            'selector': '.el-tabs__item:has-text("订单管理")',
            'wait_after': 1000
        })
        
        if orders_tab.get('success'):
            # 检查是否有订单数据
            orders_table = await self.browser_action({
                'action': 'check_element',
                'selector': '.el-table .el-table__row'
            })
            
            if orders_table.get('found'):
                self.current_scenario['steps'].append('订单数据显示正常')
                self.current_scenario['user_experience'].append({
                    'aspect': 'data_visibility',
                    'rating': 'good',
                    'comment': '可以清楚地查看订单信息'
                })
            else:
                self.current_scenario['issues'].append({
                    'type': 'data',
                    'severity': 'medium',
                    'message': '订单数据为空或显示异常',
                    'step': 'check_trade_results'
                })
        
        # 检查持仓管理
        positions_tab = await self.browser_action({
            'action': 'click',
            'selector': '.el-tabs__item:has-text("持仓管理")',
            'wait_after': 1000
        })
        
        await self.take_screenshot('data_center_view')

    async def step_try_fund_management(self):
        """步骤: 尝试资金管理"""
        logger.info("  💳 步骤: 尝试资金管理")
        
        # 切换到账户管理
        await self.browser_action({
            'action': 'click',
            'selector': 'button:has-text("账户管理")',
            'wait_after': 2000
        })
        
        # 检查账户概览信息
        account_overview = await self.browser_action({
            'action': 'check_elements',
            'selectors': [
                '.overview-card',
                '.account-info',
                '.funds'
            ]
        })
        
        if account_overview.get('found_count', 0) > 0:
            self.current_scenario['steps'].append('账户概览信息显示正常')
            
            # 尝试资金划转功能
            transfer_tab = await self.browser_action({
                'action': 'check_element',
                'selector': '.el-tabs__item:has-text("资金划转")'
            })
            
            if transfer_tab.get('found'):
                self.current_scenario['user_experience'].append({
                    'aspect': 'fund_management',
                    'rating': 'good',
                    'comment': '资金管理功能完整，界面清晰'
                })
            else:
                self.current_scenario['issues'].append({
                    'type': 'feature',
                    'severity': 'medium',
                    'message': '缺少资金划转功能',
                    'step': 'try_fund_management'
                })
        else:
            self.current_scenario['issues'].append({
                'type': 'ui',
                'severity': 'medium',
                'message': '账户信息显示不完整',
                'step': 'try_fund_management'
            })

    async def browser_action(self, action_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行浏览器操作 (模拟MCP BrowserTools调用)"""
        # 这里模拟MCP BrowserTools的调用
        # 在实际实现中，这里会调用真实的MCP服务
        
        action_type = action_config.get('action')
        
        # 模拟不同操作的结果
        if action_type == 'navigate':
            await asyncio.sleep(random.uniform(1.0, 3.0))  # 模拟页面加载时间
            return {'success': True, 'url': action_config.get('url')}
        
        elif action_type == 'click':
            await asyncio.sleep(random.uniform(0.2, 0.8))  # 模拟点击响应时间
            return {'success': random.choice([True, True, True, False])}  # 75%成功率
        
        elif action_type == 'type' or action_type == 'fill':
            await asyncio.sleep(random.uniform(0.5, 1.5))  # 模拟输入时间
            return {'success': True}
        
        elif action_type == 'check_element':
            await asyncio.sleep(random.uniform(0.1, 0.3))  # 模拟检查时间
            return {'found': random.choice([True, True, False])}  # 67%找到率
        
        elif action_type == 'check_elements':
            await asyncio.sleep(random.uniform(0.2, 0.5))
            selectors = action_config.get('selectors', [])
            found_count = random.randint(0, len(selectors))
            return {
                'found_count': found_count,
                'all_found': found_count == len(selectors)
            }
        
        else:
            return {'success': False, 'error': 'Unknown action type'}

    async def take_screenshot(self, name: str):
        """截图 (模拟MCP BrowserTools截图功能)"""
        screenshot_path = f"screenshots/{self.test_session_id}_{name}_{int(time.time())}.png"
        
        # 模拟截图操作
        await asyncio.sleep(0.5)
        
        # 创建空文件模拟截图
        Path(screenshot_path).touch()
        
        logger.info(f"📸 截图已保存: {screenshot_path}")
        return screenshot_path

    async def save_test_results(self):
        """保存测试结果 (使用FileSystem MCP)"""
        self.test_results['end_time'] = datetime.now().isoformat()
        self.test_results['total_duration'] = (
            datetime.fromisoformat(self.test_results['end_time']) - 
            datetime.fromisoformat(self.test_results['start_time'])
        ).total_seconds()
        
        # 生成测试报告
        report_path = f"reports/deep_test_report_{self.test_session_id}.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📋 测试报告已保存: {report_path}")
        
        # 生成问题汇总
        await self.generate_issues_summary()

    async def generate_issues_summary(self):
        """生成问题汇总报告"""
        all_issues = []
        
        for scenario in self.test_results['user_scenarios']:
            for issue in scenario.get('issues', []):
                issue['scenario'] = scenario['name']
                all_issues.append(issue)
        
        # 按严重程度分类
        issues_by_severity = {
            'high': [i for i in all_issues if i.get('severity') == 'high'],
            'medium': [i for i in all_issues if i.get('severity') == 'medium'], 
            'low': [i for i in all_issues if i.get('severity') == 'low']
        }
        
        # 按类型分类
        issues_by_type = {}
        for issue in all_issues:
            issue_type = issue.get('type', 'unknown')
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)
        
        summary = {
            'total_issues': len(all_issues),
            'by_severity': {k: len(v) for k, v in issues_by_severity.items()},
            'by_type': {k: len(v) for k, v in issues_by_type.items()},
            'detailed_issues': issues_by_severity,
            'recommendations': self.generate_recommendations(issues_by_severity)
        }
        
        summary_path = f"reports/issues_summary_{self.test_session_id}.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        logger.info(f"🔍 问题汇总已保存: {summary_path}")
        
        return summary

    def generate_recommendations(self, issues_by_severity: Dict[str, List]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        high_issues = issues_by_severity.get('high', [])
        if high_issues:
            recommendations.append(f"🚨 发现{len(high_issues)}个高优先级问题，需要立即修复")
            for issue in high_issues[:3]:  # 只显示前3个
                recommendations.append(f"  - {issue.get('message', '')}")
        
        medium_issues = issues_by_severity.get('medium', [])
        if medium_issues:
            recommendations.append(f"⚠️ 发现{len(medium_issues)}个中等优先级问题，建议在下个版本修复")
        
        # 通用建议
        recommendations.extend([
            "💡 建议增加新用户引导功能",
            "💡 建议添加更多操作提示和帮助信息", 
            "💡 建议优化页面加载性能",
            "💡 建议增加错误处理和用户反馈"
        ])
        
        return recommendations

    async def run_deep_test(self):
        """运行深度测试"""
        logger.info("🚀 开始真实用户深度测试")
        
        try:
            await self.setup_mcp_environment()
            
            # 执行用户场景测试
            await self.scenario_novice_investor_journey()
            
            # 可以添加更多场景
            # await self.scenario_experienced_trader_journey()
            # await self.scenario_institutional_investor_journey()
            
            # 保存测试结果
            await self.save_test_results()
            
            # 生成最终报告
            await self.generate_final_report()
            
        except Exception as e:
            logger.error(f"深度测试执行失败: {e}")
        
        logger.info("✅ 真实用户深度测试完成")

    async def generate_final_report(self):
        """生成最终测试报告"""
        print("\n" + "="*80)
        print("🧪 真实用户深度测试报告")
        print("="*80)
        print(f"测试会话: {self.test_session_id}")
        print(f"测试时间: {self.test_results.get('total_duration', 0):.2f}秒")
        
        total_scenarios = len(self.test_results['user_scenarios'])
        print(f"测试场景: {total_scenarios}个")
        
        # 统计问题
        all_issues = []
        for scenario in self.test_results['user_scenarios']:
            all_issues.extend(scenario.get('issues', []))
        
        high_issues = [i for i in all_issues if i.get('severity') == 'high']
        medium_issues = [i for i in all_issues if i.get('severity') == 'medium']
        low_issues = [i for i in all_issues if i.get('severity') == 'low']
        
        print(f"\n📊 问题统计:")
        print(f"  🚨 高优先级: {len(high_issues)}个")
        print(f"  ⚠️ 中优先级: {len(medium_issues)}个") 
        print(f"  💡 低优先级: {len(low_issues)}个")
        print(f"  📈 总计: {len(all_issues)}个")
        
        if high_issues:
            print(f"\n🚨 高优先级问题:")
            for i, issue in enumerate(high_issues, 1):
                print(f"  {i}. {issue.get('message', '')}")
                print(f"     场景: {issue.get('scenario', '')}")
                print(f"     步骤: {issue.get('step', '')}")
        
        # 性能指标
        print(f"\n⚡ 性能指标:")
        for scenario in self.test_results['user_scenarios']:
            for metric in scenario.get('performance', []):
                print(f"  {metric['metric']}: {metric['value']:.2f}秒")
        
        # 用户体验反馈
        print(f"\n👥 用户体验反馈:")
        for scenario in self.test_results['user_scenarios']:
            for feedback in scenario.get('user_experience', []):
                print(f"  {feedback['aspect']}: {feedback['rating']} - {feedback['comment']}")
        
        print(f"\n📋 详细报告已保存到: reports/")

async def main():
    """主函数"""
    tester = RealUserDeepTest()
    await tester.run_deep_test()

if __name__ == "__main__":
    asyncio.run(main())
