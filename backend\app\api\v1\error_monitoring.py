"""
错误监控API端点
提供错误统计、趋势分析和监控信息的API接口  
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Query, HTTPException, Depends

from app.core.error_monitoring import get_error_monitor
from app.core.logging_config import get_contextual_logger
from app.core.exceptions import ResourceNotFoundError

router = APIRouter(prefix="/monitoring", tags=["错误监控"])
logger = get_contextual_logger("error_monitoring_api")


@router.get("/errors/statistics", summary="获取错误统计信息")
async def get_error_statistics() -> Dict[str, Any]:
    """
    获取系统错误统计信息
    
    返回:
    - 总错误数量
    - 按严重级别分类的错误统计
    - 按类别分类的错误统计
    - 最常见的错误类型
    - 错误趋势信息
    """
    try:
        error_monitor = get_error_monitor()
        stats = error_monitor.get_error_statistics()
        
        logger.info("Error statistics retrieved", 
                   total_events=stats.get("total_events", 0))
        
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get error statistics", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="获取错误统计信息失败"
        )


@router.get("/errors/trends", summary="获取错误趋势分析")
async def get_error_trends(
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）")
) -> Dict[str, Any]:
    """
    获取指定时间范围内的错误趋势分析
    
    参数:
    - hours: 分析时间范围（1-168小时）
    
    返回:
    - 时间范围内的错误总数
    - 按小时分组的错误数量
    - 按严重级别分组的错误统计
    - 最常见的错误类型
    """
    try:
        error_monitor = get_error_monitor()
        trends = error_monitor.get_error_trends(hours=hours)
        
        logger.info("Error trends retrieved", 
                   hours=hours, 
                   total_events=trends.get("total_events", 0))
        
        return {
            "success": True,
            "data": trends,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get error trends", 
                    hours=hours, 
                    error=str(e), 
                    exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="获取错误趋势失败"
        )


@router.get("/errors/recent", summary="获取最近的错误事件")
async def get_recent_errors(
    minutes: int = Query(60, ge=1, le=1440, description="时间范围（分钟）"),
    limit: int = Query(50, ge=1, le=500, description="最大返回数量")
) -> Dict[str, Any]:
    """
    获取最近的错误事件列表
    
    参数:
    - minutes: 时间范围（1-1440分钟）
    - limit: 最大返回数量（1-500）
    
    返回:
    - 错误事件列表
    - 总数量
    - 时间范围信息
    """
    try:
        error_monitor = get_error_monitor()
        recent_events = error_monitor.get_recent_events(minutes=minutes)
        
        # 限制返回数量
        limited_events = recent_events[:limit]
        
        # 转换为可序列化的格式
        serialized_events = []
        for event in limited_events:
            serialized_events.append({
                "id": event.id,
                "timestamp": event.timestamp.isoformat(),
                "exception_type": event.exception_type,
                "error_code": event.error_code,
                "message": event.message,
                "severity": event.severity.value,
                "category": event.category.value,
                "request_id": event.request_id,
                "user_id": event.user_id,
                "path": event.path,
                "method": event.method,
                "client_ip": event.client_ip,
                "details": event.details
            })
        
        logger.info("Recent errors retrieved", 
                   minutes=minutes, 
                   total_found=len(recent_events),
                   returned=len(limited_events))
        
        return {
            "success": True,
            "data": {
                "events": serialized_events,
                "total_found": len(recent_events),
                "returned": len(limited_events),
                "time_range": {
                    "minutes": minutes,
                    "from": (datetime.utcnow() - timedelta(minutes=minutes)).isoformat(),
                    "to": datetime.utcnow().isoformat()
                }
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get recent errors", 
                    minutes=minutes, 
                    limit=limit,
                    error=str(e), 
                    exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="获取最近错误事件失败"
        )


@router.get("/alerts/rules", summary="获取告警规则列表")
async def get_alert_rules() -> Dict[str, Any]:
    """
    获取当前配置的告警规则列表
    
    返回:
    - 告警规则列表
    - 规则状态信息
    - 最后触发时间
    """
    try:
        error_monitor = get_error_monitor()
        
        # 序列化告警规则
        rules_data = []
        for rule in error_monitor.alert_rules:
            rules_data.append({
                "name": rule.name,
                "description": rule.description,
                "alert_level": rule.alert_level.value,
                "cooldown_minutes": rule.cooldown_minutes,
                "enabled": rule.enabled,
                "last_triggered": rule.last_triggered.isoformat() if rule.last_triggered else None
            })
        
        logger.info("Alert rules retrieved", rule_count=len(rules_data))
        
        return {
            "success": True,
            "data": {
                "rules": rules_data,
                "total_rules": len(rules_data),
                "enabled_rules": len([r for r in error_monitor.alert_rules if r.enabled])
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get alert rules", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="获取告警规则失败"
        )


@router.get("/health", summary="监控系统健康检查")
async def monitoring_health_check() -> Dict[str, Any]:
    """
    检查错误监控系统的健康状态
    
    返回:
    - 监控系统状态
    - 关键指标
    - 最后更新时间
    """
    try:
        error_monitor = get_error_monitor()
        stats = error_monitor.get_error_statistics()
        
        # 计算健康状态
        total_events = stats.get("total_events", 0)
        recent_events = error_monitor.get_recent_events(minutes=5)
        recent_critical = [e for e in recent_events if e.severity.value == "critical"]
        
        # 判断健康状态
        if len(recent_critical) > 0:
            health_status = "critical"
        elif len(recent_events) > 100:  # 5分钟内超过100个错误
            health_status = "warning"
        elif len(recent_events) > 50:   # 5分钟内超过50个错误
            health_status = "degraded"
        else:
            health_status = "healthy"
        
        health_data = {
            "status": health_status,
            "monitoring_active": error_monitor._monitoring_task is not None and not error_monitor._monitoring_task.done(),
            "total_events": total_events,
            "recent_events_5min": len(recent_events),
            "recent_critical_5min": len(recent_critical),
            "alert_rules_count": len(error_monitor.alert_rules),
            "alert_handlers_count": len(error_monitor.alert_handlers),
            "last_updated": stats.get("last_updated", datetime.utcnow().isoformat())
        }
        
        logger.info("Monitoring health check completed", 
                   status=health_status,
                   recent_events=len(recent_events))
        
        return {
            "success": True,
            "data": health_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Monitoring health check failed", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="监控系统健康检查失败"
        )


@router.post("/alerts/test", summary="触发测试告警")
async def trigger_test_alert() -> Dict[str, Any]:
    """
    触发一个测试告警，用于验证告警系统是否正常工作
    
    返回:
    - 测试结果
    - 告警信息
    """
    try:
        from app.core.exceptions import BusinessLogicError
        from app.core.error_monitoring import ErrorEvent, ErrorSeverity, ErrorCategory
        
        error_monitor = get_error_monitor()
        
        # 创建测试错误事件
        test_event = ErrorEvent(
            id=f"test_{datetime.utcnow().isoformat()}",
            timestamp=datetime.utcnow(),
            exception_type="TestException",
            error_code="TEST_ALERT",
            message="这是一个测试告警",
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.SYSTEM,
            request_id="test_request",
            user_id="test_user",
            path="/api/v1/monitoring/alerts/test",
            method="POST",
            client_ip="127.0.0.1",
            details={"test": True, "purpose": "alert_testing"}
        )
        
        # 添加测试事件
        error_monitor.add_error_event(test_event)
        
        logger.warning("Test alert triggered", 
                      event_id=test_event.id,
                      severity=test_event.severity.value)
        
        return {
            "success": True,
            "data": {
                "message": "测试告警已触发",
                "event_id": test_event.id,
                "severity": test_event.severity.value,
                "timestamp": test_event.timestamp.isoformat()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to trigger test alert", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="触发测试告警失败"
        )


@router.get("/dashboard", summary="错误监控仪表板数据")
async def get_monitoring_dashboard() -> Dict[str, Any]:
    """
    获取错误监控仪表板所需的所有数据
    
    返回:
    - 综合的监控数据
    - 图表数据
    - 关键指标
    """
    try:
        error_monitor = get_error_monitor()
        
        # 获取基础统计
        stats = error_monitor.get_error_statistics()
        
        # 获取24小时趋势
        trends_24h = error_monitor.get_error_trends(hours=24)
        
        # 获取最近1小时的事件
        recent_1h = error_monitor.get_recent_events(minutes=60)
        
        # 获取告警规则状态
        alert_rules = []
        for rule in error_monitor.alert_rules:
            alert_rules.append({
                "name": rule.name,
                "enabled": rule.enabled,
                "last_triggered": rule.last_triggered.isoformat() if rule.last_triggered else None,
                "alert_level": rule.alert_level.value
            })
        
        # 计算关键指标
        critical_events_1h = len([e for e in recent_1h if e.severity.value == "critical"])
        high_events_1h = len([e for e in recent_1h if e.severity.value == "high"])
        
        dashboard_data = {
            "overview": {
                "total_events": stats.get("total_events", 0),
                "events_last_hour": len(recent_1h),
                "critical_events_last_hour": critical_events_1h,
                "high_events_last_hour": high_events_1h,
                "most_common_errors": stats.get("most_common_errors", [])[:5]
            },
            "trends": trends_24h,
            "alert_rules": {
                "total": len(alert_rules),
                "enabled": len([r for r in alert_rules if r["enabled"]]),
                "recently_triggered": len([r for r in alert_rules if r["last_triggered"]]),
                "rules": alert_rules
            },
            "health_indicators": {
                "error_rate_trend": trends_24h.get("trend", "stable"),
                "system_stability": "stable" if critical_events_1h == 0 else "degraded"
            }
        }
        
        logger.info("Dashboard data retrieved successfully")
        
        return {
            "success": True,
            "data": dashboard_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to get dashboard data", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="获取仪表板数据失败"
        )