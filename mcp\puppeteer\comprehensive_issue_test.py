#!/usr/bin/env python3
"""
量化投资平台综合问题测试
专门测试可能存在的问题和边界情况
"""

import asyncio
import json
import time
from datetime import datetime
from puppeteer import BrowserManager

class ComprehensiveIssueTest:
    def __init__(self):
        self.manager = BrowserManager()
        self.page = None
        self.issues_found = []
        self.test_results = []
        
    async def setup(self):
        """初始化浏览器"""
        self.page = await self.manager.ensure_browser()
        print("🔍 开始综合问题测试...")
        print("🎯 目标：发现平台潜在问题和改进点")
        
    async def teardown(self):
        """清理资源"""
        if self.manager.browser:
            await self.manager.browser.close()
        print("🔚 问题测试完成")
        
    async def log_issue(self, category, severity, title, description, evidence=None):
        """记录发现的问题"""
        issue = {
            "category": category,
            "severity": severity,  # "高", "中", "低"
            "title": title,
            "description": description,
            "evidence": evidence,
            "timestamp": datetime.now().isoformat(),
            "url": self.page.url
        }
        self.issues_found.append(issue)
        
        severity_icon = "🚨" if severity == "高" else "⚠️" if severity == "中" else "💡"
        print(f"{severity_icon} [{category}] {title}: {description}")
        
    async def log_test_result(self, test_name, status, details=""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {details}")
        
    async def take_screenshot(self, name):
        """截图"""
        filename = f"issue_test_{name}_{datetime.now().strftime('%H%M%S')}.png"
        await self.page.screenshot(path=filename, full_page=True)
        return filename
        
    async def test_page_load_performance(self):
        """测试页面加载性能"""
        print("\n🚀 测试1: 页面加载性能分析")
        
        pages_to_test = [
            ('/', '首页'),
            ('/dashboard', '仪表盘'),
            ('/market', '市场数据'),
            ('/trading', '交易终端'),
            ('/strategy', '策略中心'),
            ('/portfolio', '投资组合'),
            ('/risk', '风险管理')
        ]
        
        for path, name in pages_to_test:
            try:
                start_time = time.time()
                await self.page.goto(f'http://localhost:5173{path}', wait_until='networkidle', timeout=30000)
                load_time = time.time() - start_time
                
                if load_time > 5:
                    await self.log_issue("性能", "高", f"{name}页面加载过慢", 
                                       f"加载时间: {load_time:.2f}秒，超过5秒阈值")
                elif load_time > 3:
                    await self.log_issue("性能", "中", f"{name}页面加载较慢", 
                                       f"加载时间: {load_time:.2f}秒，建议优化")
                
                await self.log_test_result(f"{name}性能测试", "PASS", f"加载时间: {load_time:.2f}秒")
                
            except Exception as e:
                await self.log_issue("功能", "高", f"{name}页面无法访问", str(e))
                await self.log_test_result(f"{name}性能测试", "FAIL", str(e))
                
    async def test_console_errors(self):
        """测试控制台错误"""
        print("\n🐛 测试2: 控制台错误检查")
        
        # 监听控制台消息
        console_errors = []
        console_warnings = []
        
        def handle_console(msg):
            if msg.type == 'error':
                console_errors.append(msg.text)
            elif msg.type == 'warning':
                console_warnings.append(msg.text)
                
        self.page.on('console', handle_console)
        
        # 访问各个页面收集错误
        pages = ['/', '/dashboard', '/market', '/trading', '/strategy', '/portfolio', '/risk']
        
        for path in pages:
            try:
                await self.page.goto(f'http://localhost:5173{path}', wait_until='networkidle')
                await self.page.wait_for_timeout(2000)  # 等待页面完全加载
            except:
                pass
                
        # 分析错误
        if console_errors:
            for error in console_errors:
                await self.log_issue("代码质量", "中", "控制台错误", error)
                
        if console_warnings:
            for warning in console_warnings:
                await self.log_issue("代码质量", "低", "控制台警告", warning)
                
        await self.log_test_result("控制台错误检查", "PASS", 
                                 f"发现 {len(console_errors)} 个错误, {len(console_warnings)} 个警告")
        
    async def test_network_requests(self):
        """测试网络请求问题"""
        print("\n🌐 测试3: 网络请求分析")
        
        failed_requests = []
        slow_requests = []
        
        def handle_response(response):
            if response.status >= 400:
                failed_requests.append({
                    'url': response.url,
                    'status': response.status,
                    'method': response.request.method
                })
            
            # 检查响应时间（这里简化处理）
            if '/api/' in response.url and response.status == 200:
                # 可以添加响应时间检查逻辑
                pass
                
        self.page.on('response', handle_response)
        
        # 访问数据密集型页面
        await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
        await self.page.wait_for_timeout(5000)
        
        # 分析失败的请求
        for req in failed_requests:
            severity = "高" if req['status'] >= 500 else "中"
            await self.log_issue("API", severity, f"API请求失败", 
                               f"{req['method']} {req['url']} - 状态码: {req['status']}")
                               
        await self.log_test_result("网络请求分析", "PASS", 
                                 f"发现 {len(failed_requests)} 个失败请求")
        
    async def test_ui_responsiveness(self):
        """测试UI响应性"""
        print("\n📱 测试4: UI响应性检查")
        
        viewports = [
            (1920, 1080, "桌面端"),
            (768, 1024, "平板端"),
            (375, 667, "手机端")
        ]
        
        for width, height, device in viewports:
            try:
                await self.page.set_viewport_size({"width": width, "height": height})
                await self.page.goto('http://localhost:5173', wait_until='networkidle')
                
                # 检查是否有横向滚动条
                has_horizontal_scroll = await self.page.evaluate('''() => {
                    return document.body.scrollWidth > window.innerWidth;
                }''')
                
                if has_horizontal_scroll:
                    await self.log_issue("UI/UX", "中", f"{device}出现横向滚动", 
                                       f"页面宽度超出视口，影响用户体验")
                    
                # 检查文字是否过小
                if device == "手机端":
                    small_text = await self.page.evaluate('''() => {
                        const elements = document.querySelectorAll('*');
                        let smallTextCount = 0;
                        elements.forEach(el => {
                            const style = window.getComputedStyle(el);
                            const fontSize = parseFloat(style.fontSize);
                            if (fontSize < 14 && el.innerText && el.innerText.trim()) {
                                smallTextCount++;
                            }
                        });
                        return smallTextCount;
                    }''')
                    
                    if small_text > 10:
                        await self.log_issue("UI/UX", "中", "移动端文字过小", 
                                           f"发现 {small_text} 个小于14px的文字元素")
                        
                await self.log_test_result(f"{device}响应性", "PASS", "检查完成")
                
            except Exception as e:
                await self.log_test_result(f"{device}响应性", "FAIL", str(e))
                
    async def test_accessibility_issues(self):
        """测试可访问性问题"""
        print("\n♿ 测试5: 可访问性检查")
        
        await self.page.goto('http://localhost:5173', wait_until='networkidle')
        
        # 检查图片alt属性
        images_without_alt = await self.page.evaluate('''() => {
            const images = document.querySelectorAll('img');
            let count = 0;
            images.forEach(img => {
                if (!img.alt || img.alt.trim() === '') {
                    count++;
                }
            });
            return count;
        }''')
        
        if images_without_alt > 0:
            await self.log_issue("可访问性", "中", "图片缺少alt属性", 
                               f"{images_without_alt} 张图片缺少alt属性")
            
        # 检查表单标签
        unlabeled_inputs = await self.page.evaluate('''() => {
            const inputs = document.querySelectorAll('input, textarea, select');
            let count = 0;
            inputs.forEach(input => {
                const id = input.id;
                const hasLabel = id && document.querySelector(`label[for="${id}"]`);
                const hasAriaLabel = input.getAttribute('aria-label');
                const hasPlaceholder = input.placeholder;
                
                if (!hasLabel && !hasAriaLabel && !hasPlaceholder) {
                    count++;
                }
            });
            return count;
        }''')
        
        if unlabeled_inputs > 0:
            await self.log_issue("可访问性", "中", "表单元素缺少标签", 
                               f"{unlabeled_inputs} 个表单元素缺少适当标签")
            
        # 检查颜色对比度（简化检查）
        low_contrast_elements = await self.page.evaluate('''() => {
            const elements = document.querySelectorAll('*');
            let count = 0;
            elements.forEach(el => {
                const style = window.getComputedStyle(el);
                const color = style.color;
                const backgroundColor = style.backgroundColor;
                
                // 简化的对比度检查
                if (color === 'rgb(128, 128, 128)' || color === '#808080') {
                    count++;
                }
            });
            return count;
        }''')
        
        if low_contrast_elements > 5:
            await self.log_issue("可访问性", "低", "可能存在对比度问题", 
                               f"发现 {low_contrast_elements} 个可能的低对比度元素")
                               
        await self.log_test_result("可访问性检查", "PASS", "检查完成")
        
    async def test_data_loading_issues(self):
        """测试数据加载问题"""
        print("\n📊 测试6: 数据加载问题检查")
        
        await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
        await self.page.wait_for_timeout(5000)
        
        # 检查是否有空的数据容器
        empty_containers = await self.page.evaluate('''() => {
            const containers = document.querySelectorAll('.data-container, .chart-container, table, .list-container');
            let emptyCount = 0;
            containers.forEach(container => {
                if (container.children.length === 0 || 
                    (container.innerText && container.innerText.trim() === '')) {
                    emptyCount++;
                }
            });
            return emptyCount;
        }''')
        
        if empty_containers > 0:
            await self.log_issue("数据", "中", "发现空的数据容器", 
                               f"{empty_containers} 个数据容器可能未正确加载数据")
            
        # 检查是否有加载指示器一直显示
        persistent_loaders = await self.page.evaluate('''() => {
            const loaders = document.querySelectorAll('.loading, .spinner, .skeleton');
            return loaders.length;
        }''')
        
        if persistent_loaders > 0:
            await self.log_issue("UI/UX", "低", "加载指示器可能未消失", 
                               f"发现 {persistent_loaders} 个加载指示器")
                               
        await self.log_test_result("数据加载检查", "PASS", "检查完成")
        
    async def test_security_headers(self):
        """测试安全头信息"""
        print("\n🔒 测试7: 安全头信息检查")
        
        response = await self.page.goto('http://localhost:5173', wait_until='networkidle')
        headers = response.headers
        
        security_headers = [
            'x-frame-options',
            'x-content-type-options',
            'x-xss-protection',
            'strict-transport-security',
            'content-security-policy'
        ]
        
        missing_headers = []
        for header in security_headers:
            if header not in headers:
                missing_headers.append(header)
                
        if missing_headers:
            await self.log_issue("安全", "中", "缺少安全头信息", 
                               f"缺少: {', '.join(missing_headers)}")
                               
        await self.log_test_result("安全头检查", "PASS", 
                                 f"缺少 {len(missing_headers)} 个安全头")
        
    async def generate_issue_report(self):
        """生成问题报告"""
        report = {
            "test_summary": {
                "total_tests": len(self.test_results),
                "total_issues": len(self.issues_found),
                "high_severity": len([i for i in self.issues_found if i['severity'] == '高']),
                "medium_severity": len([i for i in self.issues_found if i['severity'] == '中']),
                "low_severity": len([i for i in self.issues_found if i['severity'] == '低']),
                "test_time": datetime.now().isoformat()
            },
            "issues_by_category": {},
            "detailed_issues": self.issues_found,
            "test_results": self.test_results,
            "recommendations": self.generate_recommendations()
        }
        
        # 按类别分组问题
        for issue in self.issues_found:
            category = issue['category']
            if category not in report['issues_by_category']:
                report['issues_by_category'][category] = []
            report['issues_by_category'][category].append(issue)
            
        # 保存报告
        filename = f"comprehensive_issue_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n📋 问题报告已保存: {filename}")
        self.print_summary(report)
        
        return report
        
    def generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        high_issues = [i for i in self.issues_found if i['severity'] == '高']
        if high_issues:
            recommendations.append({
                "priority": "紧急",
                "title": "修复高优先级问题",
                "description": f"发现 {len(high_issues)} 个高优先级问题需要立即处理",
                "actions": [issue['title'] for issue in high_issues[:3]]
            })
            
        performance_issues = [i for i in self.issues_found if i['category'] == '性能']
        if performance_issues:
            recommendations.append({
                "priority": "高",
                "title": "性能优化",
                "description": "优化页面加载性能和响应速度",
                "actions": ["实施代码分割", "优化图片加载", "减少初始包大小", "添加缓存策略"]
            })
            
        ui_issues = [i for i in self.issues_found if i['category'] == 'UI/UX']
        if ui_issues:
            recommendations.append({
                "priority": "中",
                "title": "用户体验改进",
                "description": "提升界面响应性和用户体验",
                "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]
            })
            
        return recommendations
        
    def print_summary(self, report):
        """打印测试摘要"""
        summary = report['test_summary']
        
        print(f"\n📊 问题测试摘要:")
        print(f"   总测试数: {summary['total_tests']}")
        print(f"   发现问题: {summary['total_issues']}")
        print(f"   高优先级: {summary['high_severity']}")
        print(f"   中优先级: {summary['medium_severity']}")
        print(f"   低优先级: {summary['low_severity']}")
        
        if summary['total_issues'] == 0:
            print("🎉 未发现重大问题，平台运行良好！")
        elif summary['high_severity'] > 0:
            print("⚠️ 发现高优先级问题，建议立即处理")
        else:
            print("✅ 整体状况良好，有一些改进空间")

async def main():
    """主测试函数"""
    tester = ComprehensiveIssueTest()
    
    try:
        await tester.setup()
        
        # 执行所有测试
        await tester.test_page_load_performance()
        await tester.test_console_errors()
        await tester.test_network_requests()
        await tester.test_ui_responsiveness()
        await tester.test_accessibility_issues()
        await tester.test_data_loading_issues()
        await tester.test_security_headers()
        
        # 生成报告
        await tester.generate_issue_report()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        await tester.teardown()

if __name__ == "__main__":
    asyncio.run(main())
