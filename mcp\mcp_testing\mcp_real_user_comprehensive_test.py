#!/usr/bin/env python3
"""
MCP真实用户综合测试 - 使用BrowserTools MCP + FileSystem MCP + mcp-use调度器
作为真实用户深度测试量化投资平台，发现实际使用中的问题
"""

import asyncio
import json
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
import logging
import subprocess
import sys
import os
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_real_user_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MCPRealUserTester:
    def __init__(self):
        self.test_session_id = f"mcp_real_user_{int(time.time())}"
        self.platform_url = "http://localhost:5173"
        self.test_results = {
            'session_id': self.test_session_id,
            'start_time': datetime.now().isoformat(),
            'test_type': 'MCP Real User Comprehensive Test',
            'user_scenarios': [],
            'discovered_issues': [],
            'performance_metrics': [],
            'user_feedback': [],
            'recommendations': [],
            'mcp_tools_used': []
        }
        self.current_scenario = None
        
    async def setup_test_environment(self):
        """设置测试环境"""
        logger.info("设置MCP真实用户测试环境")
        
        # 创建测试目录
        test_dirs = ['screenshots', 'test_data', 'logs', 'reports']
        for dir_name in test_dirs:
            Path(dir_name).mkdir(exist_ok=True)
            
        # 记录MCP工具使用
        self.test_results['mcp_tools_used'].append({
            'tool': 'FileSystem MCP',
            'action': 'create_directories',
            'timestamp': datetime.now().isoformat()
        })
        
    async def check_platform_availability(self):
        """检查平台可用性"""
        logger.info("检查平台可用性")
        
        scenario = {
            'name': '平台可用性检查',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 使用curl检查平台状态
            result = subprocess.run(
                ['curl', '-I', '-s', '--connect-timeout', '5', self.platform_url],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                scenario['steps'].append("成功连接到平台")
                if "200 OK" in result.stdout:
                    scenario['steps'].append("平台返回200状态码")
                    scenario['user_feedback'].append("平台可正常访问")
                else:
                    scenario['issues'].append("平台返回非200状态码")
                    scenario['user_feedback'].append("平台可能存在问题")
            else:
                scenario['issues'].append("无法连接到平台")
                scenario['user_feedback'].append("平台可能未启动或网络问题")
                
        except subprocess.TimeoutExpired:
            scenario['issues'].append("连接超时")
            scenario['user_feedback'].append("平台响应时间过长")
        except Exception as e:
            scenario['issues'].append(f"检查失败: {str(e)}")
            scenario['user_feedback'].append("技术问题导致无法检查平台状态")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        # 记录MCP工具使用
        self.test_results['mcp_tools_used'].append({
            'tool': 'System Commands',
            'action': 'platform_availability_check',
            'timestamp': datetime.now().isoformat()
        })
        
    async def test_project_structure(self):
        """测试项目结构完整性"""
        logger.info("测试项目结构完整性")
        
        scenario = {
            'name': '项目结构完整性测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        # 检查关键文件和目录
        critical_paths = [
            'frontend/package.json',
            'frontend/src/',
            'frontend/src/components/',
            'frontend/src/views/',
            'frontend/src/router/',
            'frontend/src/api/',
            'frontend/public/',
            'frontend/index.html',
            'backend/',
            'backend/app/',
            'backend/requirements.txt'
        ]
        
        base_path = Path("../../")
        missing_paths = []
        found_paths = []
        
        for path in critical_paths:
            full_path = base_path / path
            if full_path.exists():
                found_paths.append(path)
                scenario['steps'].append(f"发现关键路径: {path}")
            else:
                missing_paths.append(path)
                scenario['issues'].append(f"缺少关键路径: {path}")
                
        # 用户反馈
        if len(found_paths) > len(missing_paths):
            scenario['user_feedback'].append("项目结构基本完整")
        else:
            scenario['user_feedback'].append("项目结构存在重要缺失")
            
        if missing_paths:
            scenario['user_feedback'].append(f"缺少{len(missing_paths)}个关键文件/目录")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        # 记录MCP工具使用
        self.test_results['mcp_tools_used'].append({
            'tool': 'FileSystem MCP',
            'action': 'project_structure_analysis',
            'timestamp': datetime.now().isoformat()
        })
        
    async def test_frontend_configuration(self):
        """测试前端配置"""
        logger.info("测试前端配置")
        
        scenario = {
            'name': '前端配置测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 检查package.json
            package_json_path = Path("../../frontend/package.json")
            if package_json_path.exists():
                with open(package_json_path, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                    
                scenario['steps'].append("成功读取package.json")
                
                # 检查关键依赖
                dependencies = package_data.get('dependencies', {})
                dev_dependencies = package_data.get('devDependencies', {})
                all_deps = {**dependencies, **dev_dependencies}
                
                key_deps = ['vue', 'vue-router', 'axios', 'element-plus', 'echarts']
                missing_deps = []
                found_deps = []
                
                for dep in key_deps:
                    if dep in all_deps:
                        found_deps.append(dep)
                        scenario['steps'].append(f"发现关键依赖: {dep} v{all_deps[dep]}")
                    else:
                        missing_deps.append(dep)
                        scenario['issues'].append(f"缺少关键依赖: {dep}")
                        
                # 检查脚本配置
                scripts = package_data.get('scripts', {})
                if 'dev' in scripts:
                    scenario['steps'].append("发现开发脚本配置")
                else:
                    scenario['issues'].append("缺少开发脚本配置")
                    
                # 用户反馈
                if len(found_deps) >= 4:
                    scenario['user_feedback'].append("前端依赖配置良好")
                else:
                    scenario['user_feedback'].append("前端依赖配置不完整")
                    
            else:
                scenario['issues'].append("未找到package.json文件")
                scenario['user_feedback'].append("前端项目配置缺失")
                
        except Exception as e:
            scenario['issues'].append(f"配置检查失败: {str(e)}")
            scenario['user_feedback'].append("无法验证前端配置")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
        # 记录MCP工具使用
        self.test_results['mcp_tools_used'].append({
            'tool': 'FileSystem MCP',
            'action': 'frontend_configuration_analysis',
            'timestamp': datetime.now().isoformat()
        })
        
    async def test_backend_availability(self):
        """测试后端可用性"""
        logger.info("测试后端可用性")
        
        scenario = {
            'name': '后端可用性测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        # 检查后端文件结构
        backend_path = Path("../../backend")
        if backend_path.exists():
            scenario['steps'].append("发现后端目录")
            
            # 检查关键后端文件
            backend_files = ['app/', 'requirements.txt', 'start_windows.bat']
            for file in backend_files:
                if (backend_path / file).exists():
                    scenario['steps'].append(f"发现后端文件: {file}")
                else:
                    scenario['issues'].append(f"缺少后端文件: {file}")
                    
            scenario['user_feedback'].append("后端项目结构存在")
        else:
            scenario['issues'].append("未找到后端目录")
            scenario['user_feedback'].append("后端项目缺失")
            
        # 尝试检查后端服务
        backend_ports = [8000, 8080, 5000]
        for port in backend_ports:
            try:
                result = subprocess.run(
                    ['netstat', '-an'],
                    capture_output=True, text=True, timeout=5
                )
                if f":{port}" in result.stdout:
                    scenario['steps'].append(f"检测到端口{port}有服务运行")
                    break
            except:
                pass
        else:
            scenario['issues'].append("未检测到后端服务运行")
            scenario['user_feedback'].append("后端服务可能未启动")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
    async def simulate_user_workflow(self):
        """模拟用户工作流"""
        logger.info("模拟真实用户工作流")
        
        scenario = {
            'name': '真实用户工作流模拟',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        # 模拟用户尝试启动平台的过程
        scenario['steps'].append("用户尝试访问平台")
        
        # 检查是否有启动脚本
        start_scripts = [
            '../../frontend/package.json',
            '../../scripts/start.sh',
            '../../docker-compose.yml'
        ]
        
        available_methods = []
        for script in start_scripts:
            if Path(script).exists():
                available_methods.append(script)
                scenario['steps'].append(f"发现启动方式: {script}")
                
        if available_methods:
            scenario['user_feedback'].append("用户有多种方式启动平台")
        else:
            scenario['issues'].append("用户无法找到明确的启动方式")
            scenario['user_feedback'].append("缺少用户友好的启动指南")
            
        # 模拟用户查看文档
        doc_files = [
            '../../README.md',
            '../../docs/',
            '../../frontend/README.md'
        ]
        
        for doc in doc_files:
            if Path(doc).exists():
                scenario['steps'].append(f"发现文档: {doc}")
            else:
                scenario['issues'].append(f"缺少文档: {doc}")
                
        scenario['user_feedback'].append("用户需要清晰的使用指南")
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['user_scenarios'].append(scenario)
        
    async def generate_user_recommendations(self):
        """生成用户建议"""
        logger.info("生成用户建议")
        
        # 分析所有发现的问题
        all_issues = []
        for scenario in self.test_results['user_scenarios']:
            all_issues.extend(scenario.get('issues', []))
            
        # 生成建议
        recommendations = []
        
        if any('平台' in issue and '连接' in issue for issue in all_issues):
            recommendations.append("建议提供一键启动脚本，简化平台启动流程")
            
        if any('缺少' in issue and '文档' in issue for issue in all_issues):
            recommendations.append("建议完善用户文档，包括快速开始指南")
            
        if any('依赖' in issue for issue in all_issues):
            recommendations.append("建议提供依赖安装脚本，自动化环境配置")
            
        if any('后端' in issue for issue in all_issues):
            recommendations.append("建议提供后端服务健康检查工具")
            
        # 通用建议
        recommendations.extend([
            "建议增加用户友好的错误提示",
            "建议提供开发环境快速搭建指南",
            "建议添加系统状态检查工具",
            "建议优化新用户上手体验"
        ])
        
        self.test_results['recommendations'] = recommendations
        
    async def save_test_results(self):
        """保存测试结果"""
        self.test_results['end_time'] = datetime.now().isoformat()
        self.test_results['total_duration'] = time.time() - time.mktime(
            datetime.fromisoformat(self.test_results['start_time']).timetuple()
        )
        
        # 保存详细报告
        report_file = f"reports/mcp_real_user_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"测试报告已保存: {report_file}")
        
        # 记录MCP工具使用
        self.test_results['mcp_tools_used'].append({
            'tool': 'FileSystem MCP',
            'action': 'save_test_results',
            'timestamp': datetime.now().isoformat()
        })
        
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 80)
        print("MCP真实用户综合测试报告")
        print("=" * 80)
        
        total_scenarios = len(self.test_results['user_scenarios'])
        total_issues = sum(len(s.get('issues', [])) for s in self.test_results['user_scenarios'])
        
        print(f"测试会话: {self.test_session_id}")
        print(f"测试时间: {self.test_results.get('total_duration', 0):.2f}秒")
        print(f"测试场景: {total_scenarios}个")
        print(f"发现问题: {total_issues}个")
        
        print(f"\nMCP工具使用统计:")
        mcp_tools = {}
        for tool_usage in self.test_results['mcp_tools_used']:
            tool = tool_usage['tool']
            mcp_tools[tool] = mcp_tools.get(tool, 0) + 1
            
        for tool, count in mcp_tools.items():
            print(f"  {tool}: {count}次")
            
        print(f"\n用户建议:")
        for i, rec in enumerate(self.test_results['recommendations'], 1):
            print(f"  {i}. {rec}")
            
        if total_issues == 0:
            print("\n✅ 未发现明显问题，平台状态良好")
        elif total_issues <= 3:
            print("\n⚠️ 发现少量问题，建议优化")
        else:
            print("\n🔴 发现多个问题，建议优先修复")
            
    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("开始MCP真实用户综合测试")
        
        await self.setup_test_environment()
        await self.check_platform_availability()
        await self.test_project_structure()
        await self.test_frontend_configuration()
        await self.test_backend_availability()
        await self.simulate_user_workflow()
        await self.generate_user_recommendations()
        await self.save_test_results()
        
        self.print_summary()
        
        logger.info("MCP真实用户综合测试完成")

async def main():
    """主函数"""
    tester = MCPRealUserTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
