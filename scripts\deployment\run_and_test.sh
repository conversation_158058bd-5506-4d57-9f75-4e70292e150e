#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 量化投资平台集成测试脚本 ===${NC}"
echo "此脚本将启动后端服务并运行集成测试"
echo ""

# 检查Python环境
echo -e "${YELLOW}检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到Python3${NC}"
    exit 1
fi

# 进入backend目录
cd /Users/<USER>/Desktop/quant011/backend

# 安装依赖（如果需要）
echo -e "${YELLOW}检查依赖...${NC}"
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate 2>/dev/null || . venv/Scripts/activate 2>/dev/null

# 安装依赖
pip install -r requirements.txt > /dev/null 2>&1

# 启动后端服务
echo -e "${GREEN}启动后端服务...${NC}"
python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
SERVER_PID=$!

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务是否启动成功
if ! curl -s http://localhost:8000/api/v1/health > /dev/null; then
    echo -e "${RED}服务启动失败！${NC}"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

echo -e "${GREEN}服务启动成功！${NC}"
echo ""

# 运行集成测试
echo -e "${GREEN}运行集成测试...${NC}"
cd app/api/v1
python3 test_integration.py

# 询问是否保持服务运行
echo ""
echo -e "${YELLOW}测试完成。是否保持服务运行？(y/n)${NC}"
read -r response

if [[ "$response" != "y" ]]; then
    echo "停止服务..."
    kill $SERVER_PID 2>/dev/null
    echo -e "${GREEN}服务已停止${NC}"
else
    echo -e "${GREEN}服务仍在运行，PID: $SERVER_PID${NC}"
    echo "使用 'kill $SERVER_PID' 命令停止服务"
fi

echo ""
echo -e "${GREEN}完成！${NC}"