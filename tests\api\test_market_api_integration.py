#!/usr/bin/env python3
"""
市场服务与API集成测试
"""
import asyncio
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_market_api_integration():
    """测试市场服务与API的集成"""
    try:
        # 导入市场服务
        from app.services.market_service import market_service
        print("✓ 成功导入market_service")
        
        # 模拟API调用场景
        
        # 1. 获取股票行情 - 模拟 GET /api/v1/market/quote/{symbol}
        print("\n=== 测试股票行情API ===")
        quote = await market_service.get_quote("600519")
        if quote:
            quote_dict = quote.dict() if hasattr(quote, 'dict') else quote
            print(f"✓ 股票行情: {json.dumps(quote_dict, indent=2, ensure_ascii=False)}")
        
        # 2. 批量获取行情 - 模拟 POST /api/v1/market/quotes
        print("\n=== 测试批量行情API ===")
        quotes = await market_service.get_quotes(["600519", "000858", "000001"])
        if quotes:
            print(f"✓ 批量行情: 获取到 {len(quotes)} 只股票的行情")
            for quote in quotes[:2]:  # 只显示前2个
                print(f"  - {quote.get('symbol')}: {quote.get('name')} ${quote.get('currentPrice')}")
        
        # 3. 获取市场概览 - 模拟 GET /api/v1/market/overview
        print("\n=== 测试市场概览API ===")
        overview = await market_service.get_market_overview()
        if overview:
            print(f"✓ 市场概览: {len(overview.get('marketStats', []))} 个市场")
            for market in overview.get('marketStats', []):
                print(f"  - {market.get('name')}: 上涨 {market.get('upCount')}, 下跌 {market.get('downCount')}")
        
        # 4. 获取指数 - 模拟 GET /api/v1/market/indices
        print("\n=== 测试指数API ===")
        indices = await market_service.get_indices()
        if indices:
            print(f"✓ 指数数据: {len(indices)} 个指数")
            for index in indices[:2]:  # 只显示前2个
                print(f"  - {index.get('name')}: {index.get('currentPrice')} ({index.get('changePercent'):.2f}%)")
        
        # 5. 获取板块 - 模拟 GET /api/v1/market/sectors
        print("\n=== 测试板块API ===")
        sectors = await market_service.get_sectors()
        if sectors:
            print(f"✓ 板块数据: {len(sectors)} 个板块")
            for sector in sectors[:3]:  # 只显示前3个
                print(f"  - {sector.get('name')}: {sector.get('changePercent'):.2f}% ({sector.get('stockCount')} 只股票)")
        
        # 6. 搜索股票 - 模拟 GET /api/v1/market/search?q=茅台
        print("\n=== 测试股票搜索API ===")
        search_results = await market_service.search_stocks("茅台", 5)
        if search_results:
            print(f"✓ 搜索结果: {len(search_results)} 个结果")
            for result in search_results:
                print(f"  - {result.get('symbol')}: {result.get('name')} ({result.get('market')})")
        
        # 7. 获取排行榜 - 模拟 GET /api/v1/market/rankings?type=CHANGE_PERCENT
        print("\n=== 测试排行榜API ===")
        rankings = await market_service.get_rankings("CHANGE_PERCENT", 5)
        if rankings:
            print(f"✓ 涨幅排行榜: 前 {len(rankings)} 名")
            for rank in rankings[:3]:  # 只显示前3名
                print(f"  {rank.get('rank')}. {rank.get('name')}: {rank.get('changePercent'):.2f}%")
        
        # 8. 获取自选股 - 模拟 GET /api/v1/market/watchlist
        print("\n=== 测试自选股API ===")
        watchlist = await market_service.get_watchlist(1)  # 用户ID=1
        if watchlist:
            print(f"✓ 自选股列表: {len(watchlist)} 只股票")
            for item in watchlist:
                print(f"  - {item.get('symbol')}: {item.get('name')}")
        
        # 9. 获取订单簿 - 模拟 GET /api/v1/market/orderbook/{symbol}
        print("\n=== 测试订单簿API ===")
        orderbook = await market_service.get_orderbook("600519", depth=3)
        if orderbook:
            print("✓ 订单簿数据:")
            print(f"  买盘: {len(orderbook.get('bids', []))} 档")
            print(f"  卖盘: {len(orderbook.get('asks', []))} 档")
            
            # 显示前3档买卖盘
            for i, bid in enumerate(orderbook.get('bids', [])[:3]):
                print(f"  买{i+1}: {bid.get('price'):.2f} × {bid.get('volume')}")
            for i, ask in enumerate(orderbook.get('asks', [])[:3]):
                print(f"  卖{i+1}: {ask.get('price'):.2f} × {ask.get('volume')}")
        
        # 10. 获取K线数据 - 模拟 GET /api/v1/market/kline/{symbol}
        print("\n=== 测试K线API ===")
        klines = await market_service.get_kline_data("600519", "DAILY", limit=5)
        if klines:
            print(f"✓ K线数据: {len(klines)} 根K线")
            for i, kline in enumerate(klines[:2]):  # 只显示前2根
                print(f"  K线{i+1}: O:{kline.get('open_price'):.2f} H:{kline.get('high_price'):.2f} L:{kline.get('low_price'):.2f} C:{kline.get('close_price'):.2f}")
        
        print("\n✅ 所有API集成测试通过！")
        print("🎉 市场服务可以完美支持API路由调用")
        return True
        
    except Exception as e:
        print(f"✗ API集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_market_api_integration())
    if success:
        print("\n🚀 市场服务API集成测试成功！")
        sys.exit(0)
    else:
        print("\n❌ 市场服务API集成测试失败")
        sys.exit(1)