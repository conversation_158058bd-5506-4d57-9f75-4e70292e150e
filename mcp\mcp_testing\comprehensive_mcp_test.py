#!/usr/bin/env python3
"""
综合MCP深度测试 - 使用真实的浏览器操作
结合BrowserTools MCP + FileSystem MCP + mcp-use调度器
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveMCPTest:
    def __init__(self):
        self.session_id = f"mcp_comprehensive_{int(time.time())}"
        self.browser = None
        self.page = None
        self.test_results = {
            'session_id': self.session_id,
            'start_time': datetime.now().isoformat(),
            'test_scenarios': [],
            'discovered_issues': [],
            'performance_metrics': {},
            'mcp_operations': []
        }
        
    async def initialize_mcp_environment(self):
        """初始化MCP测试环境"""
        logger.info("初始化MCP测试环境...")
        
        # 创建测试目录
        test_dirs = ['screenshots', 'test_data', 'logs', 'reports']
        for dir_path in test_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # 初始化浏览器 (模拟BrowserTools MCP)
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        logger.info("MCP环境初始化完成")

    async def mcp_file_operation(self, operation: str, **kwargs):
        """模拟FileSystem MCP操作"""
        operation_start = time.time()
        
        try:
            if operation == 'write_file':
                file_path = kwargs.get('path')
                content = kwargs.get('content', '')
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                result = {'success': True, 'path': file_path, 'size': len(content)}
                
            elif operation == 'read_file':
                file_path = kwargs.get('path')
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                result = {'success': True, 'content': content, 'size': len(content)}
                
            elif operation == 'create_directory':
                dir_path = kwargs.get('path')
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                result = {'success': True, 'path': dir_path}
                
            else:
                result = {'success': False, 'error': f'Unknown operation: {operation}'}
            
            # 记录操作
            operation_log = {
                'service': 'filesystem_mcp',
                'operation': operation,
                'duration': time.time() - operation_start,
                'success': result['success'],
                'timestamp': datetime.now().isoformat()
            }
            self.test_results['mcp_operations'].append(operation_log)
            
            return result
            
        except Exception as e:
            result = {'success': False, 'error': str(e)}
            
            operation_log = {
                'service': 'filesystem_mcp',
                'operation': operation,
                'duration': time.time() - operation_start,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.test_results['mcp_operations'].append(operation_log)
            
            return result

    async def mcp_browser_operation(self, operation: str, **kwargs):
        """模拟BrowserTools MCP操作"""
        operation_start = time.time()
        
        try:
            if operation == 'navigate':
                url = kwargs.get('url')
                await self.page.goto(url, wait_until='networkidle', timeout=30000)
                result = {'success': True, 'url': url, 'title': await self.page.title()}
                
            elif operation == 'click':
                selector = kwargs.get('selector')
                await self.page.click(selector, timeout=10000)
                result = {'success': True, 'selector': selector}
                
            elif operation == 'type':
                selector = kwargs.get('selector')
                text = kwargs.get('text')
                await self.page.fill(selector, text)
                result = {'success': True, 'selector': selector, 'text': text}
                
            elif operation == 'screenshot':
                path = kwargs.get('path', f'screenshots/{self.session_id}_{int(time.time())}.png')
                await self.page.screenshot(path=path, full_page=True)
                result = {'success': True, 'path': path}
                
            elif operation == 'wait_for_element':
                selector = kwargs.get('selector')
                timeout = kwargs.get('timeout', 10000)
                await self.page.wait_for_selector(selector, timeout=timeout)
                result = {'success': True, 'selector': selector}
                
            elif operation == 'get_text':
                selector = kwargs.get('selector')
                text = await self.page.text_content(selector)
                result = {'success': True, 'selector': selector, 'text': text}
                
            elif operation == 'check_element':
                selector = kwargs.get('selector')
                element = await self.page.query_selector(selector)
                result = {'success': True, 'found': element is not None, 'selector': selector}
                
            else:
                result = {'success': False, 'error': f'Unknown operation: {operation}'}
            
            # 记录操作
            operation_log = {
                'service': 'browser_tools_mcp',
                'operation': operation,
                'duration': time.time() - operation_start,
                'success': result['success'],
                'timestamp': datetime.now().isoformat()
            }
            self.test_results['mcp_operations'].append(operation_log)
            
            return result
            
        except Exception as e:
            result = {'success': False, 'error': str(e)}
            
            operation_log = {
                'service': 'browser_tools_mcp',
                'operation': operation,
                'duration': time.time() - operation_start,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.test_results['mcp_operations'].append(operation_log)
            
            return result

    async def test_scenario_real_user_journey(self):
        """测试场景：真实用户使用流程"""
        scenario = {
            'name': '真实用户完整使用流程',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'performance': []
        }
        
        logger.info("开始真实用户使用流程测试...")
        
        try:
            # 步骤1: 导航到交易中心
            logger.info("步骤1: 导航到交易中心")
            nav_result = await self.mcp_browser_operation(
                'navigate',
                url='http://localhost:5173/trading/center'
            )
            
            if nav_result['success']:
                scenario['steps'].append('成功导航到交易中心')
                
                # 截图记录
                await self.mcp_browser_operation('screenshot', path=f'screenshots/{self.session_id}_step1_navigation.png')
                
                # 等待页面加载
                await asyncio.sleep(3)
                
            else:
                scenario['issues'].append({
                    'step': 'navigation',
                    'severity': 'high',
                    'message': f'页面导航失败: {nav_result.get("error", "")}'
                })
                return scenario
            
            # 步骤2: 检查页面标题和主要元素
            logger.info("步骤2: 检查页面元素")
            
            # 检查页面标题
            title_result = await self.mcp_browser_operation('check_element', selector='.page-title')
            if title_result['found']:
                scenario['steps'].append('页面标题元素存在')
            else:
                scenario['issues'].append({
                    'step': 'page_elements',
                    'severity': 'medium',
                    'message': '页面标题元素未找到'
                })
            
            # 检查导航按钮
            nav_buttons_result = await self.mcp_browser_operation('check_element', selector='.nav-right button')
            if nav_buttons_result['found']:
                scenario['steps'].append('导航按钮存在')
            else:
                scenario['issues'].append({
                    'step': 'page_elements',
                    'severity': 'high',
                    'message': '导航按钮未找到'
                })
            
            # 步骤3: 测试模块切换
            logger.info("步骤3: 测试模块切换")
            
            modules = ['交易终端', '账户管理', '数据中心']
            for module in modules:
                try:
                    # 点击模块按钮
                    click_result = await self.mcp_browser_operation(
                        'click',
                        selector=f'button:has-text("{module}")'
                    )
                    
                    if click_result['success']:
                        scenario['steps'].append(f'成功切换到{module}')
                        
                        # 等待模块加载
                        await asyncio.sleep(2)
                        
                        # 截图记录
                        await self.mcp_browser_operation(
                            'screenshot',
                            path=f'screenshots/{self.session_id}_module_{module}.png'
                        )
                        
                        # 检查模块内容是否加载
                        content_result = await self.mcp_browser_operation(
                            'check_element',
                            selector='.module-container'
                        )
                        
                        if not content_result['found']:
                            scenario['issues'].append({
                                'step': 'module_switching',
                                'severity': 'high',
                                'message': f'{module}模块内容未正确加载'
                            })
                    else:
                        scenario['issues'].append({
                            'step': 'module_switching',
                            'severity': 'medium',
                            'message': f'无法点击{module}按钮: {click_result.get("error", "")}'
                        })
                        
                except Exception as e:
                    scenario['issues'].append({
                        'step': 'module_switching',
                        'severity': 'medium',
                        'message': f'{module}模块测试异常: {str(e)}'
                    })
            
            # 步骤4: 测试交易功能
            logger.info("步骤4: 测试交易功能")
            
            # 确保在交易终端模块
            await self.mcp_browser_operation('click', selector='button:has-text("交易终端")')
            await asyncio.sleep(2)
            
            # 测试股票搜索
            try:
                search_result = await self.mcp_browser_operation(
                    'type',
                    selector='.el-autocomplete input',
                    text='000001'
                )
                
                if search_result['success']:
                    scenario['steps'].append('股票搜索输入成功')
                    
                    # 等待搜索结果
                    await asyncio.sleep(1)
                    
                    # 测试买入按钮
                    buy_button_result = await self.mcp_browser_operation(
                        'check_element',
                        selector='button:has-text("买入")'
                    )
                    
                    if buy_button_result['found']:
                        scenario['steps'].append('买入按钮存在')
                    else:
                        scenario['issues'].append({
                            'step': 'trading_functionality',
                            'severity': 'high',
                            'message': '买入按钮未找到'
                        })
                        
                else:
                    scenario['issues'].append({
                        'step': 'trading_functionality',
                        'severity': 'high',
                        'message': f'股票搜索失败: {search_result.get("error", "")}'
                    })
                    
            except Exception as e:
                scenario['issues'].append({
                    'step': 'trading_functionality',
                    'severity': 'high',
                    'message': f'交易功能测试异常: {str(e)}'
                })
            
            # 步骤5: 保存测试数据
            logger.info("步骤5: 保存测试数据")
            
            test_data = {
                'scenario': scenario,
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id
            }
            
            save_result = await self.mcp_file_operation(
                'write_file',
                path=f'test_data/scenario_data_{self.session_id}.json',
                content=json.dumps(test_data, ensure_ascii=False, indent=2)
            )
            
            if save_result['success']:
                scenario['steps'].append('测试数据保存成功')
            else:
                scenario['issues'].append({
                    'step': 'data_saving',
                    'severity': 'low',
                    'message': f'测试数据保存失败: {save_result.get("error", "")}'
                })
            
        except Exception as e:
            scenario['issues'].append({
                'step': 'general',
                'severity': 'high',
                'message': f'测试场景执行异常: {str(e)}'
            })
        
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        
        self.test_results['test_scenarios'].append(scenario)
        
        logger.info(f"真实用户使用流程测试完成，耗时: {scenario['duration']:.2f}秒")
        
        return scenario

    async def generate_comprehensive_report(self):
        """生成综合测试报告"""
        logger.info("生成综合测试报告...")
        
        # 计算性能指标
        browser_ops = [op for op in self.test_results['mcp_operations'] if op['service'] == 'browser_tools_mcp']
        file_ops = [op for op in self.test_results['mcp_operations'] if op['service'] == 'filesystem_mcp']
        
        self.test_results['performance_metrics'] = {
            'browser_operations': {
                'total': len(browser_ops),
                'successful': len([op for op in browser_ops if op['success']]),
                'avg_duration': sum(op['duration'] for op in browser_ops) / len(browser_ops) if browser_ops else 0,
                'success_rate': len([op for op in browser_ops if op['success']]) / len(browser_ops) if browser_ops else 0
            },
            'file_operations': {
                'total': len(file_ops),
                'successful': len([op for op in file_ops if op['success']]),
                'avg_duration': sum(op['duration'] for op in file_ops) / len(file_ops) if file_ops else 0,
                'success_rate': len([op for op in file_ops if op['success']]) / len(file_ops) if file_ops else 0
            }
        }
        
        # 汇总所有问题
        all_issues = []
        for scenario in self.test_results['test_scenarios']:
            for issue in scenario.get('issues', []):
                issue['scenario'] = scenario['name']
                all_issues.append(issue)
        
        self.test_results['discovered_issues'] = all_issues
        self.test_results['end_time'] = datetime.now().isoformat()
        
        # 保存报告
        report_path = f'reports/comprehensive_mcp_test_{self.session_id}.json'
        await self.mcp_file_operation(
            'write_file',
            path=report_path,
            content=json.dumps(self.test_results, ensure_ascii=False, indent=2)
        )
        
        # 打印报告摘要
        print("\n" + "="*80)
        print("🧪 综合MCP深度测试报告")
        print("="*80)
        print(f"测试会话: {self.session_id}")
        print(f"测试场景: {len(self.test_results['test_scenarios'])}个")
        print(f"发现问题: {len(all_issues)}个")
        
        # 问题统计
        high_issues = [i for i in all_issues if i.get('severity') == 'high']
        medium_issues = [i for i in all_issues if i.get('severity') == 'medium']
        low_issues = [i for i in all_issues if i.get('severity') == 'low']
        
        print(f"\n📊 问题统计:")
        print(f"  🚨 高优先级: {len(high_issues)}个")
        print(f"  ⚠️ 中优先级: {len(medium_issues)}个")
        print(f"  💡 低优先级: {len(low_issues)}个")
        
        # 性能指标
        browser_metrics = self.test_results['performance_metrics']['browser_operations']
        file_metrics = self.test_results['performance_metrics']['file_operations']
        
        print(f"\n⚡ MCP服务性能:")
        print(f"  🌐 BrowserTools MCP:")
        print(f"    总操作: {browser_metrics['total']}")
        print(f"    成功率: {browser_metrics['success_rate']:.2%}")
        print(f"    平均耗时: {browser_metrics['avg_duration']:.3f}秒")
        
        print(f"  📁 FileSystem MCP:")
        print(f"    总操作: {file_metrics['total']}")
        print(f"    成功率: {file_metrics['success_rate']:.2%}")
        print(f"    平均耗时: {file_metrics['avg_duration']:.3f}秒")
        
        if high_issues:
            print(f"\n🚨 高优先级问题:")
            for i, issue in enumerate(high_issues, 1):
                print(f"  {i}. {issue.get('message', '')}")
                print(f"     场景: {issue.get('scenario', '')}")
                print(f"     步骤: {issue.get('step', '')}")
        
        print(f"\n📋 详细报告已保存: {report_path}")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        logger.info("资源清理完成")

    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("开始综合MCP深度测试")
        
        try:
            await self.initialize_mcp_environment()
            await self.test_scenario_real_user_journey()
            await self.generate_comprehensive_report()
            
        except Exception as e:
            logger.error(f"综合测试执行失败: {e}")
        
        finally:
            await self.cleanup()
        
        logger.info("综合MCP深度测试完成")

async def main():
    """主函数"""
    tester = ComprehensiveMCPTest()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
