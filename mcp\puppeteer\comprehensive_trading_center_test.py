#!/usr/bin/env python3
"""
量化投资平台交易中心全面深度测试
使用Puppeteer MCP作为真实用户测试http://localhost:5173的所有功能
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
import logging
from playwright.async_api import async_playwright
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_trading_center_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveTradingCenterTester:
    def __init__(self):
        self.test_session_id = f"trading_center_test_{int(time.time())}"
        self.platform_url = "file:///C:/Users/<USER>/Desktop/quant012/test_frontend_simple.html"
        self.test_results = {
            'session_id': self.test_session_id,
            'start_time': datetime.now().isoformat(),
            'test_type': 'Comprehensive Trading Center Deep Test',
            'platform_url': self.platform_url,
            'test_scenarios': [],
            'discovered_issues': [],
            'performance_metrics': [],
            'user_experience_feedback': [],
            'screenshots': [],
            'console_errors': [],
            'network_issues': [],
            'recommendations': []
        }
        self.browser = None
        self.page = None
        self.screenshot_counter = 1
        
    async def setup_browser(self):
        """设置浏览器环境"""
        logger.info("设置Puppeteer浏览器环境...")
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 可视化测试
            slow_mo=500,     # 慢动作，模拟真实用户
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--allow-running-insecure-content'
            ]
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await context.new_page()
        
        # 设置事件监听
        self.page.on('console', self._handle_console_message)
        self.page.on('pageerror', self._handle_page_error)
        self.page.on('requestfailed', self._handle_request_failed)
        
    async def _handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type in ['error', 'warning']:
            self.test_results['console_errors'].append({
                'type': msg.type,
                'message': msg.text,
                'timestamp': datetime.now().isoformat(),
                'location': msg.location
            })
            
    async def _handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['discovered_issues'].append({
            'type': 'page_error',
            'message': str(error),
            'timestamp': datetime.now().isoformat()
        })
        
    async def _handle_request_failed(self, request):
        """处理网络请求失败"""
        self.test_results['network_issues'].append({
            'url': request.url,
            'method': request.method,
            'failure': request.failure,
            'timestamp': datetime.now().isoformat()
        })
        
    async def take_screenshot(self, name: str, description: str = ""):
        """截图并记录"""
        screenshot_name = f"{self.test_session_id}_{self.screenshot_counter:03d}_{name}_{int(time.time())}.png"
        await self.page.screenshot(path=screenshot_name, full_page=True)
        
        self.test_results['screenshots'].append({
            'filename': screenshot_name,
            'description': description,
            'timestamp': datetime.now().isoformat(),
            'step': self.screenshot_counter
        })
        
        self.screenshot_counter += 1
        logger.info(f"截图已保存: {screenshot_name}")
        return screenshot_name
        
    async def test_initial_access(self):
        """测试初始访问"""
        logger.info("测试初始访问交易中心...")
        
        scenario = {
            'name': '初始访问测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 访问平台
            start_time = time.time()
            await self.page.goto(self.platform_url, wait_until='networkidle', timeout=30000)
            load_time = time.time() - start_time
            
            scenario['steps'].append(f"成功访问平台，加载时间: {load_time:.2f}秒")
            
            # 截图
            await self.take_screenshot('initial_access', '首次访问交易中心')
            
            # 检查页面标题
            title = await self.page.title()
            scenario['steps'].append(f"页面标题: {title}")
            
            # 检查是否有加载指示器
            loading_indicators = await self.page.query_selector_all('.loading, .spinner, [class*="loading"]')
            if loading_indicators:
                scenario['steps'].append(f"发现{len(loading_indicators)}个加载指示器")
                await asyncio.sleep(3)  # 等待加载完成
                
            # 检查页面基本结构
            nav_elements = await self.page.query_selector_all('nav, .nav, .navigation, .menu')
            scenario['steps'].append(f"发现{len(nav_elements)}个导航元素")
            
            if len(nav_elements) == 0:
                scenario['issues'].append("未发现导航元素")
                scenario['user_feedback'].append("页面缺少明显的导航结构")
                
            # 性能指标
            self.test_results['performance_metrics'].append({
                'metric': 'initial_load_time',
                'value': load_time,
                'timestamp': datetime.now().isoformat()
            })
            
            if load_time > 5:
                scenario['issues'].append("页面加载时间过长")
                scenario['user_feedback'].append("用户可能会因为加载慢而失去耐心")
            else:
                scenario['user_feedback'].append("页面加载速度可接受")
                
        except Exception as e:
            scenario['issues'].append(f"访问失败: {str(e)}")
            scenario['user_feedback'].append("无法正常访问交易中心")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['test_scenarios'].append(scenario)
        
    async def test_navigation_system(self):
        """测试导航系统"""
        logger.info("测试导航系统...")
        
        scenario = {
            'name': '导航系统测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 查找所有可点击的导航元素
            nav_links = await self.page.query_selector_all('a, button, [role="button"], .nav-item, .menu-item')
            scenario['steps'].append(f"发现{len(nav_links)}个可点击的导航元素")
            
            # 测试主要导航项
            main_nav_items = [
                '仪表盘', '市场数据', '交易终端', '投资组合', '策略中心', '风险管理',
                'Dashboard', 'Market', 'Trading', 'Portfolio', 'Strategy', 'Risk'
            ]
            
            found_nav_items = []
            for item_text in main_nav_items:
                try:
                    # 查找包含特定文本的导航项
                    nav_item = await self.page.query_selector(f'text="{item_text}"')
                    if nav_item:
                        found_nav_items.append(item_text)
                        scenario['steps'].append(f"发现导航项: {item_text}")
                        
                        # 尝试点击并测试
                        await nav_item.click()
                        await asyncio.sleep(2)  # 等待页面加载
                        
                        # 截图记录
                        await self.take_screenshot(f'nav_{item_text}', f'点击导航项: {item_text}')
                        
                        # 检查URL变化
                        current_url = self.page.url
                        scenario['steps'].append(f"点击{item_text}后URL: {current_url}")
                        
                except Exception as e:
                    scenario['issues'].append(f"点击{item_text}失败: {str(e)}")
                    
            if len(found_nav_items) < 3:
                scenario['issues'].append("主要导航项不足")
                scenario['user_feedback'].append("导航功能不够完整，用户难以找到核心功能")
            else:
                scenario['user_feedback'].append("导航系统基本完整")
                
        except Exception as e:
            scenario['issues'].append(f"导航测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['test_scenarios'].append(scenario)
        
    async def test_trading_functionality(self):
        """测试交易功能"""
        logger.info("测试交易功能...")
        
        scenario = {
            'name': '交易功能测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 尝试访问交易页面
            trading_selectors = [
                'text="交易终端"',
                'text="交易"',
                'text="Trading"',
                '[href*="trading"]',
                '.trading'
            ]
            
            trading_found = False
            for selector in trading_selectors:
                try:
                    trading_link = await self.page.query_selector(selector)
                    if trading_link:
                        await trading_link.click()
                        await asyncio.sleep(3)
                        trading_found = True
                        scenario['steps'].append(f"成功进入交易页面，使用选择器: {selector}")
                        break
                except:
                    continue
                    
            if not trading_found:
                scenario['issues'].append("无法找到交易页面入口")
                scenario['user_feedback'].append("交易功能不易访问")
                return
                
            # 截图交易页面
            await self.take_screenshot('trading_page', '交易页面界面')
            
            # 检查交易相关元素
            trading_elements = {
                '股票搜索': ['input[placeholder*="搜索"], input[placeholder*="股票"], .search-input'],
                '买入按钮': ['button:has-text("买入"), button:has-text("Buy"), .buy-button'],
                '卖出按钮': ['button:has-text("卖出"), button:has-text("Sell"), .sell-button'],
                '价格输入': ['input[placeholder*="价格"], input[type="number"], .price-input'],
                '数量输入': ['input[placeholder*="数量"], input[placeholder*="quantity"], .quantity-input'],
                '订单列表': ['.order-list, .orders, table']
            }
            
            for element_name, selectors in trading_elements.items():
                found = False
                for selector in selectors:
                    try:
                        element = await self.page.query_selector(selector)
                        if element:
                            scenario['steps'].append(f"发现{element_name}")
                            found = True
                            break
                    except:
                        continue
                        
                if not found:
                    scenario['issues'].append(f"缺少{element_name}")
                    
            # 测试搜索功能
            try:
                search_input = await self.page.query_selector('input[placeholder*="搜索"], input[placeholder*="股票"]')
                if search_input:
                    await search_input.fill('000001')
                    await asyncio.sleep(2)
                    scenario['steps'].append("测试股票搜索功能")
                    
                    # 检查搜索结果
                    search_results = await self.page.query_selector_all('.search-result, .stock-item, .suggestion')
                    scenario['steps'].append(f"搜索返回{len(search_results)}个结果")
                    
            except Exception as e:
                scenario['issues'].append(f"搜索功能测试失败: {str(e)}")
                
        except Exception as e:
            scenario['issues'].append(f"交易功能测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['test_scenarios'].append(scenario)
        
    async def test_market_data(self):
        """测试市场数据功能"""
        logger.info("测试市场数据功能...")
        
        scenario = {
            'name': '市场数据测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 访问市场数据页面
            market_selectors = [
                'text="市场数据"',
                'text="Market"',
                '[href*="market"]',
                '.market'
            ]
            
            market_found = False
            for selector in market_selectors:
                try:
                    market_link = await self.page.query_selector(selector)
                    if market_link:
                        await market_link.click()
                        await asyncio.sleep(3)
                        market_found = True
                        scenario['steps'].append(f"成功进入市场数据页面")
                        break
                except:
                    continue
                    
            if not market_found:
                scenario['issues'].append("无法找到市场数据页面")
                return
                
            # 截图市场数据页面
            await self.take_screenshot('market_data', '市场数据页面')
            
            # 检查图表元素
            chart_elements = await self.page.query_selector_all('canvas, .chart, .echarts, [id*="chart"]')
            scenario['steps'].append(f"发现{len(chart_elements)}个图表元素")
            
            if len(chart_elements) == 0:
                scenario['issues'].append("未发现图表元素")
                scenario['user_feedback'].append("市场数据页面缺少可视化图表")
            else:
                scenario['user_feedback'].append("市场数据可视化功能存在")
                
            # 检查股票列表
            stock_lists = await self.page.query_selector_all('table, .stock-list, .market-list')
            scenario['steps'].append(f"发现{len(stock_lists)}个股票列表")
            
            # 检查实时数据更新
            try:
                # 等待一段时间看是否有数据更新
                await asyncio.sleep(5)
                scenario['steps'].append("等待实时数据更新")
                
                # 检查是否有WebSocket连接
                ws_errors = [error for error in self.test_results['console_errors'] 
                           if 'websocket' in error.get('message', '').lower()]
                if ws_errors:
                    scenario['issues'].append("WebSocket连接存在问题")
                    
            except Exception as e:
                scenario['issues'].append(f"实时数据测试失败: {str(e)}")
                
        except Exception as e:
            scenario['issues'].append(f"市场数据测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['test_scenarios'].append(scenario)
        
    async def test_user_interactions(self):
        """测试用户交互"""
        logger.info("测试用户交互...")
        
        scenario = {
            'name': '用户交互测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 测试表单交互
            forms = await self.page.query_selector_all('form, .form')
            scenario['steps'].append(f"发现{len(forms)}个表单")
            
            # 测试输入框
            inputs = await self.page.query_selector_all('input, textarea, select')
            scenario['steps'].append(f"发现{len(inputs)}个输入元素")
            
            # 测试按钮响应
            buttons = await self.page.query_selector_all('button, .btn, [role="button"]')
            scenario['steps'].append(f"发现{len(buttons)}个按钮")
            
            # 随机测试几个按钮的响应
            for i, button in enumerate(buttons[:3]):
                try:
                    button_text = await button.inner_text()
                    if button_text and len(button_text.strip()) > 0:
                        start_time = time.time()
                        await button.click()
                        response_time = time.time() - start_time
                        
                        scenario['steps'].append(f"按钮'{button_text[:20]}'响应时间: {response_time:.3f}秒")
                        
                        if response_time > 1:
                            scenario['issues'].append(f"按钮'{button_text}'响应慢")
                            
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    scenario['issues'].append(f"按钮测试失败: {str(e)}")
                    
            # 测试键盘导航
            try:
                await self.page.keyboard.press('Tab')
                await asyncio.sleep(0.5)
                await self.page.keyboard.press('Tab')
                scenario['steps'].append("测试键盘导航")
            except Exception as e:
                scenario['issues'].append(f"键盘导航测试失败: {str(e)}")
                
        except Exception as e:
            scenario['issues'].append(f"用户交互测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['test_scenarios'].append(scenario)
        
    async def test_responsive_design(self):
        """测试响应式设计"""
        logger.info("测试响应式设计...")
        
        scenario = {
            'name': '响应式设计测试',
            'start_time': time.time(),
            'steps': [],
            'issues': [],
            'user_feedback': []
        }
        
        try:
            # 测试不同屏幕尺寸
            screen_sizes = [
                {'width': 1920, 'height': 1080, 'name': '桌面'},
                {'width': 1366, 'height': 768, 'name': '笔记本'},
                {'width': 768, 'height': 1024, 'name': '平板'},
                {'width': 375, 'height': 667, 'name': '手机'}
            ]
            
            for size in screen_sizes:
                await self.page.set_viewport_size({'width': size['width'], 'height': size['height']})
                await asyncio.sleep(2)
                
                # 截图不同尺寸
                await self.take_screenshot(f'responsive_{size["name"]}', f'{size["name"]}屏幕尺寸')
                
                # 检查是否有横向滚动条
                scroll_width = await self.page.evaluate('document.documentElement.scrollWidth')
                client_width = await self.page.evaluate('document.documentElement.clientWidth')
                
                if scroll_width > client_width:
                    scenario['issues'].append(f'{size["name"]}尺寸出现横向滚动')
                    
                scenario['steps'].append(f'测试{size["name"]}尺寸: {size["width"]}x{size["height"]}')
                
            # 恢复原始尺寸
            await self.page.set_viewport_size(1920, 1080)
            
        except Exception as e:
            scenario['issues'].append(f"响应式设计测试失败: {str(e)}")
            
        scenario['end_time'] = time.time()
        scenario['duration'] = scenario['end_time'] - scenario['start_time']
        self.test_results['test_scenarios'].append(scenario)
        
    async def generate_comprehensive_report(self):
        """生成综合报告"""
        logger.info("生成综合测试报告...")
        
        # 统计问题
        total_issues = sum(len(scenario.get('issues', [])) for scenario in self.test_results['test_scenarios'])
        total_console_errors = len(self.test_results['console_errors'])
        total_network_issues = len(self.test_results['network_issues'])
        
        # 生成建议
        recommendations = []
        
        if total_issues > 5:
            recommendations.append("发现较多功能性问题，建议优先修复")
            
        if total_console_errors > 0:
            recommendations.append("存在JavaScript错误，影响用户体验")
            
        if total_network_issues > 0:
            recommendations.append("网络请求存在问题，可能影响数据加载")
            
        # 性能建议
        load_times = [metric['value'] for metric in self.test_results['performance_metrics'] 
                     if metric['metric'] == 'initial_load_time']
        if load_times and max(load_times) > 3:
            recommendations.append("页面加载速度需要优化")
            
        self.test_results['recommendations'] = recommendations
        
        # 计算总体评分
        total_scenarios = len(self.test_results['test_scenarios'])
        if total_scenarios > 0:
            issue_rate = total_issues / total_scenarios
            if issue_rate < 1:
                grade = "优秀"
            elif issue_rate < 2:
                grade = "良好"
            elif issue_rate < 3:
                grade = "一般"
            else:
                grade = "需要改进"
        else:
            grade = "无法评估"
            
        self.test_results['overall_assessment'] = {
            'grade': grade,
            'total_scenarios': total_scenarios,
            'total_issues': total_issues,
            'console_errors': total_console_errors,
            'network_issues': total_network_issues,
            'issue_rate': issue_rate if total_scenarios > 0 else 0
        }
        
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
            
    async def save_test_results(self):
        """保存测试结果"""
        self.test_results['end_time'] = datetime.now().isoformat()
        self.test_results['total_duration'] = time.time() - time.mktime(
            datetime.fromisoformat(self.test_results['start_time']).timetuple()
        )
        
        # 保存详细报告
        report_file = f"comprehensive_trading_center_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"测试报告已保存: {report_file}")
        
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 80)
        print("量化投资平台交易中心全面深度测试报告")
        print("=" * 80)
        
        assessment = self.test_results['overall_assessment']
        print(f"测试会话: {self.test_session_id}")
        print(f"测试时间: {self.test_results.get('total_duration', 0):.2f}秒")
        print(f"测试场景: {assessment['total_scenarios']}个")
        print(f"发现问题: {assessment['total_issues']}个")
        print(f"控制台错误: {assessment['console_errors']}个")
        print(f"网络问题: {assessment['network_issues']}个")
        print(f"总体评级: {assessment['grade']}")
        
        print(f"\n改进建议:")
        for i, rec in enumerate(self.test_results['recommendations'], 1):
            print(f"  {i}. {rec}")
            
        print(f"\n截图数量: {len(self.test_results['screenshots'])}张")
        
    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("开始量化投资平台交易中心全面深度测试")
        
        try:
            await self.setup_browser()
            await self.test_initial_access()
            await self.test_navigation_system()
            await self.test_trading_functionality()
            await self.test_market_data()
            await self.test_user_interactions()
            await self.test_responsive_design()
            await self.generate_comprehensive_report()
            await self.save_test_results()
            
            self.print_summary()
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            await self.cleanup()
            
        logger.info("量化投资平台交易中心全面深度测试完成")

async def main():
    """主函数"""
    tester = ComprehensiveTradingCenterTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
