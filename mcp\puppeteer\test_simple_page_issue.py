#!/usr/bin/env python3
"""
使用Puppeteer MCP测试页面简化问题
"""

import asyncio
import subprocess
import time
import json
import os
from datetime import datetime
from playwright.async_api import async_playwright

class PlatformTester:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.project_root = "C:/Users/<USER>/Desktop/quant012"
        
    async def kill_existing_processes(self):
        """终止现有进程"""
        print("🔄 终止现有服务进程...")
        
        # 终止占用端口的进程
        for port in [5173, 8000]:
            try:
                result = subprocess.run(
                    f"netstat -ano | findstr :{port}",
                    shell=True, capture_output=True, text=True
                )
                
                for line in result.stdout.split('\n'):
                    if 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) > 4:
                            pid = parts[-1]
                            print(f"   终止端口{port}的进程 PID: {pid}")
                            subprocess.run(f"taskkill /PID {pid} /F", shell=True)
            except Exception as e:
                print(f"   终止端口{port}进程时出错: {e}")
        
        await asyncio.sleep(2)
    
    async def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        backend_dir = os.path.join(self.project_root, "backend")
        venv_python = os.path.join(self.project_root, ".venv", "Scripts", "python.exe")
        
        cmd = [
            venv_python, "-m", "uvicorn", 
            "app.main_simple:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ]
        
        self.backend_process = subprocess.Popen(
            cmd, 
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待后端启动
        for i in range(30):
            try:
                import requests
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    print("   ✅ 后端启动成功")
                    return True
            except:
                pass
            await asyncio.sleep(1)
            print(f"   等待后端启动... ({i+1}/30)")
        
        print("   ❌ 后端启动超时")
        return False
    
    async def start_frontend(self):
        """启动前端服务"""
        print("🌐 启动前端服务...")
        
        frontend_dir = os.path.join(self.project_root, "frontend")
        
        self.frontend_process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待前端启动
        for i in range(30):
            try:
                import requests
                response = requests.get("http://localhost:5173", timeout=2)
                if response.status_code == 200:
                    print("   ✅ 前端启动成功")
                    return True
            except:
                pass
            await asyncio.sleep(1)
            print(f"   等待前端启动... ({i+1}/30)")
        
        print("   ❌ 前端启动超时")
        return False
    
    async def test_page_with_puppeteer(self):
        """使用Puppeteer测试页面"""
        print("🔍 使用Puppeteer测试页面...")
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器窗口
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = await context.new_page()
            
            # 监听控制台消息
            console_messages = []
            page.on('console', lambda msg: console_messages.append({
                'type': msg.type,
                'text': msg.text,
                'location': msg.location
            }))
            
            # 监听网络请求
            network_requests = []
            page.on('request', lambda req: network_requests.append({
                'url': req.url,
                'method': req.method
            }))
            
            # 监听响应
            network_responses = []
            page.on('response', lambda resp: network_responses.append({
                'url': resp.url,
                'status': resp.status,
                'ok': resp.ok
            }))
            
            try:
                # 访问前端页面
                print("   访问前端页面...")
                await page.goto("http://localhost:5173", wait_until='networkidle')
                
                # 等待页面加载
                await asyncio.sleep(3)
                
                # 获取页面信息
                title = await page.title()
                print(f"   📄 页面标题: {title}")
                
                # 检查Vue应用是否加载
                app_element = await page.query_selector('#app')
                if app_element:
                    app_content = await app_element.inner_html()
                    print(f"   📦 Vue应用内容长度: {len(app_content)} 字符")
                    
                    if len(app_content) < 1000:
                        print("   ⚠️ Vue应用内容过少，可能未正确加载")
                        print(f"   内容预览: {app_content[:500]}...")
                    else:
                        print("   ✅ Vue应用内容丰富")
                        
                        # 检查关键元素
                        sidebar = await page.query_selector('.layout-sidebar')
                        if sidebar:
                            print("   ✅ 检测到侧边栏")
                        else:
                            print("   ❌ 未检测到侧边栏")
                        
                        dashboard = await page.query_selector('.dashboard-view')
                        if dashboard:
                            print("   ✅ 检测到仪表板")
                        else:
                            print("   ❌ 未检测到仪表板")
                else:
                    print("   ❌ 未找到Vue应用容器")
                
                # 截图
                timestamp = datetime.now().strftime("%H%M%S")
                screenshot_path = f"page_test_{timestamp}.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                print(f"   📸 已保存截图: {screenshot_path}")
                
                # 分析控制台消息
                print(f"\n   📋 控制台消息 ({len(console_messages)} 条):")
                error_count = 0
                for msg in console_messages:
                    if msg['type'] == 'error':
                        error_count += 1
                        print(f"      ❌ 错误: {msg['text']}")
                    elif msg['type'] == 'warning':
                        print(f"      ⚠️ 警告: {msg['text']}")
                
                if error_count == 0:
                    print("      ✅ 无JavaScript错误")
                else:
                    print(f"      ❌ 发现 {error_count} 个JavaScript错误")
                
                # 分析网络请求
                print(f"\n   🌐 网络请求分析 ({len(network_responses)} 个响应):")
                failed_requests = [r for r in network_responses if not r['ok']]
                
                if failed_requests:
                    print(f"      ❌ 失败的请求 ({len(failed_requests)} 个):")
                    for req in failed_requests:
                        print(f"         {req['status']} - {req['url']}")
                else:
                    print("      ✅ 所有网络请求成功")
                
                # 检查API请求
                api_requests = [r for r in network_responses if '/api/' in r['url']]
                print(f"      📊 API请求: {len(api_requests)} 个")
                
                # 生成报告
                report = {
                    'timestamp': datetime.now().isoformat(),
                    'page_title': title,
                    'app_content_length': len(app_content) if app_element else 0,
                    'console_errors': error_count,
                    'total_console_messages': len(console_messages),
                    'failed_requests': len(failed_requests),
                    'total_requests': len(network_responses),
                    'api_requests': len(api_requests),
                    'screenshot': screenshot_path,
                    'console_messages': console_messages,
                    'network_responses': network_responses
                }
                
                # 保存报告
                report_path = f"page_test_report_{timestamp}.json"
                with open(report_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                print(f"   📄 已保存报告: {report_path}")
                
                return report
                
            except Exception as e:
                print(f"   ❌ 页面测试失败: {e}")
                return None
            
            finally:
                await browser.close()
    
    async def cleanup(self):
        """清理进程"""
        print("🧹 清理进程...")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("   后端进程已终止")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("   前端进程已终止")
    
    async def run_test(self):
        """运行完整测试"""
        print("🔍 开始页面简化问题测试")
        print("="*60)
        
        try:
            # 1. 终止现有进程
            await self.kill_existing_processes()
            
            # 2. 启动后端
            if await self.start_backend():
                # 3. 启动前端
                if await self.start_frontend():
                    # 4. 等待服务稳定
                    await asyncio.sleep(5)
                    
                    # 5. 测试页面
                    report = await self.test_page_with_puppeteer()
                    
                    if report:
                        print("\n" + "="*60)
                        print("📋 测试总结:")
                        print(f"   页面标题: {report['page_title']}")
                        print(f"   应用内容长度: {report['app_content_length']} 字符")
                        print(f"   JavaScript错误: {report['console_errors']} 个")
                        print(f"   失败的网络请求: {report['failed_requests']} 个")
                        print(f"   API请求: {report['api_requests']} 个")
                        
                        if report['app_content_length'] < 1000:
                            print("\n⚠️ 问题确认: 页面内容确实过于简单!")
                            print("   可能的原因:")
                            print("   1. Vue.js未正确加载")
                            print("   2. JavaScript执行错误")
                            print("   3. API请求失败")
                            print("   4. 路由配置问题")
                        else:
                            print("\n✅ 页面内容正常，可能是显示问题")
                    
                else:
                    print("❌ 前端启动失败")
            else:
                print("❌ 后端启动失败")
        
        finally:
            await self.cleanup()

async def main():
    tester = PlatformTester()
    await tester.run_test()

if __name__ == "__main__":
    asyncio.run(main())
