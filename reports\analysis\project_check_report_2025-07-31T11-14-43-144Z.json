{"timestamp": "2025-07-31T11:14:06.907Z", "backend": {"status": "healthy", "apis": [{"name": "Health Check", "url": "http://localhost:8000/health", "method": "GET", "status": 200, "success": true, "response": {"status": 200, "statusText": "OK", "headers": {"content-length": "61", "content-type": "application/json"}}}, {"name": "API Docs", "url": "http://localhost:8000/docs", "method": "GET", "status": 200, "success": true, "response": {"status": 200, "statusText": "OK", "headers": {"content-length": "964", "content-type": "text/html; charset=utf-8"}}}, {"name": "OpenAPI Schema", "url": "http://localhost:8000/openapi.json", "method": "GET", "status": 200, "success": true, "response": {"status": 200, "statusText": "OK", "headers": {"content-length": "90622", "content-type": "application/json"}}}, {"name": "Market Data", "url": "http://localhost:8000/api/v1/market/stocks", "method": "GET", "status": 200, "success": true, "response": {"status": 200, "statusText": "OK", "headers": {"content-length": "628", "content-type": "application/json"}}}, {"name": "User <PERSON>", "url": "http://localhost:8000/api/v1/auth/login", "method": "POST", "status": 422, "success": false, "response": {"status": 422, "statusText": "Unprocessable Content", "headers": {"content-length": "133", "content-type": "application/json"}}}, {"name": "Strategy List", "url": "http://localhost:8000/api/v1/strategies", "method": "GET", "status": 200, "success": true, "response": {"status": 200, "statusText": "OK", "headers": {"content-length": "415", "content-type": "application/json"}}}], "errors": []}, "frontend": {"status": "issues", "pages": [], "components": [], "errors": [{"type": "console_error", "message": "Failed to load resource: the server responded with a status of 422 (Unprocessable Content)", "timestamp": "2025-07-31T11:14:10.102Z"}, {"type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:13.035Z"}, {"type": "console_error", "message": "错误堆栈: TypeError: marketStore.news.slice is not a function\n    at ComputedRefImpl.fn (http://localhost:5173/src/views/Dashboard/DashboardView.vue:63:31)\n    at refreshComputed (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:642:29)\n    at isDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:613:68)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:525:9)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:2480:9)", "timestamp": "2025-07-31T11:14:13.035Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:13.039Z"}, {"type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:13.039Z"}, {"type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:13.039Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:13.039Z"}, {"type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:13.039Z"}, {"type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:13.040Z"}, {"page": "Home", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:13.600Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:15.756Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:15.756Z"}, {"type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:15.762Z"}, {"type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:15.762Z"}, {"type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:15.763Z"}, {"type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:15.763Z"}, {"type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:16.042Z"}, {"type": "console_error", "message": "错误堆栈: TypeError: $setup.marketNews.slice is not a function\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue:1717:59\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=e1122e58:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7444:46)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:488:19)\n    at setupRenderEffect (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7579:5)\n    at mountComponent (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7353:7)\n    at processComponent (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7306:9)", "timestamp": "2025-07-31T11:14:16.043Z"}, {"page": "Market", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:16.784Z"}, {"page": "Trading", "error": "Navigation timeout of 10000 ms exceeded", "timestamp": "2025-07-31T11:14:26.786Z"}, {"page": "Strategy", "error": "Navigation timeout of 10000 ms exceeded", "timestamp": "2025-07-31T11:14:36.786Z"}, {"page": "Portfolio", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:39.697Z"}, {"type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:40.961Z"}, {"type": "console_error", "message": "错误堆栈: TypeError: data2 is not iterable\n    at checkSelectedStatus (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:35995:25)\n    at Object.updateAllSelected (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:36011:28)\n    at Object.setData (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:36278:22)\n    at Object.commit (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:36401:24)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:38400:17)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2279:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:8342:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2002:18)\n    at flushPreFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2428:7)", "timestamp": "2025-07-31T11:14:40.961Z"}, {"type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:40.961Z"}, {"type": "console_error", "message": "错误堆栈: TypeError: data.reduce is not a function\n    at Proxy.render (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:38137:12)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2480:9)", "timestamp": "2025-07-31T11:14:40.961Z"}, {"type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:40.961Z"}, {"type": "console_error", "message": "错误堆栈: TypeError: backtestStore.backtests.filter is not a function\n    at ComputedRefImpl.fn (http://localhost:5173/src/views/Backtest/BacktestView.vue:68:38)\n    at refreshComputed (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:642:29)\n    at isDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:613:68)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:525:9)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2480:9)", "timestamp": "2025-07-31T11:14:40.961Z"}, {"page": "Backtest", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:41.514Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:42.621Z"}, {"type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:42.621Z"}, {"type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:42.622Z"}, {"type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:42.629Z"}, {"type": "console_error", "message": "错误堆栈: TypeError: marketStore.news.slice is not a function\n    at ComputedRefImpl.fn (http://localhost:5173/src/views/Dashboard/DashboardView.vue:63:31)\n    at refreshComputed (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:642:29)\n    at isDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:613:68)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:525:9)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2480:9)", "timestamp": "2025-07-31T11:14:42.629Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:42.629Z"}, {"type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:42.629Z"}, {"type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:42.629Z"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:43.142Z"}]}, "integration": {"status": "healthy", "tests": [{"name": "API调用测试", "result": {"success": true, "status": 404, "data": "{\"detail\":\"Not Found\"}"}, "success": true}], "errors": []}, "summary": {"totalTests": 13, "passedTests": 6, "failedTests": 7, "issues": [{"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 422 (Unprocessable Content)", "timestamp": "2025-07-31T11:14:10.102Z"}, {"source": "frontend", "type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:13.035Z"}, {"source": "frontend", "type": "console_error", "message": "错误堆栈: TypeError: marketStore.news.slice is not a function\n    at ComputedRefImpl.fn (http://localhost:5173/src/views/Dashboard/DashboardView.vue:63:31)\n    at refreshComputed (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:642:29)\n    at isDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:613:68)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:525:9)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:2480:9)", "timestamp": "2025-07-31T11:14:13.035Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:13.039Z"}, {"source": "frontend", "type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:13.039Z"}, {"source": "frontend", "type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:13.039Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:13.039Z"}, {"source": "frontend", "type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:13.039Z"}, {"source": "frontend", "type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:13.040Z"}, {"source": "frontend", "page": "Home", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:13.600Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:15.756Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:15.756Z"}, {"source": "frontend", "type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:15.762Z"}, {"source": "frontend", "type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:15.762Z"}, {"source": "frontend", "type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:15.763Z"}, {"source": "frontend", "type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:15.763Z"}, {"source": "frontend", "type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:16.042Z"}, {"source": "frontend", "type": "console_error", "message": "错误堆栈: TypeError: $setup.marketNews.slice is not a function\n    at http://localhost:5173/src/views/Market/MarketViewOptimized.vue:1717:59\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=e1122e58:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7444:46)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:488:19)\n    at setupRenderEffect (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7579:5)\n    at mountComponent (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7353:7)\n    at processComponent (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=e1122e58:7306:9)", "timestamp": "2025-07-31T11:14:16.043Z"}, {"source": "frontend", "page": "Market", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:16.784Z"}, {"source": "frontend", "page": "Trading", "error": "Navigation timeout of 10000 ms exceeded", "timestamp": "2025-07-31T11:14:26.786Z"}, {"source": "frontend", "page": "Strategy", "error": "Navigation timeout of 10000 ms exceeded", "timestamp": "2025-07-31T11:14:36.786Z"}, {"source": "frontend", "page": "Portfolio", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:39.697Z"}, {"source": "frontend", "type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:40.961Z"}, {"source": "frontend", "type": "console_error", "message": "错误堆栈: TypeError: data2 is not iterable\n    at checkSelectedStatus (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:35995:25)\n    at Object.updateAllSelected (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:36011:28)\n    at Object.setData (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:36278:22)\n    at Object.commit (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:36401:24)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:38400:17)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2279:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:8342:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2002:18)\n    at flushPreFlushCbs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2428:7)", "timestamp": "2025-07-31T11:14:40.961Z"}, {"source": "frontend", "type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:40.961Z"}, {"source": "frontend", "type": "console_error", "message": "错误堆栈: TypeError: data.reduce is not a function\n    at Proxy.render (http://localhost:5173/node_modules/.vite/deps/chunk-ZIYUGO5S.js?v=69dc4954:38137:12)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2480:9)", "timestamp": "2025-07-31T11:14:40.961Z"}, {"source": "frontend", "type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:40.961Z"}, {"source": "frontend", "type": "console_error", "message": "错误堆栈: TypeError: backtestStore.backtests.filter is not a function\n    at ComputedRefImpl.fn (http://localhost:5173/src/views/Backtest/BacktestView.vue:68:38)\n    at refreshComputed (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:642:29)\n    at isDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:613:68)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:525:9)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2480:9)", "timestamp": "2025-07-31T11:14:40.961Z"}, {"source": "frontend", "page": "Backtest", "error": "this.page.waitForTimeout is not a function", "timestamp": "2025-07-31T11:14:41.514Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:42.621Z"}, {"source": "frontend", "type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:42.621Z"}, {"source": "frontend", "type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:42.622Z"}, {"source": "frontend", "type": "console_error", "message": "🚨 全局错误捕获: JSHandle@object", "timestamp": "2025-07-31T11:14:42.629Z"}, {"source": "frontend", "type": "console_error", "message": "错误堆栈: TypeError: marketStore.news.slice is not a function\n    at ComputedRefImpl.fn (http://localhost:5173/src/views/Dashboard/DashboardView.vue:63:31)\n    at refreshComputed (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:642:29)\n    at isDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:613:68)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:525:9)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=69dc4954:2480:9)", "timestamp": "2025-07-31T11:14:42.629Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:42.629Z"}, {"source": "frontend", "type": "console_error", "message": "[HTTP Response Error] JSHandle@object", "timestamp": "2025-07-31T11:14:42.629Z"}, {"source": "frontend", "type": "console_error", "message": "获取排行榜失败: JSHandle@object", "timestamp": "2025-07-31T11:14:42.629Z"}, {"source": "frontend", "type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-31T11:14:43.142Z"}], "successRate": "46.15%", "overallStatus": "needs_attention"}}