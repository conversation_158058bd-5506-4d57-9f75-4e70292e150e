"""
通知任务单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import json

from app.tasks.notification_tasks import (
    send_email_notification,
    send_sms_notification,
    send_websocket_notification,
    send_daily_summary,
    check_alert_conditions,
    batch_send_notifications,
    NotificationConfig,
    NotificationType,
    NotificationStatus,
    NotificationPriority,
    EmailNotificationSender,
    SMSNotificationSender,
    WebSocketNotificationSender,
    NotificationTemplate,
    AlertChecker,
    NotificationBatch,
)


@pytest.mark.unit
@pytest.mark.notification_tasks
class TestNotificationTasks:
    """通知任务测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = AsyncMock()
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.close = AsyncMock()
        return session

    @pytest.fixture
    def mock_redis(self):
        """模拟Redis连接"""
        redis = AsyncMock()
        redis.get = AsyncMock(return_value=None)
        redis.set = AsyncMock()
        redis.publish = AsyncMock()
        redis.lpush = AsyncMock()
        redis.rpop = AsyncMock()
        return redis

    @pytest.fixture
    def mock_websocket_manager(self):
        """模拟WebSocket管理器"""
        manager = AsyncMock()
        manager.send_to_user = AsyncMock()
        manager.send_to_group = AsyncMock()
        manager.broadcast = AsyncMock()
        return manager

    @pytest.fixture
    def notification_config(self):
        """通知配置"""
        return NotificationConfig(
            email_enabled=True,
            sms_enabled=True,
            websocket_enabled=True,
            email_smtp_server="smtp.gmail.com",
            email_smtp_port=587,
            email_username="<EMAIL>",
            email_password="test_password",
            sms_provider="twilio",
            sms_account_sid="test_sid",
            sms_auth_token="test_token",
            sms_from_number="+**********",
        )

    @pytest.fixture
    def email_sender(self, notification_config):
        """邮件发送器"""
        return EmailNotificationSender(notification_config)

    @pytest.fixture
    def sms_sender(self, notification_config):
        """短信发送器"""
        return SMSNotificationSender(notification_config)

    @pytest.fixture
    def websocket_sender(self, mock_websocket_manager):
        """WebSocket发送器"""
        return WebSocketNotificationSender(mock_websocket_manager)

    @pytest.fixture
    def notification_template(self):
        """通知模板"""
        return NotificationTemplate(
            template_id="daily_summary",
            template_name="Daily Summary",
            email_subject="Daily Trading Summary - {date}",
            email_body="Dear {user_name}, here is your daily trading summary...",
            sms_body="Trading summary: {total_trades} trades, {profit} profit",
            websocket_message={
                "type": "daily_summary",
                "data": {"date": "{date}", "summary": "{summary}"},
            },
        )

    @pytest.fixture
    def alert_checker(self):
        """告警检查器"""
        return AlertChecker()

    @pytest.fixture
    def sample_notification_data(self):
        """样本通知数据"""
        return {
            "user_id": "user123",
            "user_name": "Test User",
            "user_email": "<EMAIL>",
            "user_phone": "+**********",
            "date": datetime.now().strftime("%Y-%m-%d"),
            "total_trades": 15,
            "profit": 1250.50,
            "loss": -350.25,
            "win_rate": 0.67,
            "portfolio_value": 125000.00,
        }

    @pytest.mark.asyncio
    async def test_send_email_notification(
        self, mock_db_session, email_sender, sample_notification_data
    ):
        """测试发送邮件通知"""
        subject = "Test Email Notification"
        body = "This is a test email notification"
        recipients = ["<EMAIL>"]

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch.object(email_sender, "send_email", return_value=True):
                result = await send_email_notification(subject, body, recipients)

        assert result is not None
        assert "status" in result
        assert result["status"] == NotificationStatus.SENT
        assert "notification_id" in result

    @pytest.mark.asyncio
    async def test_send_sms_notification(
        self, mock_db_session, sms_sender, sample_notification_data
    ):
        """测试发送短信通知"""
        message = "Test SMS notification"
        recipients = ["+**********"]

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch.object(sms_sender, "send_sms", return_value=True):
                result = await send_sms_notification(message, recipients)

        assert result is not None
        assert "status" in result
        assert result["status"] == NotificationStatus.SENT
        assert "notification_id" in result

    @pytest.mark.asyncio
    async def test_send_websocket_notification(
        self, mock_db_session, websocket_sender, sample_notification_data
    ):
        """测试发送WebSocket通知"""
        message = {"type": "alert", "data": {"message": "Test WebSocket notification"}}
        user_id = "user123"

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            result = await send_websocket_notification(message, user_id)

        assert result is not None
        assert "status" in result
        assert result["status"] == NotificationStatus.SENT

    @pytest.mark.asyncio
    async def test_send_daily_summary(
        self, mock_db_session, mock_redis, sample_notification_data
    ):
        """测试发送日报摘要"""
        target_date = datetime.now().date()

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = [sample_notification_data]
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch(
                "app.tasks.notification_tasks.get_redis", return_value=mock_redis
            ):
                with patch(
                    "app.tasks.notification_tasks.EmailNotificationSender"
                ) as mock_email:
                    mock_email.return_value.send_email.return_value = True

                    result = await send_daily_summary(target_date)

        assert result is not None
        assert "status" in result
        assert result["status"] == NotificationStatus.SENT
        assert "sent_count" in result

    @pytest.mark.asyncio
    async def test_check_alert_conditions(self, mock_db_session, alert_checker):
        """测试检查告警条件"""
        # 模拟告警条件
        alert_conditions = [
            {
                "id": "price_alert_1",
                "type": "price_threshold",
                "symbol": "rb2405",
                "condition": "price > 4000",
                "user_id": "user123",
            },
            {
                "id": "loss_alert_1",
                "type": "portfolio_loss",
                "condition": "daily_loss > 5000",
                "user_id": "user123",
            },
        ]

        # 模拟市场数据
        market_data = {
            "rb2405": {"price": 4050.0, "volume": 1000},
            "hc2405": {"price": 3500.0, "volume": 800},
        }

        # 模拟用户数据
        user_data = {"user123": {"daily_loss": 5500.0, "portfolio_value": 120000.0}}

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch.object(
                alert_checker, "get_alert_conditions", return_value=alert_conditions
            ):
                with patch.object(
                    alert_checker, "get_market_data", return_value=market_data
                ):
                    with patch.object(
                        alert_checker, "get_user_data", return_value=user_data
                    ):
                        result = await check_alert_conditions()

        assert result is not None
        assert "triggered_alerts" in result
        assert len(result["triggered_alerts"]) == 2  # 两个条件都应该触发

    @pytest.mark.asyncio
    async def test_batch_send_notifications(self, mock_db_session, mock_redis):
        """测试批量发送通知"""
        notifications = [
            {
                "type": NotificationType.EMAIL,
                "recipient": "<EMAIL>",
                "subject": "Test Email 1",
                "body": "Test body 1",
            },
            {
                "type": NotificationType.SMS,
                "recipient": "+**********",
                "message": "Test SMS 1",
            },
            {
                "type": NotificationType.WEBSOCKET,
                "recipient": "user123",
                "message": {"type": "alert", "data": {"message": "Test WebSocket 1"}},
            },
        ]

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch(
                "app.tasks.notification_tasks.get_redis", return_value=mock_redis
            ):
                with patch(
                    "app.tasks.notification_tasks.EmailNotificationSender"
                ) as mock_email:
                    with patch(
                        "app.tasks.notification_tasks.SMSNotificationSender"
                    ) as mock_sms:
                        with patch(
                            "app.tasks.notification_tasks.WebSocketNotificationSender"
                        ) as mock_ws:
                            mock_email.return_value.send_email.return_value = True
                            mock_sms.return_value.send_sms.return_value = True
                            mock_ws.return_value.send_message.return_value = True

                            result = await batch_send_notifications(notifications)

        assert result is not None
        assert "total_sent" in result
        assert "failed_count" in result
        assert result["total_sent"] == 3

    def test_email_sender_send_email(self, email_sender):
        """测试邮件发送器发送邮件"""
        subject = "Test Email"
        body = "This is a test email"
        recipients = ["<EMAIL>"]

        with patch("smtplib.SMTP") as mock_smtp:
            mock_server = Mock()
            mock_smtp.return_value.__enter__.return_value = mock_server

            result = email_sender.send_email(subject, body, recipients)

        assert result is True
        mock_server.send_message.assert_called_once()

    def test_email_sender_send_email_with_attachment(self, email_sender):
        """测试邮件发送器发送带附件的邮件"""
        subject = "Test Email with Attachment"
        body = "This is a test email with attachment"
        recipients = ["<EMAIL>"]
        attachments = ["/tmp/test_file.pdf"]

        with patch("smtplib.SMTP") as mock_smtp:
            with patch("os.path.exists", return_value=True):
                with patch("builtins.open", mock_open(read_data=b"test file content")):
                    mock_server = Mock()
                    mock_smtp.return_value.__enter__.return_value = mock_server

                    result = email_sender.send_email(
                        subject, body, recipients, attachments
                    )

        assert result is True
        mock_server.send_message.assert_called_once()

    def test_sms_sender_send_sms(self, sms_sender):
        """测试短信发送器发送短信"""
        message = "Test SMS message"
        recipients = ["+**********"]

        with patch("twilio.rest.Client") as mock_client:
            mock_twilio = Mock()
            mock_client.return_value = mock_twilio
            mock_twilio.messages.create.return_value = Mock(sid="test_sid")

            result = sms_sender.send_sms(message, recipients)

        assert result is True
        mock_twilio.messages.create.assert_called_once()

    def test_sms_sender_send_sms_failure(self, sms_sender):
        """测试短信发送失败"""
        message = "Test SMS message"
        recipients = ["+**********"]

        with patch("twilio.rest.Client") as mock_client:
            mock_twilio = Mock()
            mock_client.return_value = mock_twilio
            mock_twilio.messages.create.side_effect = Exception("SMS sending failed")

            result = sms_sender.send_sms(message, recipients)

        assert result is False

    @pytest.mark.asyncio
    async def test_websocket_sender_send_message(
        self, websocket_sender, mock_websocket_manager
    ):
        """测试WebSocket发送器发送消息"""
        message = {"type": "alert", "data": {"message": "Test WebSocket message"}}
        user_id = "user123"

        result = await websocket_sender.send_message(message, user_id)

        assert result is True
        mock_websocket_manager.send_to_user.assert_called_once_with(user_id, message)

    @pytest.mark.asyncio
    async def test_websocket_sender_broadcast_message(
        self, websocket_sender, mock_websocket_manager
    ):
        """测试WebSocket发送器广播消息"""
        message = {
            "type": "system",
            "data": {"message": "System maintenance notification"},
        }

        result = await websocket_sender.broadcast_message(message)

        assert result is True
        mock_websocket_manager.broadcast.assert_called_once_with(message)

    def test_notification_template_render_email(
        self, notification_template, sample_notification_data
    ):
        """测试通知模板渲染邮件"""
        subject, body = notification_template.render_email_template(
            sample_notification_data
        )

        assert subject is not None
        assert body is not None
        assert sample_notification_data["date"] in subject
        assert sample_notification_data["user_name"] in body

    def test_notification_template_render_sms(
        self, notification_template, sample_notification_data
    ):
        """测试通知模板渲染短信"""
        message = notification_template.render_sms_template(sample_notification_data)

        assert message is not None
        assert str(sample_notification_data["total_trades"]) in message
        assert str(sample_notification_data["profit"]) in message

    def test_notification_template_render_websocket(
        self, notification_template, sample_notification_data
    ):
        """测试通知模板渲染WebSocket消息"""
        message = notification_template.render_websocket_template(
            sample_notification_data
        )

        assert message is not None
        assert isinstance(message, dict)
        assert "type" in message
        assert "data" in message

    def test_alert_checker_evaluate_condition(self, alert_checker):
        """测试告警检查器评估条件"""
        # 测试价格条件
        condition = "price > 4000"
        data = {"price": 4050.0}

        result = alert_checker.evaluate_condition(condition, data)
        assert result is True

        # 测试复杂条件
        condition = "price > 4000 and volume > 500"
        data = {"price": 4050.0, "volume": 1000}

        result = alert_checker.evaluate_condition(condition, data)
        assert result is True

    def test_alert_checker_evaluate_condition_false(self, alert_checker):
        """测试告警检查器评估条件为假"""
        condition = "price > 4000"
        data = {"price": 3950.0}

        result = alert_checker.evaluate_condition(condition, data)
        assert result is False

    def test_alert_checker_evaluate_condition_invalid(self, alert_checker):
        """测试告警检查器评估无效条件"""
        condition = "invalid_condition"
        data = {"price": 4050.0}

        result = alert_checker.evaluate_condition(condition, data)
        assert result is False

    def test_notification_batch_add_notification(self):
        """测试通知批次添加通知"""
        batch = NotificationBatch(batch_size=10)

        notification = {
            "type": NotificationType.EMAIL,
            "recipient": "<EMAIL>",
            "subject": "Test",
            "body": "Test body",
        }

        batch.add_notification(notification)

        assert len(batch.notifications) == 1
        assert batch.notifications[0] == notification

    def test_notification_batch_is_full(self):
        """测试通知批次是否已满"""
        batch = NotificationBatch(batch_size=2)

        notification = {
            "type": NotificationType.EMAIL,
            "recipient": "<EMAIL>",
            "subject": "Test",
            "body": "Test body",
        }

        batch.add_notification(notification)
        assert batch.is_full() is False

        batch.add_notification(notification)
        assert batch.is_full() is True

    @pytest.mark.asyncio
    async def test_notification_retry_mechanism(self, mock_db_session, email_sender):
        """测试通知重试机制"""
        subject = "Test Email"
        body = "Test body"
        recipients = ["<EMAIL>"]

        # 模拟第一次失败，第二次成功
        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch.object(email_sender, "send_email", side_effect=[False, True]):
                result = await send_email_notification(
                    subject, body, recipients, max_retries=2
                )

        assert result is not None
        assert result["status"] == NotificationStatus.SENT
        assert email_sender.send_email.call_count == 2

    @pytest.mark.asyncio
    async def test_notification_priority_handling(self, mock_db_session, mock_redis):
        """测试通知优先级处理"""
        high_priority_notification = {
            "type": NotificationType.EMAIL,
            "recipient": "<EMAIL>",
            "subject": "URGENT: System Alert",
            "body": "Urgent system alert",
            "priority": NotificationPriority.HIGH,
        }

        low_priority_notification = {
            "type": NotificationType.EMAIL,
            "recipient": "<EMAIL>",
            "subject": "Daily Report",
            "body": "Daily report",
            "priority": NotificationPriority.LOW,
        }

        notifications = [low_priority_notification, high_priority_notification]

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch(
                "app.tasks.notification_tasks.get_redis", return_value=mock_redis
            ):
                with patch(
                    "app.tasks.notification_tasks.EmailNotificationSender"
                ) as mock_email:
                    mock_email.return_value.send_email.return_value = True

                    result = await batch_send_notifications(notifications)

        assert result is not None
        assert result["total_sent"] == 2

    @pytest.mark.asyncio
    async def test_notification_rate_limiting(self, mock_redis, email_sender):
        """测试通知频率限制"""
        subject = "Test Email"
        body = "Test body"
        recipients = ["<EMAIL>"]

        # 模拟Redis中已有发送记录
        mock_redis.get.return_value = "5"  # 已发送5次

        with patch("app.tasks.notification_tasks.get_redis", return_value=mock_redis):
            result = await send_email_notification(
                subject,
                body,
                recipients,
                rate_limit=3,  # 限制3次
                rate_limit_window=3600,  # 1小时窗口
            )

        assert result is not None
        assert result["status"] == NotificationStatus.RATE_LIMITED

    @pytest.mark.asyncio
    async def test_notification_template_caching(
        self, mock_redis, notification_template
    ):
        """测试通知模板缓存"""
        template_id = "daily_summary"

        # 第一次调用，从数据库加载
        with patch("app.tasks.notification_tasks.get_redis", return_value=mock_redis):
            mock_redis.get.return_value = None  # 缓存中没有

            template = await notification_template.get_template(template_id)

        assert template is not None
        mock_redis.set.assert_called_once()  # 应该设置缓存

    @pytest.mark.asyncio
    async def test_notification_delivery_tracking(self, mock_db_session, email_sender):
        """测试通知投递跟踪"""
        subject = "Test Email"
        body = "Test body"
        recipients = ["<EMAIL>"]

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch.object(email_sender, "send_email", return_value=True):
                result = await send_email_notification(subject, body, recipients)

        assert result is not None
        assert "notification_id" in result

        # 验证数据库记录被创建
        mock_db_session.execute.assert_called()
        mock_db_session.commit.assert_called()

    @pytest.mark.asyncio
    async def test_notification_error_handling(self, mock_db_session, email_sender):
        """测试通知错误处理"""
        subject = "Test Email"
        body = "Test body"
        recipients = ["invalid_email"]

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch.object(
                email_sender, "send_email", side_effect=Exception("Invalid email")
            ):
                result = await send_email_notification(subject, body, recipients)

        assert result is not None
        assert result["status"] == NotificationStatus.FAILED
        assert "error" in result

    @pytest.mark.asyncio
    async def test_concurrent_notification_sending(self, mock_db_session, mock_redis):
        """测试并发通知发送"""
        notifications = []
        for i in range(10):
            notifications.append(
                {
                    "type": NotificationType.EMAIL,
                    "recipient": f"user{i}@example.com",
                    "subject": f"Test Email {i}",
                    "body": f"Test body {i}",
                }
            )

        with patch(
            "app.tasks.notification_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch(
                "app.tasks.notification_tasks.get_redis", return_value=mock_redis
            ):
                with patch(
                    "app.tasks.notification_tasks.EmailNotificationSender"
                ) as mock_email:
                    mock_email.return_value.send_email.return_value = True

                    # 并发发送
                    tasks = [
                        send_email_notification(
                            n["subject"], n["body"], [n["recipient"]]
                        )
                        for n in notifications
                    ]
                    results = await asyncio.gather(*tasks)

        assert len(results) == 10
        for result in results:
            assert result["status"] == NotificationStatus.SENT

    def test_notification_formatting(self, notification_template):
        """测试通知格式化"""
        data = {
            "user_name": "John Doe",
            "profit": 1250.75,
            "loss": -350.25,
            "date": "2024-01-15",
        }

        # 测试货币格式化
        formatted_profit = notification_template.format_currency(data["profit"])
        assert formatted_profit == "$1,250.75"

        # 测试百分比格式化
        win_rate = 0.675
        formatted_rate = notification_template.format_percentage(win_rate)
        assert formatted_rate == "67.5%"

        # 测试日期格式化
        formatted_date = notification_template.format_date(data["date"])
        assert formatted_date == "January 15, 2024"

    def test_notification_localization(self, notification_template):
        """测试通知本地化"""
        data = {"user_name": "张三", "profit": 1250.75, "language": "zh-CN"}

        # 测试中文模板
        subject, body = notification_template.render_localized_template(
            data, "daily_summary"
        )

        assert "张三" in body
        assert "盈利" in body or "profit" in body.lower()

    def test_memory_efficiency_large_batch(self):
        """测试大批量通知的内存效率"""
        # 创建大量通知
        notifications = []
        for i in range(10000):
            notifications.append(
                {
                    "type": NotificationType.EMAIL,
                    "recipient": f"user{i}@example.com",
                    "subject": f"Test Email {i}",
                    "body": f"Test body {i}",
                }
            )

        # 测试内存使用
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 创建批次处理器
        batch = NotificationBatch(batch_size=100)
        for notification in notifications:
            batch.add_notification(notification)
            if batch.is_full():
                batch.clear()

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该控制在合理范围内
        assert memory_increase < 50  # 少于50MB
