#!/usr/bin/env python3
"""测试已实现的功能"""

import requests
import json

# 基础URL
BASE_URL = "http://localhost:8000"

def test_api_endpoints():
    """测试主要API端点"""
    
    print("=== 测试API端点 ===\n")
    
    # 测试登录
    print("1. 测试登录功能")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
    print(f"   登录响应: {response.status_code}")
    if response.status_code == 200:
        print(f"   登录成功: {response.json()['message']}")
    
    # 测试市场数据
    print("\n2. 测试市场数据")
    response = requests.get(f"{BASE_URL}/api/v1/market/stocks")
    print(f"   获取股票列表: {response.status_code}")
    
    # 测试搜索功能
    print("\n3. 测试搜索功能")
    response = requests.get(f"{BASE_URL}/api/v1/market/search?keyword=银行")
    print(f"   搜索股票: {response.status_code}")
    
    # 测试自选股
    print("\n4. 测试自选股管理")
    response = requests.get(f"{BASE_URL}/api/v1/market/watchlist")
    print(f"   获取自选股: {response.status_code}")
    
    # 测试交易功能
    print("\n5. 测试交易功能")
    response = requests.get(f"{BASE_URL}/api/v1/trading/account")
    print(f"   获取账户信息: {response.status_code}")
    
    response = requests.get(f"{BASE_URL}/api/v1/trading/positions")
    print(f"   获取持仓信息: {response.status_code}")
    
    # 测试策略系统
    print("\n6. 测试策略系统")
    response = requests.get(f"{BASE_URL}/api/v1/strategies")
    print(f"   获取策略列表: {response.status_code}")
    
    # 测试风险管理
    print("\n7. 测试风险管理")
    response = requests.get(f"{BASE_URL}/api/v1/risk/metrics")
    print(f"   获取风险指标: {response.status_code}")
    
    print("\n=== 测试完成 ===")

def check_implementation_status():
    """检查实现状态"""
    
    print("\n=== 功能实现状态 ===\n")
    
    completed_features = [
        "✅ API路由修复 - 解决405错误",
        "✅ 核心交易功能 - 买卖、订单、持仓操作",
        "✅ 用户认证 - 登录、注册、验证码",
        "✅ 数据操作 - 刷新、搜索、自选股管理"
    ]
    
    pending_features = [
        "⏳ 表格操作 - 查看详情、编辑、删除功能",
        "⏳ 策略管理系统 - 编辑器、导入导出、执行控制",
        "⏳ 设置和配置 - 保存设置、用户资料管理"
    ]
    
    print("已完成的功能:")
    for feature in completed_features:
        print(f"  {feature}")
    
    print("\n待完成的功能:")
    for feature in pending_features:
        print(f"  {feature}")
    
    print("\n完成进度: 4/7 (57%)")

if __name__ == "__main__":
    print("量化投资平台功能测试\n")
    
    try:
        # 测试API端点
        test_api_endpoints()
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到后端服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"测试出错: {e}")
    
    # 显示实现状态
    check_implementation_status()