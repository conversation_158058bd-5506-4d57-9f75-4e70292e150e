{"session_id": "quick_test_1754359943", "start_time": "2025-08-05T10:12:23.288038", "test_type": "Quick Deep Trading Center Test", "platform_url": "http://localhost:5173", "test_scenarios": [{"name": "平台访问测试", "start_time": 1754359944.3996327, "steps": ["成功访问平台，加载时间: 1.51秒", "页面标题: 仪表盘 - 量化投资平台"], "issues": [], "user_feedback": ["页面加载速度良好"], "end_time": 1754359949.2718573, "duration": 4.872224569320679}, {"name": "导航和页面测试", "start_time": 1754359950.2801728, "steps": ["成功点击导航项: 仪表盘"], "issues": ["无法找到或点击导航项: 市场数据", "无法找到或点击导航项: 交易终端", "无法找到或点击导航项: 投资组合", "无法找到或点击导航项: 策略中心", "无法找到或点击导航项: 风险管理"], "user_feedback": ["导航系统存在问题，部分功能无法访问"], "end_time": 1754360012.8846555, "duration": 62.604482650756836}, {"name": "交易功能测试", "start_time": 1754360013.900075, "steps": [], "issues": ["缺少搜索框", "缺少买入按钮", "缺少卖出按钮", "缺少价格输入", "缺少图表"], "user_feedback": ["交易功能不完整，影响用户体验"], "end_time": 1754360025.9554274, "duration": 12.055352449417114}, {"name": "响应式设计测试", "start_time": 1754360026.967328, "steps": ["测试桌面尺寸: 1920x1080", "测试平板尺寸: 768x1024", "测试手机尺寸: 375x667"], "issues": [], "user_feedback": ["响应式设计测试完成"], "end_time": 1754360033.3173254, "duration": 6.349997282028198}], "discovered_issues": [], "console_errors": [{"type": "error", "text": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-05T10:12:24.725061"}, {"type": "warning", "text": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-05T10:12:24.725525"}, {"type": "warning", "text": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-05T10:12:24.739068"}, {"type": "warning", "text": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-05T10:12:28.595418"}, {"type": "warning", "text": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-05T10:12:28.595938"}, {"type": "error", "text": "🚨 页面错误: TypeError: Cannot read properties of undefined (reading 'pointerdown')\n    at HTMLDocument.firstInputHandler (http://localhost:5173/src/services/performance-monitor.service.ts:70:47)", "timestamp": "2025-08-05T10:12:30.356127"}, {"type": "error", "text": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: TypeError, message: Cannot read properties of undefined (reading 'pointerdown'), details: undefined, context: Object}", "timestamp": "2025-08-05T10:12:30.357263"}, {"type": "error", "text": "🚨 全局异常: {message: Uncaught TypeError: Cannot read properties of undefined (reading 'pointerdown'), filename: http://localhost:5173/src/services/performance-monitor.service.ts, lineno: 70, colno: 47, error: TypeError: Cannot read properties of undefined (reading 'pointerdown')\n    at HTMLDocument.firstInp…}", "timestamp": "2025-08-05T10:12:30.358018"}, {"type": "page_error", "text": "Cannot read properties of undefined (reading 'pointerdown')", "timestamp": "2025-08-05T10:12:30.358790"}, {"type": "warning", "text": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-05T10:12:33.402870"}, {"type": "warning", "text": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-05T10:12:33.405361"}], "network_issues": [], "screenshots": [{"filename": "quick_test_1754359943_001_platform_access_1754359948.png", "description": "platform_access", "timestamp": "2025-08-05T10:12:29.255089"}, {"filename": "quick_test_1754359943_002_nav_仪表盘_1754359952.png", "description": "nav_仪表盘", "timestamp": "2025-08-05T10:12:32.684212"}, {"filename": "quick_test_1754359943_003_trading_page_1754360022.png", "description": "trading_page", "timestamp": "2025-08-05T10:13:45.868224"}, {"filename": "quick_test_1754359943_004_responsive_桌面_1754360027.png", "description": "responsive_桌面", "timestamp": "2025-08-05T10:13:50.927716"}, {"filename": "quick_test_1754359943_005_responsive_平板_1754360031.png", "description": "responsive_平板", "timestamp": "2025-08-05T10:13:52.150316"}, {"filename": "quick_test_1754359943_006_responsive_手机_1754360033.png", "description": "responsive_手机", "timestamp": "2025-08-05T10:13:53.317005"}], "recommendations": ["发现较多问题，建议及时修复", "发现控制台错误，建议修复JavaScript问题"], "end_time": "2025-08-05T10:13:54.331676", "total_duration": 91.043666, "overall_assessment": {"grade": "需要改进", "total_scenarios": 4, "total_issues": 10, "console_errors": 11, "issue_rate": 2.5}}