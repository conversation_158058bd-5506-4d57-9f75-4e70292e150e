#!/usr/bin/env python3
"""
量化交易平台综合测试运行器
提供完整的测试套件执行和报告功能
"""
import asyncio
import os
import sys
import time
import json
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import argparse
import logging
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import signal

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from app.core.config import settings


@dataclass
class TestResult:
    """测试结果数据类"""
    category: str
    name: str
    status: str  # passed, failed, skipped, error
    duration: float
    error_message: Optional[str] = None
    coverage_percent: Optional[float] = None
    performance_metrics: Optional[Dict[str, Any]] = None


@dataclass
class TestSuiteResult:
    """测试套件结果"""
    name: str
    start_time: datetime
    end_time: datetime
    total_tests: int
    passed: int
    failed: int
    skipped: int
    errors: int
    coverage: Optional[float] = None
    results: List[TestResult] = None


class TestRunner:
    """综合测试运行器"""
    
    def __init__(self):
        self.project_root = PROJECT_ROOT
        self.test_dir = self.project_root / "tests"
        self.reports_dir = self.project_root / "test_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 测试类别配置
        self.test_categories = {
            "unit": {
                "path": "tests/unit",
                "description": "单元测试",
                "timeout": 300,
                "parallel": True
            },
            "integration": {
                "path": "tests/integration",
                "description": "集成测试",
                "timeout": 600,
                "parallel": False
            },
            "api": {
                "path": "tests/api",
                "description": "API接口测试",
                "timeout": 300,
                "parallel": True
            },
            "e2e": {
                "path": "tests/e2e",
                "description": "端到端测试",
                "timeout": 900,
                "parallel": False
            },
            "performance": {
                "path": "tests/performance",
                "description": "性能测试",
                "timeout": 1200,
                "parallel": False
            },
            "security": {
                "path": "tests/security",
                "description": "安全测试",
                "timeout": 600,
                "parallel": False
            },
            "load": {
                "path": "tests/load",
                "description": "负载测试",
                "timeout": 1800,
                "parallel": False
            }
        }
        
        self.results: List[TestSuiteResult] = []
        self.interrupted = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def setup_logging(self):
        """设置日志配置"""
        log_file = self.reports_dir / f"test_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.warning(f"收到信号 {signum}，正在优雅关闭测试...")
        self.interrupted = True
    
    async def run_test_category(self, category: str, config: Dict[str, Any]) -> TestSuiteResult:
        """运行指定类别的测试"""
        if self.interrupted:
            return None
            
        self.logger.info(f"开始运行 {config['description']} ({category})")
        start_time = datetime.now()
        
        test_path = self.project_root / config["path"]
        if not test_path.exists():
            self.logger.warning(f"测试路径不存在: {test_path}")
            return TestSuiteResult(
                name=category,
                start_time=start_time,
                end_time=datetime.now(),
                total_tests=0,
                passed=0,
                failed=0,
                skipped=0,
                errors=1,
                results=[]
            )
        
        # 构建pytest命令
        cmd = [
            sys.executable, "-m", "pytest",
            str(test_path),
            "-v",
            "--tb=short",
            f"--timeout={config['timeout']}",
            "--cov=app",
            f"--cov-report=xml:{self.reports_dir}/{category}_coverage.xml",
            f"--cov-report=html:{self.reports_dir}/{category}_htmlcov",
            f"--junitxml={self.reports_dir}/{category}_junit.xml",
            "--json-report",
            f"--json-report-file={self.reports_dir}/{category}_report.json"
        ]
        
        # 添加标记过滤
        if category == "performance":
            cmd.extend(["-m", "performance"])
        elif category == "integration":
            cmd.extend(["-m", "integration"])
        elif category == "unit":
            cmd.extend(["-m", "unit or not (integration or performance or e2e or load)"])
        elif category == "load":
            cmd.extend(["-m", "load"])
        elif category == "security":
            cmd.extend(["-m", "security"])
        
        # 并行执行配置
        if config.get("parallel", False):
            cmd.extend(["-n", "auto"])
        
        try:
            # 运行测试
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.project_root
            )
            
            stdout, stderr = await process.communicate()
            
            # 解析结果
            result = self._parse_test_results(category, stdout.decode(), stderr.decode())
            result.start_time = start_time
            result.end_time = datetime.now()
            
            if process.returncode == 0:
                self.logger.info(f"✅ {config['description']} 测试通过")
            else:
                self.logger.error(f"❌ {config['description']} 测试失败")
                
            return result
            
        except Exception as e:
            self.logger.error(f"运行 {category} 测试时出错: {e}")
            return TestSuiteResult(
                name=category,
                start_time=start_time,
                end_time=datetime.now(),
                total_tests=0,
                passed=0,
                failed=0,
                skipped=0,
                errors=1,
                results=[]
            )
    
    def _parse_test_results(self, category: str, stdout: str, stderr: str) -> TestSuiteResult:
        """解析测试结果"""
        # 尝试从JSON报告解析
        json_report_path = self.reports_dir / f"{category}_report.json"
        
        if json_report_path.exists():
            try:
                with open(json_report_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                return TestSuiteResult(
                    name=category,
                    start_time=datetime.now(),  # 将在调用处设置
                    end_time=datetime.now(),    # 将在调用处设置
                    total_tests=json_data.get('summary', {}).get('total', 0),
                    passed=json_data.get('summary', {}).get('passed', 0),
                    failed=json_data.get('summary', {}).get('failed', 0),
                    skipped=json_data.get('summary', {}).get('skipped', 0),
                    errors=json_data.get('summary', {}).get('error', 0),
                    results=self._parse_individual_tests(json_data.get('tests', []))
                )
            except Exception as e:
                self.logger.warning(f"解析JSON报告失败: {e}")
        
        # 回退到基本解析
        return self._parse_basic_output(category, stdout)
    
    def _parse_individual_tests(self, tests_data: List[Dict]) -> List[TestResult]:
        """解析单个测试结果"""
        results = []
        for test in tests_data:
            result = TestResult(
                category=test.get('nodeid', '').split('::')[0],
                name=test.get('nodeid', ''),
                status=test.get('outcome', 'unknown'),
                duration=test.get('duration', 0.0),
                error_message=test.get('call', {}).get('longrepr') if test.get('outcome') == 'failed' else None
            )
            results.append(result)
        return results
    
    def _parse_basic_output(self, category: str, output: str) -> TestSuiteResult:
        """基本输出解析"""
        lines = output.split('\n')
        passed = failed = skipped = errors = 0
        
        for line in lines:
            if 'passed' in line and 'failed' in line:
                # 解析类似 "5 passed, 2 failed, 1 skipped" 的行
                parts = line.split(',')
                for part in parts:
                    part = part.strip()
                    if 'passed' in part:
                        passed = int(part.split()[0])
                    elif 'failed' in part:
                        failed = int(part.split()[0])
                    elif 'skipped' in part:
                        skipped = int(part.split()[0])
                    elif 'error' in part:
                        errors = int(part.split()[0])
                break
        
        return TestSuiteResult(
            name=category,
            start_time=datetime.now(),
            end_time=datetime.now(),
            total_tests=passed + failed + skipped + errors,
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            results=[]
        )
    
    async def run_all_tests(self, categories: Optional[List[str]] = None, skip_categories: Optional[List[str]] = None) -> List[TestSuiteResult]:
        """运行所有测试类别"""
        if categories is None:
            categories = list(self.test_categories.keys())
        
        if skip_categories:
            categories = [cat for cat in categories if cat not in skip_categories]
        
        self.logger.info(f"开始运行测试套件，包含类别: {', '.join(categories)}")
        
        overall_start = datetime.now()
        results = []
        
        for category in categories:
            if self.interrupted:
                self.logger.warning("测试被中断")
                break
                
            if category not in self.test_categories:
                self.logger.warning(f"未知测试类别: {category}")
                continue
            
            config = self.test_categories[category]
            result = await self.run_test_category(category, config)
            
            if result:
                results.append(result)
        
        overall_end = datetime.now()
        self.results = results
        
        # 生成综合报告
        self._generate_comprehensive_report(overall_start, overall_end)
        
        return results
    
    def _generate_comprehensive_report(self, start_time: datetime, end_time: datetime):
        """生成综合测试报告"""
        duration = (end_time - start_time).total_seconds()
        
        # 统计总体数据
        total_tests = sum(r.total_tests for r in self.results)
        total_passed = sum(r.passed for r in self.results)
        total_failed = sum(r.failed for r in self.results)
        total_skipped = sum(r.skipped for r in self.results)
        total_errors = sum(r.errors for r in self.results)
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # 生成HTML报告
        html_report = self._generate_html_report(start_time, end_time, duration, {
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failed,
            'skipped': total_skipped,
            'errors': total_errors,
            'success_rate': success_rate
        })
        
        html_file = self.reports_dir / f"comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        # 生成JSON报告
        json_report = {
            'timestamp': datetime.now().isoformat(),
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'summary': {
                'total_tests': total_tests,
                'passed': total_passed,
                'failed': total_failed,
                'skipped': total_skipped,
                'errors': total_errors,
                'success_rate': success_rate
            },
            'results': [asdict(result) for result in self.results]
        }
        
        json_file = self.reports_dir / f"comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)
        
        # 控制台输出摘要
        self._print_summary(duration, {
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failed,
            'skipped': total_skipped,
            'errors': total_errors,
            'success_rate': success_rate
        })
        
        self.logger.info(f"详细报告已生成: {html_file}")
        self.logger.info(f"JSON报告已生成: {json_file}")
    
    def _generate_html_report(self, start_time: datetime, end_time: datetime, duration: float, summary: Dict) -> str:
        """生成HTML报告"""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>量化交易平台测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .summary {{ display: flex; gap: 20px; margin-bottom: 20px; }}
        .metric {{ background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .metric.passed {{ border-left: 4px solid #28a745; }}
        .metric.failed {{ border-left: 4px solid #dc3545; }}
        .metric.skipped {{ border-left: 4px solid #ffc107; }}
        .metric.total {{ border-left: 4px solid #007bff; }}
        .results {{ margin-top: 20px; }}
        .test-category {{ background: white; margin-bottom: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .category-header {{ background: #007bff; color: white; padding: 15px; border-radius: 8px 8px 0 0; }}
        .category-content {{ padding: 15px; }}
        .success {{ color: #28a745; }}
        .failure {{ color: #dc3545; }}
        .warning {{ color: #ffc107; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 量化交易平台测试报告</h1>
        <p><strong>测试时间:</strong> {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>总耗时:</strong> {duration:.2f} 秒</p>
        <p><strong>成功率:</strong> {summary['success_rate']:.1f}%</p>
    </div>
    
    <div class="summary">
        <div class="metric total">
            <h3>{summary['total_tests']}</h3>
            <p>总测试数</p>
        </div>
        <div class="metric passed">
            <h3>{summary['passed']}</h3>
            <p>通过</p>
        </div>
        <div class="metric failed">
            <h3>{summary['failed']}</h3>
            <p>失败</p>
        </div>
        <div class="metric skipped">
            <h3>{summary['skipped']}</h3>
            <p>跳过</p>
        </div>
    </div>
    
    <div class="results">
        <h2>详细结果</h2>
"""
        
        for result in self.results:
            status_class = "success" if result.failed == 0 and result.errors == 0 else "failure"
            html += f"""
        <div class="test-category">
            <div class="category-header">
                <h3>{result.name} - {self.test_categories.get(result.name, {}).get('description', result.name)}</h3>
            </div>
            <div class="category-content">
                <p><strong>总计:</strong> {result.total_tests} | 
                   <span class="success">通过: {result.passed}</span> | 
                   <span class="failure">失败: {result.failed}</span> | 
                   <span class="warning">跳过: {result.skipped}</span> | 
                   <span class="failure">错误: {result.errors}</span></p>
                <p><strong>耗时:</strong> {(result.end_time - result.start_time).total_seconds():.2f} 秒</p>
            </div>
        </div>
"""
        
        html += """
    </div>
</body>
</html>
"""
        return html
    
    def _print_summary(self, duration: float, summary: Dict):
        """打印测试摘要"""
        print("\n" + "="*80)
        print("🧪 量化交易平台测试套件执行完成")
        print("="*80)
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"📊 测试统计:")
        print(f"   总计: {summary['total_tests']}")
        print(f"   ✅ 通过: {summary['passed']}")
        print(f"   ❌ 失败: {summary['failed']}")
        print(f"   ⏭️  跳过: {summary['skipped']}")
        print(f"   🚫 错误: {summary['errors']}")
        print(f"   📈 成功率: {summary['success_rate']:.1f}%")
        print()
        
        # 按类别显示结果
        for result in self.results:
            status = "✅" if result.failed == 0 and result.errors == 0 else "❌"
            duration_cat = (result.end_time - result.start_time).total_seconds()
            print(f"{status} {result.name}: {result.passed}/{result.total_tests} ({duration_cat:.1f}s)")
        
        print("="*80)
    
    async def setup_test_environment(self):
        """设置测试环境"""
        self.logger.info("设置测试环境...")
        
        # 创建必要的目录
        (self.project_root / "logs").mkdir(exist_ok=True)
        (self.project_root / "data" / "test").mkdir(parents=True, exist_ok=True)
        
        # 设置环境变量
        os.environ['TESTING'] = 'true'
        os.environ['DATABASE_URL'] = 'sqlite+aiosqlite:///./test.db'
        
        self.logger.info("测试环境设置完成")
    
    async def cleanup_test_environment(self):
        """清理测试环境"""
        self.logger.info("清理测试环境...")
        
        # 清理测试数据库
        test_db_path = self.project_root / "test.db"
        if test_db_path.exists():
            test_db_path.unlink()
        
        # 清理临时文件
        temp_files = list(self.project_root.glob("*.tmp"))
        for temp_file in temp_files:
            temp_file.unlink()
        
        self.logger.info("测试环境清理完成")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="量化交易平台测试运行器")
    parser.add_argument(
        "--categories", 
        nargs="+", 
        help="指定要运行的测试类别",
        choices=["unit", "integration", "api", "e2e", "performance", "security", "load"]
    )
    parser.add_argument(
        "--skip", 
        nargs="+", 
        help="跳过指定的测试类别",
        choices=["unit", "integration", "api", "e2e", "performance", "security", "load"]
    )
    parser.add_argument("--setup-only", action="store_true", help="仅设置测试环境")
    parser.add_argument("--cleanup-only", action="store_true", help="仅清理测试环境")
    parser.add_argument("--quick", action="store_true", help="快速测试模式（仅运行单元测试和API测试）")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.setup_only:
            await runner.setup_test_environment()
            return
        
        if args.cleanup_only:
            await runner.cleanup_test_environment()
            return
        
        # 设置测试环境
        await runner.setup_test_environment()
        
        # 确定要运行的类别
        categories = args.categories
        if args.quick:
            categories = ["unit", "api"]
        
        # 运行测试
        results = await runner.run_all_tests(
            categories=categories,
            skip_categories=args.skip
        )
        
        # 确定退出码
        total_failed = sum(r.failed + r.errors for r in results)
        exit_code = 1 if total_failed > 0 else 0
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        runner.logger.warning("测试被用户中断")
        sys.exit(130)
    except Exception as e:
        runner.logger.error(f"测试运行器出现错误: {e}")
        sys.exit(1)
    finally:
        # 清理测试环境
        await runner.cleanup_test_environment()


if __name__ == "__main__":
    asyncio.run(main())