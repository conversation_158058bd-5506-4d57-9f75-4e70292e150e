"""
错误处理系统测试用例
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from fastapi import Request, Response
from fastapi.responses import JSONResponse

from app.core.exceptions import (
    BaseCustomException, ValidationError, AuthenticationError,
    MarketClosedError, InsufficientFundsError, CTPConnectionError,
    ErrorSeverity, ErrorCategory
)
from app.middleware.exception_middleware import ExceptionHandlerMiddleware
from app.middleware.exception_handlers import ExceptionHandlers
from app.core.log_rotation import LogRotationManager, LogRotationConfig
from app.core.monitoring_integration import MonitoringIntegration, SentryConfig, PrometheusConfig
from app.core.environment_logging import EnvironmentLoggingManager, Environment


class TestBaseCustomException:
    """测试基础异常类"""
    
    def test_exception_creation(self):
        """测试异常创建"""
        exc = BaseCustomException(
            message="测试错误",
            error_code="TEST_001",
            severity=ErrorSeverity.HIGH,
            details={"field": "value"},
            recovery_hint="请重试",
            should_retry=True
        )
        
        assert exc.message == "测试错误"
        assert exc.error_code == "TEST_001"
        assert exc.severity == ErrorSeverity.HIGH
        assert exc.details == {"field": "value"}
        assert exc.recovery_hint == "请重试"
        assert exc.should_retry is True
        assert exc.error_id is not None
        assert exc.timestamp is not None
    
    def test_exception_to_dict(self):
        """测试异常序列化"""
        exc = ValidationError(
            message="验证失败",
            details={"field": "email", "value": "invalid"}
        )
        
        result = exc.to_dict()
        
        assert result["message"] == "验证失败"
        assert result["category"] == "validation"
        assert result["details"]["field"] == "email"
        assert "error_id" in result
        assert "timestamp" in result
    
    def test_exception_context(self):
        """测试异常上下文"""
        exc = BaseCustomException("测试")
        exc.add_context("user_id", "123")
        exc.add_context("operation", "create_order")
        
        assert exc.context["user_id"] == "123"
        assert exc.context["operation"] == "create_order"
    
    def test_exception_causes(self):
        """测试异常链"""
        root_cause = ValueError("根本原因")
        exc = BaseCustomException("业务错误")
        exc.add_cause(root_cause)
        
        assert len(exc.causes) == 1
        assert exc.causes[0] == root_cause


class TestSpecificExceptions:
    """测试特定异常类型"""
    
    def test_market_closed_error(self):
        """测试市场关闭错误"""
        exc = MarketClosedError(
            message="市场已关闭",
            details={"market": "A股", "close_time": "15:00"}
        )
        
        assert exc.category == ErrorCategory.MARKET_DATA
        assert exc.details["market"] == "A股"
    
    def test_insufficient_funds_error(self):
        """测试资金不足错误"""
        exc = InsufficientFundsError(
            message="资金不足",
            details={
                "required": 10000,
                "available": 5000
            },
            should_retry=False
        )
        
        assert exc.category == ErrorCategory.TRADING
        assert exc.should_retry is False
        assert exc.details["required"] == 10000
    
    def test_ctp_connection_error(self):
        """测试CTP连接错误"""
        exc = CTPConnectionError(
            message="CTP连接失败",
            severity=ErrorSeverity.CRITICAL,
            should_retry=True,
            retry_after=30
        )
        
        assert exc.category == ErrorCategory.CTP
        assert exc.severity == ErrorSeverity.CRITICAL
        assert exc.should_retry is True
        assert exc.retry_after == 30


class TestExceptionHandlers:
    """测试异常处理器"""
    
    @pytest.fixture
    def mock_request(self):
        """模拟请求对象"""
        request = Mock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/orders"
        return request
    
    @pytest.mark.asyncio
    async def test_handle_validation_error(self, mock_request):
        """测试验证错误处理"""
        exc = ValidationError(
            message="验证失败",
            details={"field": "amount", "value": -100}
        )
        
        response = await ExceptionHandlers.handle_data_validation_error(
            mock_request, exc, "req_123", 0.5
        )
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 422
        
        content = response.body.decode()
        assert "validation_error" in content
        assert "验证失败" in content
    
    @pytest.mark.asyncio
    async def test_handle_trading_error(self, mock_request):
        """测试交易错误处理"""
        exc = InsufficientFundsError(
            message="资金不足",
            details={"required": 10000, "available": 5000}
        )
        
        response = await ExceptionHandlers.handle_insufficient_funds_error(
            mock_request, exc, "req_456", 1.0
        )
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 402  # Payment Required
        
        content = response.body.decode()
        assert "insufficient_funds_error" in content
    
    @pytest.mark.asyncio
    async def test_handle_ctp_error(self, mock_request):
        """测试CTP错误处理"""
        exc = CTPConnectionError(
            message="CTP连接超时",
            severity=ErrorSeverity.HIGH
        )
        
        response = await ExceptionHandlers.handle_ctp_connection_error(
            mock_request, exc, "req_789", 2.0
        )
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 503  # Service Unavailable


class TestExceptionMiddleware:
    """测试异常中间件"""
    
    def test_middleware_initialization(self):
        """测试中间件初始化"""
        middleware = ExceptionHandlerMiddleware(Mock())
        
        assert ValidationError in middleware.error_handlers
        assert AuthenticationError in middleware.error_handlers
        assert MarketClosedError in middleware.error_handlers
        assert CTPConnectionError in middleware.error_handlers
    
    @pytest.mark.asyncio
    async def test_middleware_exception_handling(self):
        """测试中间件异常处理"""
        app = Mock()
        middleware = ExceptionHandlerMiddleware(app)
        
        # 模拟请求
        request = Mock(spec=Request)
        request.state = Mock()
        request.method = "GET"
        request.url.path = "/api/test"
        request.query_params = {}
        request.headers = {"user-agent": "test"}
        request.client.host = "127.0.0.1"
        
        # 模拟抛出异常的call_next
        async def failing_call_next(request):
            raise ValidationError("测试验证错误")
        
        with patch.object(middleware, '_handle_exception') as mock_handle:
            mock_handle.return_value = JSONResponse(
                status_code=400,
                content={"error": {"message": "测试错误"}}
            )
            
            response = await middleware.dispatch(request, failing_call_next)
            
            assert mock_handle.called
            assert isinstance(response, JSONResponse)


class TestLogRotation:
    """测试日志轮转"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = LogRotationConfig(
            max_size="50MB",
            max_files=5,
            max_age_days=7,
            compression=True
        )
        
        assert config.max_size == "50MB"
        assert config.max_files == 5
        assert config.max_age_days == 7
        assert config.compression is True
    
    @pytest.mark.asyncio
    async def test_log_rotation_manager(self, tmp_path):
        """测试日志轮转管理器"""
        config = LogRotationConfig(
            max_files=3,
            max_age_days=1,
            check_interval=1  # 1秒检查间隔用于测试
        )
        
        manager = LogRotationManager(config)
        manager.log_dir = tmp_path
        
        # 创建测试日志文件
        for i in range(5):
            log_file = tmp_path / f"app.log.{i}"
            log_file.write_text(f"test log {i}")
        
        # 执行清理
        stats = await manager.cleanup_logs()
        
        # 验证清理结果
        assert stats.files_deleted >= 0
        remaining_files = list(tmp_path.glob("*.log*"))
        assert len(remaining_files) <= config.max_files
    
    @pytest.mark.asyncio
    async def test_log_statistics(self, tmp_path):
        """测试日志统计"""
        manager = LogRotationManager()
        manager.log_dir = tmp_path
        
        # 创建测试文件
        (tmp_path / "app.log").write_text("app log content")
        (tmp_path / "error.log").write_text("error log content")
        (tmp_path / "old.log.gz").write_text("compressed log")
        
        stats = await manager.get_log_statistics()
        
        assert stats["total_files"] >= 3
        assert stats["total_size"] > 0
        assert "by_type" in stats


class TestMonitoringIntegration:
    """测试监控集成"""
    
    def test_sentry_config(self):
        """测试Sentry配置"""
        config = SentryConfig(
            dsn="test-dsn",
            environment="testing",
            traces_sample_rate=0.5
        )
        
        assert config.dsn == "test-dsn"
        assert config.environment == "testing"
        assert config.traces_sample_rate == 0.5
    
    def test_prometheus_config(self):
        """测试Prometheus配置"""
        config = PrometheusConfig(
            enabled=True,
            metrics_path="/custom-metrics"
        )
        
        assert config.enabled is True
        assert config.metrics_path == "/custom-metrics"
    
    @pytest.mark.asyncio
    async def test_monitoring_initialization(self):
        """测试监控初始化"""
        sentry_config = SentryConfig(dsn=None)  # 无DSN避免实际初始化
        prometheus_config = PrometheusConfig(enabled=True)
        
        monitoring = MonitoringIntegration(sentry_config, prometheus_config)
        
        # 测试初始化（应该因为无DSN而部分失败）
        result = await monitoring.initialize()
        
        # 验证Prometheus仍然可用
        assert monitoring.prometheus is not None
    
    def test_error_recording(self):
        """测试错误记录"""
        monitoring = MonitoringIntegration()
        
        # 测试异常捕获（不会实际发送）
        test_exception = ValueError("测试异常")
        monitoring.capture_exception(test_exception, level="error")
        
        # 测试消息捕获
        monitoring.capture_message("测试消息", level="info")
        
        # 这些调用不应该抛出异常
        assert True


class TestEnvironmentLogging:
    """测试环境日志配置"""
    
    def test_environment_detection(self):
        """测试环境检测"""
        with patch.object(EnvironmentLoggingManager, '_detect_environment') as mock_detect:
            mock_detect.return_value = Environment.PRODUCTION
            
            manager = EnvironmentLoggingManager()
            assert manager.current_env == Environment.PRODUCTION
    
    def test_development_config(self):
        """测试开发环境配置"""
        manager = EnvironmentLoggingManager()
        manager.current_env = Environment.DEVELOPMENT
        
        config = manager._get_development_config()
        
        assert config.environment == Environment.DEVELOPMENT
        assert config.root_level == "DEBUG"
        assert config.enable_console is True
        assert config.sensitive_data_filter is False
        assert config.sampling_rate == 1.0
    
    def test_production_config(self):
        """测试生产环境配置"""
        manager = EnvironmentLoggingManager()
        manager.current_env = Environment.PRODUCTION
        
        config = manager._get_production_config()
        
        assert config.environment == Environment.PRODUCTION
        assert config.root_level == "WARNING"
        assert config.enable_console is False
        assert config.sensitive_data_filter is True
        assert config.sampling_rate == 0.1
        assert config.retention_days == 90
    
    def test_config_summary(self):
        """测试配置摘要"""
        manager = EnvironmentLoggingManager()
        summary = manager.get_config_summary()
        
        assert "environment" in summary
        assert "root_level" in summary
        assert "console_enabled" in summary
        assert "handlers_count" in summary


class TestIntegrationScenarios:
    """测试集成场景"""
    
    @pytest.mark.asyncio
    async def test_complete_error_flow(self):
        """测试完整错误流程"""
        # 1. 创建异常
        exc = InsufficientFundsError(
            message="账户资金不足",
            details={"required": 10000, "available": 5000},
            recovery_hint="请充值后重试"
        )
        
        # 2. 模拟中间件处理
        request = Mock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/orders"
        
        response = await ExceptionHandlers.handle_insufficient_funds_error(
            request, exc, "req_test", 1.0
        )
        
        # 3. 验证响应
        assert isinstance(response, JSONResponse)
        assert response.status_code == 402
        
        # 4. 验证响应内容
        import json
        content = json.loads(response.body.decode())
        
        assert content["error"]["type"] == "insufficient_funds_error"
        assert content["error"]["message"] == "账户资金不足"
        assert content["error"]["recovery_hint"] == "请充值后重试"
        assert content["error"]["details"]["required"] == 10000
    
    def test_error_categorization(self):
        """测试错误分类"""
        # 测试不同类型错误的分类
        validation_error = ValidationError("验证错误")
        assert validation_error.category == ErrorCategory.VALIDATION
        
        auth_error = AuthenticationError("认证错误")
        assert auth_error.category == ErrorCategory.AUTHENTICATION
        
        market_error = MarketClosedError("市场关闭")
        assert market_error.category == ErrorCategory.MARKET_DATA
        
        ctp_error = CTPConnectionError("CTP连接失败")
        assert ctp_error.category == ErrorCategory.CTP
    
    def test_error_severity_mapping(self):
        """测试错误严重程度映射"""
        # 不同严重程度的错误
        low_error = BaseCustomException("低级错误", severity=ErrorSeverity.LOW)
        medium_error = BaseCustomException("中级错误", severity=ErrorSeverity.MEDIUM)
        high_error = BaseCustomException("高级错误", severity=ErrorSeverity.HIGH)
        critical_error = BaseCustomException("严重错误", severity=ErrorSeverity.CRITICAL)
        
        assert low_error.severity == ErrorSeverity.LOW
        assert medium_error.severity == ErrorSeverity.MEDIUM
        assert high_error.severity == ErrorSeverity.HIGH
        assert critical_error.severity == ErrorSeverity.CRITICAL


class TestPerformance:
    """测试性能相关功能"""
    
    @pytest.mark.asyncio
    async def test_large_scale_log_rotation(self, tmp_path):
        """测试大规模日志轮转性能"""
        config = LogRotationConfig(max_files=10, max_age_days=1)
        manager = LogRotationManager(config)
        manager.log_dir = tmp_path
        
        # 创建大量测试文件
        for i in range(100):
            log_file = tmp_path / f"test_{i}.log"
            log_file.write_text("x" * 1000)  # 1KB per file
        
        # 测试清理性能
        start_time = datetime.now()
        stats = await manager.cleanup_logs()
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        assert duration < 5.0  # 应该在5秒内完成
        assert stats.files_deleted > 0
    
    def test_exception_creation_performance(self):
        """测试异常创建性能"""
        import time
        
        start_time = time.time()
        
        # 创建大量异常对象
        exceptions = []
        for i in range(1000):
            exc = BaseCustomException(
                message=f"测试异常 {i}",
                details={"index": i, "data": "test" * 100}
            )
            exceptions.append(exc)
        
        end_time = time.time()
        duration = end_time - start_time
        
        assert duration < 1.0  # 应该在1秒内完成
        assert len(exceptions) == 1000


# 集成测试标记
pytestmark = pytest.mark.asyncio