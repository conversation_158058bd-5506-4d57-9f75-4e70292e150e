#!/usr/bin/env python3
"""完整的API端点测试"""

import json
import time
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# API基础URL
BASE_URL = "http://localhost:8000"

# 颜色代码
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def test_endpoint(method, path, data=None, expected_status=[200], headers=None):
    """测试单个端点"""
    url = f"{BASE_URL}{path}"
    
    try:
        req_headers = {'Content-Type': 'application/json'}
        if headers:
            req_headers.update(headers)
            
        if data:
            data = json.dumps(data).encode('utf-8')
            
        request = Request(url, data=data, headers=req_headers)
        request.get_method = lambda: method
        
        try:
            response = urlopen(request)
            status_code = response.getcode()
            response_data = response.read().decode('utf-8')
        except HTTPError as e:
            status_code = e.code
            response_data = e.read().decode('utf-8')
            
        if status_code in expected_status:
            return True, f"状态码: {status_code}"
        else:
            return False, f"状态码: {status_code}, 响应: {response_data[:100]}"
            
    except URLError as e:
        return False, f"连接错误: {str(e)}"
    except Exception as e:
        return False, f"错误: {str(e)}"

def main():
    """主测试函数"""
    print(f"\n{BLUE}{'='*80}")
    print(f"📊 量化投资平台 完整API测试")
    print(f"{'='*80}{RESET}\n")
    
    # 测试用例定义
    test_cases = [
        # 基础测试
        ("GET", "/", None, [200], None, "根路径"),
        ("GET", "/docs", None, [200], None, "API文档"),
        ("GET", "/health", None, [200, 404], None, "健康检查"),
        
        # 认证模块 - 正确的路径
        ("POST", "/api/v1/auth/login", {"username": "admin", "password": "admin123"}, [200], None, "登录API (正确密码)"),
        ("POST", "/api/v1/auth/login", {"username": "admin", "password": "wrong"}, [200, 401], None, "登录API (错误密码)"),
        ("POST", "/api/v1/auth/register", {"username": "test_" + str(int(time.time())), "email": f"test{int(time.time())}@example.com", "password": "test123"}, [200], None, "注册API"),
        ("GET", "/api/captcha/slider", None, [200], None, "滑动验证码"),
        
        # 使用v1前缀的路径
        ("POST", "/api/v1/auth/login", {"username": "admin", "password": "admin123"}, [200, 404], None, "登录API (v1)"),
        
        # 市场数据
        ("GET", "/api/stocks", None, [200], None, "股票列表"),
        ("GET", "/api/market_data", None, [200], None, "实时行情"),
        ("GET", "/api/v1/market/kline/000001.SZ", None, [200, 404], None, "K线数据"),
        ("GET", "/api/v1/market/quote/000001.SZ", None, [200, 404], None, "股票报价"),
        
        # 交易系统
        ("GET", "/api/v1/trade/account", None, [200], None, "账户信息"),
        ("GET", "/api/v1/trade/orders", None, [200], None, "订单查询"),
        ("GET", "/api/v1/trade/positions", None, [200, 404], None, "持仓查询"),
        ("POST", "/api/v1/trade/order", {"symbol": "000001.SZ", "side": "buy", "price": 10.0, "quantity": 100}, [200, 404], None, "下单接口"),
        
        # 策略系统
        ("GET", "/api/v1/strategies", None, [200], None, "策略列表"),
        ("POST", "/api/v1/strategies/backtest", {"strategy_id": "1", "start_date": "2024-01-01", "end_date": "2024-12-31"}, [200, 404], None, "策略回测"),
        
        # 风险管理
        ("GET", "/api/v1/risk/metrics", None, [200, 404], None, "风险指标"),
        ("GET", "/api/v1/risk/alerts", None, [200, 404], None, "风险警报"),
        
        # WebSocket连接测试（只测试HTTP握手）
        ("GET", "/ws", None, [404, 403, 426], None, "WebSocket端点"),
        ("GET", "/ws/market", None, [404, 403, 426], None, "市场数据WebSocket"),
    ]
    
    # 统计结果
    results = {
        "passed": 0,
        "failed": 0,
        "details": []
    }
    
    # 执行测试
    for method, path, data, expected_status, headers, name in test_cases:
        success, message = test_endpoint(method, path, data, expected_status, headers)
        
        result = {
            "name": name,
            "method": method,
            "path": path,
            "success": success,
            "message": message
        }
        results["details"].append(result)
        
        if success:
            print(f"{GREEN}✅ 通过{RESET} [{name}] {method} {path} - {message}")
            results["passed"] += 1
        else:
            print(f"{RED}❌ 失败{RESET} [{name}] {method} {path} - {message}")
            results["failed"] += 1
        
        time.sleep(0.1)  # 避免请求过快
    
    # 输出统计
    total = results["passed"] + results["failed"]
    print(f"\n{BLUE}{'='*80}")
    print(f"📊 测试统计")
    print(f"{'='*80}{RESET}")
    print(f"总测试数: {total}")
    print(f"{GREEN}✅ 通过: {results['passed']} ({results['passed']/total*100:.1f}%){RESET}")
    print(f"{RED}❌ 失败: {results['failed']} ({results['failed']/total*100:.1f}%){RESET}")
    
    # 分析问题
    print(f"\n{YELLOW}💡 问题分析:{RESET}")
    
    # 检查API路径问题
    v1_failures = [r for r in results["details"] if not r["success"] and "/api/v1/" in r["path"]]
    if v1_failures:
        print(f"\n1. API版本路径问题:")
        print(f"   - 发现 {len(v1_failures)} 个使用 /api/v1/ 路径的API返回404")
        print(f"   - 后端实际使用的路径可能是 /api/ (没有v1)")
        print(f"   - 建议：统一API路径规范")
    
    # 检查未实现的功能
    not_implemented = [r for r in results["details"] if not r["success"] and "404" in r["message"]]
    if not_implemented:
        print(f"\n2. 未实现的功能:")
        for r in not_implemented[:5]:  # 只显示前5个
            print(f"   - {r['name']}: {r['method']} {r['path']}")
    
    # 输出建议
    print(f"\n{YELLOW}🔧 解决方案建议:{RESET}")
    print("1. 前端配置修改：")
    print("   - 修改 .env.development 中的 VITE_API_BASE_URL")
    print("   - 从 'http://localhost:8000/api' 改为 'http://localhost:8000'")
    print("   - 或者在后端统一添加 /api 前缀")
    print("\n2. 后端路由修正：")
    print("   - 添加缺失的API端点")
    print("   - 统一API路径规范（是否使用v1版本号）")
    print("\n3. 数据库相关：")
    print("   - 确保数据库服务正常运行")
    print("   - 运行数据库迁移脚本")
    
    # 保存测试报告
    report_file = f"api_test_report_{int(time.time())}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"\n📄 详细测试报告已保存至: {report_file}")

if __name__ == "__main__":
    main()