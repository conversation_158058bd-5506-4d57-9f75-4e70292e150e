/**
 * 诊断量化投资平台的具体问题
 */

const puppeteer = require('puppeteer');
const fs = require('fs');

async function diagnoseIssues() {
    console.log('🔍 开始诊断平台问题...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 },
        args: ['--start-maximized']
    });

    const page = await browser.newPage();
    
    // 收集所有错误
    const errors = [];
    const consoleMessages = [];
    
    page.on('console', msg => {
        consoleMessages.push({
            type: msg.type(),
            text: msg.text(),
            timestamp: new Date()
        });
        if (msg.type() === 'error') {
            console.log(`❌ 控制台错误: ${msg.text()}`);
        }
    });

    page.on('pageerror', error => {
        errors.push({
            type: 'page_error',
            message: error.message,
            stack: error.stack,
            timestamp: new Date()
        });
        console.log(`❌ 页面错误: ${error.message}`);
    });

    try {
        console.log('\n📱 测试1: 检查首页...');
        await page.goto('http://localhost:5173', { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });

        // 获取页面基本信息
        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                url: window.location.href,
                bodyContent: document.body.innerText.substring(0, 500),
                hasVueApp: !!window.Vue || !!document.querySelector('#app'),
                elementCount: document.querySelectorAll('*').length,
                scriptCount: document.querySelectorAll('script').length,
                styleCount: document.querySelectorAll('style, link[rel="stylesheet"]').length
            };
        });

        console.log('📊 首页信息:', pageInfo);
        await page.screenshot({ path: 'diagnosis_homepage.png', fullPage: true });

        console.log('\n💰 测试2: 检查模拟交易页面...');
        await page.goto('http://localhost:5173/trading/simulated', { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });

        // 等待Vue应用加载
        await new Promise(resolve => setTimeout(resolve, 3000));

        const simulatedPageInfo = await page.evaluate(() => {
            // 检查Vue应用状态
            const app = document.querySelector('#app');
            const vueInstance = app?.__vue__ || app?._vnode;
            
            // 检查所有可能的选择器
            const selectors = [
                '.simulated-trading-modern',
                '.simulated-trading',
                '.modern-header',
                '.trading-header',
                '.modern-search',
                '.search-input',
                '.account-dashboard',
                '.account-overview',
                '.trading-workspace',
                '.trading-main',
                '.left-panel',
                '.right-panel',
                '.trade-form',
                '.trading-forms'
            ];

            const foundElements = {};
            selectors.forEach(selector => {
                const element = document.querySelector(selector);
                foundElements[selector] = {
                    exists: !!element,
                    visible: element ? element.offsetParent !== null : false,
                    text: element ? element.textContent.substring(0, 100) : ''
                };
            });

            return {
                title: document.title,
                bodyContent: document.body.innerText.substring(0, 500),
                hasVueApp: !!vueInstance,
                elementCount: document.querySelectorAll('*').length,
                foundElements,
                allClasses: Array.from(document.querySelectorAll('[class]')).map(el => el.className).slice(0, 20)
            };
        });

        console.log('📊 模拟交易页面信息:', JSON.stringify(simulatedPageInfo, null, 2));
        await page.screenshot({ path: 'diagnosis_simulated.png', fullPage: true });

        console.log('\n🔴 测试3: 检查实盘交易页面...');
        await page.goto('http://localhost:5173/trading/live', { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });

        await new Promise(resolve => setTimeout(resolve, 3000));

        const livePageInfo = await page.evaluate(() => {
            const selectors = [
                '.live-trading',
                '.trading-terminal',
                '.modern-header',
                '.connection-status',
                '.order-panel',
                '.depth-panel'
            ];

            const foundElements = {};
            selectors.forEach(selector => {
                const element = document.querySelector(selector);
                foundElements[selector] = {
                    exists: !!element,
                    visible: element ? element.offsetParent !== null : false
                };
            });

            return {
                title: document.title,
                bodyContent: document.body.innerText.substring(0, 500),
                foundElements,
                allClasses: Array.from(document.querySelectorAll('[class]')).map(el => el.className).slice(0, 20)
            };
        });

        console.log('📊 实盘交易页面信息:', JSON.stringify(livePageInfo, null, 2));
        await page.screenshot({ path: 'diagnosis_live.png', fullPage: true });

        console.log('\n🧠 测试4: 检查网络请求...');
        
        // 监听网络请求
        const requests = [];
        page.on('request', request => {
            requests.push({
                url: request.url(),
                method: request.method(),
                resourceType: request.resourceType()
            });
        });

        // 重新加载模拟交易页面以捕获请求
        await page.goto('http://localhost:5173/trading/simulated', { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });

        console.log(`📡 捕获到 ${requests.length} 个网络请求`);
        
        // 分析请求类型
        const requestTypes = {};
        requests.forEach(req => {
            requestTypes[req.resourceType] = (requestTypes[req.resourceType] || 0) + 1;
        });
        
        console.log('📊 请求类型统计:', requestTypes);

        // 检查API请求
        const apiRequests = requests.filter(req => 
            req.url.includes('/api/') || 
            req.url.includes('localhost:8000') ||
            req.resourceType === 'xhr' ||
            req.resourceType === 'fetch'
        );

        console.log(`🔌 API请求 (${apiRequests.length}个):`, apiRequests);

        // 生成诊断报告
        const diagnosis = {
            timestamp: new Date().toISOString(),
            pages: {
                homepage: pageInfo,
                simulated: simulatedPageInfo,
                live: livePageInfo
            },
            network: {
                totalRequests: requests.length,
                requestTypes,
                apiRequests
            },
            errors,
            consoleMessages,
            issues: [],
            recommendations: []
        };

        // 分析问题
        if (simulatedPageInfo.foundElements['.simulated-trading-modern']?.exists === false &&
            simulatedPageInfo.foundElements['.simulated-trading']?.exists === false) {
            diagnosis.issues.push('模拟交易页面主容器未找到，可能是Vue组件未正确加载');
            diagnosis.recommendations.push('检查Vue路由配置和组件导入');
        }

        if (simulatedPageInfo.foundElements['.modern-header']?.exists === false &&
            simulatedPageInfo.foundElements['.trading-header']?.exists === false) {
            diagnosis.issues.push('交易页面头部组件未加载');
            diagnosis.recommendations.push('检查头部组件的CSS类名和渲染条件');
        }

        if (apiRequests.length === 0) {
            diagnosis.issues.push('未检测到API请求，后端可能未连接');
            diagnosis.recommendations.push('检查后端服务是否正常运行在8000端口');
        }

        if (errors.length > 0) {
            diagnosis.issues.push(`发现${errors.length}个JavaScript错误`);
            diagnosis.recommendations.push('修复JavaScript错误以确保页面正常功能');
        }

        // 保存诊断报告
        const reportFile = `diagnosis_report_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(diagnosis, null, 2));

        console.log('\n📋 诊断总结:');
        console.log(`❌ 发现问题: ${diagnosis.issues.length}个`);
        diagnosis.issues.forEach((issue, index) => {
            console.log(`  ${index + 1}. ${issue}`);
        });

        console.log(`💡 建议: ${diagnosis.recommendations.length}个`);
        diagnosis.recommendations.forEach((rec, index) => {
            console.log(`  ${index + 1}. ${rec}`);
        });

        console.log(`📄 详细报告: ${reportFile}`);
        console.log('📸 截图文件: diagnosis_homepage.png, diagnosis_simulated.png, diagnosis_live.png');

        // 保持浏览器打开
        console.log('\n🌐 浏览器保持打开状态，可以手动检查...');
        await new Promise(resolve => setTimeout(resolve, 10000));

    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error);
    }
}

diagnoseIssues().catch(console.error);
