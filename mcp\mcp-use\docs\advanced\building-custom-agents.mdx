---
title: Building Custom Agents
description: Learn how to build custom agents using MCPClient and integrate tools with different agent frameworks
icon: "paintbrush"
---

# Building Custom Agents

MCP-Use provides flexible options for building custom agents that can utilize MCP tools. This guide will show you how to create your own agents by leveraging the existing adapters, particularly focusing on the LangChain adapter.

<Info>
**Why Build Custom Agents?** While MCP-Use provides a built-in `MCPAgent` class, custom agents give you maximum flexibility to integrate with existing systems, implement specialized behavior, or use different agent frameworks.
</Info>

## Overview

MCP-Use allows you to:

<CardGroup cols={3}>
  <Card title="Access Tools" icon="plug">
    Connect to powerful MCP tools through flexible connectors
  </Card>
  <Card title="Convert & Adapt" icon="arrows-rotate">
    Transform MCP tools to work with any agent framework via adapters
  </Card>
  <Card title="Build Agents" icon="robot">
    Create specialized agents tailored to your specific use cases
  </Card>
</CardGroup>

## Using the LangChain Adapter

The `LangChainAdapter` is a powerful component that converts MCP tools to LangChain tools, enabling you to use MCP tools with any LangChain-compatible agent.

<Note>
**Simplified API**: The LangChain adapter provides a streamlined API that handles all the complexity of session management, connector initialization, and tool conversion automatically.
</Note>

### Basic Example

Here's a simple example of creating a custom agent using the LangChain adapter:

```python
import asyncio
from langchain_openai import ChatOpenAI
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder

from mcp_use.client import MCPClient
from mcp_use.adapters import LangChainAdapter

async def main():
    # Initialize the MCP client
    client = MCPClient.from_config_file("path/to/config.json")

    # Create adapter instance
    adapter = LangChainAdapter()

    # Get LangChain tools directly from the client with a single line
    tools = await adapter.create_tools(client)

    # Initialize your language model
    llm = ChatOpenAI(model="gpt-4o")

    # Create a prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a helpful assistant with access to powerful tools."),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])

    # Create the agent
    agent = create_tool_calling_agent(llm=llm, tools=tools, prompt=prompt)

    # Create the agent executor
    agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

    # Run the agent
    result = await agent_executor.ainvoke({"input": "What can you do?"})
    print(result["output"])

if __name__ == "__main__":
    asyncio.run(main())
```

<Tip>
**One-Line Tool Creation**: The API simplifies tool creation - all you need is to create an adapter instance and call its `create_tools` method:

```python
adapter = LangChainAdapter()
tools = await adapter.create_tools(client)
```

You don't need to worry about sessions, connectors, or initialization. The adapter handles everything for you.
</Tip>

## Contributing New Adapters

<Info>
MCP-Use welcomes contributions for integrating with different agent frameworks! The adapter architecture is designed to make this process straightforward and requires minimal implementation effort.
</Info>

### Adapter Architecture

MCP-Use provides a `BaseAdapter` abstract class that handles most of the common functionality:

<CardGroup cols={2}>
  <Card title="Automatic Handling" icon="magic">
    - Tool caching management
    - Connector initialization
    - Multi-connector iteration
  </Card>
  <Card title="Simple Implementation" icon="code">
    Only implement `_convert_tool` method to convert MCP tools to your framework's format
  </Card>
</CardGroup>

<Warning>
**Single Required Method**: To create an adapter for a new framework, you only need to implement one method: `_convert_tool` to convert a single MCP tool to your framework's tool format.
</Warning>

### Creating a New Adapter

Here's a simple template for creating a new adapter:

```python
from typing import Any

from mcp_use.adapters.base import BaseAdapter
from mcp_use.connectors.base import BaseConnector
from your_framework import YourFrameworkTool  # Import your framework's tool class

class YourFrameworkAdapter(BaseAdapter):
    """Adapter for converting MCP tools to YourFramework tools."""

    def _convert_tool(self, mcp_tool: dict[str, Any], connector: BaseConnector) -> YourFrameworkTool:
        """Convert an MCP tool to your framework's tool format.

        Args:
            mcp_tool: The MCP tool to convert.
            connector: The connector that provides this tool.

        Returns:
            A tool in your framework's format, or None if conversion failed.
        """
        try:
            # Implement your framework-specific conversion logic
            converted_tool = YourFrameworkTool(
                name=mcp_tool.name,
                description=mcp_tool.description,
                # Map the MCP tool properties to your framework's tool properties
                # You might need custom handling for argument schemas, function execution, etc.
            )

            return converted_tool
        except Exception as e:
            self.logger.error(f"Error converting tool {mcp_tool.name}: {e}")
            return None
```

### Using Your Custom Adapter

Once you've implemented your adapter, you can use it with the simplified API:

```python
from your_module import YourFrameworkAdapter
from mcp_use.client import MCPClient

# Initialize the client
client = MCPClient.from_config_file("config.json")

# Create an adapter instance
adapter = YourFrameworkAdapter()

# Get tools with a single line
tools = await adapter.create_tools(client)

# Use the tools with your framework
agent = your_framework.create_agent(tools=tools)
```

### Tips for Implementing an Adapter

<Accordion title="Implementation Guidelines">
  <AccordionItem title="Schema Conversion">
    Most frameworks have their own way of handling argument schemas. You'll need to convert the MCP tool's JSON Schema to your framework's format.

    <Tip>
    Look at the LangChain adapter implementation as a reference for handling schema conversion patterns.
    </Tip>
  </AccordionItem>

  <AccordionItem title="Tool Execution">
    When a tool is called in your framework, you'll need to pass the call to the connector's `call_tool` method and handle the result.

    <Warning>
    Always ensure proper async/await handling when calling MCP tools, as they are inherently asynchronous.
    </Warning>
  </AccordionItem>

  <AccordionItem title="Result Parsing">
    MCP tools return structured data with types like text, images, or embedded resources. Your adapter should parse these into a format your framework understands.
  </AccordionItem>

  <AccordionItem title="Error Handling">
    Ensure your adapter handles errors gracefully, both during tool conversion and execution.

    <Note>
    The base adapter provides logging utilities to help with error reporting and debugging.
    </Note>
  </AccordionItem>
</Accordion>


## Conclusion

<CardGroup cols={2}>
  <Card title="Maximum Flexibility" icon="expand">
    Build specialized agents tailored to your specific tasks or integrate MCP capabilities into existing systems
  </Card>
  <Card title="Simple Architecture" icon="puzzle-piece">
    Easy extension with minimal implementation - just one `_convert_tool` method needed
  </Card>
</CardGroup>

<Info>
**Key Benefits:**
- **Simplified API**: Create tools directly from MCPClient with a single method call
- **Automatic Management**: Session and connector complexity is handled automatically
- **Flexible Integration**: Works with any agent framework that has a LangChain-style interface
</Info>

<Tip>
**Contributing Back**: We welcome contributions to expand the adapter ecosystem! If you develop an adapter for a new framework, please consider contributing it back to the project to help the community.
</Tip>
