"""
简化的FastAPI应用用于测试认证功能
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 导入认证路由
from app.api.v1.auth_fixed import router as auth_router

app = FastAPI(title="测试应用", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册认证路由
app.include_router(auth_router, prefix="/api/v1/auth", tags=["认证"])

@app.get("/")
async def root():
    return {"message": "简化测试应用", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)