<template>
  <div class="trading-center">
    <!-- 顶部导航栏 -->
    <div class="trading-nav">
      <div class="nav-left">
        <h1 class="page-title">交易中心</h1>
        <div class="account-info">
          <el-tag type="success">模拟账户</el-tag>
          <span class="account-name">模拟账户001</span>
          <span class="available-funds">可用资金: ¥100,000.00</span>
        </div>
      </div>

      <div class="nav-right">
        <el-button-group>
          <el-button
            :type="activeModule === 'terminal' ? 'primary' : 'default'"
            :loading="moduleLoading && activeModule === 'terminal'"
            @click="switchModule('terminal')"
          >
            <el-icon><Monitor /></el-icon>
            交易终端
          </el-button>
          <el-button
            :type="activeModule === 'account' ? 'primary' : 'default'"
            :loading="moduleLoading && activeModule === 'account'"
            @click="switchModule('account')"
          >
            <el-icon><User /></el-icon>
            账户管理
          </el-button>
          <el-button
            :type="activeModule === 'data' ? 'primary' : 'default'"
            :loading="moduleLoading && activeModule === 'data'"
            @click="switchModule('data')"
          >
            <el-icon><DataBoard /></el-icon>
            数据中心
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="trading-content">
      <!-- 交易终端模块 -->
      <div v-if="activeModule === 'terminal'" class="module-container" key="terminal">
        <div class="module-wrapper">
          <TradingTerminalEnhanced
            :account="currentAccount"
            @order-placed="handleOrderPlaced"
            @account-switch="handleAccountSwitch"
          />
        </div>
      </div>

      <!-- 账户管理模块 -->
      <div v-if="activeModule === 'account'" class="module-container" key="account">
        <div class="module-wrapper">
          <AccountManagementEnhanced
            :current-account="currentAccount"
            @funds-transfer="handleFundsTransfer"
            @settings-change="handleSettingsChange"
          />
        </div>
      </div>

      <!-- 数据中心模块 -->
      <div v-if="activeModule === 'data'" class="module-container" key="data">
        <div class="module-wrapper">
          <DataCenterModule
            :account="currentAccount"
            :active-tab="dataActiveTab"
            @tab-change="handleDataTabChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor, User, DataBoard } from '@element-plus/icons-vue'
import DataCenterModule from './modules/DataCenterModule.vue'
import TradingTerminalEnhanced from './modules/TradingTerminalEnhanced.vue'
import AccountManagementEnhanced from './modules/AccountManagementEnhanced.vue'

// 响应式数据
const activeModule = ref<'terminal' | 'account' | 'data'>('terminal')
const dataActiveTab = ref<'orders' | 'positions' | 'trades'>('orders')
const moduleLoading = ref(false)

// 模拟账户数据
const currentAccount = ref({
  id: 'sim_001',
  type: 'simulation',
  name: '模拟账户001',
  availableFunds: 100000,
  totalAssets: 150000,
  status: 'active'
})

// 方法
const switchModule = async (module: 'terminal' | 'account' | 'data') => {
  if (activeModule.value === module) return

  moduleLoading.value = true

  try {
    // 模拟模块加载延迟
    await new Promise(resolve => setTimeout(resolve, 200))

    activeModule.value = module

    const moduleNames = {
      terminal: '交易终端',
      account: '账户管理',
      data: '数据中心'
    }

    ElMessage.success(`切换到${moduleNames[module]}`)
    console.log(`用户切换到${module}模块`)

  } catch (error) {
    ElMessage.error('模块切换失败')
    console.error('模块切换错误:', error)
  } finally {
    moduleLoading.value = false
  }
}

const handleDataTabChange = (tab: 'orders' | 'positions' | 'trades') => {
  dataActiveTab.value = tab
  console.log(`数据中心切换到${tab}标签页`)
}

const handleOrderPlaced = (orderData: any) => {
  console.log('订单已提交:', orderData)
  ElMessage.success(`${orderData.side === 'buy' ? '买入' : '卖出'}订单提交成功`)

  // 这里可以添加订单到数据中心
  // 实际项目中会调用API保存订单
}

const handleAccountSwitch = (accountType: 'simulation' | 'real') => {
  currentAccount.value.type = accountType
  console.log(`账户切换到${accountType}模式`)
}

const handleFundsTransfer = (transferData: any) => {
  console.log('资金划转:', transferData)

  // 模拟更新账户资金
  if (transferData.type === 'bank-to-stock') {
    currentAccount.value.availableFunds += transferData.amount
    currentAccount.value.totalAssets += transferData.amount
  } else if (transferData.type === 'stock-to-bank') {
    currentAccount.value.availableFunds -= transferData.amount
    currentAccount.value.totalAssets -= transferData.amount
  }

  ElMessage.success('资金划转成功')
}

const handleSettingsChange = (settings: any) => {
  console.log('设置变更:', settings)
  ElMessage.success('设置已保存')
}
</script>

<style scoped lang="scss">
.trading-center {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.trading-nav {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px;

  .page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .account-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .account-name {
      font-weight: 500;
      color: #606266;
    }

    .available-funds {
      color: #67c23a;
      font-weight: 600;
    }
  }
}

.nav-right {
  .el-button-group {
    .el-button {
      padding: 10px 16px;

      .el-icon {
        margin-right: 6px;
      }
    }
  }
}

.trading-content {
  flex: 1;
  padding: 24px;
  overflow: hidden;
}

.module-container {
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .module-wrapper {
    height: 100%;
    width: 100%;
    overflow: auto;
  }
}

.simple-module {
  padding: 40px;
  text-align: center;

  h2 {
    margin: 0 0 16px 0;
    color: #303133;
  }

  p {
    margin: 0 0 24px 0;
    color: #606266;
    font-size: 16px;
  }

  .el-button {
    margin: 0 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .trading-nav {
    flex-direction: column;
    gap: 16px;

    .nav-left {
      .account-info {
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .nav-right {
      width: 100%;

      .el-button-group {
        width: 100%;
        display: flex;

        .el-button {
          flex: 1;
          padding: 10px 12px;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
