"""
WebSocket市场数据路由
提供实时行情推送服务
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.auth import verify_token
from app.services.websocket_manager import manager, realtime_pusher

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer(auto_error=False)

async def get_current_user_ws(
    websocket: WebSocket,
    token: Optional[str] = Query(None)
):
    """WebSocket认证"""
    if not token:
        await websocket.close(code=1008, reason="Missing authentication")
        return None
    
    try:
        # 验证token
        payload = verify_token(token)
        return payload.get("sub")  # 返回用户ID
    except Exception as e:
        logger.error(f"WebSocket authentication failed: {e}")
        await websocket.close(code=1008, reason="Invalid authentication")
        return None

@router.websocket("/ws/market")
async def websocket_market_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None)
):
    """
    WebSocket市场数据端点
    
    支持的消息类型：
    - subscribe: 订阅股票 {"type": "subscribe", "symbols": ["600519", "000858"]}
    - unsubscribe: 取消订阅 {"type": "unsubscribe", "symbols": ["600519"]}
    - ping: 心跳检测 {"type": "ping"}
    
    推送的数据类型：
    - quote: 实时行情
    - orderbook: 订单簿
    - trades: 成交明细
    """
    # 验证用户
    user_id = await get_current_user_ws(websocket, token)
    if not user_id:
        return
    
    # 生成客户端ID
    client_id = f"user_{user_id}_{datetime.now().timestamp()}"
    
    # 连接客户端
    await manager.connect(client_id, websocket)
    
    try:
        # 处理客户端消息
        while True:
            # 接收消息
            data = await websocket.receive_text()
            
            try:
                # 解析JSON消息
                message = json.loads(data)
                
                # 处理消息
                await manager.handle_client_message(client_id, message)
                
            except json.JSONDecodeError:
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.now().isoformat()
                    },
                    client_id
                )
            except Exception as e:
                logger.error(f"Error handling message from {client_id}: {e}")
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    },
                    client_id
                )
                
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected normally")
    except Exception as e:
        logger.error(f"WebSocket error for {client_id}: {e}")
    finally:
        # 断开连接
        await manager.disconnect(client_id)

@router.websocket("/api/v1/ws/market/public")
async def websocket_public_market_endpoint(websocket: WebSocket):
    """
    公开的WebSocket市场数据端点（无需认证）
    
    功能受限：
    - 只能订阅有限的股票
    - 数据推送频率较低
    """
    # 生成客户端ID
    client_id = f"public_{datetime.now().timestamp()}"
    
    # 连接客户端
    await manager.connect(client_id, websocket)
    
    # 限制公开用户只能订阅特定股票
    allowed_symbols = ["000001", "600519", "000858", "399001", "000001.SH"]
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            
            try:
                # 解析JSON消息
                message = json.loads(data)
                
                # 过滤订阅请求
                if message.get("type") == "subscribe":
                    symbols = message.get("symbols", [])
                    # 只允许订阅白名单中的股票
                    filtered_symbols = [s for s in symbols if s in allowed_symbols]
                    message["symbols"] = filtered_symbols
                
                # 处理消息
                await manager.handle_client_message(client_id, message)
                
            except json.JSONDecodeError:
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.now().isoformat()
                    },
                    client_id
                )
                
    except WebSocketDisconnect:
        logger.info(f"Public client {client_id} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error for public client {client_id}: {e}")
    finally:
        # 断开连接
        await manager.disconnect(client_id)

@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计信息"""
    return {
        "stats": manager.get_stats(),
        "clients": manager.get_client_info(),
        "subscribed_symbols": manager.get_subscribed_symbols()
    }

# 启动和关闭事件
async def start_websocket_services():
    """启动WebSocket相关服务"""
    await realtime_pusher.start()
    logger.info("WebSocket services started")

async def stop_websocket_services():
    """停止WebSocket相关服务"""
    await realtime_pusher.stop()
    logger.info("WebSocket services stopped")