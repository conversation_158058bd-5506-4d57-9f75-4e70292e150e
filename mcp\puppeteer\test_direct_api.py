#!/usr/bin/env python3
"""
直接测试API调用
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

async def test_direct_api():
    """直接在浏览器中测试API调用"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 直接测试API调用...")
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 在浏览器控制台中直接调用登录API
        print("🔧 在浏览器中直接调用登录API...")
        
        api_result = await page.evaluate('''
            async () => {
                try {
                    console.log('开始直接API调用测试...');
                    
                    // 直接使用fetch调用登录API
                    const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: 'admin',
                            password: 'admin123'
                        })
                    });
                    
                    console.log('API响应状态:', response.status);
                    
                    if (response.ok) {
                        const data = await response.json();
                        console.log('API响应数据:', data);
                        return {
                            success: true,
                            status: response.status,
                            data: data
                        };
                    } else {
                        const errorText = await response.text();
                        console.log('API错误响应:', errorText);
                        return {
                            success: false,
                            status: response.status,
                            error: errorText
                        };
                    }
                } catch (error) {
                    console.error('API调用失败:', error);
                    return {
                        success: false,
                        error: error.message
                    };
                }
            }
        ''')
        
        print(f"📥 API调用结果: {api_result}")
        
        if api_result.get('success'):
            print("✅ 直接API调用成功！")
            print(f"   - 状态码: {api_result.get('status')}")
            print(f"   - 响应数据: {api_result.get('data')}")
            
            # 现在测试通过前端组件的登录
            print("🔧 现在测试通过前端组件的登录...")
            
            # 等待演示登录按钮
            await page.wait_for_selector('button:has-text("演示登录")', timeout=10000)
            
            # 等待按钮可用
            await page.wait_for_function('''
                () => {
                    const buttons = document.querySelectorAll('button');
                    const demoButton = Array.from(buttons).find(btn => btn.textContent.includes('演示登录'));
                    return demoButton && !demoButton.disabled && !demoButton.classList.contains('is-loading');
                }
            ''', timeout=10000)
            
            print("✅ 演示登录按钮可用")
            
            # 监听网络请求
            requests = []
            
            async def handle_request(request):
                if '/auth/login' in request.url:
                    requests.append({
                        "url": request.url,
                        "method": request.method,
                        "timestamp": datetime.now().isoformat()
                    })
                    print(f"📡 拦截到登录请求: {request.method} {request.url}")
            
            page.on('request', handle_request)
            
            # 点击演示登录按钮
            demo_button = await page.query_selector('button:has-text("演示登录")')
            await demo_button.click()
            print("🖱️ 已点击演示登录按钮")
            
            # 等待处理
            await page.wait_for_timeout(5000)
            
            print(f"📊 拦截到的请求数量: {len(requests)}")
            for req in requests:
                print(f"   - {req}")
                
        else:
            print("❌ 直接API调用失败")
            print(f"   - 错误: {api_result.get('error')}")
        
        # 截图
        screenshot_name = f"direct_api_test_{datetime.now().strftime('%H%M%S')}.png"
        await page.screenshot(path=screenshot_name, full_page=True)
        print(f"📸 测试截图已保存: {screenshot_name}")
        
    except Exception as e:
        print(f"💥 测试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_direct_api())
