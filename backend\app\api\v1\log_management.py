"""
日志和错误上报API端点
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

from fastapi import APIRouter, HTTPException, Depends, Request
from app.core.logging_config import (
    get_contextual_logger,
    log_security_event,
    set_request_context
)
from app.monitoring.metrics_collector import record_error, record_custom_metric
from app.monitoring.alerting import trigger_custom_alert, AlertSeverity


router = APIRouter(prefix="/logs", tags=["logging"])
logger = get_contextual_logger(__name__)


class LogEntry(BaseModel):
    level: str = Field(..., description="日志级别")
    message: str = Field(..., description="日志消息")
    timestamp: str = Field(..., description="时间戳")
    context: Dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    url: str = Field(..., description="页面URL")
    userAgent: str = Field(..., description="用户代理")
    userId: Optional[str] = Field(None, description="用户ID")
    sessionId: Optional[str] = Field(None, description="会话ID")


class ErrorReport(BaseModel):
    type: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    stack: Optional[str] = Field(None, description="错误堆栈")
    url: str = Field(..., description="页面URL")
    userAgent: str = Field(..., description="用户代理")
    timestamp: str = Field(..., description="时间戳")
    userId: Optional[str] = Field(None, description="用户ID")
    sessionId: Optional[str] = Field(None, description="会话ID")
    component: Optional[str] = Field(None, description="组件名称")
    severity: str = Field(default="medium", description="严重程度")
    fingerprint: Optional[str] = Field(None, description="错误指纹")
    details: Dict[str, Any] = Field(default_factory=dict, description="详细信息")


class BatchLogRequest(BaseModel):
    logs: List[LogEntry] = Field(..., description="日志条目列表")
    timestamp: str = Field(..., description="批次时间戳")


class BatchErrorRequest(BaseModel):
    errors: List[ErrorReport] = Field(..., description="错误报告列表")
    timestamp: str = Field(..., description="批次时间戳")


class MetricReport(BaseModel):
    name: str = Field(..., description="指标名称")
    value: float = Field(..., description="指标值")
    labels: Dict[str, str] = Field(default_factory=dict, description="标签")
    timestamp: str = Field(..., description="时间戳")


@router.post("/frontend", summary="接收前端日志")
async def receive_frontend_logs(
    request_data: BatchLogRequest,
    request: Request
):
    """接收前端批量日志"""
    try:
        # 设置请求上下文
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        processed_count = 0
        for log_entry in request_data.logs:
            try:
                # 设置用户上下文
                if log_entry.userId:
                    set_request_context(request_id, log_entry.userId, log_entry.sessionId)
                
                # 解析日志级别
                level = log_entry.level.lower()
                if level not in ['debug', 'info', 'warning', 'error', 'critical']:
                    level = 'info'
                
                # 构造日志数据
                log_data = {
                    **log_entry.context,
                    "source": "frontend",
                    "url": log_entry.url,
                    "user_agent": log_entry.userAgent,
                    "frontend_timestamp": log_entry.timestamp,
                    "session_id": log_entry.sessionId
                }
                
                # 记录日志
                getattr(logger, level)(
                    f"[Frontend] {log_entry.message}",
                    **log_data
                )
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Failed to process log entry: {e}")
                continue
        
        logger.info(
            f"Processed {processed_count}/{len(request_data.logs)} frontend log entries",
            processed_count=processed_count,
            total_count=len(request_data.logs)
        )
        
        return {
            "status": "success",
            "processed": processed_count,
            "total": len(request_data.logs)
        }
        
    except Exception as e:
        logger.error(f"Failed to process frontend logs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/errors", summary="接收前端错误报告")
async def receive_error_reports(
    request_data: BatchErrorRequest,
    request: Request
):
    """接收前端批量错误报告"""
    try:
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        processed_count = 0
        critical_errors = 0
        
        for error_report in request_data.errors:
            try:
                # 设置用户上下文
                if error_report.userId:
                    set_request_context(request_id, error_report.userId, error_report.sessionId)
                
                # 记录错误指标
                record_error(
                    error_type=error_report.type,
                    severity=error_report.severity,
                    component=error_report.component or "frontend"
                )
                
                # 构造错误日志数据
                error_data = {
                    **error_report.details,
                    "error_type": error_report.type,
                    "source": "frontend",
                    "url": error_report.url,
                    "user_agent": error_report.userAgent,
                    "frontend_timestamp": error_report.timestamp,
                    "session_id": error_report.sessionId,
                    "component": error_report.component,
                    "severity": error_report.severity,
                    "fingerprint": error_report.fingerprint
                }
                
                # 记录错误日志
                if error_report.stack:
                    error_data["stack_trace"] = error_report.stack
                
                logger.error(
                    f"[Frontend Error] {error_report.message}",
                    **error_data
                )
                
                # 对于严重错误，触发自定义报警
                if error_report.severity in ['critical', 'high']:
                    critical_errors += 1
                    
                    # 检查是否需要触发报警
                    if critical_errors > 5:  # 5个严重错误触发报警
                        severity_map = {
                            'critical': AlertSeverity.CRITICAL,
                            'high': AlertSeverity.HIGH,
                            'medium': AlertSeverity.MEDIUM,
                            'low': AlertSeverity.LOW
                        }
                        
                        trigger_custom_alert(
                            name="frontend_critical_errors",
                            message=f"前端发生多个严重错误: {critical_errors}个",
                            severity=severity_map.get(error_report.severity, AlertSeverity.MEDIUM),
                            metadata={
                                "error_count": critical_errors,
                                "latest_error": error_report.message,
                                "url": error_report.url,
                                "component": error_report.component
                            }
                        )
                
                # 安全相关错误需要特别记录
                if error_report.type in ['security', 'authentication', 'authorization']:
                    log_security_event(
                        event_type=f"frontend_{error_report.type}_error",
                        severity=error_report.severity,
                        details={
                            "message": error_report.message,
                            "url": error_report.url,
                            "component": error_report.component,
                            "user_agent": error_report.userAgent
                        },
                        user_id=error_report.userId
                    )
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Failed to process error report: {e}")
                continue
        
        logger.info(
            f"Processed {processed_count}/{len(request_data.errors)} error reports",
            processed_count=processed_count,
            total_count=len(request_data.errors),
            critical_errors=critical_errors
        )
        
        return {
            "status": "success",
            "processed": processed_count,
            "total": len(request_data.errors),
            "critical_errors": critical_errors
        }
        
    except Exception as e:
        logger.error(f"Failed to process error reports: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/metrics", summary="接收前端指标")
async def receive_frontend_metrics(
    metrics: List[MetricReport],
    request: Request
):
    """接收前端性能指标"""
    try:
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        processed_count = 0
        for metric in metrics:
            try:
                # 记录自定义指标
                record_custom_metric(
                    name=f"frontend_{metric.name}",
                    value=metric.value,
                    labels={
                        **metric.labels,
                        "source": "frontend"
                    }
                )
                
                # 记录到日志
                logger.info(
                    f"Frontend metric: {metric.name} = {metric.value}",
                    metric_name=metric.name,
                    metric_value=metric.value,
                    labels=metric.labels,
                    frontend_timestamp=metric.timestamp
                )
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Failed to process metric: {e}")
                continue
        
        return {
            "status": "success",
            "processed": processed_count,
            "total": len(metrics)
        }
        
    except Exception as e:
        logger.error(f"Failed to process frontend metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health", summary="日志服务健康检查")
async def health_check():
    """日志服务健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "logging-api"
    }