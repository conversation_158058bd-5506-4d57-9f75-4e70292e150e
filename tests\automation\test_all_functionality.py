#!/usr/bin/env python3
"""
全面功能检查脚本 - 测试所有API和功能
Consumer Level Testing Script
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Any

class ConsumerTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        self.auth_token = None
        
    async def start_session(self):
        """启动HTTP会话"""
        self.session = aiohttp.ClientSession()
        
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, method: str, endpoint: str, data: Dict = None, headers: Dict = None) -> Dict:
        """测试单个API端点"""
        url = f"{self.base_url}{endpoint}"
        result = {
            "endpoint": endpoint,
            "method": method,
            "url": url,
            "success": False,
            "status_code": None,
            "response_time": 0,
            "error": None,
            "response_data": None
        }
        
        try:
            start_time = time.time()
            
            if method.upper() == "GET":
                async with self.session.get(url, headers=headers) as response:
                    result["status_code"] = response.status
                    result["response_data"] = await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data, headers=headers) as response:
                    result["status_code"] = response.status
                    result["response_data"] = await response.json()
            elif method.upper() == "PUT":
                async with self.session.put(url, json=data, headers=headers) as response:
                    result["status_code"] = response.status
                    result["response_data"] = await response.json()
            elif method.upper() == "DELETE":
                async with self.session.delete(url, headers=headers) as response:
                    result["status_code"] = response.status
                    result["response_data"] = await response.json()
            
            result["response_time"] = time.time() - start_time
            result["success"] = 200 <= result["status_code"] < 300
            
        except Exception as e:
            result["error"] = str(e)
            result["response_time"] = time.time() - start_time
            
        return result
    
    async def test_authentication(self):
        """测试认证系统"""
        print("🔐 测试认证系统...")
        
        # 测试注册
        register_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "test123456"
        }
        
        result = await self.test_endpoint("POST", "/api/v1/auth/register", register_data)
        self.test_results.append(result)
        print(f"  注册: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试登录
        login_data = {
            "username": "test_user",
            "password": "test123456"
        }
        
        result = await self.test_endpoint("POST", "/api/v1/auth/login", login_data)
        self.test_results.append(result)
        print(f"  登录: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        if result['success'] and result['response_data']:
            self.auth_token = result['response_data'].get('access_token')
    
    async def test_market_data(self):
        """测试市场数据功能"""
        print("📈 测试市场数据功能...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {}
        
        # 测试股票列表
        result = await self.test_endpoint("GET", "/api/v1/market/stocks", headers=headers)
        self.test_results.append(result)
        print(f"  股票列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试实时行情
        result = await self.test_endpoint("GET", "/api/v1/market/quotes/000001", headers=headers)
        self.test_results.append(result)
        print(f"  实时行情: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试K线数据
        result = await self.test_endpoint("GET", "/api/v1/market/kline/000001?period=1d&limit=100", headers=headers)
        self.test_results.append(result)
        print(f"  K线数据: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试市场深度
        result = await self.test_endpoint("GET", "/api/v1/market/depth/000001", headers=headers)
        self.test_results.append(result)
        print(f"  市场深度: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    async def test_trading_terminal(self):
        """测试交易终端功能"""
        print("💰 测试交易终端功能...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {}
        
        # 测试终端概览
        result = await self.test_endpoint("GET", "/api/v1/trading/overview", headers=headers)
        self.test_results.append(result)
        print(f"  交易概览: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试快速下单
        order_data = {
            "symbol": "000001",
            "side": "buy",
            "order_type": "limit",
            "quantity": 100,
            "price": 15.50
        }
        
        result = await self.test_endpoint("POST", "/api/v1/trading/quick-order", order_data, headers)
        self.test_results.append(result)
        print(f"  快速下单: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试持仓查询
        result = await self.test_endpoint("GET", "/api/v1/trading/positions", headers=headers)
        self.test_results.append(result)
        print(f"  持仓查询: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    async def test_order_management(self):
        """测试订单管理功能"""
        print("📋 测试订单管理功能...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {}
        
        # 测试订单列表
        result = await self.test_endpoint("GET", "/api/v1/orders?page=1&size=20", headers=headers)
        self.test_results.append(result)
        print(f"  订单列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试订单详情
        result = await self.test_endpoint("GET", "/api/v1/orders/1", headers=headers)
        self.test_results.append(result)
        print(f"  订单详情: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试订单修改
        modify_data = {
            "price": 16.00,
            "quantity": 200
        }
        
        result = await self.test_endpoint("PUT", "/api/v1/orders/1", modify_data, headers)
        self.test_results.append(result)
        print(f"  订单修改: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    async def test_strategy_development(self):
        """测试策略开发功能"""
        print("🧠 测试策略开发功能...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {}
        
        # 测试策略列表
        result = await self.test_endpoint("GET", "/api/v1/strategies", headers=headers)
        self.test_results.append(result)
        print(f"  策略列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试创建策略
        strategy_data = {
            "name": "测试策略",
            "description": "这是一个测试策略",
            "code": "# 策略代码示例\nprint('Hello Strategy')",
            "parameters": {"param1": "value1"}
        }
        
        result = await self.test_endpoint("POST", "/api/v1/strategies", strategy_data, headers)
        self.test_results.append(result)
        print(f"  创建策略: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试回测
        backtest_data = {
            "strategy_id": 1,
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 100000
        }
        
        result = await self.test_endpoint("POST", "/api/v1/strategies/backtest", backtest_data, headers)
        self.test_results.append(result)
        print(f"  策略回测: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    async def test_historical_data(self):
        """测试历史数据功能"""
        print("📚 测试历史数据功能...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {}
        
        # 测试历史股票列表
        result = await self.test_endpoint("GET", "/api/v1/historical/stocks", headers=headers)
        self.test_results.append(result)
        print(f"  历史股票列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试历史数据查询
        result = await self.test_endpoint("GET", "/api/v1/historical/data/000001?start_date=2024-01-01&end_date=2024-12-31", headers=headers)
        self.test_results.append(result)
        print(f"  历史数据查询: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 功能测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {success_rate:.1f}%")
        
        print("\n" + "-"*60)
        print("详细结果:")
        print("-"*60)
        
        for result in self.test_results:
            status_icon = "✅" if result['success'] else "❌"
            print(f"{status_icon} {result['method']} {result['endpoint']} - {result['status_code']}")
            if result['error']:
                print(f"    错误: {result['error']}")
            if result['response_time'] > 1:
                print(f"    响应时间: {result['response_time']:.2f}s (较慢)")
        
        print("\n" + "="*60)
        
        # 问题分析
        print("🔍 问题分析:")
        print("="*60)
        
        error_405_count = sum(1 for result in self.test_results if result['status_code'] == 405)
        error_404_count = sum(1 for result in self.test_results if result['status_code'] == 404)
        error_500_count = sum(1 for result in self.test_results if result['status_code'] == 500)
        
        if error_405_count > 0:
            print(f"❌ 发现 {error_405_count} 个 405 错误 (方法不允许)")
        if error_404_count > 0:
            print(f"❌ 发现 {error_404_count} 个 404 错误 (路径不存在)")
        if error_500_count > 0:
            print(f"❌ 发现 {error_500_count} 个 500 错误 (服务器内部错误)")
        
        if failed_tests == 0:
            print("🎉 所有功能正常运行!")
        else:
            print(f"⚠️  发现 {failed_tests} 个问题需要修复")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始全面功能检查...")
        print("="*60)
        
        await self.start_session()
        
        try:
            await self.test_authentication()
            await self.test_market_data()
            await self.test_trading_terminal()
            await self.test_order_management()
            await self.test_strategy_development()
            await self.test_historical_data()
            
            self.generate_report()
            
        finally:
            await self.close_session()

async def main():
    """主函数"""
    tester = ConsumerTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())