"""
WebSocket端点配置示例
展示如何在FastAPI应用中集成修复后的WebSocket功能
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession

# 导入修复后的WebSocket组件
from app.core.websocket import ws_service, simple_websocket_endpoint
from app.api.websocket.handlers import (
    handle_general_websocket,
    handle_trading_websocket,
    handle_market_data_websocket,
    handle_strategy_websocket
)
from app.core.dependencies import get_current_user_from_websocket
from app.core.database import get_db
from app.db.models.user import User

app = FastAPI()

# WebSocket端点配置
@app.websocket("/ws/simple")
async def websocket_simple_endpoint(websocket: WebSocket):
    """简单WebSocket端点，无需认证"""
    await simple_websocket_endpoint(websocket)


@app.websocket("/ws/general")
async def websocket_general_endpoint(websocket: WebSocket, token: str = None):
    """通用WebSocket端点，支持多种消息类型"""
    await handle_general_websocket(websocket, token)


@app.websocket("/ws/trading")
async def websocket_trading_endpoint(
    websocket: WebSocket,
    user: User = Depends(get_current_user_from_websocket),
    db: AsyncSession = Depends(get_db)
):
    """交易WebSocket端点，需要用户认证"""
    await handle_trading_websocket(websocket, user, db)


@app.websocket("/ws/market")
async def websocket_market_endpoint(
    websocket: WebSocket,
    user: User = Depends(get_current_user_from_websocket),
    db: AsyncSession = Depends(get_db)
):
    """行情数据WebSocket端点"""
    await handle_market_data_websocket(websocket, user, db)


@app.websocket("/ws/strategy")
async def websocket_strategy_endpoint(
    websocket: WebSocket,
    user: User = Depends(get_current_user_from_websocket),
    db: AsyncSession = Depends(get_db)
):
    """策略WebSocket端点"""
    await handle_strategy_websocket(websocket, user, db)


@app.websocket("/ws/core/{client_id}")
async def websocket_core_endpoint(websocket: WebSocket, client_id: str):
    """使用核心WebSocket服务的端点"""
    await ws_service.handle_connection(websocket, client_id)


# WebSocket统计和管理端点
from app.api.websocket.connection import websocket_manager

@app.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    return {
        "manager_stats": websocket_manager.get_connection_stats(),
        "core_stats": ws_service.get_connection_info()
    }


# 广播消息示例
@app.post("/ws/broadcast")
async def broadcast_message(topic: str, message: dict):
    """向指定主题广播消息"""
    await websocket_manager.broadcast_to_topic(topic, message)
    return {"success": True, "message": f"消息已广播到主题 {topic}"}


@app.post("/ws/user/{user_id}/send")
async def send_user_message(user_id: int, message: dict):
    """向指定用户发送消息"""
    await websocket_manager.send_to_user(user_id, message)
    return {"success": True, "message": f"消息已发送给用户 {user_id}"}


if __name__ == "__main__":
    import uvicorn
    print("WebSocket端点配置示例")
    print("可用的WebSocket端点:")
    print("- /ws/simple        - 简单WebSocket连接")
    print("- /ws/general       - 通用WebSocket连接")
    print("- /ws/trading       - 交易WebSocket连接")
    print("- /ws/market        - 行情WebSocket连接")
    print("- /ws/strategy      - 策略WebSocket连接")
    print("- /ws/core/{id}     - 核心WebSocket服务")
    print()
    print("HTTP端点:")
    print("- GET /ws/stats                    - 获取连接统计")
    print("- POST /ws/broadcast               - 广播消息")
    print("- POST /ws/user/{user_id}/send     - 发送用户消息")
    print()
    print("注意: 这个文件仅为示例，不要直接运行")