#!/usr/bin/env python3
"""
全面策略链接审计 - 检查所有策略相关页面的链接状态
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveStrategyAudit:
    def __init__(self):
        self.session_id = f"comprehensive_audit_{int(datetime.now().timestamp())}"
        self.browser = None
        self.page = None
        self.audit_results = {
            'session_id': self.session_id,
            'start_time': datetime.now().isoformat(),
            'pages_tested': [],
            'total_links_found': 0,
            'working_links': 0,
            'broken_links': 0,
            'issues_summary': [],
            'recommendations': []
        }
        
    async def initialize(self):
        """初始化浏览器"""
        logger.info("🔧 初始化MCP浏览器工具...")
        
        Path('screenshots').mkdir(exist_ok=True)
        Path('audit_reports').mkdir(exist_ok=True)
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
    async def test_strategy_pages(self):
        """测试所有策略相关页面"""
        logger.info("🔍 开始全面策略页面审计...")
        
        # 定义要测试的策略页面
        strategy_pages = [
            {
                'name': '策略中心',
                'url': 'http://localhost:5173/strategy/center',
                'expected_title': '策略中心'
            },
            {
                'name': '策略详情-mock-1',
                'url': 'http://localhost:5173/strategy/detail/mock-1',
                'expected_title': '策略详情'
            },
            {
                'name': '策略详情-mock-2',
                'url': 'http://localhost:5173/strategy/detail/mock-2',
                'expected_title': '策略详情'
            },
            {
                'name': '策略详情-test-1',
                'url': 'http://localhost:5173/strategy/detail/test-1',
                'expected_title': '策略详情'
            },
            {
                'name': '策略开发',
                'url': 'http://localhost:5173/strategy/development',
                'expected_title': '策略开发'
            },
            {
                'name': '策略监控',
                'url': 'http://localhost:5173/strategy/monitor',
                'expected_title': '策略监控'
            },
            {
                'name': '策略文库',
                'url': 'http://localhost:5173/strategy/library',
                'expected_title': '策略文库'
            }
        ]
        
        for page_config in strategy_pages:
            await self.test_single_page(page_config)
        
        logger.info("✅ 全面策略页面审计完成")

    async def test_single_page(self, page_config):
        """测试单个页面"""
        logger.info(f"🌐 测试页面: {page_config['name']}")
        
        page_result = {
            'name': page_config['name'],
            'url': page_config['url'],
            'expected_title': page_config['expected_title'],
            'actual_title': '',
            'status': 'unknown',
            'content_length': 0,
            'clickable_elements': 0,
            'issues': [],
            'screenshot_path': ''
        }
        
        try:
            # 导航到页面
            await self.page.goto(page_config['url'], wait_until='networkidle', timeout=30000)
            await asyncio.sleep(3)
            
            # 获取页面信息
            page_info = await self.page.evaluate("""
                () => {
                    return {
                        title: document.title,
                        contentLength: document.body ? document.body.innerHTML.length : 0,
                        hasVueApp: !!document.querySelector('#app'),
                        vueAppContent: document.querySelector('#app') ? document.querySelector('#app').innerHTML.length : 0
                    };
                }
            """)
            
            page_result['actual_title'] = page_info['title']
            page_result['content_length'] = page_info['contentLength']
            
            # 检查页面状态
            if '页面不存在' in page_info['title']:
                page_result['status'] = 'not_found'
                page_result['issues'].append({
                    'type': 'page_not_found',
                    'severity': 'high',
                    'message': '页面不存在，返回404错误页面'
                })
                self.audit_results['broken_links'] += 1
                
            elif page_config['expected_title'] in page_info['title']:
                page_result['status'] = 'working'
                self.audit_results['working_links'] += 1
                
                # 检查可点击元素
                clickable_count = await self.count_clickable_elements()
                page_result['clickable_elements'] = clickable_count
                
                if clickable_count == 0:
                    page_result['issues'].append({
                        'type': 'no_clickable_elements',
                        'severity': 'medium',
                        'message': '页面没有找到可点击元素'
                    })
                
            else:
                page_result['status'] = 'unexpected_content'
                page_result['issues'].append({
                    'type': 'unexpected_title',
                    'severity': 'medium',
                    'message': f'页面标题不符合预期。期望: {page_config["expected_title"]}, 实际: {page_info["title"]}'
                })
            
            # 截图
            screenshot_path = f'screenshots/{self.session_id}_{page_config["name"]}.png'
            await self.page.screenshot(path=screenshot_path)
            page_result['screenshot_path'] = screenshot_path
            
        except Exception as e:
            page_result['status'] = 'error'
            page_result['issues'].append({
                'type': 'navigation_error',
                'severity': 'high',
                'message': f'页面访问失败: {str(e)}'
            })
            self.audit_results['broken_links'] += 1
            logger.error(f"❌ 页面测试失败: {e}")
        
        self.audit_results['pages_tested'].append(page_result)
        self.audit_results['total_links_found'] += 1
        
        # 输出测试结果
        status_emoji = {
            'working': '✅',
            'not_found': '❌',
            'error': '🚨',
            'unexpected_content': '⚠️'
        }
        
        logger.info(f"  {status_emoji.get(page_result['status'], '❓')} {page_config['name']}: {page_result['status']}")
        if page_result['issues']:
            for issue in page_result['issues']:
                logger.info(f"    - {issue['message']}")

    async def count_clickable_elements(self):
        """统计可点击元素数量"""
        try:
            count = await self.page.evaluate("""
                () => {
                    const selectors = [
                        'a[href]', 'button', '[role="button"]', '[onclick]',
                        '.el-button', '.btn', 'div[class*="card"]', 'div[class*="item"]'
                    ];
                    
                    let totalCount = 0;
                    const seen = new Set();
                    
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                const rect = el.getBoundingClientRect();
                                const style = window.getComputedStyle(el);
                                
                                if (rect.width > 0 && rect.height > 0 && 
                                    style.display !== 'none' && 
                                    style.visibility !== 'hidden') {
                                    
                                    const key = `${Math.round(rect.x)},${Math.round(rect.y)}`;
                                    if (!seen.has(key)) {
                                        seen.add(key);
                                        totalCount++;
                                    }
                                }
                            });
                        } catch (e) {
                            console.warn('Error with selector:', selector, e);
                        }
                    });
                    
                    return totalCount;
                }
            """)
            
            return count
            
        except Exception as e:
            logger.warning(f"统计可点击元素失败: {e}")
            return 0

    async def generate_comprehensive_report(self):
        """生成综合审计报告"""
        logger.info("📋 生成综合审计报告...")
        
        # 统计问题
        all_issues = []
        for page in self.audit_results['pages_tested']:
            for issue in page['issues']:
                issue['page'] = page['name']
                all_issues.append(issue)
        
        # 按严重程度分类
        high_issues = [i for i in all_issues if i.get('severity') == 'high']
        medium_issues = [i for i in all_issues if i.get('severity') == 'medium']
        low_issues = [i for i in all_issues if i.get('severity') == 'low']
        
        # 生成建议
        recommendations = []
        
        if high_issues:
            recommendations.append(f"🚨 立即修复 {len(high_issues)} 个高优先级问题")
            
        if medium_issues:
            recommendations.append(f"⚠️ 计划修复 {len(medium_issues)} 个中等优先级问题")
        
        # 成功率分析
        total_pages = len(self.audit_results['pages_tested'])
        working_pages = len([p for p in self.audit_results['pages_tested'] if p['status'] == 'working'])
        success_rate = (working_pages / total_pages * 100) if total_pages > 0 else 0
        
        if success_rate < 80:
            recommendations.append("💡 建议全面检查路由配置和页面组件")
        elif success_rate < 100:
            recommendations.append("💡 建议修复剩余的页面问题")
        else:
            recommendations.append("🎉 所有策略页面工作正常！")
        
        recommendations.extend([
            "💡 建议添加策略页面的自动化测试",
            "💡 建议建立页面健康监控机制",
            "💡 建议为所有策略详情页面添加统一的错误处理"
        ])
        
        self.audit_results['issues_summary'] = {
            'total': len(all_issues),
            'high': len(high_issues),
            'medium': len(medium_issues),
            'low': len(low_issues)
        }
        self.audit_results['recommendations'] = recommendations
        self.audit_results['success_rate'] = success_rate
        self.audit_results['end_time'] = datetime.now().isoformat()
        
        # 保存详细报告
        report_path = f'audit_reports/comprehensive_strategy_audit_{self.session_id}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.audit_results, f, ensure_ascii=False, indent=2)
        
        # 打印报告摘要
        print("\n" + "="*80)
        print("🔍 全面策略页面审计报告")
        print("="*80)
        print(f"📊 测试统计:")
        print(f"  总页面数: {total_pages}")
        print(f"  正常工作: {working_pages}")
        print(f"  成功率: {success_rate:.1f}%")
        
        print(f"\n🚨 问题统计:")
        print(f"  高优先级: {len(high_issues)}个")
        print(f"  中优先级: {len(medium_issues)}个")
        print(f"  低优先级: {len(low_issues)}个")
        print(f"  总计: {len(all_issues)}个")
        
        if high_issues:
            print(f"\n🚨 高优先级问题:")
            for i, issue in enumerate(high_issues, 1):
                print(f"  {i}. [{issue['page']}] {issue['message']}")
        
        if medium_issues:
            print(f"\n⚠️ 中优先级问题:")
            for i, issue in enumerate(medium_issues, 1):
                print(f"  {i}. [{issue['page']}] {issue['message']}")
        
        print(f"\n💡 改进建议:")
        for rec in recommendations:
            print(f"  {rec}")
        
        print(f"\n📋 详细报告已保存: {report_path}")
        
        logger.info(f"📋 综合审计报告生成完成: {report_path}")

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
    
    async def run_comprehensive_audit(self):
        """运行全面审计"""
        try:
            await self.initialize()
            await self.test_strategy_pages()
            await self.generate_comprehensive_report()
        finally:
            await self.cleanup()

async def main():
    auditor = ComprehensiveStrategyAudit()
    await auditor.run_comprehensive_audit()

if __name__ == "__main__":
    asyncio.run(main())
