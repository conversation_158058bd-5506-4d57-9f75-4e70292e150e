<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明Vue路由工作正常</p>
    <div class="test-content">
      <el-button type="primary">测试按钮</el-button>
      <el-input v-model="testInput" placeholder="测试输入框" />
      <p>输入内容: {{ testInput }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const testInput = ref('')
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-content {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 300px;
}
</style>
