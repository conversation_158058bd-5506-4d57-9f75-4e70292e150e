# =============================================================================
# MCP-Use Environment Configuration
# =============================================================================
# Copy this file to .env and fill in your actual values
# The .env file is already in .gitignore and won't be committed

# =============================================================================
# Observability - Optional but recommended for debugging and monitoring
# =============================================================================

# Langfuse Configuration (https://langfuse.com)
# Sign up at https://cloud.langfuse.com or self-host
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
# LANGFUSE_HOST=https://cloud.langfuse.com  # Default, uncomment for self-hosted

# Laminar Configuration (https://www.lmnr.ai)
# Sign up at https://www.lmnr.ai and get your project API key
LAMINAR_PROJECT_API_KEY=your-laminar-project-api-key-here

# =============================================================================
# LLM Provider API Keys
# =============================================================================

# OpenAI
OPENAI_API_KEY=sk-your-openai-api-key-here

# Anthropic
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# Google (for Gemini)
GOOGLE_API_KEY=your-google-api-key-here

# Azure OpenAI
AZURE_OPENAI_API_KEY=your-azure-openai-key-here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/

# =============================================================================
# Debug and Development
# =============================================================================

# MCP-Use Debug Level (1=INFO, 2=DEBUG)
DEBUG=1
# Alternative debug variable
MCP_USE_DEBUG=1

# Disable specific features (set to 'false' to disable)
# MCP_USE_LANGFUSE=false
# MCP_USE_LAMINAR=false
# MCP_USE_TELEMETRY=false

# =============================================================================
# MCP Server Specific Configuration
# =============================================================================

# E2B Sandbox (for sandboxed execution)
E2B_API_KEY=your-e2b-api-key-here

# Custom MCP server endpoints (if using HTTP/WebSocket servers)
# MCP_SERVER_URL=http://localhost:8080
# MCP_WEBSOCKET_URL=ws://localhost:8081
