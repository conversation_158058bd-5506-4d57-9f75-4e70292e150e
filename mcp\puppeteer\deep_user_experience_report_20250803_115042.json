{"test_summary": {"test_name": "深度用户体验测试", "test_time": "2025-08-03T11:50:42.672105", "total_actions": 24, "successful_actions": 20, "failed_actions": 4, "screenshots_taken": 8}, "performance_metrics": {"homepage_load": {"domContentLoaded": 0, "loadComplete": 0.3999999910593033, "firstPaint": 752, "firstContentfulPaint": 2420, "totalLoadTime": NaN, "actionDuration": 0.008846044540405273}}, "user_actions": [{"action": "访问首页", "details": "加载时间: 2.58秒", "success": true, "timing": 2.584763765335083, "timestamp": "2025-08-03T11:48:46.151913", "url": "http://localhost:5173/"}, {"action": "获取页面标题", "details": "标题: 仪表盘 - 量化投资平台", "success": true, "timing": null, "timestamp": "2025-08-03T11:48:46.413550", "url": "http://localhost:5173/"}, {"action": "性能测量", "details": "DOM加载: 0ms", "success": true, "timing": null, "timestamp": "2025-08-03T11:48:46.429820", "url": "http://localhost:5173/"}, {"action": "控制台检查", "details": "无错误", "success": true, "timing": null, "timestamp": "2025-08-03T11:48:46.433091", "url": "http://localhost:5173/"}, {"action": "访问仪表盘", "details": "查看投资概览", "success": true, "timing": 0.8665452003479004, "timestamp": "2025-08-03T11:48:48.393172", "url": "http://localhost:5173/dashboard"}, {"action": "仪表盘内容检查", "details": "发现107个内容元素", "success": true, "timing": null, "timestamp": "2025-08-03T11:48:48.700793", "url": "http://localhost:5173/dashboard"}, {"action": "访问市场数据", "details": "查看实时行情", "success": true, "timing": 2.369337558746338, "timestamp": "2025-08-03T11:48:57.064640", "url": "http://localhost:5173/market"}, {"action": "市场数据内容检查", "details": "发现316个内容元素", "success": true, "timing": null, "timestamp": "2025-08-03T11:48:57.429028", "url": "http://localhost:5173/market"}, {"action": "访问交易终端", "details": "进行交易操作", "success": true, "timing": 1.7512309551239014, "timestamp": "2025-08-03T11:49:06.498519", "url": "http://localhost:5173/trading/terminal"}, {"action": "交易终端内容检查", "details": "发现524个内容元素", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:06.807019", "url": "http://localhost:5173/trading/terminal"}, {"action": "访问策略中心", "details": "管理投资策略", "success": true, "timing": 5.1964170932769775, "timestamp": "2025-08-03T11:49:18.220738", "url": "http://localhost:5173/strategy/center"}, {"action": "策略中心内容检查", "details": "发现184个内容元素", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:18.528889", "url": "http://localhost:5173/strategy/center"}, {"action": "访问投资组合", "details": "查看持仓情况", "success": true, "timing": 1.8851737976074219, "timestamp": "2025-08-03T11:49:24.950780", "url": "http://localhost:5173/portfolio"}, {"action": "投资组合内容检查", "details": "发现41个内容元素", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:25.161158", "url": "http://localhost:5173/portfolio"}, {"action": "访问风险管理", "details": "监控风险指标", "success": true, "timing": 2.8080685138702393, "timestamp": "2025-08-03T11:49:34.691144", "url": "http://localhost:5173/risk"}, {"action": "风险管理内容检查", "details": "发现38个内容元素", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:34.828754", "url": "http://localhost:5173/risk"}, {"action": "访问登录页面", "details": "准备进行登录", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:41.259189", "url": "http://localhost:5173/login"}, {"action": "登录表单不完整", "details": "缺少输入框", "success": false, "timing": null, "timestamp": "2025-08-03T11:49:41.266970", "url": "http://localhost:5173/login"}, {"action": "发现按钮元素", "details": "总数: 10", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:41.949827", "url": "http://localhost:5173/"}, {"action": "点击按钮1", "details": "按钮文本: ", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:43.268452", "url": "http://localhost:5173/"}, {"action": "点击按钮2", "details": "按钮文本: ", "success": true, "timing": null, "timestamp": "2025-08-03T11:49:45.308329", "url": "http://localhost:5173/"}, {"action": "按钮3点击失败", "details": "ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n    - retrying click action\n    - waiting 20ms\n    - waiting for element to be visible, enabled and stable\n    - element is visible, enabled and stable\n    - scrolling into view if needed\n    - done scrolling\n    - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  2 × retrying click action\n      - waiting 100ms\n      - waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  13 × retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  2 × retrying click action\n      - waiting 500ms\n      - waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  - retrying click action\n    - waiting 500ms\n", "success": false, "timing": null, "timestamp": "2025-08-03T11:50:17.006213", "url": "http://localhost:5173/"}, {"action": "按钮4点击失败", "details": "ElementHandle.click: Target page, context or browser has been closed\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n    - retrying click action\n    - waiting 20ms\n    - waiting for element to be visible, enabled and stable\n    - element is visible, enabled and stable\n    - scrolling into view if needed\n    - done scrolling\n    - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  2 × retrying click action\n      - waiting 100ms\n      - waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  10 × retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n       - waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  2 × retrying click action\n      - waiting 500ms\n      - waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <span role=\"heading\" aria-level=\"2\" id=\"el-id-3125-6\" class=\"el-drawer__title\">通知中心</span> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  - retrying click action\n    - waiting 500ms\n    - waiting for element to be visible, enabled and stable\n    - element is visible, enabled and stable\n    - scrolling into view if needed\n    - done scrolling\n    - <header class=\"el-drawer__header\">…</header> from <div class=\"el-overlay\">…</div> subtree intercepts pointer events\n  - retrying click action\n    - waiting 500ms\n", "success": false, "timing": null, "timestamp": "2025-08-03T11:50:42.461819", "url": "http://localhost:5173/"}, {"action": "按钮5点击失败", "details": "Page.wait_for_timeout: Target page, context or browser has been closed", "success": false, "timing": null, "timestamp": "2025-08-03T11:50:42.480464", "url": "http://localhost:5173/"}], "screenshots": [{"name": "homepage_initial", "filename": "deep_test_homepage_initial_114846.png", "description": "首页初始加载状态", "timestamp": "2025-08-03T11:48:46.409977", "url": "http://localhost:5173/"}, {"name": "page_仪表盘", "filename": "deep_test_page_仪表盘_114848.png", "description": "仪表盘页面浏览", "timestamp": "2025-08-03T11:48:48.655026", "url": "http://localhost:5173/dashboard"}, {"name": "page_市场数据", "filename": "deep_test_page_市场数据_114857.png", "description": "市场数据页面浏览", "timestamp": "2025-08-03T11:48:57.344080", "url": "http://localhost:5173/market"}, {"name": "page_交易终端", "filename": "deep_test_page_交易终端_114906.png", "description": "交易终端页面浏览", "timestamp": "2025-08-03T11:49:06.685928", "url": "http://localhost:5173/trading/terminal"}, {"name": "page_策略中心", "filename": "deep_test_page_策略中心_114918.png", "description": "策略中心页面浏览", "timestamp": "2025-08-03T11:49:18.467062", "url": "http://localhost:5173/strategy/center"}, {"name": "page_投资组合", "filename": "deep_test_page_投资组合_114924.png", "description": "投资组合页面浏览", "timestamp": "2025-08-03T11:49:25.138718", "url": "http://localhost:5173/portfolio"}, {"name": "page_风险管理", "filename": "deep_test_page_风险管理_114934.png", "description": "风险管理页面浏览", "timestamp": "2025-08-03T11:49:34.812973", "url": "http://localhost:5173/risk"}, {"name": "login_page", "filename": "deep_test_login_page_114941.png", "description": "登录页面", "timestamp": "2025-08-03T11:49:41.258905", "url": "http://localhost:5173/login"}], "recommendations": [{"priority": "高", "category": "功能问题", "description": "发现4个失败操作，需要修复", "details": ["登录表单不完整", "按钮3点击失败", "按钮4点击失败", "按钮5点击失败"]}]}