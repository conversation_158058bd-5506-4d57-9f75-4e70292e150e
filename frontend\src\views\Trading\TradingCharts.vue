<template>
  <div class="trading-charts">
    <!-- 顶部工具栏 -->
    <div class="chart-toolbar">
      <div class="stock-selector">
        <el-autocomplete
          v-model="selectedStock"
          :fetch-suggestions="searchStocks"
          placeholder="搜索股票代码或名称"
          style="width: 300px"
          @select="handleStockSelect"
        >
          <template #default="{ item }">
            <div class="stock-suggestion">
              <span class="stock-code">{{ item.symbol }}</span>
              <span class="stock-name">{{ item.name }}</span>
            </div>
          </template>
        </el-autocomplete>
      </div>
      
      <div class="chart-controls">
        <el-button-group>
          <el-button 
            v-for="period in timePeriods" 
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            @click="changePeriod(period.value)"
          >
            {{ period.label }}
          </el-button>
        </el-button-group>
        
        <el-button @click="refreshChart" :loading="chartLoading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="chart-settings">
        <el-button @click="showIndicators = true" type="info" plain>
          <el-icon><TrendCharts /></el-icon>
          技术指标
        </el-button>
        <el-button @click="showSettings = true" type="info" plain>
          <el-icon><Setting /></el-icon>
          图表设置
        </el-button>
      </div>
    </div>

    <!-- 主图表区域 -->
    <div class="chart-container">
      <div class="chart-header">
        <div v-if="currentStockInfo" class="stock-info">
          <h2>{{ currentStockInfo.symbol }} {{ currentStockInfo.name }}</h2>
          <div class="price-info">
            <span class="current-price" :class="getPriceClass(currentStockInfo.changePercent)">
              ¥{{ formatPrice(currentStockInfo.currentPrice) }}
            </span>
            <span class="price-change" :class="getPriceClass(currentStockInfo.changePercent)">
              {{ currentStockInfo.changePercent >= 0 ? '+' : '' }}{{ currentStockInfo.changePercent.toFixed(2) }}%
            </span>
            <span class="price-change" :class="getPriceClass(currentStockInfo.changePercent)">
              {{ currentStockInfo.changePercent >= 0 ? '+' : '' }}{{ formatPrice(currentStockInfo.change) }}
            </span>
          </div>
        </div>
        
        <div class="chart-info">
          <span>周期: {{ getCurrentPeriodLabel() }}</span>
          <span>更新时间: {{ lastUpdateTime }}</span>
        </div>
      </div>
      
      <!-- K线图表 -->
      <div class="main-chart">
        <div v-if="chartLoading" class="chart-loading">
          <el-loading-directive v-loading="true" text="加载图表数据...">
            <div style="height: 400px;"></div>
          </el-loading-directive>
        </div>
        
        <div v-else-if="!currentStockInfo" class="chart-placeholder">
          <div class="placeholder-content">
            <el-icon class="placeholder-icon"><TrendCharts /></el-icon>
            <h3>请选择股票查看K线图</h3>
            <p>在上方搜索框中输入股票代码或名称</p>
          </div>
        </div>
        
        <div v-else class="chart-content">
          <!-- 这里应该集成真实的图表库，如ECharts -->
          <div class="mock-chart">
            <div class="chart-title">{{ currentStockInfo.symbol }} K线图 ({{ getCurrentPeriodLabel() }})</div>
            <div class="chart-canvas">
              <!-- 模拟K线图 -->
              <div class="kline-container">
                <div v-for="(candle, index) in mockKlineData" :key="index" class="kline-bar">
                  <div 
                    class="kline-body" 
                    :class="candle.close >= candle.open ? 'up' : 'down'"
                    :style="getKlineStyle(candle)"
                  ></div>
                </div>
              </div>
              
              <!-- 成交量 -->
              <div class="volume-container">
                <div v-for="(volume, index) in mockVolumeData" :key="index" class="volume-bar">
                  <div 
                    class="volume-body"
                    :style="{ height: `${volume.height}%` }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 技术指标图表 -->
      <div v-if="enabledIndicators.length > 0" class="indicator-charts">
        <div v-for="indicator in enabledIndicators" :key="indicator" class="indicator-chart">
          <div class="indicator-title">{{ getIndicatorName(indicator) }}</div>
          <div class="indicator-content">
            <!-- 这里应该显示具体的技术指标图表 -->
            <div class="mock-indicator">
              <div class="indicator-line" :class="indicator"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术指标选择对话框 -->
    <el-dialog v-model="showIndicators" title="技术指标设置" width="600px">
      <div class="indicators-config">
        <div class="indicator-categories">
          <div v-for="category in indicatorCategories" :key="category.name" class="category">
            <h4>{{ category.name }}</h4>
            <div class="indicator-list">
              <div v-for="indicator in category.indicators" :key="indicator.key" class="indicator-item">
                <el-checkbox 
                  v-model="indicator.enabled"
                  @change="toggleIndicator(indicator.key, indicator.enabled)"
                >
                  {{ indicator.name }}
                </el-checkbox>
                <span class="indicator-desc">{{ indicator.description }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showIndicators = false">关闭</el-button>
        <el-button type="primary" @click="applyIndicators">应用</el-button>
      </template>
    </el-dialog>

    <!-- 图表设置对话框 -->
    <el-dialog v-model="showSettings" title="图表设置" width="500px">
      <el-form :model="chartSettings" label-width="100px">
        <el-form-item label="图表主题">
          <el-select v-model="chartSettings.theme" style="width: 100%">
            <el-option label="浅色主题" value="light" />
            <el-option label="深色主题" value="dark" />
            <el-option label="经典主题" value="classic" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="K线样式">
          <el-select v-model="chartSettings.candleStyle" style="width: 100%">
            <el-option label="蜡烛图" value="candle" />
            <el-option label="美国线" value="ohlc" />
            <el-option label="收盘线" value="line" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="显示成交量">
          <el-switch v-model="chartSettings.showVolume" />
        </el-form-item>
        
        <el-form-item label="显示网格">
          <el-switch v-model="chartSettings.showGrid" />
        </el-form-item>
        
        <el-form-item label="自动刷新">
          <el-switch v-model="chartSettings.autoRefresh" />
        </el-form-item>
        
        <el-form-item v-if="chartSettings.autoRefresh" label="刷新间隔">
          <el-select v-model="chartSettings.refreshInterval" style="width: 100%">
            <el-option label="5秒" :value="5000" />
            <el-option label="10秒" :value="10000" />
            <el-option label="30秒" :value="30000" />
            <el-option label="1分钟" :value="60000" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveChartSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, TrendCharts, Setting } from '@element-plus/icons-vue'

// 响应式数据
const selectedStock = ref('')
const selectedPeriod = ref('1d')
const chartLoading = ref(false)
const showIndicators = ref(false)
const showSettings = ref(false)
const lastUpdateTime = ref('')

const currentStockInfo = ref(null)
const enabledIndicators = ref(['ma', 'volume'])

const timePeriods = [
  { label: '1分', value: '1m' },
  { label: '5分', value: '5m' },
  { label: '15分', value: '15m' },
  { label: '30分', value: '30m' },
  { label: '1小时', value: '1h' },
  { label: '日线', value: '1d' },
  { label: '周线', value: '1w' },
  { label: '月线', value: '1M' }
]

const chartSettings = reactive({
  theme: 'light',
  candleStyle: 'candle',
  showVolume: true,
  showGrid: true,
  autoRefresh: false,
  refreshInterval: 30000
})

const indicatorCategories = reactive([
  {
    name: '趋势指标',
    indicators: [
      { key: 'ma', name: 'MA均线', description: '移动平均线', enabled: true },
      { key: 'ema', name: 'EMA指数均线', description: '指数移动平均线', enabled: false },
      { key: 'boll', name: 'BOLL布林带', description: '布林带指标', enabled: false }
    ]
  },
  {
    name: '震荡指标',
    indicators: [
      { key: 'rsi', name: 'RSI相对强弱', description: '相对强弱指标', enabled: false },
      { key: 'kdj', name: 'KDJ随机指标', description: '随机指标', enabled: false },
      { key: 'macd', name: 'MACD指标', description: '指数平滑移动平均线', enabled: false }
    ]
  },
  {
    name: '成交量指标',
    indicators: [
      { key: 'volume', name: '成交量', description: '成交量柱状图', enabled: true },
      { key: 'obv', name: 'OBV能量潮', description: '能量潮指标', enabled: false }
    ]
  }
])

// 模拟数据
const mockKlineData = ref([])
const mockVolumeData = ref([])

let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const formatPrice = (price: number) => price.toFixed(2)

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const getCurrentPeriodLabel = () => {
  const period = timePeriods.find(p => p.value === selectedPeriod.value)
  return period ? period.label : '日线'
}

const getIndicatorName = (key: string) => {
  for (const category of indicatorCategories) {
    const indicator = category.indicators.find(ind => ind.key === key)
    if (indicator) return indicator.name
  }
  return key.toUpperCase()
}

// 方法
const searchStocks = (queryString: string, callback: Function) => {
  const mockStocks = [
    { symbol: '000001', name: '平安银行' },
    { symbol: '000002', name: '万科A' },
    { symbol: '600000', name: '浦发银行' },
    { symbol: '600036', name: '招商银行' },
    { symbol: '600519', name: '贵州茅台' },
    { symbol: '000858', name: '五粮液' }
  ]
  
  const results = queryString 
    ? mockStocks.filter(stock => 
        stock.symbol.includes(queryString) || 
        stock.name.includes(queryString)
      )
    : mockStocks
  
  callback(results)
}

const handleStockSelect = (item: any) => {
  selectedStock.value = `${item.symbol} ${item.name}`
  loadStockData(item.symbol)
}

const loadStockData = async (symbol: string) => {
  chartLoading.value = true
  
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    currentStockInfo.value = {
      symbol: symbol,
      name: '测试股票',
      currentPrice: 12.45,
      change: 0.23,
      changePercent: 1.88
    }
    
    // 生成模拟K线数据
    generateMockData()
    
    lastUpdateTime.value = new Date().toLocaleTimeString()
    
  } catch (error) {
    ElMessage.error('股票数据加载失败')
  } finally {
    chartLoading.value = false
  }
}

const generateMockData = () => {
  const dataCount = 50
  mockKlineData.value = []
  mockVolumeData.value = []
  
  let basePrice = 12.00
  
  for (let i = 0; i < dataCount; i++) {
    const open = basePrice + (Math.random() - 0.5) * 0.5
    const close = open + (Math.random() - 0.5) * 0.8
    const high = Math.max(open, close) + Math.random() * 0.3
    const low = Math.min(open, close) - Math.random() * 0.3
    
    mockKlineData.value.push({
      open,
      high,
      low,
      close
    })
    
    mockVolumeData.value.push({
      height: Math.random() * 80 + 20
    })
    
    basePrice = close
  }
}

const getKlineStyle = (candle: any) => {
  const isUp = candle.close >= candle.open
  const bodyHeight = Math.abs(candle.close - candle.open) * 20
  const shadowTop = (candle.high - Math.max(candle.open, candle.close)) * 20
  const shadowBottom = (Math.min(candle.open, candle.close) - candle.low) * 20
  
  return {
    height: `${Math.max(bodyHeight, 2)}px`,
    marginTop: `${shadowTop}px`,
    marginBottom: `${shadowBottom}px`
  }
}

const changePeriod = (period: string) => {
  selectedPeriod.value = period
  if (currentStockInfo.value) {
    loadStockData(currentStockInfo.value.symbol)
  }
}

const refreshChart = () => {
  if (currentStockInfo.value) {
    loadStockData(currentStockInfo.value.symbol)
  }
}

const toggleIndicator = (key: string, enabled: boolean) => {
  if (enabled && !enabledIndicators.value.includes(key)) {
    enabledIndicators.value.push(key)
  } else if (!enabled) {
    const index = enabledIndicators.value.indexOf(key)
    if (index > -1) {
      enabledIndicators.value.splice(index, 1)
    }
  }
}

const applyIndicators = () => {
  showIndicators.value = false
  ElMessage.success('技术指标已更新')
}

const saveChartSettings = () => {
  showSettings.value = false
  ElMessage.success('图表设置已保存')
  
  // 如果开启自动刷新
  if (chartSettings.autoRefresh) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh()
  refreshTimer = setInterval(() => {
    if (currentStockInfo.value) {
      refreshChart()
    }
  }, chartSettings.refreshInterval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  console.log('图表分析页面初始化完成')
  lastUpdateTime.value = new Date().toLocaleTimeString()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped lang="scss">
.trading-charts {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.chart-toolbar {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .stock-selector {
    .stock-suggestion {
      display: flex;
      gap: 12px;
      
      .stock-code {
        font-weight: 600;
        color: #303133;
      }
      
      .stock-name {
        color: #606266;
      }
    }
  }
  
  .chart-controls {
    display: flex;
    gap: 16px;
    align-items: center;
  }
  
  .chart-settings {
    display: flex;
    gap: 12px;
  }
}

.chart-container {
  flex: 1;
  padding: 24px;
  overflow: auto;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .stock-info {
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    .price-info {
      display: flex;
      gap: 16px;
      align-items: center;
      
      .current-price {
        font-size: 24px;
        font-weight: 700;
        
        &.price-up { color: #f56c6c; }
        &.price-down { color: #67c23a; }
        &.price-neutral { color: #909399; }
      }
      
      .price-change {
        font-weight: 600;
        
        &.price-up { color: #f56c6c; }
        &.price-down { color: #67c23a; }
        &.price-neutral { color: #909399; }
      }
    }
  }
  
  .chart-info {
    display: flex;
    gap: 16px;
    color: #606266;
    font-size: 14px;
  }
}

.main-chart {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 500px;
  
  .chart-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 500px;
    
    .placeholder-content {
      text-align: center;
      
      .placeholder-icon {
        font-size: 64px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 0 0 8px 0;
        color: #606266;
      }
      
      p {
        margin: 0;
        color: #909399;
      }
    }
  }
  
  .chart-content {
    padding: 20px;
    
    .mock-chart {
      .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        text-align: center;
      }
      
      .chart-canvas {
        .kline-container {
          display: flex;
          gap: 2px;
          height: 300px;
          align-items: flex-end;
          margin-bottom: 20px;
          
          .kline-bar {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            
            .kline-body {
              width: 8px;
              min-height: 2px;
              
              &.up {
                background: #f56c6c;
              }
              
              &.down {
                background: #67c23a;
              }
            }
          }
        }
        
        .volume-container {
          display: flex;
          gap: 2px;
          height: 100px;
          align-items: flex-end;
          
          .volume-bar {
            flex: 1;
            
            .volume-body {
              width: 8px;
              background: #c0c4cc;
              opacity: 0.6;
            }
          }
        }
      }
    }
  }
}

.indicator-charts {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .indicator-chart {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    height: 200px;
    
    .indicator-title {
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
    }
    
    .indicator-content {
      height: calc(100% - 32px);
      
      .mock-indicator {
        height: 100%;
        position: relative;
        
        .indicator-line {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 2px;
          background: #409eff;
          
          &.ma {
            background: linear-gradient(90deg, #409eff, #67c23a);
          }
          
          &.volume {
            background: #c0c4cc;
            height: 60%;
            top: 40%;
          }
        }
      }
    }
  }
}

.indicators-config {
  .indicator-categories {
    .category {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }
      
      .indicator-list {
        .indicator-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          
          .indicator-desc {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .chart-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    
    .chart-controls {
      justify-content: center;
    }
    
    .chart-settings {
      justify-content: center;
    }
  }
}

@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .stock-info {
    .price-info {
      flex-wrap: wrap;
    }
  }
}
</style>
