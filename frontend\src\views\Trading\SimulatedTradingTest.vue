<template>
  <div class="simulated-trading-modern">
    <div class="modern-header">
      <h1>现代化模拟交易测试</h1>
      <p>这是一个简化的测试版本</p>
    </div>
    
    <div class="trading-workspace">
      <div class="left-panel">
        <div class="stock-card">
          <h2>股票信息</h2>
          <p>测试股票: 000001 平安银行</p>
          <p>当前价格: ¥10.80</p>
        </div>
      </div>
      
      <div class="right-panel">
        <div class="trade-form-container">
          <h2>交易表单</h2>
          <p>买入/卖出操作</p>
        </div>
      </div>
    </div>
    
    <div class="bottom-data-panel">
      <h2>数据面板</h2>
      <p>持仓、委托、成交记录</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const testData = ref('现代化组件测试')

console.log('SimulatedTradingTest 组件已加载')
</script>

<style scoped>
.simulated-trading-modern {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modern-header {
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  text-align: center;
}

.trading-workspace {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
}

.left-panel, .right-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.stock-card, .trade-form-container {
  height: 100%;
}

.bottom-data-panel {
  height: 200px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding: 16px 24px;
}

h1, h2 {
  color: #303133;
  margin: 0 0 16px 0;
}

p {
  color: #606266;
  margin: 8px 0;
}
</style>
