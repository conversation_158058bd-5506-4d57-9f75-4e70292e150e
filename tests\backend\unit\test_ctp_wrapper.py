"""
CTP封装器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal

from app.services.ctp_wrapper import (
    CTPWrapper,
    CTPConfig,
    ConnectionStatus,
    OrderStatus,
    PositionDirection,
    TradeData,
    OrderData,
    PositionData,
    TickData,
    DepthData,
    CTPError,
    CTPConnectionError,
    CTPTradingError,
    CTPDataError,
)


@pytest.mark.unit
@pytest.mark.ctp
class TestCTPWrapper:
    """CTP封装器测试类"""

    @pytest.fixture
    def ctp_config(self):
        """CTP配置"""
        return CTPConfig(
            broker_id="9999",
            user_id="test_user",
            password="test_password",
            auth_code="test_auth",
            app_id="test_app",
            md_server="tcp://127.0.0.1:10010",
            td_server="tcp://127.0.0.1:10000",
            product_info="test_product",
            user_product_info="test_user_product",
        )

    @pytest.fixture
    def mock_md_api(self):
        """模拟行情API"""
        mock_api = Mock()
        mock_api.CreateFtdcMdApi.return_value = Mock()
        mock_api.RegisterSpi = Mock()
        mock_api.RegisterFront = Mock()
        mock_api.Init = Mock()
        mock_api.Join = Mock()
        mock_api.ReqUserLogin = Mock(return_value=0)
        mock_api.SubscribeMarketData = Mock(return_value=0)
        mock_api.UnSubscribeMarketData = Mock(return_value=0)
        mock_api.ReqUserLogout = Mock(return_value=0)
        mock_api.Release = Mock()
        return mock_api

    @pytest.fixture
    def mock_td_api(self):
        """模拟交易API"""
        mock_api = Mock()
        mock_api.CreateFtdcTraderApi.return_value = Mock()
        mock_api.RegisterSpi = Mock()
        mock_api.RegisterFront = Mock()
        mock_api.Init = Mock()
        mock_api.Join = Mock()
        mock_api.ReqAuthenticate = Mock(return_value=0)
        mock_api.ReqUserLogin = Mock(return_value=0)
        mock_api.ReqSettlementInfoConfirm = Mock(return_value=0)
        mock_api.ReqOrderInsert = Mock(return_value=0)
        mock_api.ReqOrderAction = Mock(return_value=0)
        mock_api.ReqQryInvestorPosition = Mock(return_value=0)
        mock_api.ReqQryTradingAccount = Mock(return_value=0)
        mock_api.ReqUserLogout = Mock(return_value=0)
        mock_api.Release = Mock()
        return mock_api

    @pytest.fixture
    async def ctp_wrapper(self, ctp_config, mock_md_api, mock_td_api):
        """创建CTP封装器实例"""
        with patch(
            "app.services.ctp_wrapper.CTPWrapper._create_md_api",
            return_value=mock_md_api,
        ):
            with patch(
                "app.services.ctp_wrapper.CTPWrapper._create_td_api",
                return_value=mock_td_api,
            ):
                wrapper = CTPWrapper(ctp_config)
                yield wrapper
                await wrapper.cleanup()

    async def test_ctp_wrapper_initialization(self, ctp_wrapper, ctp_config):
        """测试CTP封装器初始化"""
        assert ctp_wrapper.config == ctp_config
        assert ctp_wrapper.md_status == ConnectionStatus.DISCONNECTED
        assert ctp_wrapper.td_status == ConnectionStatus.DISCONNECTED
        assert ctp_wrapper.subscribed_symbols == set()
        assert ctp_wrapper.positions == {}
        assert ctp_wrapper.orders == {}

    async def test_connect_market_data(self, ctp_wrapper, mock_md_api):
        """测试行情连接"""
        # 模拟连接成功
        with patch.object(ctp_wrapper, "_wait_for_connection") as mock_wait:
            mock_wait.return_value = True

            result = await ctp_wrapper.connect_market_data()

            assert result is True
            assert ctp_wrapper.md_status == ConnectionStatus.CONNECTED
            mock_md_api.RegisterFront.assert_called_once()
            mock_md_api.Init.assert_called_once()

    async def test_connect_market_data_failure(self, ctp_wrapper, mock_md_api):
        """测试行情连接失败"""
        # 模拟连接失败
        with patch.object(ctp_wrapper, "_wait_for_connection") as mock_wait:
            mock_wait.return_value = False

            with pytest.raises(
                CTPConnectionError, match="Market data connection failed"
            ):
                await ctp_wrapper.connect_market_data()

            assert ctp_wrapper.md_status == ConnectionStatus.DISCONNECTED

    async def test_connect_trading(self, ctp_wrapper, mock_td_api):
        """测试交易连接"""
        # 模拟连接成功
        with patch.object(ctp_wrapper, "_wait_for_connection") as mock_wait:
            mock_wait.return_value = True

            result = await ctp_wrapper.connect_trading()

            assert result is True
            assert ctp_wrapper.td_status == ConnectionStatus.CONNECTED
            mock_td_api.RegisterFront.assert_called_once()
            mock_td_api.Init.assert_called_once()

    async def test_connect_trading_failure(self, ctp_wrapper, mock_td_api):
        """测试交易连接失败"""
        # 模拟连接失败
        with patch.object(ctp_wrapper, "_wait_for_connection") as mock_wait:
            mock_wait.return_value = False

            with pytest.raises(CTPConnectionError, match="Trading connection failed"):
                await ctp_wrapper.connect_trading()

            assert ctp_wrapper.td_status == ConnectionStatus.DISCONNECTED

    async def test_subscribe_market_data(self, ctp_wrapper, mock_md_api):
        """测试行情订阅"""
        # 设置为已连接状态
        ctp_wrapper.md_status = ConnectionStatus.CONNECTED

        symbols = ["rb2405", "hc2405"]
        result = await ctp_wrapper.subscribe_market_data(symbols)

        assert result is True
        assert ctp_wrapper.subscribed_symbols == set(symbols)
        mock_md_api.SubscribeMarketData.assert_called_once()

    async def test_subscribe_market_data_not_connected(self, ctp_wrapper):
        """测试未连接时订阅行情"""
        symbols = ["rb2405"]

        with pytest.raises(CTPConnectionError, match="Market data not connected"):
            await ctp_wrapper.subscribe_market_data(symbols)

    async def test_unsubscribe_market_data(self, ctp_wrapper, mock_md_api):
        """测试取消行情订阅"""
        # 设置为已连接状态并有订阅
        ctp_wrapper.md_status = ConnectionStatus.CONNECTED
        ctp_wrapper.subscribed_symbols = {"rb2405", "hc2405"}

        symbols = ["rb2405"]
        result = await ctp_wrapper.unsubscribe_market_data(symbols)

        assert result is True
        assert ctp_wrapper.subscribed_symbols == {"hc2405"}
        mock_md_api.UnSubscribeMarketData.assert_called_once()

    async def test_place_order(self, ctp_wrapper, mock_td_api):
        """测试下单"""
        # 设置为已连接状态
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        order_data = {
            "symbol": "rb2405",
            "direction": PositionDirection.LONG,
            "price": Decimal("3850.0"),
            "volume": 1,
            "order_type": "LIMIT",
        }

        with patch.object(ctp_wrapper, "_generate_order_ref", return_value="123456"):
            result = await ctp_wrapper.place_order(**order_data)

            assert result is not None
            assert result.startswith("123456")
            mock_td_api.ReqOrderInsert.assert_called_once()

    async def test_place_order_not_connected(self, ctp_wrapper):
        """测试未连接时下单"""
        order_data = {
            "symbol": "rb2405",
            "direction": PositionDirection.LONG,
            "price": Decimal("3850.0"),
            "volume": 1,
            "order_type": "LIMIT",
        }

        with pytest.raises(CTPConnectionError, match="Trading not connected"):
            await ctp_wrapper.place_order(**order_data)

    async def test_cancel_order(self, ctp_wrapper, mock_td_api):
        """测试撤单"""
        # 设置为已连接状态
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        # 添加一个订单
        order_id = "test_order_123"
        ctp_wrapper.orders[order_id] = OrderData(
            order_id=order_id,
            symbol="rb2405",
            direction=PositionDirection.LONG,
            price=Decimal("3850.0"),
            volume=1,
            status=OrderStatus.SUBMITTED,
            timestamp=datetime.now(),
        )

        result = await ctp_wrapper.cancel_order(order_id)

        assert result is True
        mock_td_api.ReqOrderAction.assert_called_once()

    async def test_cancel_order_not_found(self, ctp_wrapper):
        """测试撤销不存在的订单"""
        # 设置为已连接状态
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        with pytest.raises(CTPTradingError, match="Order not found"):
            await ctp_wrapper.cancel_order("nonexistent_order")

    async def test_query_positions(self, ctp_wrapper, mock_td_api):
        """测试查询持仓"""
        # 设置为已连接状态
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        result = await ctp_wrapper.query_positions()

        assert result is True
        mock_td_api.ReqQryInvestorPosition.assert_called_once()

    async def test_query_account(self, ctp_wrapper, mock_td_api):
        """测试查询账户"""
        # 设置为已连接状态
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        result = await ctp_wrapper.query_account()

        assert result is True
        mock_td_api.ReqQryTradingAccount.assert_called_once()

    async def test_get_positions(self, ctp_wrapper):
        """测试获取持仓"""
        # 添加测试持仓数据
        position_data = PositionData(
            symbol="rb2405",
            direction=PositionDirection.LONG,
            volume=5,
            price=Decimal("3850.0"),
            profit=Decimal("1000.0"),
            margin=Decimal("19250.0"),
            timestamp=datetime.now(),
        )
        ctp_wrapper.positions["rb2405_LONG"] = position_data

        positions = ctp_wrapper.get_positions()

        assert len(positions) == 1
        assert positions[0] == position_data

    async def test_get_orders(self, ctp_wrapper):
        """测试获取订单"""
        # 添加测试订单数据
        order_data = OrderData(
            order_id="test_order_123",
            symbol="rb2405",
            direction=PositionDirection.LONG,
            price=Decimal("3850.0"),
            volume=1,
            status=OrderStatus.SUBMITTED,
            timestamp=datetime.now(),
        )
        ctp_wrapper.orders["test_order_123"] = order_data

        orders = ctp_wrapper.get_orders()

        assert len(orders) == 1
        assert orders[0] == order_data

    async def test_get_order_by_id(self, ctp_wrapper):
        """测试根据ID获取订单"""
        # 添加测试订单数据
        order_data = OrderData(
            order_id="test_order_123",
            symbol="rb2405",
            direction=PositionDirection.LONG,
            price=Decimal("3850.0"),
            volume=1,
            status=OrderStatus.SUBMITTED,
            timestamp=datetime.now(),
        )
        ctp_wrapper.orders["test_order_123"] = order_data

        order = ctp_wrapper.get_order_by_id("test_order_123")
        assert order == order_data

        # 测试不存在的订单
        order = ctp_wrapper.get_order_by_id("nonexistent")
        assert order is None

    async def test_disconnect(self, ctp_wrapper, mock_md_api, mock_td_api):
        """测试断开连接"""
        # 设置为已连接状态
        ctp_wrapper.md_status = ConnectionStatus.CONNECTED
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        await ctp_wrapper.disconnect()

        assert ctp_wrapper.md_status == ConnectionStatus.DISCONNECTED
        assert ctp_wrapper.td_status == ConnectionStatus.DISCONNECTED
        mock_md_api.ReqUserLogout.assert_called_once()
        mock_td_api.ReqUserLogout.assert_called_once()

    async def test_cleanup(self, ctp_wrapper, mock_md_api, mock_td_api):
        """测试清理资源"""
        # 设置为已连接状态
        ctp_wrapper.md_status = ConnectionStatus.CONNECTED
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        await ctp_wrapper.cleanup()

        assert ctp_wrapper.md_status == ConnectionStatus.DISCONNECTED
        assert ctp_wrapper.td_status == ConnectionStatus.DISCONNECTED
        mock_md_api.Release.assert_called_once()
        mock_td_api.Release.assert_called_once()

    async def test_on_tick_data_callback(self, ctp_wrapper):
        """测试Tick数据回调"""
        callback_called = False
        received_data = None

        async def test_callback(data):
            nonlocal callback_called, received_data
            callback_called = True
            received_data = data

        ctp_wrapper.set_tick_callback(test_callback)

        # 模拟Tick数据
        tick_data = TickData(
            symbol="rb2405",
            last_price=Decimal("3850.0"),
            bid_price=Decimal("3849.0"),
            ask_price=Decimal("3851.0"),
            volume=12345,
            timestamp=datetime.now(),
        )

        await ctp_wrapper._on_tick_data(tick_data)

        assert callback_called is True
        assert received_data == tick_data

    async def test_on_order_callback(self, ctp_wrapper):
        """测试订单回调"""
        callback_called = False
        received_data = None

        async def test_callback(data):
            nonlocal callback_called, received_data
            callback_called = True
            received_data = data

        ctp_wrapper.set_order_callback(test_callback)

        # 模拟订单数据
        order_data = OrderData(
            order_id="test_order_123",
            symbol="rb2405",
            direction=PositionDirection.LONG,
            price=Decimal("3850.0"),
            volume=1,
            status=OrderStatus.FILLED,
            timestamp=datetime.now(),
        )

        await ctp_wrapper._on_order_update(order_data)

        assert callback_called is True
        assert received_data == order_data

    async def test_on_trade_callback(self, ctp_wrapper):
        """测试成交回调"""
        callback_called = False
        received_data = None

        async def test_callback(data):
            nonlocal callback_called, received_data
            callback_called = True
            received_data = data

        ctp_wrapper.set_trade_callback(test_callback)

        # 模拟成交数据
        trade_data = TradeData(
            trade_id="test_trade_123",
            order_id="test_order_123",
            symbol="rb2405",
            direction=PositionDirection.LONG,
            price=Decimal("3850.0"),
            volume=1,
            timestamp=datetime.now(),
        )

        await ctp_wrapper._on_trade_update(trade_data)

        assert callback_called is True
        assert received_data == trade_data

    async def test_connection_timeout(self, ctp_wrapper):
        """测试连接超时"""
        with patch.object(ctp_wrapper, "_wait_for_connection") as mock_wait:
            mock_wait.side_effect = asyncio.TimeoutError()

            with pytest.raises(CTPConnectionError, match="Connection timeout"):
                await ctp_wrapper.connect_market_data()

    async def test_api_error_handling(self, ctp_wrapper, mock_md_api):
        """测试API错误处理"""
        # 模拟API调用失败
        mock_md_api.SubscribeMarketData.return_value = -1

        ctp_wrapper.md_status = ConnectionStatus.CONNECTED

        with pytest.raises(CTPDataError, match="Failed to subscribe market data"):
            await ctp_wrapper.subscribe_market_data(["rb2405"])

    async def test_order_validation(self, ctp_wrapper):
        """测试订单验证"""
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        # 测试无效价格
        with pytest.raises(CTPTradingError, match="Invalid price"):
            await ctp_wrapper.place_order(
                symbol="rb2405",
                direction=PositionDirection.LONG,
                price=Decimal("0"),
                volume=1,
                order_type="LIMIT",
            )

        # 测试无效数量
        with pytest.raises(CTPTradingError, match="Invalid volume"):
            await ctp_wrapper.place_order(
                symbol="rb2405",
                direction=PositionDirection.LONG,
                price=Decimal("3850.0"),
                volume=0,
                order_type="LIMIT",
            )

    async def test_position_calculation(self, ctp_wrapper):
        """测试持仓计算"""
        # 添加多个持仓
        position1 = PositionData(
            symbol="rb2405",
            direction=PositionDirection.LONG,
            volume=3,
            price=Decimal("3850.0"),
            profit=Decimal("600.0"),
            margin=Decimal("11550.0"),
            timestamp=datetime.now(),
        )

        position2 = PositionData(
            symbol="rb2405",
            direction=PositionDirection.SHORT,
            volume=2,
            price=Decimal("3860.0"),
            profit=Decimal("-400.0"),
            margin=Decimal("7720.0"),
            timestamp=datetime.now(),
        )

        ctp_wrapper.positions["rb2405_LONG"] = position1
        ctp_wrapper.positions["rb2405_SHORT"] = position2

        # 计算净持仓
        net_position = ctp_wrapper.get_net_position("rb2405")
        assert net_position == 1  # 3 - 2 = 1

        # 计算总盈亏
        total_profit = ctp_wrapper.get_total_profit()
        assert total_profit == Decimal("200.0")  # 600 - 400 = 200

    async def test_risk_management(self, ctp_wrapper):
        """测试风险管理"""
        # 设置风险参数
        ctp_wrapper.set_risk_limits(
            max_position_size=10, max_daily_loss=Decimal("5000.0"), max_order_size=5
        )

        # 测试超过最大订单大小
        ctp_wrapper.td_status = ConnectionStatus.CONNECTED

        with pytest.raises(CTPTradingError, match="Order size exceeds limit"):
            await ctp_wrapper.place_order(
                symbol="rb2405",
                direction=PositionDirection.LONG,
                price=Decimal("3850.0"),
                volume=10,  # 超过限制
                order_type="LIMIT",
            )

    async def test_reconnection_mechanism(self, ctp_wrapper, mock_md_api):
        """测试重连机制"""
        # 模拟连接断开
        ctp_wrapper.md_status = ConnectionStatus.DISCONNECTED

        with patch.object(ctp_wrapper, "_auto_reconnect") as mock_reconnect:
            mock_reconnect.return_value = True

            # 触发重连
            await ctp_wrapper._handle_disconnection("market_data")

            mock_reconnect.assert_called_once_with("market_data")

    async def test_heartbeat_mechanism(self, ctp_wrapper):
        """测试心跳机制"""
        # 启动心跳
        ctp_wrapper.start_heartbeat()

        # 等待一段时间
        await asyncio.sleep(0.1)

        # 停止心跳
        ctp_wrapper.stop_heartbeat()

        # 验证心跳任务已停止
        assert (
            ctp_wrapper._heartbeat_task is None
            or ctp_wrapper._heartbeat_task.cancelled()
        )

    async def test_data_validation(self, ctp_wrapper):
        """测试数据验证"""
        # 测试无效的合约代码
        with pytest.raises(CTPDataError, match="Invalid symbol"):
            await ctp_wrapper.subscribe_market_data([""])

        # 测试无效的回调函数
        with pytest.raises(CTPError, match="Callback must be callable"):
            ctp_wrapper.set_tick_callback("not_a_function")

    async def test_performance_metrics(self, ctp_wrapper):
        """测试性能指标"""
        # 重置统计信息
        ctp_wrapper.reset_statistics()

        # 模拟一些操作
        await ctp_wrapper._update_statistics("tick_received", 1)
        await ctp_wrapper._update_statistics("order_sent", 1)
        await ctp_wrapper._update_statistics("trade_received", 1)

        stats = ctp_wrapper.get_statistics()

        assert stats["tick_received"] == 1
        assert stats["order_sent"] == 1
        assert stats["trade_received"] == 1
        assert "uptime" in stats
        assert "last_update" in stats

    async def test_memory_management(self, ctp_wrapper):
        """测试内存管理"""
        # 添加大量订单数据
        for i in range(1000):
            order_data = OrderData(
                order_id=f"order_{i}",
                symbol="rb2405",
                direction=PositionDirection.LONG,
                price=Decimal("3850.0"),
                volume=1,
                status=OrderStatus.FILLED,
                timestamp=datetime.now(),
            )
            ctp_wrapper.orders[f"order_{i}"] = order_data

        # 清理旧数据
        ctp_wrapper.cleanup_old_data(max_age_hours=1)

        # 验证数据已清理
        assert len(ctp_wrapper.orders) <= 1000

    async def test_thread_safety(self, ctp_wrapper):
        """测试线程安全"""
        import threading
        import concurrent.futures

        results = []

        def add_order():
            order_data = OrderData(
                order_id=f"order_{threading.current_thread().ident}",
                symbol="rb2405",
                direction=PositionDirection.LONG,
                price=Decimal("3850.0"),
                volume=1,
                status=OrderStatus.SUBMITTED,
                timestamp=datetime.now(),
            )
            ctp_wrapper.orders[order_data.order_id] = order_data
            results.append(order_data.order_id)

        # 并发添加订单
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(add_order) for _ in range(50)]
            concurrent.futures.wait(futures)

        # 验证所有订单都已添加
        assert len(results) == 50
        assert len(ctp_wrapper.orders) == 50
