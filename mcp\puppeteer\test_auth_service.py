#!/usr/bin/env python3
"""
测试authService
"""

import asyncio
from puppeteer import BrowserManager

async def test_auth_service():
    manager = BrowserManager()
    try:
        page = await manager.ensure_browser()
        print('🔧 测试authService...')
        
        await page.goto('http://localhost:5173/login')
        await page.wait_for_timeout(2000)
        
        # 直接调用authService.login
        result = await page.evaluate('''
            async () => {
                try {
                    // 获取authService
                    const authService = window.__APP_DEBUG__?.authService;
                    if (!authService) {
                        return { error: 'authService not found' };
                    }
                    
                    // 调用login
                    const response = await authService.login({
                        username: 'admin',
                        password: 'admin123'
                    });
                    
                    return { success: true, response };
                } catch (error) {
                    return { error: error.message, stack: error.stack };
                }
            }
        ''')
        
        print(f'📊 AuthService测试结果: {result}')
        
        # 如果authService不在window.__APP_DEBUG__中，尝试其他方式
        if result.get('error') == 'authService not found':
            print('🔍 尝试其他方式获取authService...')
            
            # 通过模块导入测试
            result2 = await page.evaluate('''
                async () => {
                    try {
                        // 模拟HTTP请求
                        const response = await fetch('/api/v1/auth/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: 'admin',
                                password: 'admin123'
                            })
                        });
                        
                        const data = await response.json();
                        return { success: true, response: data };
                    } catch (error) {
                        return { error: error.message };
                    }
                }
            ''')
            
            print(f'📊 直接HTTP请求结果: {result2}')
        
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_auth_service())
