version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.simple
    container_name: quant_backend
    restart: unless-stopped
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=sqlite:///./data/quant.db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - JWT_SECRET_KEY=your-jwt-secret-key-here
      - CORS_ORIGINS=http://localhost:5173,http://localhost:3000
    ports:
      - "8000:8000"
    depends_on:
      - redis
    networks:
      - quant_network
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.simple
    container_name: quant_frontend
    restart: unless-stopped
    ports:
      - "5173:5173"
    depends_on:
      - backend
    networks:
      - quant_network
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm run dev -- --host 0.0.0.0

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: quant_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - quant_network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库 (可选，用于生产环境)
  postgres:
    image: postgres:15-alpine
    container_name: quant_postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=quant_db
      - POSTGRES_USER=quant_user
      - POSTGRES_PASSWORD=quant_password
    ports:
      - "5432:5432"
    networks:
      - quant_network
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quant_user -d quant_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: quant_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    networks:
      - quant_network
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/ssl:/etc/nginx/ssl:ro
    profiles:
      - production

  # 监控服务 Prometheus (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: quant_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    networks:
      - quant_network
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  # Grafana监控面板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: quant_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    networks:
      - quant_network
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    profiles:
      - monitoring

networks:
  quant_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 开发环境快速启动配置
# 使用方式:
# 1. 开发环境 (最小配置): docker-compose up backend frontend redis
# 2. 完整开发环境: docker-compose up
# 3. 生产环境: docker-compose --profile production up
# 4. 监控环境: docker-compose --profile monitoring up
