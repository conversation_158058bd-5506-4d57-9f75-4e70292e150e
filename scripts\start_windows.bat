@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动量化投资平台...
echo.

:: 进入项目根目录
cd /d "%~dp0\.."

:: 检查依赖
echo 🔍 检查运行环境...

:: 检查 Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装，请安装 Python 3.9+
    echo 📥 下载地址: https://www.python.org/
    pause
    exit /b 1
)

:: 检查 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请安装 Node.js 16+
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ 环境检查通过

:: 停止现有服务
echo 🧹 停止现有服务...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 >nul

:: 启动后端
echo 📡 启动后端服务...
cd backend

:: 检查后端文件
if exist "app\main_minimal.py" (
    echo 使用最小化后端: app\main_minimal.py
    start /b python app\main_minimal.py > ..\logs\backend.log 2>&1
) else if exist "app\main_simple.py" (
    echo 使用简化后端: app\main_simple.py
    start /b python app\main_simple.py > ..\logs\backend.log 2>&1
) else if exist "app\main.py" (
    echo 使用完整后端: app\main.py
    start /b python app\main.py > ..\logs\backend.log 2>&1
) else (
    echo ❌ 未找到后端启动文件
    pause
    exit /b 1
)

:: 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 5 >nul

:: 检查后端是否启动成功
for /l %%i in (1,1,10) do (
    curl -s http://localhost:8000/health >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 后端服务启动成功
        goto backend_ready
    )
    echo    等待后端启动... (%%i/10)
    timeout /t 2 >nul
)

echo ❌ 后端服务启动失败
echo 📄 后端日志:
type ..\logs\backend.log
pause
exit /b 1

:backend_ready
:: 返回项目根目录
cd ..

:: 启动前端
echo 🎨 启动前端服务...
cd frontend

:: 检查依赖
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
)

:: 启动前端服务
echo 启动前端开发服务器...
start /b npm run dev > ..\logs\frontend.log 2>&1

:: 等待前端启动
echo ⏳ 等待前端服务启动...
timeout /t 10 >nul

:: 检查前端是否启动成功
for /l %%i in (1,1,15) do (
    curl -s http://localhost:5173 >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 前端服务启动成功
        goto frontend_ready
    )
    echo    等待前端启动... (%%i/15)
    timeout /t 2 >nul
)

echo ❌ 前端服务启动失败
echo 📄 前端日志:
type ..\logs\frontend.log
pause
exit /b 1

:frontend_ready
:: 返回项目根目录
cd ..

echo.
echo 🎉 量化投资平台启动完成！
echo.
echo 📊 前端访问地址: http://localhost:5173
echo 🔌 后端API地址: http://localhost:8000
echo 📚 API文档地址: http://localhost:8000/docs
echo.
echo 🎯 直接访问页面:
echo    交易中心: http://localhost:5173/trading/center
echo    MiniQMT实盘: http://localhost:5173/trading/miniqmt
echo    模拟交易: http://localhost:5173/trading/simulated
echo.
echo 📄 日志文件:
echo    后端日志: logs\backend.log
echo    前端日志: logs\frontend.log
echo.
echo 🛑 停止服务: scripts\stop_windows.bat
echo 🌐 正在打开浏览器...

:: 打开浏览器
start http://localhost:5173/trading/miniqmt

echo.
echo 按任意键退出...
pause >nul
