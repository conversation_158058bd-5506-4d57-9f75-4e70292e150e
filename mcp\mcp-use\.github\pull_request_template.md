# Pull Request Description

## Changes

Describe the changes introduced by this PR in a concise manner.

## Implementation Details

1. List the specific implementation details
2. Include code organization, architectural decisions
3. Note any dependencies that were added or modified

## Example Usage (Before)

```python
# Include example code showing how things worked before (if applicable)
```

## Example Usage (After)

```python
# Include example code showing how things work after your changes
```

## Documentation Updates

* List any documentation files that were updated
* Explain what was changed in each file

## Testing

Describe how you tested these changes:
- Unit tests added/modified
- Manual testing performed
- Edge cases considered

## Backwards Compatibility

Explain whether these changes are backwards compatible. If not, describe what users will need to do to adapt to these changes.

## Related Issues

Closes #[issue_number]
