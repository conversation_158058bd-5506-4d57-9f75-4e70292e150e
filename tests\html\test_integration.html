<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端集成测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        input, button { padding: 8px; margin: 5px; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffe6e6; border-color: #ff0000; }
        .success { background: #e6ffe6; border-color: #00ff00; }
    </style>
</head>
<body>
    <h1>量化交易平台 - 认证测试</h1>
    
    <h2>用户注册</h2>
    <div class="form-group">
        <input id="reg-username" type="text" placeholder="用户名" value="testuser123">
        <input id="reg-email" type="email" placeholder="邮箱" value="<EMAIL>">
        <input id="reg-password" type="password" placeholder="密码" value="test123456">
        <input id="reg-fullname" type="text" placeholder="全名" value="测试用户123">
        <button onclick="register()">注册</button>
    </div>
    
    <h2>用户登录</h2>
    <div class="form-group">
        <input id="login-username" type="text" placeholder="用户名" value="testuser123">
        <input id="login-password" type="password" placeholder="密码" value="test123456">
        <button onclick="login()">登录</button>
    </div>
    
    <div id="result" class="result"></div>
    
    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = isError ? 'result error' : 'result success';
        }
        
        async function register() {
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: document.getElementById('reg-username').value,
                        email: document.getElementById('reg-email').value,
                        password: document.getElementById('reg-password').value,
                        full_name: document.getElementById('reg-fullname').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`注册成功！<br>用户ID: ${data.id}<br>用户名: ${data.username}<br>邮箱: ${data.email}`);
                } else {
                    showResult(`注册失败: ${data.detail || JSON.stringify(data)}`, true);
                }
            } catch (error) {
                showResult(`注册错误: ${error.message}`, true);
            }
        }
        
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: document.getElementById('login-username').value,
                        password: document.getElementById('login-password').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`登录成功！<br>访问令牌: ${data.access_token}<br>用户名: ${data.user.username}<br>邮箱: ${data.user.email}<br>令牌有效期: ${data.expires_in}秒`);
                    
                    // 存储令牌到localStorage用于后续请求
                    localStorage.setItem('access_token', data.access_token);
                } else {
                    showResult(`登录失败: ${data.detail || JSON.stringify(data)}`, true);
                }
            } catch (error) {
                showResult(`登录错误: ${error.message}`, true);
            }
        }
        
        // 页面加载时显示API连接状态
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE}/../health`);
                const data = await response.json();
                showResult(`API连接正常，当前用户数: ${data.users_count || 0}`);
            } catch (error) {
                showResult(`API连接失败: ${error.message}`, true);
            }
        };
    </script>
</body>
</html>