#!/usr/bin/env python3
"""
量化投资平台快速测试
快速验证平台的核心功能和用户体验
"""

import asyncio
import json
import time
from datetime import datetime
from puppeteer import BrowserManager

class QuickPlatformTest:
    def __init__(self):
        self.manager = BrowserManager()
        self.page = None
        self.results = []
        
    async def setup(self):
        """初始化浏览器"""
        self.page = await self.manager.ensure_browser()
        print("🚀 开始快速平台测试...")
        
    async def teardown(self):
        """清理资源"""
        if self.manager.browser:
            await self.manager.browser.close()
        print("🔚 测试完成")
        
    async def log_result(self, test_name, success, details="", timing=None):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timing": timing,
            "timestamp": datetime.now().isoformat()
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        timing_info = f" ({timing:.2f}s)" if timing else ""
        print(f"{status} {test_name}: {details}{timing_info}")
        
    async def take_screenshot(self, name):
        """截图"""
        filename = f"quick_test_{name}_{datetime.now().strftime('%H%M%S')}.png"
        await self.page.screenshot(path=filename, full_page=True)
        print(f"📸 截图: {filename}")
        return filename
        
    async def test_homepage_load(self):
        """测试首页加载"""
        print("\n📋 测试1: 首页加载")
        
        start_time = time.time()
        try:
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            load_time = time.time() - start_time
            
            title = await self.page.title()
            await self.log_result("首页加载", True, f"标题: {title}", load_time)
            await self.take_screenshot("homepage")
            
            # 检查页面内容
            body_text = await self.page.evaluate('() => document.body.innerText')
            if len(body_text) > 100:
                await self.log_result("页面内容", True, f"内容长度: {len(body_text)} 字符")
            else:
                await self.log_result("页面内容", False, "内容过少，可能未完全加载")
                
        except Exception as e:
            await self.log_result("首页加载", False, str(e))
            
    async def test_navigation(self):
        """测试导航功能"""
        print("\n📋 测试2: 页面导航")
        
        pages = [
            ('/dashboard', '仪表盘'),
            ('/market', '市场数据'),
            ('/trading', '交易终端'),
            ('/strategy', '策略中心'),
            ('/portfolio', '投资组合'),
            ('/risk', '风险管理')
        ]
        
        for path, name in pages:
            try:
                start_time = time.time()
                await self.page.goto(f'http://localhost:5173{path}', 
                                   wait_until='networkidle', timeout=30000)
                load_time = time.time() - start_time
                
                await self.log_result(f"{name}页面", True, f"访问成功", load_time)
                await self.take_screenshot(f"page_{name}")
                
            except Exception as e:
                await self.log_result(f"{name}页面", False, str(e))
                
    async def test_login_flow(self):
        """测试登录流程"""
        print("\n📋 测试3: 登录流程")
        
        try:
            await self.page.goto('http://localhost:5173/login', wait_until='networkidle')
            await self.take_screenshot("login_page")
            
            # 查找登录元素
            username_input = await self.page.query_selector('input[type="text"], input[name="username"]')
            password_input = await self.page.query_selector('input[type="password"]')
            login_button = await self.page.query_selector('button[type="submit"], .login-btn')
            
            if username_input and password_input and login_button:
                await self.log_result("登录表单", True, "发现完整的登录表单")
                
                # 尝试演示登录
                demo_button = await self.page.query_selector('button:has-text("演示登录"), .demo-login')
                if demo_button:
                    await demo_button.click()
                    await self.page.wait_for_timeout(2000)
                    await self.log_result("演示登录", True, "点击演示登录按钮")
                    await self.take_screenshot("after_demo_login")
                else:
                    await self.log_result("演示登录", False, "未找到演示登录按钮")
            else:
                await self.log_result("登录表单", False, "登录表单不完整")
                
        except Exception as e:
            await self.log_result("登录流程", False, str(e))
            
    async def test_api_connectivity(self):
        """测试API连接"""
        print("\n📋 测试4: API连接")
        
        # 监听网络请求
        api_requests = []
        
        def handle_response(response):
            if '/api/' in response.url:
                api_requests.append({
                    'url': response.url,
                    'status': response.status,
                    'method': response.request.method
                })
                
        self.page.on('response', handle_response)
        
        # 访问数据页面触发API请求
        try:
            await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
            await self.page.wait_for_timeout(5000)  # 等待API请求
            
            if api_requests:
                successful_apis = [r for r in api_requests if 200 <= r['status'] < 300]
                failed_apis = [r for r in api_requests if r['status'] >= 400]
                
                await self.log_result("API请求", True, 
                                    f"总请求: {len(api_requests)}, 成功: {len(successful_apis)}, 失败: {len(failed_apis)}")
                
                for api in failed_apis:
                    await self.log_result("API失败", False, f"{api['url']} - {api['status']}")
            else:
                await self.log_result("API请求", False, "未检测到API请求")
                
        except Exception as e:
            await self.log_result("API连接测试", False, str(e))
            
    async def test_interactive_elements(self):
        """测试交互元素"""
        print("\n📋 测试5: 交互元素")
        
        try:
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            
            # 测试按钮
            buttons = await self.page.query_selector_all('button, .btn')
            await self.log_result("按钮检测", True, f"发现 {len(buttons)} 个按钮")
            
            # 测试输入框
            inputs = await self.page.query_selector_all('input, textarea')
            await self.log_result("输入框检测", True, f"发现 {len(inputs)} 个输入框")
            
            # 测试链接
            links = await self.page.query_selector_all('a[href]')
            await self.log_result("链接检测", True, f"发现 {len(links)} 个链接")
            
            # 随机点击一个按钮测试交互
            if buttons:
                try:
                    button = buttons[0]
                    button_text = await button.inner_text()
                    await button.click()
                    await self.page.wait_for_timeout(1000)
                    await self.log_result("按钮交互", True, f"成功点击: {button_text[:20]}")
                except Exception as e:
                    await self.log_result("按钮交互", False, str(e))
                    
        except Exception as e:
            await self.log_result("交互元素测试", False, str(e))
            
    async def test_performance(self):
        """测试性能指标"""
        print("\n📋 测试6: 性能指标")
        
        try:
            start_time = time.time()
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            
            # 获取性能指标
            metrics = await self.page.evaluate('''() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (!navigation) return null;
                
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalLoadTime: navigation.loadEventEnd - navigation.navigationStart
                };
            }''')
            
            if metrics:
                await self.log_result("性能测量", True, 
                                    f"DOM加载: {metrics['domContentLoaded']:.0f}ms, 总加载: {metrics['totalLoadTime']:.0f}ms")
            else:
                await self.log_result("性能测量", False, "无法获取性能指标")
                
        except Exception as e:
            await self.log_result("性能测试", False, str(e))
            
    async def generate_report(self):
        """生成测试报告"""
        total_tests = len(self.results)
        successful_tests = len([r for r in self.results if r['success']])
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": total_tests - successful_tests,
                "success_rate": (successful_tests / total_tests * 100) if total_tests > 0 else 0,
                "test_time": datetime.now().isoformat()
            },
            "detailed_results": self.results
        }
        
        filename = f"quick_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n📊 测试报告已保存: {filename}")
        print(f"📈 测试摘要: {successful_tests}/{total_tests} 通过 ({report['test_summary']['success_rate']:.1f}%)")
        
        return report

async def main():
    """主测试函数"""
    tester = QuickPlatformTest()
    
    try:
        await tester.setup()
        
        # 执行测试
        await tester.test_homepage_load()
        await tester.test_navigation()
        await tester.test_login_flow()
        await tester.test_api_connectivity()
        await tester.test_interactive_elements()
        await tester.test_performance()
        
        # 生成报告
        await tester.generate_report()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        await tester.teardown()

if __name__ == "__main__":
    asyncio.run(main())
