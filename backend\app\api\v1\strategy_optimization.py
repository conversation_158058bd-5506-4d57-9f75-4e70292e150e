"""
策略参数优化API
支持多种优化算法：网格搜索、随机搜索、贝叶斯优化、遗传算法
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
import json
import uuid
from concurrent.futures import ThreadPoolExecutor
import itertools
import random
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

from app.core.database import get_db
from app.strategies.classic_strategies import StrategyFactory
from app.utils.optimization import (
    GridSearchOptimizer,
    RandomSearchOptimizer, 
    BayesianOptimizer,
    GeneticOptimizer
)

router = APIRouter()

# 请求模型
class ParameterRange(BaseModel):
    min: float
    max: float
    step: float = 1.0
    type: str = "float"  # "int", "float", "categorical"
    values: Optional[List[Any]] = None  # 用于分类参数

class OptimizationConstraints(BaseModel):
    min_return: Optional[float] = None
    max_drawdown: Optional[float] = None
    min_sharpe: Optional[float] = None
    min_win_rate: Optional[float] = None
    max_trades: Optional[int] = None
    min_trades: Optional[int] = None

class OptimizationRequest(BaseModel):
    strategy_type: str
    objective: str = "sharpe_ratio"  # "total_return", "sharpe_ratio", "calmar_ratio", "win_rate"
    method: str = "grid_search"  # "grid_search", "random_search", "bayesian", "genetic"
    max_iterations: int = 100
    parameter_ranges: Dict[str, ParameterRange]
    constraints: Optional[OptimizationConstraints] = None
    data_start_date: Optional[str] = None
    data_end_date: Optional[str] = None
    initial_capital: float = 100000
    commission: float = 0.001

class OptimizationResult(BaseModel):
    id: str
    iteration: int
    parameters: Dict[str, Any]
    metrics: Dict[str, float]
    objective_value: float
    constraints_satisfied: bool
    execution_time: float

class OptimizationStatus(BaseModel):
    task_id: str
    status: str  # "running", "completed", "failed", "stopped"
    progress: float
    current_iteration: int
    best_objective_value: Optional[float] = None
    best_parameters: Optional[Dict[str, Any]] = None
    start_time: datetime
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None

# 响应模型
class OptimizationResponse(BaseModel):
    task_id: str
    status: str
    message: str

class OptimizationResults(BaseModel):
    task_id: str
    status: str
    results: List[OptimizationResult]
    best_result: Optional[OptimizationResult] = None
    summary: Dict[str, Any]

# 全局存储正在运行的优化任务
active_optimizations: Dict[str, Dict] = {}

def generate_sample_data():
    """生成模拟数据用于回测"""
    dates = pd.date_range('2022-01-01', '2023-12-31', freq='D')
    np.random.seed(42)
    
    # 生成模拟股价数据
    returns = np.random.normal(0.0008, 0.02, len(dates))  # 日收益率
    prices = [100]  # 初始价格
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    data = pd.DataFrame({
        'date': dates,
        'close': prices,
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'volume': np.random.randint(1000000, 10000000, len(dates))
    })
    
    data.set_index('date', inplace=True)
    return data

def evaluate_strategy(strategy_type: str, parameters: Dict[str, Any], 
                     data: pd.DataFrame, initial_capital: float = 100000,
                     commission: float = 0.001) -> Dict[str, float]:
    """评估策略参数组合"""
    try:
        # 创建策略实例
        strategy = StrategyFactory.create_strategy(strategy_type, **parameters)
        
        # 执行回测
        result = strategy.backtest(data)
        
        # 返回评估指标
        metrics = result.metrics.copy()
        
        # 确保所有指标都是数值类型
        for key, value in metrics.items():
            if not isinstance(value, (int, float)):
                metrics[key] = 0.0
            elif np.isnan(value) or np.isinf(value):
                metrics[key] = 0.0
        
        return metrics
        
    except Exception as e:
        print(f"策略评估失败: {e}")
        # 返回默认的差指标
        return {
            'total_return': -0.1,
            'annual_return': -0.1,
            'sharpe_ratio': -1.0,
            'max_drawdown': -0.5,
            'win_rate': 0.3,
            'profit_loss_ratio': 0.5,
            'total_trades': 0,
            'volatility': 0.3
        }

def check_constraints(metrics: Dict[str, float], 
                     constraints: Optional[OptimizationConstraints]) -> bool:
    """检查约束条件是否满足"""
    if not constraints:
        return True
    
    if constraints.min_return is not None and metrics.get('total_return', 0) < constraints.min_return:
        return False
    
    if constraints.max_drawdown is not None and metrics.get('max_drawdown', 0) < -abs(constraints.max_drawdown):
        return False
    
    if constraints.min_sharpe is not None and metrics.get('sharpe_ratio', 0) < constraints.min_sharpe:
        return False
    
    if constraints.min_win_rate is not None and metrics.get('win_rate', 0) < constraints.min_win_rate:
        return False
    
    if constraints.max_trades is not None and metrics.get('total_trades', 0) > constraints.max_trades:
        return False
    
    if constraints.min_trades is not None and metrics.get('total_trades', 0) < constraints.min_trades:
        return False
    
    return True

def generate_parameter_combinations(parameter_ranges: Dict[str, ParameterRange], 
                                  method: str = "grid_search",
                                  max_combinations: int = 1000) -> List[Dict[str, Any]]:
    """生成参数组合"""
    if method == "grid_search":
        return generate_grid_combinations(parameter_ranges)
    elif method == "random_search":
        return generate_random_combinations(parameter_ranges, max_combinations)
    else:
        return generate_random_combinations(parameter_ranges, max_combinations)

def generate_grid_combinations(parameter_ranges: Dict[str, ParameterRange]) -> List[Dict[str, Any]]:
    """生成网格搜索参数组合"""
    param_values = {}
    
    for param_name, param_range in parameter_ranges.items():
        if param_range.type == "categorical":
            param_values[param_name] = param_range.values or []
        else:
            # 生成数值范围
            start = param_range.min
            stop = param_range.max + param_range.step
            step = param_range.step
            
            values = []
            current = start
            while current <= param_range.max:
                if param_range.type == "int":
                    values.append(int(current))
                else:
                    values.append(float(current))
                current += step
            
            param_values[param_name] = values
    
    # 生成所有组合
    param_names = list(param_values.keys())
    combinations = []
    
    for combo in itertools.product(*param_values.values()):
        param_dict = dict(zip(param_names, combo))
        combinations.append(param_dict)
    
    return combinations

def generate_random_combinations(parameter_ranges: Dict[str, ParameterRange], 
                               max_combinations: int) -> List[Dict[str, Any]]:
    """生成随机搜索参数组合"""
    combinations = []
    
    for _ in range(max_combinations):
        param_dict = {}
        
        for param_name, param_range in parameter_ranges.items():
            if param_range.type == "categorical":
                param_dict[param_name] = random.choice(param_range.values or [])
            elif param_range.type == "int":
                value = random.randint(int(param_range.min), int(param_range.max))
                param_dict[param_name] = value
            else:
                value = random.uniform(param_range.min, param_range.max)
                param_dict[param_name] = round(value, 3)
        
        combinations.append(param_dict)
    
    return combinations

async def run_optimization_task(task_id: str, request: OptimizationRequest):
    """运行优化任务"""
    try:
        # 更新任务状态
        active_optimizations[task_id]["status"] = "running"
        active_optimizations[task_id]["start_time"] = datetime.now()
        
        # 生成测试数据
        data = generate_sample_data()
        
        # 生成参数组合
        if request.method == "grid_search":
            combinations = generate_grid_combinations(request.parameter_ranges)
        elif request.method == "random_search":
            combinations = generate_random_combinations(request.parameter_ranges, request.max_iterations)
        else:
            combinations = generate_random_combinations(request.parameter_ranges, request.max_iterations)
        
        # 限制组合数量
        if len(combinations) > request.max_iterations:
            combinations = random.sample(combinations, request.max_iterations)
        
        results = []
        best_result = None
        
        # 执行优化
        for i, parameters in enumerate(combinations):
            if task_id not in active_optimizations or active_optimizations[task_id]["status"] == "stopped":
                break
            
            start_time = datetime.now()
            
            # 评估策略
            metrics = evaluate_strategy(
                request.strategy_type, 
                parameters, 
                data,
                request.initial_capital,
                request.commission
            )
            
            # 计算目标值
            objective_value = metrics.get(request.objective, 0)
            
            # 检查约束条件
            constraints_satisfied = check_constraints(metrics, request.constraints)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = OptimizationResult(
                id=str(uuid.uuid4()),
                iteration=i + 1,
                parameters=parameters,
                metrics=metrics,
                objective_value=objective_value,
                constraints_satisfied=constraints_satisfied,
                execution_time=execution_time
            )
            
            results.append(result)
            
            # 更新最佳结果
            if constraints_satisfied and (not best_result or objective_value > best_result.objective_value):
                best_result = result
            
            # 更新进度
            progress = (i + 1) / len(combinations) * 100
            active_optimizations[task_id].update({
                "progress": progress,
                "current_iteration": i + 1,
                "best_objective_value": best_result.objective_value if best_result else None,
                "best_parameters": best_result.parameters if best_result else None
            })
            
            # 短暂休眠避免阻塞
            await asyncio.sleep(0.01)
        
        # 完成优化
        active_optimizations[task_id].update({
            "status": "completed",
            "end_time": datetime.now(),
            "results": results,
            "best_result": best_result
        })
        
    except Exception as e:
        active_optimizations[task_id].update({
            "status": "failed",
            "end_time": datetime.now(),
            "error_message": str(e)
        })

@router.post("/start", response_model=OptimizationResponse)
async def start_optimization(
    request: OptimizationRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """开始参数优化"""
    
    # 验证策略类型
    available_strategies = StrategyFactory.get_available_strategies()
    if request.strategy_type not in available_strategies:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的策略类型: {request.strategy_type}"
        )
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 初始化任务状态
    active_optimizations[task_id] = {
        "task_id": task_id,
        "status": "pending",
        "progress": 0.0,
        "current_iteration": 0,
        "best_objective_value": None,
        "best_parameters": None,
        "start_time": None,
        "end_time": None,
        "error_message": None,
        "results": [],
        "best_result": None
    }
    
    # 添加后台任务
    background_tasks.add_task(run_optimization_task, task_id, request)
    
    return OptimizationResponse(
        task_id=task_id,
        status="started",
        message="参数优化任务已启动"
    )

@router.get("/status/{task_id}", response_model=OptimizationStatus)
async def get_optimization_status(task_id: str):
    """获取优化任务状态"""
    if task_id not in active_optimizations:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task_info = active_optimizations[task_id]
    
    return OptimizationStatus(
        task_id=task_id,
        status=task_info["status"],
        progress=task_info["progress"],
        current_iteration=task_info["current_iteration"],
        best_objective_value=task_info["best_objective_value"],
        best_parameters=task_info["best_parameters"],
        start_time=task_info["start_time"] or datetime.now(),
        end_time=task_info["end_time"],
        error_message=task_info["error_message"]
    )

@router.get("/results/{task_id}", response_model=OptimizationResults)
async def get_optimization_results(task_id: str):
    """获取优化结果"""
    if task_id not in active_optimizations:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task_info = active_optimizations[task_id]
    
    if task_info["status"] not in ["completed", "failed"]:
        raise HTTPException(status_code=400, detail="任务尚未完成")
    
    results = task_info.get("results", [])
    best_result = task_info.get("best_result")
    
    # 生成摘要统计
    summary = {
        "total_iterations": len(results),
        "completed_iterations": len([r for r in results if r.constraints_satisfied]),
        "best_objective_value": best_result.objective_value if best_result else None,
        "success_rate": len([r for r in results if r.constraints_satisfied]) / len(results) if results else 0,
        "average_execution_time": np.mean([r.execution_time for r in results]) if results else 0,
        "total_execution_time": sum([r.execution_time for r in results]) if results else 0
    }
    
    return OptimizationResults(
        task_id=task_id,
        status=task_info["status"],
        results=results,
        best_result=best_result,
        summary=summary
    )

@router.post("/stop/{task_id}")
async def stop_optimization(task_id: str):
    """停止优化任务"""
    if task_id not in active_optimizations:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task_info = active_optimizations[task_id]
    if task_info["status"] == "running":
        active_optimizations[task_id]["status"] = "stopped"
        active_optimizations[task_id]["end_time"] = datetime.now()
        return {"message": "优化任务已停止"}
    else:
        return {"message": "任务未在运行"}

@router.delete("/task/{task_id}")
async def delete_optimization_task(task_id: str):
    """删除优化任务"""
    if task_id not in active_optimizations:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    del active_optimizations[task_id]
    return {"message": "任务已删除"}

@router.get("/tasks")
async def list_optimization_tasks():
    """列出所有优化任务"""
    tasks = []
    for task_id, task_info in active_optimizations.items():
        tasks.append({
            "task_id": task_id,
            "status": task_info["status"],
            "progress": task_info["progress"],
            "start_time": task_info["start_time"],
            "end_time": task_info["end_time"]
        })
    
    return {"tasks": tasks}

@router.get("/strategies")
async def get_available_strategies():
    """获取可用的策略类型"""
    strategies = StrategyFactory.get_available_strategies()
    
    # 获取每个策略的参数定义
    strategy_info = {}
    for strategy_name in strategies:
        # 这里可以添加策略参数的元数据
        if strategy_name == "double_ma":
            strategy_info[strategy_name] = {
                "name": "双均线策略",
                "description": "基于短期和长期移动平均线交叉的策略",
                "parameters": {
                    "short_period": {"type": "int", "min": 1, "max": 50, "default": 5},
                    "long_period": {"type": "int", "min": 10, "max": 200, "default": 20}
                }
            }
        elif strategy_name == "rsi":
            strategy_info[strategy_name] = {
                "name": "RSI策略",
                "description": "基于相对强弱指标的反转策略",
                "parameters": {
                    "period": {"type": "int", "min": 2, "max": 50, "default": 14},
                    "overbought": {"type": "float", "min": 50, "max": 90, "default": 70},
                    "oversold": {"type": "float", "min": 10, "max": 50, "default": 30}
                }
            }
        # 可以继续添加其他策略的信息
    
    return {"strategies": strategy_info}

# 示例优化配置
@router.get("/example")
async def get_example_optimization():
    """获取示例优化配置"""
    return {
        "strategy_type": "double_ma",
        "objective": "sharpe_ratio",
        "method": "grid_search",
        "max_iterations": 100,
        "parameter_ranges": {
            "short_period": {
                "min": 5,
                "max": 20,
                "step": 1,
                "type": "int"
            },
            "long_period": {
                "min": 20,
                "max": 50,
                "step": 5,
                "type": "int"
            }
        },
        "constraints": {
            "min_return": 0.05,
            "max_drawdown": 0.2,
            "min_sharpe": 1.0
        },
        "initial_capital": 100000,
        "commission": 0.001
    }