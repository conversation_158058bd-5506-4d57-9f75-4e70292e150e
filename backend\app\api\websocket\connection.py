"""
WebSocket连接管理器
"""

import logging
from typing import Any, Dict

from fastapi import WebSocket

from app.core.websocket import WSMessage, ws_service

logger = logging.getLogger(__name__)
class WebSocketManager:
    """WebSocket连接管理器
    
    这是一个兼容性包装器，内部使用全局的ConnectionManager实例(ws_service.manager)。
    这样可以确保所有模块操作同一个全局管理器，同时保持向后兼容的接口。
    """

    def __init__(self):
        # 使用共享的ConnectionManager确保单一数据源
        self._manager = ws_service.manager
        # WebSocket -> client_id 映射，用于转换旧版方法调用
        self._socket_client_map: Dict[WebSocket, str] = {}

    # ---------------------------------------------------------------------
    # 旧版接口方法 (connect/disconnect/subscribe/...) 
    # 这些方法委托给ConnectionManager，保持向后兼容
    # ---------------------------------------------------------------------
    async def connect(self, websocket: WebSocket, user_id: int):
        """建立WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            user_id: 用户ID
        """
        client_id = f"user-{user_id}-{id(websocket)}"
        self._socket_client_map[websocket] = client_id
        success = await self._manager.connect(websocket, client_id=client_id, user_id=user_id)
        if success:
            logger.info(f"用户 {user_id} WebSocket连接建立 (client_id: {client_id})")
        return success

    async def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        client_id = self._socket_client_map.pop(websocket, None)
        if client_id:
            await self._manager.disconnect(client_id)
            logger.info(f"WebSocket连接断开 (client_id: {client_id})")

    async def subscribe(self, websocket: WebSocket, topic: str):
        """订阅主题"""
        client_id = self._socket_client_map.get(websocket)
        if client_id:
            success = self._manager.subscribe_channel(client_id, topic)
            if success:
                logger.debug(f"客户端 {client_id} 订阅主题: {topic}")
            return success
        return False

    async def unsubscribe(self, websocket: WebSocket, topic: str):
        """取消订阅主题"""
        client_id = self._socket_client_map.get(websocket)
        if client_id:
            success = self._manager.unsubscribe_channel(client_id, topic)
            if success:
                logger.debug(f"客户端 {client_id} 取消订阅主题: {topic}")
            return success
        return False

    async def send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]):
        """发送消息到指定连接"""
        client_id = self._socket_client_map.get(websocket)
        if client_id:
            ws_msg = WSMessage(
                type=message.get("type", "message"), 
                data=message.get("data", message)
            )
            return await self._manager.send_personal_message(ws_msg, client_id)
        return False

    async def send_to_user(self, user_id: int, message: Dict[str, Any]):
        """发送消息到指定用户的所有连接"""
        ws_msg = WSMessage(
            type=message.get("type", "message"), 
            data=message.get("data", message)
        )
        await self._manager.send_to_user(ws_msg, user_id)

    async def broadcast_to_topic(self, topic: str, message: Dict[str, Any]):
        """广播消息到订阅指定主题的所有用户"""
        ws_msg = WSMessage(
            type=message.get("type", "message"), 
            data=message.get("data", message)
        )
        await self._manager.broadcast_to_channel(ws_msg, topic)

    async def broadcast_to_all(self, message: Dict[str, Any]):
        """广播消息到所有连接"""
        ws_msg = WSMessage(
            type=message.get("type", "message"), 
            data=message.get("data", message)
        )
        await self._manager.broadcast_to_all(ws_msg)

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return self._manager.get_connection_info()


# 导出单例实例以便使用 (保持之前的模式)
websocket_manager = WebSocketManager()
