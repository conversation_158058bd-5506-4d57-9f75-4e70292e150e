const puppeteer = require('puppeteer-core');

async function debugHistoricalErrors() {
  const browser = await puppeteer.launch({
    executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();
  
  // 收集所有错误信息
  const errors = [];
  const warnings = [];
  const networkErrors = [];
  
  // 监听所有控制台消息
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    
    if (type === 'error') {
      errors.push(text);
      console.log(`🚨 [ERROR]: ${text}`);
    } else if (type === 'warn') {
      warnings.push(text);
      console.log(`⚠️ [WARN]: ${text}`);
    } else {
      console.log(`🖥️ [${type}]: ${text}`);
    }
  });

  // 监听页面错误
  page.on('pageerror', error => {
    const errorMsg = `页面错误: ${error.message}\n堆栈: ${error.stack}`;
    errors.push(errorMsg);
    console.log(`🚨 ${errorMsg}`);
  });

  // 监听网络请求失败
  page.on('requestfailed', request => {
    const failureMsg = `网络请求失败: ${request.method()} ${request.url()} - ${request.failure().errorText}`;
    networkErrors.push(failureMsg);
    console.log(`📡 ${failureMsg}`);
  });

  // 监听网络响应
  page.on('response', response => {
    if (!response.ok()) {
      const errorMsg = `HTTP错误: ${response.status()} ${response.url()}`;
      networkErrors.push(errorMsg);
      console.log(`📥 ${errorMsg}`);
    }
  });

  try {
    console.log('🚀 访问历史数据页面进行错误调试...');
    await page.goto('http://localhost:5173/market/historical', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // 等待页面完全加载
    await new Promise(resolve => setTimeout(resolve, 10000));

    // 检查Vue应用状态
    const vueStatus = await page.evaluate(() => {
      try {
        // 检查Vue应用是否正确挂载
        const app = document.querySelector('#app');
        if (!app) return { error: 'App element not found' };
        
        // 检查Vue实例
        const vueApp = app.__vue__;
        if (!vueApp) return { error: 'Vue instance not found' };
        
        // 检查路由状态
        const router = vueApp.$router;
        const route = vueApp.$route;
        
        // 检查组件状态
        const historicalComponent = document.querySelector('.historical-data-page');
        
        return {
          hasApp: !!app,
          hasVue: !!vueApp,
          hasRouter: !!router,
          currentRoute: route ? route.path : 'unknown',
          hasHistoricalComponent: !!historicalComponent,
          componentVisible: historicalComponent ? 
            (historicalComponent.offsetWidth > 0 && historicalComponent.offsetHeight > 0) : false,
          appChildren: vueApp.$children ? vueApp.$children.length : 0
        };
      } catch (e) {
        return { error: e.message, stack: e.stack };
      }
    });

    console.log('\n🔍 Vue应用状态:');
    console.log(JSON.stringify(vueStatus, null, 2));

    // 检查API调用状态
    const apiStatus = await page.evaluate(() => {
      try {
        // 检查是否有全局的API状态
        if (window.__APP_DEBUG__) {
          return {
            hasDebug: true,
            apiCalls: window.__APP_DEBUG__.apiCalls || [],
            errors: window.__APP_DEBUG__.errors || []
          };
        }
        
        // 检查网络请求状态
        const performanceEntries = performance.getEntriesByType('navigation');
        const resourceEntries = performance.getEntriesByType('resource');
        
        return {
          hasDebug: false,
          navigationTiming: performanceEntries[0] || null,
          resourceCount: resourceEntries.length,
          failedResources: resourceEntries.filter(entry => 
            entry.transferSize === 0 && entry.decodedBodySize === 0
          ).map(entry => entry.name)
        };
      } catch (e) {
        return { error: e.message };
      }
    });

    console.log('\n📡 API调用状态:');
    console.log(JSON.stringify(apiStatus, null, 2));

    // 检查Element Plus组件状态
    const elementStatus = await page.evaluate(() => {
      try {
        // 检查Element Plus是否正确加载
        const hasElementPlus = !!window.ElementPlus;
        
        // 检查表格组件
        const tables = document.querySelectorAll('.el-table');
        const tableStatus = Array.from(tables).map(table => ({
          visible: table.offsetWidth > 0 && table.offsetHeight > 0,
          rows: table.querySelectorAll('.el-table__row').length,
          hasData: table.querySelector('.el-table__empty-block') === null
        }));
        
        // 检查加载状态
        const loadingMasks = document.querySelectorAll('.el-loading-mask');
        
        // 检查错误消息
        const errorMessages = Array.from(document.querySelectorAll('.el-message--error'))
          .map(el => el.textContent);
        
        return {
          hasElementPlus,
          tableCount: tables.length,
          tableStatus,
          loadingMasks: loadingMasks.length,
          errorMessages
        };
      } catch (e) {
        return { error: e.message };
      }
    });

    console.log('\n🎨 Element Plus状态:');
    console.log(JSON.stringify(elementStatus, null, 2));

    // 检查具体的数据状态
    const dataStatus = await page.evaluate(() => {
      try {
        // 尝试获取组件数据
        const historicalPage = document.querySelector('.historical-data-page');
        if (!historicalPage) return { error: 'Historical page component not found' };
        
        // 检查统计数据
        const statNumbers = Array.from(document.querySelectorAll('.stat-number'))
          .map(el => el.textContent);
        
        // 检查表格数据
        const tableRows = document.querySelectorAll('.el-table__row');
        const tableData = Array.from(tableRows).slice(0, 3).map(row => {
          const cells = row.querySelectorAll('td');
          return Array.from(cells).map(cell => cell.textContent.trim()).slice(0, 5);
        });
        
        // 检查是否有"No Data"显示
        const noDataElements = document.querySelectorAll('.el-table__empty-text');
        const hasNoData = noDataElements.length > 0;
        
        return {
          statNumbers,
          tableRowCount: tableRows.length,
          sampleTableData: tableData,
          hasNoData,
          noDataText: hasNoData ? Array.from(noDataElements).map(el => el.textContent) : []
        };
      } catch (e) {
        return { error: e.message };
      }
    });

    console.log('\n📊 数据状态:');
    console.log(JSON.stringify(dataStatus, null, 2));

    // 汇总错误报告
    console.log('\n📋 错误汇总报告:');
    console.log('='.repeat(60));
    console.log(`🚨 JavaScript错误: ${errors.length}`);
    errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
    
    console.log(`⚠️ 警告信息: ${warnings.length}`);
    warnings.slice(0, 5).forEach((warning, index) => {
      console.log(`  ${index + 1}. ${warning}`);
    });
    
    console.log(`📡 网络错误: ${networkErrors.length}`);
    networkErrors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });

    // 尝试手动触发数据重新加载
    console.log('\n🔄 尝试手动重新加载数据...');
    await page.evaluate(() => {
      // 尝试重新加载页面数据
      if (window.location.reload) {
        // 不要真的刷新页面，而是尝试重新触发数据加载
        const event = new Event('load');
        window.dispatchEvent(event);
      }
    });

    await new Promise(resolve => setTimeout(resolve, 5000));

    // 最终状态检查
    const finalStatus = await page.evaluate(() => {
      return {
        tableRows: document.querySelectorAll('.el-table__row').length,
        contentLength: document.body.textContent.length,
        hasData: document.querySelector('.el-table__body') !== null,
        visibleElements: {
          historicalPage: !!document.querySelector('.historical-data-page'),
          statsCards: !!document.querySelector('.stats-cards'),
          searchCard: !!document.querySelector('.search-card'),
          stockTable: !!document.querySelector('.el-table')
        }
      };
    });

    console.log('\n🔍 最终状态:');
    console.log(JSON.stringify(finalStatus, null, 2));

    // 保持浏览器打开以便用户查看
    console.log('\n✅ 调试完成，浏览器保持打开状态供进一步检查...');
    await new Promise(resolve => setTimeout(resolve, 60000));

  } catch (error) {
    console.error('❌ 调试过程失败:', error);
  } finally {
    await browser.close();
  }
}

debugHistoricalErrors();
