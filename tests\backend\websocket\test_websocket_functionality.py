"""
WebSocket功能测试
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, <PERSON><PERSON>, patch
from datetime import datetime
from decimal import Decimal

import websockets
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app
from app.services.websocket_manager import WebSocketManager
from app.schemas.market_data import TickData, KlineData, MarketDepth


@pytest.mark.websocket
@pytest.mark.asyncio
class TestWebSocketFunctionality:
    """WebSocket功能测试类"""

    @pytest.fixture
    def websocket_manager(self):
        """WebSocket管理器实例"""
        return WebSocketManager()

    @pytest.fixture
    def mock_websocket(self):
        """模拟WebSocket连接"""
        mock_ws = AsyncMock()
        mock_ws.send_text = AsyncMock()
        mock_ws.send_json = AsyncMock()
        mock_ws.receive_text = AsyncMock()
        mock_ws.receive_json = AsyncMock()
        mock_ws.close = AsyncMock()
        mock_ws.closed = False
        return mock_ws

    @pytest.fixture
    def sample_tick_data(self):
        """示例Tick数据"""
        return TickData(
            symbol="000001",
            timestamp=datetime.now(),
            price=Decimal("10.50"),
            volume=1000,
            bid_price=Decimal("10.49"),
            ask_price=Decimal("10.51"),
            bid_volume=500,
            ask_volume=600
        )

    async def test_websocket_connection_establishment(self, websocket_manager, mock_websocket):
        """测试WebSocket连接建立"""
        client_id = "test-client-001"
        
        # 添加连接
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 验证连接已添加
        assert client_id in websocket_manager.connections
        assert websocket_manager.connections[client_id] == mock_websocket

    async def test_websocket_connection_removal(self, websocket_manager, mock_websocket):
        """测试WebSocket连接移除"""
        client_id = "test-client-001"
        
        # 添加连接
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 移除连接
        await websocket_manager.remove_connection(client_id)
        
        # 验证连接已移除
        assert client_id not in websocket_manager.connections

    async def test_websocket_message_sending(self, websocket_manager, mock_websocket):
        """测试WebSocket消息发送"""
        client_id = "test-client-001"
        message = {"type": "tick", "data": {"symbol": "000001", "price": "10.50"}}
        
        # 添加连接
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 发送消息
        await websocket_manager.send_to_client(client_id, message)
        
        # 验证消息已发送
        mock_websocket.send_json.assert_called_once_with(message)

    async def test_websocket_message_broadcasting(self, websocket_manager):
        """测试WebSocket消息广播"""
        # 创建多个客户端连接
        clients = {}
        for i in range(3):
            client_id = f"test-client-{i:03d}"
            mock_ws = AsyncMock()
            mock_ws.send_json = AsyncMock()
            clients[client_id] = mock_ws
            await websocket_manager.add_connection(client_id, mock_ws)
        
        message = {"type": "broadcast", "data": "广播消息"}
        
        # 广播消息
        await websocket_manager.broadcast(message)
        
        # 验证所有客户端都收到消息
        for mock_ws in clients.values():
            mock_ws.send_json.assert_called_once_with(message)

    async def test_websocket_subscription_management(self, websocket_manager, mock_websocket):
        """测试WebSocket订阅管理"""
        client_id = "test-client-001"
        symbol = "000001"
        
        # 添加连接
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 订阅
        await websocket_manager.subscribe(client_id, "tick", symbol)
        
        # 验证订阅
        assert client_id in websocket_manager.subscriptions["tick"][symbol]
        
        # 取消订阅
        await websocket_manager.unsubscribe(client_id, "tick", symbol)
        
        # 验证取消订阅
        assert client_id not in websocket_manager.subscriptions["tick"][symbol]

    async def test_websocket_tick_data_distribution(self, websocket_manager, mock_websocket, sample_tick_data):
        """测试Tick数据分发"""
        client_id = "test-client-001"
        symbol = sample_tick_data.symbol
        
        # 添加连接并订阅
        await websocket_manager.add_connection(client_id, mock_websocket)
        await websocket_manager.subscribe(client_id, "tick", symbol)
        
        # 分发Tick数据
        await websocket_manager.distribute_tick_data(sample_tick_data)
        
        # 验证数据已发送
        mock_websocket.send_json.assert_called_once()
        call_args = mock_websocket.send_json.call_args[0][0]
        assert call_args["type"] == "tick"
        assert call_args["data"]["symbol"] == symbol

    async def test_websocket_kline_data_distribution(self, websocket_manager, mock_websocket):
        """测试K线数据分发"""
        client_id = "test-client-001"
        kline_data = KlineData(
            symbol="000001",
            timestamp=datetime.now(),
            interval="1m",
            open_price=Decimal("10.45"),
            high_price=Decimal("10.55"),
            low_price=Decimal("10.40"),
            close_price=Decimal("10.50"),
            volume=50000,
            amount=Decimal("525000.00")
        )
        
        # 添加连接并订阅
        await websocket_manager.add_connection(client_id, mock_websocket)
        await websocket_manager.subscribe(client_id, "kline", f"{kline_data.symbol}_{kline_data.interval}")
        
        # 分发K线数据
        await websocket_manager.distribute_kline_data(kline_data)
        
        # 验证数据已发送
        mock_websocket.send_json.assert_called_once()
        call_args = mock_websocket.send_json.call_args[0][0]
        assert call_args["type"] == "kline"
        assert call_args["data"]["symbol"] == kline_data.symbol

    async def test_websocket_depth_data_distribution(self, websocket_manager, mock_websocket):
        """测试深度数据分发"""
        client_id = "test-client-001"
        depth_data = MarketDepth(
            symbol="000001",
            timestamp=datetime.now(),
            bids=[[Decimal("10.49"), Decimal("500")]],
            asks=[[Decimal("10.51"), Decimal("600")]]
        )
        
        # 添加连接并订阅
        await websocket_manager.add_connection(client_id, mock_websocket)
        await websocket_manager.subscribe(client_id, "depth", depth_data.symbol)
        
        # 分发深度数据
        await websocket_manager.distribute_depth_data(depth_data)
        
        # 验证数据已发送
        mock_websocket.send_json.assert_called_once()
        call_args = mock_websocket.send_json.call_args[0][0]
        assert call_args["type"] == "depth"
        assert call_args["data"]["symbol"] == depth_data.symbol

    async def test_websocket_heartbeat_mechanism(self, websocket_manager, mock_websocket):
        """测试心跳机制"""
        client_id = "test-client-001"
        
        # 添加连接
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 发送心跳
        await websocket_manager.send_heartbeat(client_id)
        
        # 验证心跳消息
        mock_websocket.send_json.assert_called_once()
        call_args = mock_websocket.send_json.call_args[0][0]
        assert call_args["type"] == "heartbeat"
        assert "timestamp" in call_args

    async def test_websocket_error_handling(self, websocket_manager):
        """测试WebSocket错误处理"""
        client_id = "test-client-001"
        
        # 模拟发送失败的WebSocket
        mock_ws = AsyncMock()
        mock_ws.send_json = AsyncMock(side_effect=Exception("连接已断开"))
        
        await websocket_manager.add_connection(client_id, mock_ws)
        
        message = {"type": "test", "data": "测试消息"}
        
        # 发送消息（应该优雅处理错误）
        try:
            await websocket_manager.send_to_client(client_id, message)
        except Exception:
            pytest.fail("WebSocket错误处理失败")
        
        # 验证连接被移除
        assert client_id not in websocket_manager.connections

    async def test_websocket_authentication(self, websocket_manager, mock_websocket):
        """测试WebSocket认证"""
        client_id = "test-client-001"
        token = "valid-token"
        
        # 模拟认证成功
        with patch("app.services.auth_service.AuthService.verify_token") as mock_verify:
            mock_verify.return_value = {"user_id": "user-001", "username": "testuser"}
            
            # 认证客户端
            auth_result = await websocket_manager.authenticate_client(client_id, token)
            
            assert auth_result is True
            assert websocket_manager.authenticated_clients[client_id]["user_id"] == "user-001"

    async def test_websocket_unauthorized_access(self, websocket_manager, mock_websocket):
        """测试WebSocket未授权访问"""
        client_id = "test-client-001"
        invalid_token = "invalid-token"
        
        # 模拟认证失败
        with patch("app.services.auth_service.AuthService.verify_token") as mock_verify:
            mock_verify.return_value = None
            
            # 认证客户端
            auth_result = await websocket_manager.authenticate_client(client_id, invalid_token)
            
            assert auth_result is False
            assert client_id not in websocket_manager.authenticated_clients

    async def test_websocket_subscription_permissions(self, websocket_manager, mock_websocket):
        """测试WebSocket订阅权限"""
        client_id = "test-client-001"
        symbol = "000001"
        
        # 添加已认证的客户端
        await websocket_manager.add_connection(client_id, mock_websocket)
        websocket_manager.authenticated_clients[client_id] = {
            "user_id": "user-001",
            "username": "testuser",
            "permissions": ["market_data:read"]
        }
        
        # 订阅（应该成功）
        result = await websocket_manager.subscribe(client_id, "tick", symbol)
        
        assert result is True
        assert client_id in websocket_manager.subscriptions["tick"][symbol]

    async def test_websocket_subscription_permission_denied(self, websocket_manager, mock_websocket):
        """测试WebSocket订阅权限拒绝"""
        client_id = "test-client-001"
        symbol = "000001"
        
        # 添加无权限的客户端
        await websocket_manager.add_connection(client_id, mock_websocket)
        websocket_manager.authenticated_clients[client_id] = {
            "user_id": "user-001",
            "username": "testuser",
            "permissions": []  # 无权限
        }
        
        # 订阅（应该失败）
        result = await websocket_manager.subscribe(client_id, "tick", symbol)
        
        assert result is False
        assert client_id not in websocket_manager.subscriptions["tick"].get(symbol, [])

    async def test_websocket_concurrent_connections(self, websocket_manager):
        """测试WebSocket并发连接"""
        # 创建多个并发连接
        tasks = []
        client_count = 100
        
        for i in range(client_count):
            client_id = f"client-{i:03d}"
            mock_ws = AsyncMock()
            task = websocket_manager.add_connection(client_id, mock_ws)
            tasks.append(task)
        
        # 等待所有连接完成
        await asyncio.gather(*tasks)
        
        # 验证所有连接都已建立
        assert len(websocket_manager.connections) == client_count

    async def test_websocket_high_frequency_messages(self, websocket_manager, mock_websocket):
        """测试WebSocket高频消息处理"""
        client_id = "test-client-001"
        message_count = 1000
        
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 快速发送大量消息
        tasks = []
        for i in range(message_count):
            message = {"type": "test", "data": f"message-{i}"}
            task = websocket_manager.send_to_client(client_id, message)
            tasks.append(task)
        
        # 等待所有消息发送完成
        await asyncio.gather(*tasks)
        
        # 验证消息发送次数
        assert mock_websocket.send_json.call_count == message_count

    async def test_websocket_memory_cleanup(self, websocket_manager):
        """测试WebSocket内存清理"""
        # 添加大量连接
        for i in range(100):
            client_id = f"client-{i:03d}"
            mock_ws = AsyncMock()
            await websocket_manager.add_connection(client_id, mock_ws)
            
            # 添加订阅
            await websocket_manager.subscribe(client_id, "tick", "000001")
        
        # 执行清理
        await websocket_manager.cleanup_disconnected_clients()
        
        # 验证内存使用情况
        active_connections = len(websocket_manager.connections)
        subscription_count = sum(
            len(clients) for topic_subs in websocket_manager.subscriptions.values()
            for clients in topic_subs.values()
        )
        
        # 确保没有内存泄漏
        assert active_connections == subscription_count

    async def test_websocket_message_compression(self, websocket_manager, mock_websocket):
        """测试WebSocket消息压缩"""
        client_id = "test-client-001"
        
        # 启用压缩
        websocket_manager.enable_compression = True
        
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 发送大消息
        large_data = {"type": "large_data", "data": "x" * 10000}
        await websocket_manager.send_to_client(client_id, large_data)
        
        # 验证消息被发送（压缩逻辑在实际实现中处理）
        mock_websocket.send_json.assert_called_once()

    async def test_websocket_rate_limiting(self, websocket_manager, mock_websocket):
        """测试WebSocket频率限制"""
        client_id = "test-client-001"
        
        # 启用频率限制
        websocket_manager.enable_rate_limiting = True
        websocket_manager.rate_limit = 10  # 每秒10条消息
        
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 快速发送超过限制的消息
        messages_sent = 0
        for i in range(20):
            try:
                message = {"type": "test", "data": f"message-{i}"}
                await websocket_manager.send_to_client(client_id, message)
                messages_sent += 1
            except Exception as e:
                if "rate limit" in str(e).lower():
                    break
        
        # 验证频率限制生效
        assert messages_sent <= 10

    async def test_websocket_graceful_shutdown(self, websocket_manager):
        """测试WebSocket优雅关闭"""
        # 添加多个连接
        mock_clients = {}
        for i in range(5):
            client_id = f"client-{i:03d}"
            mock_ws = AsyncMock()
            mock_ws.close = AsyncMock()
            mock_clients[client_id] = mock_ws
            await websocket_manager.add_connection(client_id, mock_ws)
        
        # 执行优雅关闭
        await websocket_manager.shutdown()
        
        # 验证所有连接都被关闭
        for mock_ws in mock_clients.values():
            mock_ws.close.assert_called_once()
        
        # 验证管理器状态被清理
        assert len(websocket_manager.connections) == 0
        assert len(websocket_manager.subscriptions) == 0

    async def test_websocket_reconnection_handling(self, websocket_manager):
        """测试WebSocket重连处理"""
        client_id = "test-client-001"
        
        # 第一次连接
        mock_ws1 = AsyncMock()
        await websocket_manager.add_connection(client_id, mock_ws1)
        await websocket_manager.subscribe(client_id, "tick", "000001")
        
        # 模拟断开
        await websocket_manager.remove_connection(client_id)
        
        # 重新连接
        mock_ws2 = AsyncMock()
        await websocket_manager.add_connection(client_id, mock_ws2)
        
        # 验证新连接已建立
        assert websocket_manager.connections[client_id] == mock_ws2
        
        # 验证需要重新订阅
        assert client_id not in websocket_manager.subscriptions["tick"].get("000001", [])

    async def test_websocket_protocol_messages(self, websocket_manager, mock_websocket):
        """测试WebSocket协议消息"""
        client_id = "test-client-001"
        await websocket_manager.add_connection(client_id, mock_websocket)
        
        # 测试各种协议消息
        protocol_messages = [
            {"type": "ping"},
            {"type": "pong"},
            {"type": "subscribe", "topic": "tick", "symbols": ["000001"]},
            {"type": "unsubscribe", "topic": "tick", "symbols": ["000001"]},
            {"type": "get_subscriptions"}
        ]
        
        for message in protocol_messages:
            await websocket_manager.handle_client_message(client_id, message)
        
        # 验证消息被处理（具体验证逻辑取决于实现）
        assert True  # 如果没有异常，说明消息处理成功