"""
指标监控单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal
import time

from app.monitoring.metrics import (
    MetricsCollector,
    PrometheusExporter,
    APIMetrics,
    TradingMetrics,
    MarketDataMetrics,
    BacktestMetrics,
    UserMetrics,
    DatabaseMetrics,
    CacheMetrics,
    TaskMetrics,
    WebSocketMetrics,
    SystemMetrics,
    MetricType,
    MetricRegistry,
    CustomMetric,
    MetricsAggregator,
    MetricsReporter,
)


@pytest.mark.unit
@pytest.mark.metrics
class TestMetrics:
    """指标监控测试类"""

    @pytest.fixture
    def metrics_collector(self):
        """指标收集器"""
        return MetricsCollector()

    @pytest.fixture
    def prometheus_exporter(self):
        """Prometheus导出器"""
        return PrometheusExporter()

    @pytest.fixture
    def api_metrics(self):
        """API指标"""
        return APIMetrics()

    @pytest.fixture
    def trading_metrics(self):
        """交易指标"""
        return TradingMetrics()

    @pytest.fixture
    def market_data_metrics(self):
        """市场数据指标"""
        return MarketDataMetrics()

    @pytest.fixture
    def backtest_metrics(self):
        """回测指标"""
        return BacktestMetrics()

    @pytest.fixture
    def user_metrics(self):
        """用户指标"""
        return UserMetrics()

    @pytest.fixture
    def database_metrics(self):
        """数据库指标"""
        return DatabaseMetrics()

    @pytest.fixture
    def cache_metrics(self):
        """缓存指标"""
        return CacheMetrics()

    @pytest.fixture
    def task_metrics(self):
        """任务指标"""
        return TaskMetrics()

    @pytest.fixture
    def websocket_metrics(self):
        """WebSocket指标"""
        return WebSocketMetrics()

    @pytest.fixture
    def system_metrics(self):
        """系统指标"""
        return SystemMetrics()

    @pytest.fixture
    def metric_registry(self):
        """指标注册表"""
        return MetricRegistry()

    @pytest.fixture
    def metrics_aggregator(self):
        """指标聚合器"""
        return MetricsAggregator()

    @pytest.fixture
    def metrics_reporter(self):
        """指标报告器"""
        return MetricsReporter()

    def test_metrics_collector_initialization(self, metrics_collector):
        """测试指标收集器初始化"""
        assert metrics_collector is not None
        assert hasattr(metrics_collector, "metrics")
        assert hasattr(metrics_collector, "collectors")

    def test_prometheus_exporter_initialization(self, prometheus_exporter):
        """测试Prometheus导出器初始化"""
        assert prometheus_exporter is not None
        assert hasattr(prometheus_exporter, "registry")
        assert hasattr(prometheus_exporter, "metrics")

    def test_api_metrics_record_request(self, api_metrics):
        """测试API指标记录请求"""
        endpoint = "/api/v1/orders"
        method = "POST"
        status_code = 200
        response_time = 0.123

        api_metrics.record_request(endpoint, method, status_code, response_time)

        # 验证指标被记录
        assert (
            api_metrics.request_count.labels(
                endpoint=endpoint, method=method, status=status_code
            )._value._value
            == 1
        )
        assert (
            api_metrics.request_duration.labels(
                endpoint=endpoint, method=method
            )._sum._value
            > 0
        )

    def test_api_metrics_record_error(self, api_metrics):
        """测试API指标记录错误"""
        endpoint = "/api/v1/orders"
        method = "POST"
        error_type = "ValidationError"

        api_metrics.record_error(endpoint, method, error_type)

        # 验证错误指标被记录
        assert (
            api_metrics.error_count.labels(
                endpoint=endpoint, method=method, error_type=error_type
            )._value._value
            == 1
        )

    def test_trading_metrics_record_order(self, trading_metrics):
        """测试交易指标记录订单"""
        symbol = "rb2405"
        side = "buy"
        quantity = 1
        price = Decimal("3850.0")

        trading_metrics.record_order(symbol, side, quantity, price)

        # 验证订单指标被记录
        assert (
            trading_metrics.order_count.labels(symbol=symbol, side=side)._value._value
            == 1
        )
        assert (
            trading_metrics.order_volume.labels(symbol=symbol, side=side)._value._value
            == quantity
        )

    def test_trading_metrics_record_trade(self, trading_metrics):
        """测试交易指标记录成交"""
        symbol = "rb2405"
        side = "buy"
        quantity = 1
        price = Decimal("3850.0")
        commission = Decimal("5.0")

        trading_metrics.record_trade(symbol, side, quantity, price, commission)

        # 验证成交指标被记录
        assert (
            trading_metrics.trade_count.labels(symbol=symbol, side=side)._value._value
            == 1
        )
        assert (
            trading_metrics.trade_volume.labels(symbol=symbol, side=side)._value._value
            == quantity
        )
        assert trading_metrics.commission_total.labels(
            symbol=symbol
        )._value._value == float(commission)

    def test_trading_metrics_record_position(self, trading_metrics):
        """测试交易指标记录持仓"""
        symbol = "rb2405"
        side = "long"
        quantity = 5
        avg_price = Decimal("3850.0")
        unrealized_pnl = Decimal("250.0")

        trading_metrics.record_position(
            symbol, side, quantity, avg_price, unrealized_pnl
        )

        # 验证持仓指标被记录
        assert (
            trading_metrics.position_count.labels(
                symbol=symbol, side=side
            )._value._value
            == quantity
        )
        assert trading_metrics.unrealized_pnl.labels(
            symbol=symbol, side=side
        )._value._value == float(unrealized_pnl)

    def test_market_data_metrics_record_tick(self, market_data_metrics):
        """测试市场数据指标记录Tick"""
        symbol = "rb2405"
        exchange = "SHFE"

        market_data_metrics.record_tick(symbol, exchange)

        # 验证Tick指标被记录
        assert (
            market_data_metrics.tick_count.labels(
                symbol=symbol, exchange=exchange
            )._value._value
            == 1
        )

    def test_market_data_metrics_record_kline(self, market_data_metrics):
        """测试市场数据指标记录K线"""
        symbol = "rb2405"
        exchange = "SHFE"
        interval = "1m"

        market_data_metrics.record_kline(symbol, exchange, interval)

        # 验证K线指标被记录
        assert (
            market_data_metrics.kline_count.labels(
                symbol=symbol, exchange=exchange, interval=interval
            )._value._value
            == 1
        )

    def test_market_data_metrics_record_subscription(self, market_data_metrics):
        """测试市场数据指标记录订阅"""
        symbol = "rb2405"
        data_type = "tick"

        market_data_metrics.record_subscription(symbol, data_type)

        # 验证订阅指标被记录
        assert (
            market_data_metrics.subscription_count.labels(
                symbol=symbol, data_type=data_type
            )._value._value
            == 1
        )

    def test_backtest_metrics_record_backtest(self, backtest_metrics):
        """测试回测指标记录回测"""
        strategy_id = "test_strategy"
        status = "completed"
        duration = 120.5

        backtest_metrics.record_backtest(strategy_id, status, duration)

        # 验证回测指标被记录
        assert (
            backtest_metrics.backtest_count.labels(
                strategy_id=strategy_id, status=status
            )._value._value
            == 1
        )
        assert (
            backtest_metrics.backtest_duration.labels(
                strategy_id=strategy_id
            )._sum._value
            > 0
        )

    def test_backtest_metrics_record_performance(self, backtest_metrics):
        """测试回测指标记录绩效"""
        strategy_id = "test_strategy"
        total_return = 0.15
        sharpe_ratio = 1.2
        max_drawdown = 0.05

        backtest_metrics.record_performance(
            strategy_id, total_return, sharpe_ratio, max_drawdown
        )

        # 验证绩效指标被记录
        assert (
            backtest_metrics.total_return.labels(strategy_id=strategy_id)._value._value
            == total_return
        )
        assert (
            backtest_metrics.sharpe_ratio.labels(strategy_id=strategy_id)._value._value
            == sharpe_ratio
        )
        assert (
            backtest_metrics.max_drawdown.labels(strategy_id=strategy_id)._value._value
            == max_drawdown
        )

    def test_user_metrics_record_login(self, user_metrics):
        """测试用户指标记录登录"""
        user_id = "user123"
        login_method = "password"

        user_metrics.record_login(user_id, login_method)

        # 验证登录指标被记录
        assert (
            user_metrics.login_count.labels(
                user_id=user_id, method=login_method
            )._value._value
            == 1
        )

    def test_user_metrics_record_action(self, user_metrics):
        """测试用户指标记录操作"""
        user_id = "user123"
        action = "place_order"

        user_metrics.record_action(user_id, action)

        # 验证操作指标被记录
        assert (
            user_metrics.user_action_count.labels(
                user_id=user_id, action=action
            )._value._value
            == 1
        )

    def test_user_metrics_update_active_users(self, user_metrics):
        """测试用户指标更新活跃用户"""
        active_count = 25

        user_metrics.update_active_users(active_count)

        # 验证活跃用户指标被更新
        assert user_metrics.active_users.get() == active_count

    def test_database_metrics_record_query(self, database_metrics):
        """测试数据库指标记录查询"""
        table = "orders"
        operation = "SELECT"
        duration = 0.045

        database_metrics.record_query(table, operation, duration)

        # 验证查询指标被记录
        assert (
            database_metrics.query_count.labels(
                table=table, operation=operation
            )._value._value
            == 1
        )
        assert (
            database_metrics.query_duration.labels(
                table=table, operation=operation
            )._sum._value
            > 0
        )

    def test_database_metrics_record_connection(self, database_metrics):
        """测试数据库指标记录连接"""
        database_metrics.record_connection_acquired()
        database_metrics.record_connection_released()

        # 验证连接指标被记录
        assert database_metrics.connection_acquired_count._value._value == 1
        assert database_metrics.connection_released_count._value._value == 1

    def test_cache_metrics_record_operation(self, cache_metrics):
        """测试缓存指标记录操作"""
        operation = "get"
        key_type = "market_data"
        hit = True

        cache_metrics.record_operation(operation, key_type, hit)

        # 验证缓存指标被记录
        assert (
            cache_metrics.cache_operations.labels(
                operation=operation, key_type=key_type
            )._value._value
            == 1
        )
        assert cache_metrics.cache_hits.labels(key_type=key_type)._value._value == 1

    def test_cache_metrics_update_memory_usage(self, cache_metrics):
        """测试缓存指标更新内存使用"""
        memory_usage = 1024 * 1024  # 1MB

        cache_metrics.update_memory_usage(memory_usage)

        # 验证内存使用指标被更新
        assert cache_metrics.memory_usage.get() == memory_usage

    def test_task_metrics_record_task(self, task_metrics):
        """测试任务指标记录任务"""
        task_name = "sync_market_data"
        status = "success"
        duration = 45.2

        task_metrics.record_task(task_name, status, duration)

        # 验证任务指标被记录
        assert (
            task_metrics.task_count.labels(
                task_name=task_name, status=status
            )._value._value
            == 1
        )
        assert task_metrics.task_duration.labels(task_name=task_name)._sum._value > 0

    def test_task_metrics_update_queue_size(self, task_metrics):
        """测试任务指标更新队列大小"""
        queue_name = "default"
        size = 10

        task_metrics.update_queue_size(queue_name, size)

        # 验证队列大小指标被更新
        assert task_metrics.queue_size.labels(queue=queue_name).get() == size

    def test_websocket_metrics_record_connection(self, websocket_metrics):
        """测试WebSocket指标记录连接"""
        websocket_metrics.record_connection_opened()
        websocket_metrics.record_connection_closed()

        # 验证连接指标被记录
        assert websocket_metrics.connection_opened_count._value._value == 1
        assert websocket_metrics.connection_closed_count._value._value == 1

    def test_websocket_metrics_record_message(self, websocket_metrics):
        """测试WebSocket指标记录消息"""
        message_type = "market_data"
        direction = "outbound"

        websocket_metrics.record_message(message_type, direction)

        # 验证消息指标被记录
        assert (
            websocket_metrics.message_count.labels(
                message_type=message_type, direction=direction
            )._value._value
            == 1
        )

    def test_websocket_metrics_update_active_connections(self, websocket_metrics):
        """测试WebSocket指标更新活跃连接"""
        active_count = 50

        websocket_metrics.update_active_connections(active_count)

        # 验证活跃连接指标被更新
        assert websocket_metrics.active_connections.get() == active_count

    def test_system_metrics_update_cpu_usage(self, system_metrics):
        """测试系统指标更新CPU使用率"""
        cpu_usage = 65.5

        system_metrics.update_cpu_usage(cpu_usage)

        # 验证CPU使用率指标被更新
        assert system_metrics.cpu_usage.get() == cpu_usage

    def test_system_metrics_update_memory_usage(self, system_metrics):
        """测试系统指标更新内存使用"""
        memory_usage = 1024 * 1024 * 512  # 512MB

        system_metrics.update_memory_usage(memory_usage)

        # 验证内存使用指标被更新
        assert system_metrics.memory_usage.get() == memory_usage

    def test_system_metrics_update_disk_usage(self, system_metrics):
        """测试系统指标更新磁盘使用"""
        disk_usage = 1024 * 1024 * 1024 * 10  # 10GB

        system_metrics.update_disk_usage(disk_usage)

        # 验证磁盘使用指标被更新
        assert system_metrics.disk_usage.get() == disk_usage

    def test_custom_metric_creation(self, metric_registry):
        """测试自定义指标创建"""
        metric_name = "custom_counter"
        metric_type = MetricType.COUNTER
        description = "A custom counter metric"
        labels = ["label1", "label2"]

        custom_metric = metric_registry.create_metric(
            metric_name, metric_type, description, labels
        )

        assert custom_metric is not None
        assert custom_metric.name == metric_name
        assert custom_metric.type == metric_type
        assert custom_metric.description == description
        assert custom_metric.labels == labels

    def test_custom_metric_registration(self, metric_registry):
        """测试自定义指标注册"""
        metric_name = "test_gauge"
        metric_type = MetricType.GAUGE
        description = "A test gauge metric"

        custom_metric = CustomMetric(metric_name, metric_type, description)
        metric_registry.register_metric(custom_metric)

        # 验证指标已注册
        assert metric_name in metric_registry.metrics
        assert metric_registry.metrics[metric_name] == custom_metric

    def test_metrics_aggregator_aggregate_by_time(self, metrics_aggregator):
        """测试指标聚合器按时间聚合"""
        metrics_data = [
            {"timestamp": datetime.now() - timedelta(minutes=5), "value": 10},
            {"timestamp": datetime.now() - timedelta(minutes=3), "value": 20},
            {"timestamp": datetime.now() - timedelta(minutes=1), "value": 30},
        ]

        aggregated = metrics_aggregator.aggregate_by_time(metrics_data, "5min")

        assert aggregated is not None
        assert len(aggregated) > 0

    def test_metrics_aggregator_calculate_percentiles(self, metrics_aggregator):
        """测试指标聚合器计算百分位数"""
        values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        percentiles = [50, 90, 95, 99]

        result = metrics_aggregator.calculate_percentiles(values, percentiles)

        assert result is not None
        assert len(result) == len(percentiles)
        assert result[50] == 5.5  # 中位数
        assert result[90] == 9.1

    def test_metrics_reporter_generate_report(self, metrics_reporter):
        """测试指标报告器生成报告"""
        start_time = datetime.now() - timedelta(hours=1)
        end_time = datetime.now()

        with patch.object(metrics_reporter, "_collect_metrics_data") as mock_collect:
            mock_collect.return_value = {
                "api_requests": 1000,
                "trading_volume": 50000,
                "active_users": 25,
            }

            report = metrics_reporter.generate_report(start_time, end_time)

        assert report is not None
        assert "api_requests" in report
        assert "trading_volume" in report
        assert "active_users" in report

    def test_prometheus_exporter_export_metrics(self, prometheus_exporter):
        """测试Prometheus导出器导出指标"""
        with patch("prometheus_client.generate_latest") as mock_generate:
            mock_generate.return_value = b"# HELP test_metric A test metric\n# TYPE test_metric counter\ntest_metric 1\n"

            metrics_output = prometheus_exporter.export_metrics()

        assert metrics_output is not None
        assert b"test_metric" in metrics_output

    def test_metrics_collector_collect_all(self, metrics_collector):
        """测试指标收集器收集所有指标"""
        with patch.object(metrics_collector, "_collect_api_metrics") as mock_api:
            with patch.object(
                metrics_collector, "_collect_trading_metrics"
            ) as mock_trading:
                with patch.object(
                    metrics_collector, "_collect_system_metrics"
                ) as mock_system:
                    mock_api.return_value = {"api_requests": 1000}
                    mock_trading.return_value = {"total_trades": 50}
                    mock_system.return_value = {"cpu_usage": 65.5}

                    all_metrics = metrics_collector.collect_all()

        assert all_metrics is not None
        assert "api_requests" in all_metrics
        assert "total_trades" in all_metrics
        assert "cpu_usage" in all_metrics

    def test_metrics_performance_with_high_volume(self, api_metrics):
        """测试高并发下的指标性能"""
        import threading
        import time

        def record_metrics():
            for i in range(1000):
                api_metrics.record_request("/api/test", "GET", 200, 0.1)

        # 创建多个线程并发记录指标
        threads = []
        start_time = time.time()

        for _ in range(10):
            thread = threading.Thread(target=record_metrics)
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        end_time = time.time()
        duration = end_time - start_time

        # 验证性能 - 10000个指标记录应该在1秒内完成
        assert duration < 1.0
        assert (
            api_metrics.request_count.labels(
                endpoint="/api/test", method="GET", status=200
            )._value._value
            == 10000
        )

    def test_metrics_memory_efficiency(self, trading_metrics):
        """测试指标内存效率"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 记录大量指标
        for i in range(10000):
            trading_metrics.record_order(
                f"symbol_{i % 100}", "buy", 1, Decimal("100.0")
            )

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该控制在合理范围内
        assert memory_increase < 50  # 少于50MB

    def test_metrics_thread_safety(self, api_metrics):
        """测试指标线程安全性"""
        import threading
        import concurrent.futures

        def record_metric(thread_id):
            for i in range(100):
                api_metrics.record_request(f"/api/test_{thread_id}", "GET", 200, 0.1)

        # 并发执行多个线程
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(record_metric, i) for i in range(10)]
            concurrent.futures.wait(futures)

        # 验证所有指标都被正确记录
        total_requests = 0
        for i in range(10):
            metric = api_metrics.request_count.labels(
                endpoint=f"/api/test_{i}", method="GET", status=200
            )
            total_requests += metric._value._value

        assert total_requests == 1000

    def test_metrics_cleanup(self, metrics_collector):
        """测试指标清理"""
        # 添加一些指标
        metrics_collector.metrics["test_metric"] = 100
        metrics_collector.metrics["old_metric"] = 200

        # 清理指标
        metrics_collector.cleanup_metrics(max_age_hours=1)

        # 验证指标数量
        assert len(metrics_collector.metrics) >= 0

    def test_metrics_persistence(self, metrics_collector):
        """测试指标持久化"""
        metrics_data = {
            "api_requests": 1000,
            "trading_volume": 50000,
            "timestamp": datetime.now(),
        }

        with patch.object(metrics_collector, "_save_to_storage") as mock_save:
            metrics_collector.persist_metrics(metrics_data)

        mock_save.assert_called_once_with(metrics_data)

    def test_metrics_alerting(self, metrics_collector):
        """测试指标告警"""
        # 设置告警阈值
        thresholds = {"cpu_usage": 80.0, "memory_usage": 90.0, "error_rate": 5.0}

        # 模拟超过阈值的指标
        metrics_data = {"cpu_usage": 85.0, "memory_usage": 75.0, "error_rate": 8.0}

        with patch.object(metrics_collector, "_send_alert") as mock_alert:
            alerts = metrics_collector.check_thresholds(metrics_data, thresholds)

        assert len(alerts) == 2  # CPU和错误率超过阈值
        assert "cpu_usage" in [alert["metric"] for alert in alerts]
        assert "error_rate" in [alert["metric"] for alert in alerts]

    def test_metrics_dashboard_data(self, metrics_collector):
        """测试指标仪表盘数据"""
        with patch.object(metrics_collector, "collect_all") as mock_collect:
            mock_collect.return_value = {
                "api_requests_total": 10000,
                "trading_volume_total": 500000,
                "active_users": 50,
                "system_cpu_usage": 65.5,
                "system_memory_usage": 1024 * 1024 * 512,
            }

            dashboard_data = metrics_collector.get_dashboard_data()

        assert dashboard_data is not None
        assert "api_requests_total" in dashboard_data
        assert "trading_volume_total" in dashboard_data
        assert "active_users" in dashboard_data

    def test_metrics_export_formats(self, prometheus_exporter):
        """测试指标导出格式"""
        metrics_data = {"api_requests": 1000, "trading_volume": 50000}

        # 测试JSON格式
        json_output = prometheus_exporter.export_json(metrics_data)
        assert json_output is not None
        assert "api_requests" in json_output

        # 测试CSV格式
        csv_output = prometheus_exporter.export_csv(metrics_data)
        assert csv_output is not None
        assert "api_requests" in csv_output

    def test_metrics_historical_data(self, metrics_collector):
        """测试指标历史数据"""
        start_time = datetime.now() - timedelta(hours=24)
        end_time = datetime.now()

        with patch.object(metrics_collector, "_query_historical_data") as mock_query:
            mock_query.return_value = [
                {"timestamp": start_time, "value": 100},
                {"timestamp": end_time, "value": 200},
            ]

            historical_data = metrics_collector.get_historical_data(
                "api_requests", start_time, end_time
            )

        assert historical_data is not None
        assert len(historical_data) == 2
        assert historical_data[0]["value"] == 100
        assert historical_data[1]["value"] == 200
