const puppeteer = require('puppeteer-core');

async function testComponentLoading() {
  const browser = await puppeteer.launch({
    executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();
  
  // 监听所有控制台消息
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ [${type}]: ${text}`);
  });

  // 监听页面错误
  page.on('pageerror', error => {
    console.log(`🚨 页面错误: ${error.message}`);
    console.log(`🚨 错误堆栈: ${error.stack}`);
  });

  // 监听网络请求失败
  page.on('requestfailed', request => {
    console.log(`🌐 请求失败: ${request.url()} - ${request.failure().errorText}`);
  });

  try {
    console.log('🚀 访问首页...');
    await page.goto('http://localhost:5173/', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // 检查首页状态
    const homeInfo = await page.evaluate(() => {
      return {
        currentPath: window.location.pathname,
        hasDefaultLayout: !!document.querySelector('.default-layout'),
        hasRouterView: !!document.querySelector('router-view'),
        hasVueApp: !!window.Vue || !!document.querySelector('#app').__vue__,
        contentLength: document.body.textContent.length
      };
    });

    console.log('\n📋 首页状态:');
    console.log(JSON.stringify(homeInfo, null, 2));

    // 尝试导航到实时行情页面
    console.log('\n🧭 导航到实时行情页面...');
    await page.goto('http://localhost:5173/market/realtime', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    await new Promise(resolve => setTimeout(resolve, 8000));

    // 检查导航后的状态
    const marketInfo = await page.evaluate(() => {
      return {
        currentPath: window.location.pathname,
        hasMarketView: !!document.querySelector('.market-view-optimized'),
        hasMarketHeader: !!document.querySelector('.market-header'),
        hasMarketIndices: !!document.querySelector('.market-indices'),
        hasStockTable: !!document.querySelector('.el-table'),
        hasChartSection: !!document.querySelector('.market-chart-section'),
        hasChartContainer: !!document.querySelector('.market-chart'),
        contentLength: document.body.textContent.length,
        
        // 检查错误状态
        hasErrorContainer: !!document.querySelector('.error-container'),
        hasLoadingContainer: !!document.querySelector('.loading-container'),
        
        // 检查Vue组件状态
        vueComponents: Array.from(document.querySelectorAll('[data-v-]')).length,
        
        // 检查具体的错误信息
        errorMessages: Array.from(document.querySelectorAll('.el-result, .error-container')).map(el => el.textContent),
        
        // 检查loading状态
        loadingElements: Array.from(document.querySelectorAll('.el-skeleton, .loading-container')).length
      };
    });

    console.log('\n📊 实时行情页面状态:');
    console.log(JSON.stringify(marketInfo, null, 2));

    // 如果MarketView不存在，尝试手动导航
    if (!marketInfo.hasMarketView) {
      console.log('\n🔧 MarketView不存在，尝试手动导航...');
      
      await page.evaluate(() => {
        // 尝试通过Vue Router导航
        const app = document.querySelector('#app').__vue__;
        if (app && app.$router) {
          app.$router.push('/market/realtime');
          return 'Router navigation triggered';
        }
        return 'Router not available';
      });

      await new Promise(resolve => setTimeout(resolve, 3000));

      // 再次检查
      const finalCheck = await page.evaluate(() => {
        return {
          currentPath: window.location.pathname,
          hasMarketView: !!document.querySelector('.market-view-optimized'),
          contentLength: document.body.textContent.length,
          routerViewContent: document.querySelector('.layout-content')?.innerHTML?.length || 0
        };
      });

      console.log('\n🔍 手动导航后状态:');
      console.log(JSON.stringify(finalCheck, null, 2));
    }

    // 检查Vue Router的状态
    const routerState = await page.evaluate(() => {
      try {
        const app = document.querySelector('#app').__vue__;
        if (app && app.$router) {
          const currentRoute = app.$router.currentRoute.value || app.$router.currentRoute;
          return {
            currentRouteName: currentRoute.name,
            currentRoutePath: currentRoute.path,
            routeParams: currentRoute.params,
            routeMeta: currentRoute.meta,
            totalRoutes: app.$router.getRoutes ? app.$router.getRoutes().length : 'unknown'
          };
        }
        return { error: 'Router not found' };
      } catch (e) {
        return { error: e.message };
      }
    });

    console.log('\n🧭 Vue Router状态:');
    console.log(JSON.stringify(routerState, null, 2));

    // 保持浏览器打开
    console.log('\n✅ 测试完成，浏览器保持打开状态...');
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

testComponentLoading();
