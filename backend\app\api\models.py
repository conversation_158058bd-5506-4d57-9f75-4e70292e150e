"""
API数据模型
定义所有API接口的请求和响应模型
"""
from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, date
from enum import Enum

# 基础枚举类型
class BacktestStatus(str, Enum):
    """回测状态"""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class StrategyTypeEnum(str, Enum):
    """策略类型"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    PAIRS_TRADING = "pairs_trading"
    ARBITRAGE = "arbitrage"
    FACTOR = "factor"
    ML = "machine_learning"

class SlippageType(str, Enum):
    """滑点类型"""
    PERCENTAGE = "percentage"
    FIXED = "fixed"
    VOLUME_BASED = "volume_based"
    MARKET_IMPACT = "market_impact"

# 配置模型
class StrategyConfigModel(BaseModel):
    """策略配置模型"""
    name: str = Field(..., description="策略名称")
    description: str = Field(..., description="策略描述")
    strategy_type: StrategyTypeEnum = Field(..., description="策略类型")
    version: str = Field("1.0.0", description="策略版本")
    
    # 参数配置
    parameters: Dict[str, Any] = Field(default_factory=dict, description="策略参数")
    
    # 交易设置
    universe: List[str] = Field(default_factory=list, description="股票池")
    max_positions: int = Field(10, description="最大持仓数", ge=1, le=100)
    position_size: float = Field(0.1, description="单股仓位", gt=0, le=1)
    
    # 风控设置
    stop_loss: Optional[float] = Field(None, description="止损比例", ge=0, le=1)
    take_profit: Optional[float] = Field(None, description="止盈比例", ge=0, le=5)
    max_drawdown: float = Field(0.2, description="最大回撤", ge=0, le=1)
    
    # 回测设置
    start_date: Optional[Union[str, date]] = Field(None, description="开始日期")
    end_date: Optional[Union[str, date]] = Field(None, description="结束日期")
    benchmark: str = Field("000300.SH", description="基准指数")
    
    @validator('start_date', 'end_date', pre=True)
    def validate_dates(cls, v):
        if isinstance(v, str):
            try:
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v

class BacktestConfigModel(BaseModel):
    """回测配置模型"""
    initial_capital: float = Field(1000000, description="初始资金", gt=0)
    start_date: Union[str, date] = Field(..., description="回测开始日期")
    end_date: Union[str, date] = Field(..., description="回测结束日期")
    benchmark: str = Field("000300.SH", description="基准指数")
    
    # 交易成本设置
    commission_rate: float = Field(0.0003, description="佣金费率", ge=0, le=0.01)
    stamp_tax_rate: float = Field(0.001, description="印花税费率", ge=0, le=0.01)
    transfer_fee_rate: float = Field(0.00002, description="过户费费率", ge=0, le=0.001)
    min_commission: float = Field(5.0, description="最低佣金", ge=0)
    
    # 滑点设置
    slippage_type: SlippageType = Field(SlippageType.PERCENTAGE, description="滑点类型")
    slippage_value: float = Field(0.001, description="滑点值", ge=0, le=0.1)
    
    # 其他设置
    cash_interest_rate: float = Field(0.02, description="现金利率", ge=0, le=0.1)
    
    @validator('start_date', 'end_date', pre=True)
    def validate_dates(cls, v):
        if isinstance(v, str):
            try:
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v

class RiskLimitsModel(BaseModel):
    """风险限制模型"""
    # 仓位限制
    max_position_size: float = Field(0.1, description="单股最大仓位", gt=0, le=1)
    max_total_position: float = Field(0.95, description="总仓位限制", gt=0, le=1)
    max_sector_exposure: float = Field(0.3, description="单行业最大敞口", gt=0, le=1)
    max_positions_count: int = Field(20, description="最大持仓数量", ge=1, le=100)
    
    # 损失限制
    max_daily_loss: float = Field(0.05, description="日最大损失", gt=0, le=1)
    max_drawdown: float = Field(0.2, description="最大回撤", gt=0, le=1)
    stop_loss_threshold: float = Field(0.1, description="止损阈值", gt=0, le=1)
    
    # 波动率限制
    max_portfolio_volatility: float = Field(0.3, description="最大组合波动率", gt=0, le=2)
    max_tracking_error: float = Field(0.1, description="最大跟踪误差", gt=0, le=1)
    
    # 流动性限制
    min_avg_volume: float = Field(1000000, description="最小平均成交量", ge=0)
    max_volume_participation: float = Field(0.1, description="最大成交量参与率", gt=0, le=1)
    
    # VaR限制
    var_confidence_level: float = Field(0.05, description="VaR置信水平", gt=0, lt=1)
    max_var: float = Field(0.03, description="最大VaR", gt=0, le=1)

# 请求模型
class BacktestRequest(BaseModel):
    """回测请求"""
    strategy_config: StrategyConfigModel = Field(..., description="策略配置")
    backtest_config: BacktestConfigModel = Field(..., description="回测配置")
    risk_limits: Optional[RiskLimitsModel] = Field(None, description="风险限制")
    data_source: str = Field("akshare", description="数据源")

class BacktestUpdateRequest(BaseModel):
    """回测更新请求"""
    action: str = Field(..., description="操作类型", pattern="^(pause|resume|cancel)$")
    message: Optional[str] = Field(None, description="操作说明")

# 响应模型
class BacktestResponse(BaseModel):
    """回测响应"""
    task_id: str = Field(..., description="任务ID")
    status: BacktestStatus = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

class BacktestResultSummary(BaseModel):
    """回测结果摘要"""
    total_return: float = Field(..., description="总收益率")
    annual_return: float = Field(..., description="年化收益率")
    volatility: float = Field(..., description="波动率")
    sharpe_ratio: float = Field(..., description="夏普比率")
    max_drawdown: float = Field(..., description="最大回撤")
    win_rate: float = Field(..., description="胜率")
    profit_factor: float = Field(..., description="盈亏比")

class BacktestResult(BaseModel):
    """完整回测结果"""
    task_id: str = Field(..., description="任务ID")
    status: BacktestStatus = Field(..., description="状态")
    progress: float = Field(..., description="进度", ge=0, le=1)
    
    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    # 结果数据
    summary: Optional[BacktestResultSummary] = Field(None, description="结果摘要")
    portfolio_value: Optional[List[float]] = Field(None, description="组合价值序列")
    benchmark_value: Optional[List[float]] = Field(None, description="基准价值序列")
    dates: Optional[List[str]] = Field(None, description="日期序列")
    
    # 交易记录
    trades: Optional[List[Dict[str, Any]]] = Field(None, description="交易记录")
    positions: Optional[Dict[str, Any]] = Field(None, description="持仓信息")
    
    # 错误信息
    error: Optional[str] = Field(None, description="错误信息")

class StrategyInfo(BaseModel):
    """策略信息"""
    type: str = Field(..., description="策略类型")
    name: str = Field(..., description="策略名称")
    description: str = Field(..., description="策略描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="默认参数")

class ConfigTemplate(BaseModel):
    """配置模板"""
    strategy_config: Dict[str, Any] = Field(..., description="策略配置模板")
    backtest_config: Dict[str, Any] = Field(..., description="回测配置模板")
    risk_limits: Dict[str, Any] = Field(..., description="风险限制模板")

# 分析相关模型
class PerformanceMetrics(BaseModel):
    """绩效指标"""
    total_return: float = Field(..., description="总收益率")
    annual_return: float = Field(..., description="年化收益率")
    monthly_return: float = Field(..., description="月均收益率")
    daily_return: float = Field(..., description="日均收益率")
    
    volatility: float = Field(..., description="波动率")
    downside_deviation: float = Field(..., description="下行偏差")
    var_95: float = Field(..., description="95% VaR")
    var_99: float = Field(..., description="99% VaR")
    cvar_95: float = Field(..., description="95% CVaR")
    
    sharpe_ratio: float = Field(..., description="夏普比率")
    sortino_ratio: float = Field(..., description="索提诺比率")
    calmar_ratio: float = Field(..., description="卡玛比率")
    information_ratio: float = Field(..., description="信息比率")
    
    max_drawdown: float = Field(..., description="最大回撤")
    avg_drawdown: float = Field(..., description="平均回撤")
    max_drawdown_duration: int = Field(..., description="最大回撤持续期")
    
    win_rate: float = Field(..., description="胜率")
    profit_factor: float = Field(..., description="盈亏比")
    avg_win: float = Field(..., description="平均盈利")
    avg_loss: float = Field(..., description="平均亏损")
    
    skewness: float = Field(..., description="偏度")
    kurtosis: float = Field(..., description="峰度")
    tail_ratio: float = Field(..., description="尾部比率")

class ReportRequest(BaseModel):
    """报告生成请求"""
    task_id: str = Field(..., description="回测任务ID")
    format: str = Field("json", description="报告格式", pattern="^(json|html|pdf)$")
    sections: Optional[List[str]] = Field(None, description="包含的章节")

# WebSocket模型
class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    client_id: Optional[str] = Field(None, description="客户端ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

class SubscriptionRequest(BaseModel):
    """订阅请求"""
    topics: List[str] = Field(..., description="订阅主题列表")

class MarketDataMessage(BaseModel):
    """市场数据消息"""
    timestamp: datetime = Field(..., description="时间戳")
    symbol: str = Field(..., description="股票代码")
    price: float = Field(..., description="价格")
    volume: Optional[float] = Field(None, description="成交量")
    change: Optional[float] = Field(None, description="涨跌幅")

# 错误响应模型
class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")

class ValidationError(BaseModel):
    """验证错误"""
    field: str = Field(..., description="字段名")
    message: str = Field(..., description="错误消息")
    value: Any = Field(..., description="错误值")

# 分页模型
class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(1, description="页码", ge=1)
    size: int = Field(20, description="每页大小", ge=1, le=100)

class PaginatedResponse(BaseModel):
    """分页响应"""
    items: List[Any] = Field(..., description="数据项")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    
    @validator('pages', pre=False, always=True)
    def calculate_pages(cls, v, values):
        total = values.get('total', 0)
        size = values.get('size', 20)
        return (total + size - 1) // size if size > 0 else 0

# 响应包装器
class APIResponse(BaseModel):
    """API响应包装器"""
    success: bool = Field(..., description="是否成功")
    data: Optional[Any] = Field(None, description="响应数据")
    error: Optional[ErrorResponse] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    request_id: Optional[str] = Field(None, description="请求ID")

    @validator('error')
    def validate_error(cls, v, values):
        success = values.get('success')
        if not success and v is None:
            raise ValueError('失败响应必须包含错误信息')
        if success and v is not None:
            raise ValueError('成功响应不应包含错误信息')
        return v