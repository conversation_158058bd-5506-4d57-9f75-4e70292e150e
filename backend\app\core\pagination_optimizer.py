"""
分页查询优化器
针对大数据量场景的高性能分页实现
"""

import asyncio
import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, TypeVar, Generic
from dataclasses import dataclass
from enum import Enum

from sqlalchemy import func, text, select, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Query
from sqlalchemy.sql import Select

from app.core.query_cache import cache_manager, cached_query


logger = logging.getLogger(__name__)

T = TypeVar('T')


class PaginationStrategy(str, Enum):
    """分页策略"""
    OFFSET_LIMIT = "offset_limit"  # 传统OFFSET/LIMIT
    CURSOR_BASED = "cursor_based"  # 基于游标的分页
    SEEK_METHOD = "seek_method"    # 基于定位的分页
    HYBRID = "hybrid"              # 混合策略


@dataclass
class PaginationParams:
    """分页参数"""
    page: int = 1
    size: int = 20
    max_size: int = 1000
    cursor: Optional[str] = None
    sort_field: str = "id"
    sort_direction: str = "desc"
    filters: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        # 参数验证
        self.page = max(1, self.page)
        self.size = min(max(1, self.size), self.max_size)
        self.sort_direction = self.sort_direction.lower()
        if self.sort_direction not in ['asc', 'desc']:
            self.sort_direction = 'desc'


@dataclass
class PaginationResult(Generic[T]):
    """分页结果"""
    items: List[T]
    total_count: int
    page: int
    size: int
    total_pages: int
    has_next: bool
    has_prev: bool
    next_cursor: Optional[str] = None
    prev_cursor: Optional[str] = None
    strategy_used: str = PaginationStrategy.OFFSET_LIMIT
    execution_time: float = 0
    from_cache: bool = False


class CursorEncoder:
    """游标编码器"""
    
    @staticmethod
    def encode_cursor(values: Dict[str, Any]) -> str:
        """编码游标"""
        import base64
        import json
        
        cursor_data = json.dumps(values, default=str, sort_keys=True)
        return base64.b64encode(cursor_data.encode()).decode()
    
    @staticmethod
    def decode_cursor(cursor: str) -> Dict[str, Any]:
        """解码游标"""
        import base64
        import json
        
        try:
            cursor_data = base64.b64decode(cursor.encode()).decode()
            return json.loads(cursor_data)
        except Exception as e:
            logger.warning(f"游标解码失败: {e}")
            return {}


class PaginationOptimizer:
    """分页优化器"""
    
    def __init__(self):
        self.strategy_thresholds = {
            # 小数据量：使用传统分页
            'small_data': 10000,
            # 中等数据量：使用游标分页
            'medium_data': 100000,
            # 大数据量：使用定位分页
            'large_data': 1000000,
        }
        
        # 性能缓存配置
        self.cache_config = {
            'count_cache_ttl': 300,  # 总数缓存5分钟
            'page_cache_ttl': 60,    # 页面缓存1分钟
            'enable_count_estimation': True,  # 启用总数估算
        }
    
    async def determine_strategy(
        self, 
        session: AsyncSession, 
        table_name: str, 
        params: PaginationParams
    ) -> PaginationStrategy:
        """确定最佳分页策略"""
        
        # 获取表行数估算
        estimated_count = await self._estimate_table_count(session, table_name)
        
        # 检查是否请求后面的页数
        is_deep_pagination = (params.page - 1) * params.size > 10000
        
        # 策略选择逻辑
        if estimated_count <= self.strategy_thresholds['small_data'] and not is_deep_pagination:
            return PaginationStrategy.OFFSET_LIMIT
        elif params.cursor and estimated_count <= self.strategy_thresholds['large_data']:
            return PaginationStrategy.CURSOR_BASED
        elif is_deep_pagination or estimated_count > self.strategy_thresholds['medium_data']:
            return PaginationStrategy.SEEK_METHOD
        else:
            return PaginationStrategy.HYBRID
    
    async def _estimate_table_count(self, session: AsyncSession, table_name: str) -> int:
        """估算表行数"""
        cache_key = f"table_count:{table_name}"
        
        # 尝试从缓存获取
        cached_count = await cache_manager.get('table_stats', {'key': cache_key})
        if cached_count is not None:
            return cached_count
        
        try:
            # SQLite表行数查询
            if 'sqlite' in str(session.get_bind().url):
                # 使用sqlite_stat1表获取估算
                result = await session.execute(text(f"""
                    SELECT stat FROM sqlite_stat1 
                    WHERE tbl = '{table_name}' AND idx IS NULL
                """))
                row = result.first()
                if row:
                    estimated_count = int(row.stat)
                else:
                    # fallback到精确计数
                    result = await session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    estimated_count = result.scalar()
            else:
                # 其他数据库的估算方法
                result = await session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                estimated_count = result.scalar()
            
            # 缓存结果
            await cache_manager.set(
                'table_stats', 
                {'key': cache_key}, 
                estimated_count
            )
            
            return estimated_count
            
        except Exception as e:
            logger.warning(f"估算表 {table_name} 行数失败: {e}")
            return 0
    
    async def paginate_with_offset_limit(
        self,
        session: AsyncSession,
        query: Select,
        params: PaginationParams,
        count_query: Optional[Select] = None
    ) -> PaginationResult:
        """传统OFFSET/LIMIT分页"""
        import time
        start_time = time.time()
        
        offset = (params.page - 1) * params.size
        
        # 构建排序
        if hasattr(query.column_descriptions[0]['type'], params.sort_field):
            sort_column = getattr(query.column_descriptions[0]['type'], params.sort_field)
            if params.sort_direction == 'desc':
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        
        # 应用分页
        paginated_query = query.offset(offset).limit(params.size)
        
        # 执行查询
        result = await session.execute(paginated_query)
        items = result.fetchall()
        
        # 获取总数
        if count_query is not None:
            count_result = await session.execute(count_query)
            total_count = count_result.scalar()
        else:
            # 估算总数以提高性能
            total_count = await self._estimate_query_count(session, query)
        
        total_pages = math.ceil(total_count / params.size) if total_count > 0 else 0
        
        execution_time = time.time() - start_time
        
        return PaginationResult(
            items=items,
            total_count=total_count,
            page=params.page,
            size=params.size,
            total_pages=total_pages,
            has_next=params.page < total_pages,
            has_prev=params.page > 1,
            strategy_used=PaginationStrategy.OFFSET_LIMIT,
            execution_time=execution_time
        )
    
    async def paginate_with_cursor(
        self,
        session: AsyncSession,
        query: Select,
        params: PaginationParams,
        cursor_fields: List[str] = None
    ) -> PaginationResult:
        """基于游标的分页"""
        import time
        start_time = time.time()
        
        if cursor_fields is None:
            cursor_fields = [params.sort_field]
        
        # 解析游标
        cursor_values = {}
        if params.cursor:
            cursor_values = CursorEncoder.decode_cursor(params.cursor)
        
        # 构建WHERE条件
        if cursor_values:
            # 这里需要根据具体的模型来构建条件
            # 简化实现，实际需要根据具体表结构调整
            conditions = []
            for field in cursor_fields:
                if field in cursor_values:
                    if params.sort_direction == 'desc':
                        conditions.append(text(f"{field} < :{field}"))
                    else:
                        conditions.append(text(f"{field} > :{field}"))
            
            if conditions:
                for condition in conditions:
                    query = query.where(condition)
        
        # 应用排序和限制
        query = query.limit(params.size + 1)  # 多取一条用于判断是否有下一页
        
        # 执行查询
        result = await session.execute(query, cursor_values)
        items = result.fetchall()
        
        # 判断是否有下一页
        has_next = len(items) > params.size
        if has_next:
            items = items[:-1]  # 移除多取的一条
        
        # 生成下一页游标
        next_cursor = None
        if has_next and items:
            last_item = items[-1]
            cursor_data = {field: getattr(last_item, field, None) for field in cursor_fields}
            next_cursor = CursorEncoder.encode_cursor(cursor_data)
        
        execution_time = time.time() - start_time
        
        return PaginationResult(
            items=items,
            total_count=-1,  # 游标分页不提供总数
            page=params.page,
            size=params.size,
            total_pages=-1,
            has_next=has_next,
            has_prev=bool(params.cursor),
            next_cursor=next_cursor,
            strategy_used=PaginationStrategy.CURSOR_BASED,
            execution_time=execution_time
        )
    
    async def paginate_with_seek(
        self,
        session: AsyncSession,
        query: Select,
        params: PaginationParams,
        seek_columns: List[str] = None
    ) -> PaginationResult:
        """基于定位的高性能分页"""
        import time
        start_time = time.time()
        
        if seek_columns is None:
            seek_columns = [params.sort_field]
        
        # 对于深度分页，使用子查询优化
        if (params.page - 1) * params.size > 1000:
            # 使用子查询先定位到目标范围
            seek_query = select(seek_columns).order_by(
                desc(seek_columns[0]) if params.sort_direction == 'desc' else asc(seek_columns[0])
            ).offset((params.page - 1) * params.size).limit(params.size)
            
            seek_result = await session.execute(seek_query)
            seek_values = [row[0] for row in seek_result.fetchall()]
            
            if not seek_values:
                return PaginationResult(
                    items=[],
                    total_count=0,
                    page=params.page,
                    size=params.size,
                    total_pages=0,
                    has_next=False,
                    has_prev=params.page > 1,
                    strategy_used=PaginationStrategy.SEEK_METHOD,
                    execution_time=time.time() - start_time
                )
            
            # 使用定位值查询完整数据
            min_val, max_val = min(seek_values), max(seek_values)
            if params.sort_direction == 'desc':
                query = query.where(text(f"{seek_columns[0]} BETWEEN :min_val AND :max_val"))
            else:
                query = query.where(text(f"{seek_columns[0]} BETWEEN :min_val AND :max_val"))
            
            query = query.order_by(
                desc(seek_columns[0]) if params.sort_direction == 'desc' else asc(seek_columns[0])
            )
            
            result = await session.execute(query, {'min_val': min_val, 'max_val': max_val})
            items = result.fetchall()
        else:
            # 浅分页使用传统方法
            return await self.paginate_with_offset_limit(session, query, params)
        
        # 估算总数
        total_count = await self._estimate_query_count(session, query)
        total_pages = math.ceil(total_count / params.size) if total_count > 0 else 0
        
        execution_time = time.time() - start_time
        
        return PaginationResult(
            items=items,
            total_count=total_count,
            page=params.page,
            size=params.size,
            total_pages=total_pages,
            has_next=params.page < total_pages,
            has_prev=params.page > 1,
            strategy_used=PaginationStrategy.SEEK_METHOD,
            execution_time=execution_time
        )
    
    async def _estimate_query_count(self, session: AsyncSession, query: Select) -> int:
        """估算查询结果数量"""
        try:
            # 构建COUNT查询
            count_query = select(func.count()).select_from(query.alias())
            result = await session.execute(count_query)
            return result.scalar()
        except Exception as e:
            logger.warning(f"估算查询数量失败: {e}")
            return 0
    
    async def smart_paginate(
        self,
        session: AsyncSession,
        query: Select,
        params: PaginationParams,
        table_name: str = None,
        enable_cache: bool = True
    ) -> PaginationResult:
        """智能分页 - 自动选择最优策略"""
        
        # 生成缓存键
        cache_key = None
        if enable_cache:
            import hashlib
            query_str = str(query.compile(compile_kwargs={"literal_binds": True}))
            params_str = f"{params.page}_{params.size}_{params.sort_field}_{params.sort_direction}"
            cache_key = hashlib.md5(f"{query_str}_{params_str}".encode()).hexdigest()
            
            # 尝试从缓存获取
            cached_result = await cache_manager.get('pagination', {'key': cache_key})
            if cached_result:
                cached_result['from_cache'] = True
                return PaginationResult(**cached_result)
        
        # 确定策略
        strategy = PaginationStrategy.OFFSET_LIMIT
        if table_name:
            strategy = await self.determine_strategy(session, table_name, params)
        
        # 执行分页
        if strategy == PaginationStrategy.CURSOR_BASED:
            result = await self.paginate_with_cursor(session, query, params)
        elif strategy == PaginationStrategy.SEEK_METHOD:
            result = await self.paginate_with_seek(session, query, params)
        else:
            result = await self.paginate_with_offset_limit(session, query, params)
        
        # 缓存结果
        if enable_cache and cache_key:
            cache_data = {
                'items': [dict(item._mapping) if hasattr(item, '_mapping') else item for item in result.items],
                'total_count': result.total_count,
                'page': result.page,
                'size': result.size,
                'total_pages': result.total_pages,
                'has_next': result.has_next,
                'has_prev': result.has_prev,
                'next_cursor': result.next_cursor,
                'prev_cursor': result.prev_cursor,
                'strategy_used': result.strategy_used,
                'execution_time': result.execution_time,
                'from_cache': False
            }
            
            await cache_manager.set(
                'pagination',
                {'key': cache_key},
                cache_data
            )
        
        return result


# 全局分页优化器实例
pagination_optimizer = PaginationOptimizer()


# 便利函数
async def paginate_query(
    session: AsyncSession,
    query: Select,
    page: int = 1,
    size: int = 20,
    sort_field: str = "id",
    sort_direction: str = "desc",
    table_name: str = None,
    enable_cache: bool = True
) -> PaginationResult:
    """便利的分页查询函数"""
    
    params = PaginationParams(
        page=page,
        size=size,
        sort_field=sort_field,
        sort_direction=sort_direction
    )
    
    return await pagination_optimizer.smart_paginate(
        session=session,
        query=query,
        params=params,
        table_name=table_name,
        enable_cache=enable_cache
    )


# 分页装饰器
def paginated_endpoint(
    default_size: int = 20,
    max_size: int = 1000,
    default_sort: str = "id",
    allowed_sorts: List[str] = None
):
    """分页端点装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 从请求参数中提取分页参数
            page = kwargs.get('page', 1)
            size = min(kwargs.get('size', default_size), max_size)
            sort_field = kwargs.get('sort', default_sort)
            sort_direction = kwargs.get('order', 'desc')
            
            # 验证排序字段
            if allowed_sorts and sort_field not in allowed_sorts:
                sort_field = default_sort
            
            # 更新kwargs
            kwargs.update({
                'page': page,
                'size': size,
                'sort_field': sort_field,
                'sort_direction': sort_direction
            })
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# 示例使用函数
async def get_paginated_orders(
    session: AsyncSession,
    user_id: Optional[int] = None,
    status: Optional[str] = None,
    page: int = 1,
    size: int = 20,
    sort_field: str = "created_at",
    sort_direction: str = "desc"
) -> PaginationResult:
    """获取分页订单列表（示例）"""
    
    # 构建基础查询
    query = select(text("*")).select_from(text("orders"))
    
    # 添加过滤条件
    conditions = []
    params = {}
    
    if user_id is not None:
        conditions.append("user_id = :user_id")
        params['user_id'] = user_id
    
    if status is not None:
        conditions.append("status = :status")
        params['status'] = status
    
    if conditions:
        query = query.where(text(" AND ".join(conditions)))
    
    # 执行分页查询
    pagination_params = PaginationParams(
        page=page,
        size=size,
        sort_field=sort_field,
        sort_direction=sort_direction
    )
    
    result = await pagination_optimizer.smart_paginate(
        session=session,
        query=query,
        params=pagination_params,
        table_name="orders"
    )
    
    return result


async def get_paginated_market_data(
    session: AsyncSession,
    symbol_code: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    page: int = 1,
    size: int = 100,
    cursor: Optional[str] = None
) -> PaginationResult:
    """获取分页市场数据（示例）"""
    
    # 构建查询
    query = select(text("*")).select_from(text("market_data"))
    
    conditions = []
    params = {}
    
    if symbol_code:
        conditions.append("symbol_code = :symbol_code")
        params['symbol_code'] = symbol_code
    
    if start_date:
        conditions.append("timestamp >= :start_date")
        params['start_date'] = start_date
    
    if end_date:
        conditions.append("timestamp <= :end_date")
        params['end_date'] = end_date
    
    if conditions:
        query = query.where(text(" AND ".join(conditions)))
    
    # 使用游标分页策略，适合时间序列数据
    pagination_params = PaginationParams(
        page=page,
        size=size,
        cursor=cursor,
        sort_field="timestamp",
        sort_direction="desc"
    )
    
    return await pagination_optimizer.paginate_with_cursor(
        session=session,
        query=query,
        params=pagination_params,
        cursor_fields=["timestamp", "id"]
    )


if __name__ == "__main__":
    async def test_pagination_optimizer():
        """测试分页优化器"""
        print("测试分页优化器...")
        
        from app.core.database import db_manager
        
        # 初始化数据库
        db_manager.initialize()
        
        async with db_manager.get_session() as session:
            # 测试基础分页
            query = select(text("*")).select_from(text("orders"))
            
            params = PaginationParams(page=1, size=10, sort_field="created_at")
            
            result = await pagination_optimizer.smart_paginate(
                session=session,
                query=query,
                params=params,
                table_name="orders"
            )
            
            print(f"分页结果:")
            print(f"  策略: {result.strategy_used}")
            print(f"  页码: {result.page}/{result.total_pages}")
            print(f"  数据数量: {len(result.items)}")
            print(f"  执行时间: {result.execution_time:.3f}s")
            print(f"  来自缓存: {result.from_cache}")
        
        print("测试完成")
    
    asyncio.run(test_pagination_optimizer())