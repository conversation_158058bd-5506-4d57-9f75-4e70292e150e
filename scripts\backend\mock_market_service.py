"""
模拟市场数据服务
在没有真实数据源的情况下提供模拟数据
"""

import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any
import json

class MockMarketService:
    """模拟市场数据服务"""
    
    def __init__(self):
        self.stocks = self._generate_stock_list()
        self.indices = self._generate_indices()
        
    def _generate_stock_list(self) -> List[Dict[str, Any]]:
        """生成模拟股票列表"""
        stocks = []
        stock_codes = [
            ("000001", "平安银行"), ("000002", "万科A"), ("000858", "五粮液"),
            ("600000", "浦发银行"), ("600036", "招商银行"), ("600519", "贵州茅台"),
            ("600887", "伊利股份"), ("000858", "五粮液"), ("002415", "海康威视"),
            ("300059", "东方财富"), ("002594", "比亚迪"), ("600276", "恒瑞医药")
        ]
        
        for code, name in stock_codes:
            base_price = random.uniform(10, 200)
            change = random.uniform(-0.1, 0.1)
            
            stock = {
                "symbol": code,
                "name": name,
                "price": round(base_price, 2),
                "change": round(change, 4),
                "change_percent": round(change * 100, 2),
                "volume": random.randint(1000000, 50000000),
                "turnover": round(base_price * random.randint(1000000, 50000000), 2),
                "high": round(base_price * (1 + abs(change)), 2),
                "low": round(base_price * (1 - abs(change)), 2),
                "open": round(base_price * random.uniform(0.98, 1.02), 2),
                "prev_close": round(base_price - change, 2),
                "market_cap": round(base_price * random.randint(1000000, 10000000), 2),
                "pe_ratio": round(random.uniform(10, 50), 2),
                "pb_ratio": round(random.uniform(1, 10), 2),
                "timestamp": datetime.now().isoformat()
            }
            stocks.append(stock)
            
        return stocks
    
    def _generate_indices(self) -> List[Dict[str, Any]]:
        """生成模拟指数数据"""
        indices = []
        index_data = [
            ("000001", "上证指数", 3200),
            ("399001", "深证成指", 12000),
            ("399006", "创业板指", 2800),
            ("000300", "沪深300", 4200)
        ]
        
        for code, name, base_value in index_data:
            change = random.uniform(-0.05, 0.05)
            current_value = base_value * (1 + change)
            
            index = {
                "symbol": code,
                "name": name,
                "value": round(current_value, 2),
                "change": round(current_value - base_value, 2),
                "change_percent": round(change * 100, 2),
                "volume": random.randint(100000000, 500000000),
                "turnover": round(random.uniform(200000000000, 800000000000), 2),
                "timestamp": datetime.now().isoformat()
            }
            indices.append(index)
            
        return indices
    
    def get_stock_list(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取股票列表"""
        # 随机更新价格
        for stock in self.stocks:
            change = random.uniform(-0.02, 0.02)
            stock["price"] = round(stock["price"] * (1 + change), 2)
            stock["change"] = round(stock["price"] - stock["prev_close"], 4)
            stock["change_percent"] = round((stock["change"] / stock["prev_close"]) * 100, 2)
            stock["timestamp"] = datetime.now().isoformat()
            
        return self.stocks[:limit]
    
    def get_indices(self) -> List[Dict[str, Any]]:
        """获取指数数据"""
        # 随机更新指数
        for index in self.indices:
            change = random.uniform(-0.01, 0.01)
            old_value = index["value"]
            index["value"] = round(old_value * (1 + change), 2)
            index["change"] = round(index["value"] - (old_value - index["change"]), 2)
            index["change_percent"] = round(change * 100, 2)
            index["timestamp"] = datetime.now().isoformat()
            
        return self.indices
    
    def get_stock_detail(self, symbol: str) -> Dict[str, Any]:
        """获取股票详情"""
        stock = next((s for s in self.stocks if s["symbol"] == symbol), None)
        if not stock:
            return {}
            
        # 生成K线数据
        kline_data = self._generate_kline_data(symbol, stock["price"])
        
        return {
            **stock,
            "kline_data": kline_data,
            "technical_indicators": self._generate_technical_indicators()
        }
    
    def _generate_kline_data(self, symbol: str, current_price: float) -> List[Dict[str, Any]]:
        """生成K线数据"""
        kline_data = []
        base_time = datetime.now() - timedelta(days=30)
        
        for i in range(30):
            date = base_time + timedelta(days=i)
            price_change = random.uniform(-0.05, 0.05)
            
            open_price = current_price * (1 + price_change)
            close_price = open_price * (1 + random.uniform(-0.03, 0.03))
            high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.02))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.02))
            
            kline = {
                "date": date.strftime("%Y-%m-%d"),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(1000000, 10000000)
            }
            kline_data.append(kline)
            
        return kline_data
    
    def _generate_technical_indicators(self) -> Dict[str, Any]:
        """生成技术指标"""
        return {
            "ma5": round(random.uniform(50, 150), 2),
            "ma10": round(random.uniform(50, 150), 2),
            "ma20": round(random.uniform(50, 150), 2),
            "rsi": round(random.uniform(30, 70), 2),
            "macd": round(random.uniform(-2, 2), 4),
            "kdj_k": round(random.uniform(20, 80), 2),
            "kdj_d": round(random.uniform(20, 80), 2),
            "kdj_j": round(random.uniform(0, 100), 2)
        }
    
    def get_market_news(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取市场新闻"""
        news_titles = [
            "A股三大指数集体收涨，创业板指涨超1%",
            "央行降准释放流动性，市场情绪回暖",
            "科技股领涨，新能源板块表现强势",
            "外资持续流入A股市场，看好中国经济前景",
            "监管层发声支持资本市场发展",
            "上市公司三季报业绩超预期",
            "北向资金净流入创新高",
            "券商板块异动拉升，成交量放大"
        ]
        
        news = []
        for i in range(min(limit, len(news_titles))):
            news_item = {
                "id": f"news_{i+1}",
                "title": news_titles[i],
                "summary": f"这是关于{news_titles[i]}的详细报道...",
                "source": random.choice(["财经网", "证券时报", "上海证券报", "中国证券报"]),
                "publish_time": (datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat(),
                "url": f"https://example.com/news/{i+1}"
            }
            news.append(news_item)
            
        return news

# 全局实例
mock_market_service = MockMarketService()
