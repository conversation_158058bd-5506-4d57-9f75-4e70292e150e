"""
模拟交易API接口
"""
from typing import Optional, Dict, Any, List
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from pydantic import BaseModel, Field, validator

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.db.models.user import User
from app.db.models.trading import (
    Account, Order, OrderStatus, OrderSide, OrderType,
    Trade, Position, Portfolio
)
# 添加OrderDirection的别名
OrderDirection = OrderSide
from app.services.simulated_trading_engine import SimulatedTradingEngine
from app.services.mock_market_service import MockMarketService
from app.schemas.response import Response
from app.core.logging import logger
from app.core.errors import BusinessError

router = APIRouter()


# Schema定义
class SimulatedOrderRequest(BaseModel):
    account_id: str = Field(..., description="账户ID")
    symbol: str = Field(..., description="股票代码", pattern="^[0-9]{6}$")
    direction: OrderDirection = Field(..., description="交易方向")
    order_type: OrderType = Field(..., description="订单类型")
    price: float = Field(..., description="委托价格", gt=0)
    volume: int = Field(..., description="委托数量", gt=0)
    
    @validator('volume')
    def validate_volume(cls, v):
        if v % 100 != 0:
            raise ValueError('交易数量必须是100的整数倍')
        return v


class CancelOrderRequest(BaseModel):
    account_id: str = Field(..., description="账户ID")
    order_id: str = Field(..., description="订单ID")


@router.post("/order/submit", summary="提交模拟订单")
async def submit_simulated_order(
    request: SimulatedOrderRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """提交模拟交易订单"""
    try:
        # 验证账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == request.account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        if account.status != "ACTIVE":
            return Response.error(message="账户状态异常", code=400)
        
        # 创建模拟交易引擎
        market_service = MockMarketService()
        engine = SimulatedTradingEngine(market_service)
        
        # 提交订单
        order = await engine.submit_order(
            db=db,
            account=account,
            symbol=request.symbol,
            direction=request.direction,
            order_type=request.order_type,
            price=request.price,
            volume=request.volume
        )
        
        return Response.success(
            data={
                "order_id": order.order_id,
                "symbol": order.symbol,
                "direction": order.direction.value,
                "order_type": order.order_type.value,
                "price": order.price,
                "volume": order.volume,
                "status": order.status.value,
                "submit_time": order.submit_time.isoformat()
            },
            message="订单提交成功"
        )
        
    except BusinessError as e:
        return Response.error(message=str(e), code=400)
    except Exception as e:
        logger.error(f"提交模拟订单失败: {e}")
        return Response.error(message=f"提交订单失败: {str(e)}")


@router.post("/order/cancel", summary="取消模拟订单")
async def cancel_simulated_order(
    request: CancelOrderRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """取消模拟交易订单"""
    try:
        # 验证账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == request.account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 创建模拟交易引擎
        market_service = MockMarketService()
        engine = SimulatedTradingEngine(market_service)
        
        # 取消订单
        success = await engine.cancel_order(
            db=db,
            account=account,
            order_id=request.order_id
        )
        
        if success:
            return Response.success(message="订单取消成功")
        else:
            return Response.error(message="订单取消失败", code=400)
            
    except BusinessError as e:
        return Response.error(message=str(e), code=400)
    except Exception as e:
        logger.error(f"取消模拟订单失败: {e}")
        return Response.error(message=f"取消订单失败: {str(e)}")


@router.get("/orders", summary="查询模拟订单")
async def get_simulated_orders(
    account_id: str = Query(..., description="账户ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    symbol: Optional[str] = Query(None, description="股票代码"),
    status: Optional[OrderStatus] = Query(None, description="订单状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """查询模拟交易订单"""
    try:
        # 验证账户
        account_result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = account_result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 构建查询
        query = select(Order).where(Order.user_id == current_user.id)
        
        if symbol:
            query = query.where(Order.symbol == symbol)
        if status:
            query = query.where(Order.status == status)
        if start_date:
            query = query.where(Order.submit_time >= start_date)
        if end_date:
            query = query.where(Order.submit_time <= end_date)
        
        # 排序
        query = query.order_by(Order.submit_time.desc())
        
        # 执行查询
        result = await db.execute(query)
        orders = result.scalars().all()
        
        # 分页
        total = len(orders)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        orders = orders[start_idx:end_idx]
        
        # 格式化返回数据
        order_list = []
        for order in orders:
            order_data = {
                "order_id": order.order_id,
                "symbol": order.symbol,
                "exchange": order.exchange,
                "direction": order.direction.value,
                "order_type": order.order_type.value,
                "price": order.price,
                "volume": order.volume,
                "traded_volume": order.traded_volume,
                "avg_price": order.avg_price,
                "status": order.status.value,
                "commission": order.commission,
                "message": order.message,
                "submit_time": order.submit_time.isoformat(),
                "update_time": order.update_time.isoformat()
            }
            order_list.append(order_data)
        
        return Response.success(
            data={
                "total": total,
                "page": page,
                "page_size": page_size,
                "items": order_list
            },
            message="查询订单成功"
        )
        
    except Exception as e:
        logger.error(f"查询模拟订单失败: {e}")
        return Response.error(message=f"查询订单失败: {str(e)}")


@router.get("/positions", summary="查询模拟持仓")
async def get_simulated_positions(
    account_id: str = Query(..., description="账户ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """查询模拟账户持仓"""
    try:
        # 验证账户
        account_result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = account_result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 查询持仓
        result = await db.execute(
            select(Position).where(
                and_(
                    Position.user_id == current_user.id,
                    Position.volume > 0
                )
            )
        )
        positions = result.scalars().all()
        
        # 更新市值
        market_service = MockMarketService()
        engine = SimulatedTradingEngine(market_service)
        await engine.update_account_market_value(db, account)
        
        # 格式化返回数据
        position_list = []
        total_market_value = 0
        total_profit_loss = 0
        
        for position in positions:
            # 获取最新行情
            quote = await market_service.get_realtime_quote(position.symbol)
            if quote:
                position.current_price = quote["current"]
                position.market_value = position.current_price * position.volume
                position.profit_loss = (position.current_price - position.avg_price) * position.volume
                position.profit_rate = position.profit_loss / (position.avg_price * position.volume)
            
            position_data = {
                "symbol": position.symbol,
                "name": position.name,
                "exchange": position.exchange,
                "volume": position.volume,
                "available_volume": position.available_volume,
                "frozen_volume": position.frozen_volume,
                "avg_price": position.avg_price,
                "current_price": position.current_price,
                "market_value": position.market_value,
                "profit_loss": position.profit_loss,
                "profit_rate": position.profit_rate,
                "day_profit_loss": position.day_profit_loss,
                "day_profit_rate": position.day_profit_rate,
                "create_time": position.create_time.isoformat(),
                "update_time": position.update_time.isoformat()
            }
            position_list.append(position_data)
            
            total_market_value += position.market_value or 0
            total_profit_loss += position.profit_loss or 0
        
        return Response.success(
            data={
                "positions": position_list,
                "summary": {
                    "total_positions": len(positions),
                    "total_market_value": total_market_value,
                    "total_profit_loss": total_profit_loss,
                    "total_profit_rate": total_profit_loss / (total_market_value - total_profit_loss) if (total_market_value - total_profit_loss) > 0 else 0
                }
            },
            message="查询持仓成功"
        )
        
    except Exception as e:
        logger.error(f"查询模拟持仓失败: {e}")
        return Response.error(message=f"查询持仓失败: {str(e)}")