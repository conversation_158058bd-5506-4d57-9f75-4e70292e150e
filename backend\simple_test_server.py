#!/usr/bin/env python3
"""
简化的测试后端服务
用于支持前端测试，提供基本的API端点
"""

import json
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

app = FastAPI(title="量化投资平台测试API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
MOCK_STOCKS = [
    {"symbol": "000001", "name": "平安银行", "price": 12.45, "change": 0.12, "change_percent": 0.97},
    {"symbol": "000002", "name": "万科A", "price": 8.76, "change": -0.05, "change_percent": -0.57},
    {"symbol": "600000", "name": "浦发银行", "price": 7.89, "change": 0.08, "change_percent": 1.02},
    {"symbol": "600036", "name": "招商银行", "price": 35.67, "change": 0.45, "change_percent": 1.28},
    {"symbol": "000858", "name": "五粮液", "price": 128.90, "change": -2.10, "change_percent": -1.60},
]

MOCK_STRATEGIES = [
    {"id": 1, "name": "均线策略", "status": "运行中", "return_rate": 12.5, "risk_level": "中"},
    {"id": 2, "name": "动量策略", "status": "已停止", "return_rate": 8.3, "risk_level": "高"},
    {"id": 3, "name": "价值投资", "status": "运行中", "return_rate": 15.2, "risk_level": "低"},
]

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/market/stocks")
async def get_market_data():
    """获取市场数据"""
    # 模拟实时价格变动
    for stock in MOCK_STOCKS:
        change = random.uniform(-0.5, 0.5)
        stock["price"] += change
        stock["change"] += change
        stock["change_percent"] = (stock["change"] / (stock["price"] - stock["change"])) * 100
    
    return {
        "success": True,
        "data": MOCK_STOCKS,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/kline/{symbol}")
async def get_kline_data(symbol: str):
    """获取K线数据"""
    # 生成模拟K线数据
    kline_data = []
    base_price = 100
    
    for i in range(30):  # 30天数据
        date = (datetime.now() - timedelta(days=29-i)).strftime("%Y-%m-%d")
        open_price = base_price + random.uniform(-5, 5)
        close_price = open_price + random.uniform(-3, 3)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)
        volume = random.randint(1000000, 10000000)
        
        kline_data.append({
            "date": date,
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume
        })
        
        base_price = close_price
    
    return {
        "success": True,
        "data": kline_data,
        "symbol": symbol
    }

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览"""
    return {
        "success": True,
        "data": {
            "total_stocks": 4000,
            "rising_stocks": 2100,
            "falling_stocks": 1500,
            "unchanged_stocks": 400,
            "total_volume": 125000000000,
            "total_turnover": 890000000000,
            "main_indices": [
                {"name": "上证指数", "value": 3245.67, "change": 12.34, "change_percent": 0.38},
                {"name": "深证成指", "value": 12456.78, "change": -23.45, "change_percent": -0.19},
                {"name": "创业板指", "value": 2567.89, "change": 45.67, "change_percent": 1.81}
            ]
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/sectors")
async def get_market_sectors():
    """获取板块数据"""
    sectors = [
        {"name": "科技", "change_percent": 2.34, "stocks_count": 245, "leader": "腾讯控股"},
        {"name": "金融", "change_percent": -0.56, "stocks_count": 189, "leader": "中国平安"},
        {"name": "医药", "change_percent": 1.78, "stocks_count": 156, "leader": "恒瑞医药"},
        {"name": "消费", "change_percent": 0.89, "stocks_count": 234, "leader": "贵州茅台"},
        {"name": "地产", "change_percent": -1.23, "stocks_count": 123, "leader": "万科A"},
        {"name": "能源", "change_percent": 3.45, "stocks_count": 98, "leader": "中石油"},
        {"name": "制造", "change_percent": 1.56, "stocks_count": 567, "leader": "比亚迪"},
        {"name": "通信", "change_percent": -0.34, "stocks_count": 87, "leader": "中国移动"}
    ]

    return {
        "success": True,
        "data": sectors,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/news")
async def get_market_news(limit: int = 10):
    """获取市场新闻"""
    news = [
        {
            "id": i,
            "title": f"市场新闻标题 {i}",
            "summary": f"这是第{i}条市场新闻的摘要内容...",
            "source": "财经网",
            "publish_time": (datetime.now() - timedelta(hours=i)).isoformat(),
            "url": f"https://example.com/news/{i}"
        }
        for i in range(1, limit + 1)
    ]

    return {
        "success": True,
        "data": news,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/rankings")
async def get_market_rankings(type: str = "change_percent", limit: int = 50):
    """获取市场排行榜"""
    rankings = []

    for i in range(limit):
        if type == "change_percent":
            value = random.uniform(-10, 10)
            rankings.append({
                "symbol": f"00000{i+1:02d}",
                "name": f"股票{i+1}",
                "price": round(random.uniform(10, 200), 2),
                "change_percent": round(value, 2),
                "volume": random.randint(1000000, 100000000)
            })
        elif type == "turnover":
            value = random.randint(100000000, 10000000000)
            rankings.append({
                "symbol": f"00000{i+1:02d}",
                "name": f"股票{i+1}",
                "price": round(random.uniform(10, 200), 2),
                "turnover": value,
                "volume": random.randint(1000000, 100000000)
            })

    # 按指定类型排序
    if type == "change_percent":
        rankings.sort(key=lambda x: x["change_percent"], reverse=True)
    elif type == "turnover":
        rankings.sort(key=lambda x: x["turnover"], reverse=True)

    return {
        "success": True,
        "data": rankings,
        "type": type,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/kline")
async def get_kline_data_new(symbol: str, period: str = "1d", limit: int = 500):
    """获取K线数据 (新版本支持查询参数)"""
    # 生成模拟K线数据
    kline_data = []
    base_price = 100

    for i in range(min(limit, 500)):  # 限制最大500条
        if period == "1d":
            timestamp = datetime.now() - timedelta(days=limit-i)
        elif period == "1h":
            timestamp = datetime.now() - timedelta(hours=limit-i)
        else:
            timestamp = datetime.now() - timedelta(minutes=limit-i)

        open_price = base_price + random.uniform(-2, 2)
        close_price = open_price + random.uniform(-1, 1)
        high_price = max(open_price, close_price) + random.uniform(0, 0.5)
        low_price = min(open_price, close_price) - random.uniform(0, 0.5)
        volume = random.randint(1000000, 10000000)

        kline_data.append({
            "timestamp": timestamp.isoformat(),
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume
        })

        base_price = close_price

    return {
        "success": True,
        "data": kline_data,
        "symbol": symbol,
        "period": period,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/strategies")
async def get_strategies():
    """获取策略列表"""
    return {
        "success": True,
        "data": MOCK_STRATEGIES,
        "total": len(MOCK_STRATEGIES)
    }

@app.post("/api/v1/strategies")
async def create_strategy(strategy_data: dict):
    """创建新策略"""
    new_strategy = {
        "id": len(MOCK_STRATEGIES) + 1,
        "name": strategy_data.get("name", "新策略"),
        "status": "已创建",
        "return_rate": 0.0,
        "risk_level": strategy_data.get("risk_level", "中")
    }
    MOCK_STRATEGIES.append(new_strategy)
    
    return {
        "success": True,
        "data": new_strategy,
        "message": "策略创建成功"
    }

@app.get("/api/v1/portfolio")
async def get_portfolio():
    """获取投资组合"""
    portfolio_data = {
        "total_value": 1000000.00,
        "total_return": 125000.00,
        "return_rate": 12.5,
        "positions": [
            {"symbol": "000001", "name": "平安银行", "quantity": 1000, "cost": 12.00, "current_price": 12.45, "profit": 450.00},
            {"symbol": "600036", "name": "招商银行", "quantity": 500, "cost": 35.00, "current_price": 35.67, "profit": 335.00},
        ]
    }
    
    return {
        "success": True,
        "data": portfolio_data
    }

@app.get("/api/v1/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    risk_data = {
        "var": 0.05,  # Value at Risk
        "max_drawdown": 0.08,
        "sharpe_ratio": 1.25,
        "beta": 0.95,
        "volatility": 0.15
    }
    
    return {
        "success": True,
        "data": risk_data
    }

@app.post("/api/v1/trading/order")
async def place_order(order_data: dict):
    """下单"""
    order = {
        "order_id": f"ORDER_{int(time.time())}",
        "symbol": order_data.get("symbol"),
        "side": order_data.get("side"),  # buy/sell
        "quantity": order_data.get("quantity"),
        "price": order_data.get("price"),
        "status": "已提交",
        "timestamp": datetime.now().isoformat()
    }
    
    return {
        "success": True,
        "data": order,
        "message": "订单提交成功"
    }

@app.get("/api/v1/trading/orders")
async def get_orders():
    """获取订单列表"""
    orders = [
        {
            "order_id": "ORDER_001",
            "symbol": "000001",
            "side": "buy",
            "quantity": 100,
            "price": 12.40,
            "status": "已成交",
            "timestamp": datetime.now().isoformat()
        }
    ]
    
    return {
        "success": True,
        "data": orders
    }

@app.get("/api/v1/trading/trades")
async def get_trades(limit: int = 100, startDate: str = None, endDate: str = None):
    """获取成交记录"""
    trades = []

    for i in range(min(limit, 20)):  # 生成一些模拟成交记录
        trade_time = datetime.now() - timedelta(hours=i)
        trades.append({
            "trade_id": f"TRADE_{i+1:03d}",
            "order_id": f"ORDER_{i+1:03d}",
            "symbol": f"00000{(i % 10) + 1}",
            "side": "buy" if i % 2 == 0 else "sell",
            "quantity": random.randint(100, 1000),
            "price": round(random.uniform(10, 200), 2),
            "amount": round(random.uniform(1000, 20000), 2),
            "commission": round(random.uniform(5, 50), 2),
            "timestamp": trade_time.isoformat()
        })

    return {
        "success": True,
        "data": trades,
        "total": len(trades),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/ctp/status")
async def get_ctp_status():
    """获取CTP连接状态"""
    return {
        "success": True,
        "data": {
            "connected": True,
            "login_status": "已登录",
            "trading_day": datetime.now().strftime("%Y%m%d"),
            "front_id": 1,
            "session_id": 123456,
            "last_heartbeat": datetime.now().isoformat()
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/ctp/account")
async def get_ctp_account():
    """获取CTP账户信息"""
    return {
        "success": True,
        "data": {
            "account_id": "*********",
            "balance": 1000000.00,
            "available": 950000.00,
            "frozen": 50000.00,
            "margin": 30000.00,
            "profit_loss": 15000.00,
            "currency": "CNY"
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/ctp/orders")
async def get_ctp_orders():
    """获取CTP订单列表"""
    orders = [
        {
            "order_id": f"CTP_ORDER_{i+1:03d}",
            "symbol": f"00000{(i % 5) + 1}",
            "side": "buy" if i % 2 == 0 else "sell",
            "quantity": random.randint(100, 1000),
            "price": round(random.uniform(10, 200), 2),
            "status": random.choice(["已提交", "部分成交", "已成交", "已撤销"]),
            "timestamp": (datetime.now() - timedelta(hours=i)).isoformat()
        }
        for i in range(10)
    ]

    return {
        "success": True,
        "data": orders,
        "total": len(orders),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/auth/user")
async def get_user_info():
    """获取用户信息"""
    user_data = {
        "id": 1,
        "username": "demo_user",
        "email": "<EMAIL>",
        "balance": 1000000.00,
        "permissions": ["trading", "strategy", "portfolio", "risk"]
    }
    
    return {
        "success": True,
        "data": user_data
    }

@app.post("/api/v1/auth/login")
async def login(credentials: dict):
    """登录"""
    username = credentials.get("username")
    password = credentials.get("password")
    
    # 简单的模拟登录
    if username and password:
        return {
            "success": True,
            "data": {
                "token": "mock_jwt_token",
                "user": {
                    "id": 1,
                    "username": username,
                    "email": f"{username}@example.com"
                }
            },
            "message": "登录成功"
        }
    else:
        raise HTTPException(status_code=400, detail="用户名或密码不能为空")

@app.get("/api/v1/backtest/results/{strategy_id}")
async def get_backtest_results(strategy_id: int):
    """获取回测结果"""
    backtest_data = {
        "strategy_id": strategy_id,
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "total_return": 15.2,
        "max_drawdown": 8.5,
        "sharpe_ratio": 1.35,
        "win_rate": 0.65,
        "trades": 156,
        "equity_curve": [
            {"date": "2024-01-01", "value": 100000},
            {"date": "2024-06-01", "value": 108000},
            {"date": "2024-12-31", "value": 115200}
        ]
    }
    
    return {
        "success": True,
        "data": backtest_data
    }

if __name__ == "__main__":
    print("🚀 启动量化投资平台测试后端服务...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    
    uvicorn.run(
        "simple_test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
