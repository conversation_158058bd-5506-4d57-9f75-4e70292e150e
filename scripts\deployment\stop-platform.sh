#!/bin/bash

# 量化投资平台停止脚本

set -e

echo "🛑 量化投资平台停止脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 清理端口
cleanup_port() {
    local port=$1
    if check_port $port; then
        log_info "停止端口 $port 上的服务..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 1
        
        if check_port $port; then
            log_warning "端口 $port 仍被占用"
        else
            log_success "端口 $port 已释放"
        fi
    else
        log_info "端口 $port 未被占用"
    fi
}

# 停止服务
stop_services() {
    log_info "正在停止量化投资平台服务..."
    
    # 通过PID文件停止服务
    if [ -f backend.pid ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            log_info "停止后端服务 (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            log_success "后端服务已停止"
        else
            log_warning "后端进程不存在"
        fi
        rm -f backend.pid
    else
        log_info "未找到后端PID文件"
    fi
    
    if [ -f frontend.pid ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            log_info "停止前端服务 (PID: $FRONTEND_PID)..."
            kill $FRONTEND_PID
            log_success "前端服务已停止"
        else
            log_warning "前端进程不存在"
        fi
        rm -f frontend.pid
    else
        log_info "未找到前端PID文件"
    fi
    
    # 强制清理端口
    cleanup_port 8000
    cleanup_port 5174
    
    # 清理其他可能的相关进程
    log_info "清理相关进程..."
    
    # 清理 uvicorn 进程
    pkill -f "uvicorn.*minimal_backend" 2>/dev/null || true
    
    # 清理 npm run dev 进程
    pkill -f "npm run dev" 2>/dev/null || true
    
    # 清理 vite 进程
    pkill -f "vite.*development" 2>/dev/null || true
    
    log_success "所有服务已停止"
}

# 清理临时文件
cleanup_files() {
    log_info "清理临时文件..."
    
    # 清理PID文件
    rm -f backend.pid frontend.pid
    
    # 清理日志文件（可选）
    if [ "$1" = "--clean-logs" ]; then
        log_info "清理日志文件..."
        rm -f backend.log frontend.log
    fi
    
    log_success "临时文件清理完成"
}

# 显示状态
show_status() {
    echo ""
    echo "📊 服务状态检查"
    echo "================================"
    
    if check_port 8000; then
        echo "后端服务 (8000): 🔴 运行中"
    else
        echo "后端服务 (8000): ✅ 已停止"
    fi
    
    if check_port 5174; then
        echo "前端服务 (5174): 🔴 运行中"
    else
        echo "前端服务 (5174): ✅ 已停止"
    fi
    
    echo "================================"
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    # 显示当前状态
    show_status
    
    # 停止服务
    stop_services
    
    # 清理文件
    cleanup_files "$1"
    
    # 显示最终状态
    show_status
    
    echo ""
    log_success "量化投资平台已完全停止"
}

# 执行主函数
main "$@"