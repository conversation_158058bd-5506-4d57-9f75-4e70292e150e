"""
订单管理模块API
提供订单查询、修改、批量操作等完整功能
"""
from datetime import datetime, timedelta
from typing import List, Optional, Dict
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.db.models.user import User

router = APIRouter()

# 扩展订单数据库（实际应使用真实数据库）
orders_db = {}


@router.get("/orders/list")
async def get_orders_list(
    status: Optional[str] = Query(None, description="订单状态"),
    symbol: Optional[str] = Query(None, description="股票代码"),
    side: Optional[str] = Query(None, description="买卖方向"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    sort_by: str = Query("create_time", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单列表（分页、筛选、排序）
    
    - **status**: 订单状态(PENDING/PARTIAL/FILLED/CANCELLED/REJECTED)
    - **symbol**: 股票代码
    - **side**: 买卖方向(BUY/SELL)
    - **start_date**: 开始日期(YYYY-MM-DD)
    - **end_date**: 结束日期(YYYY-MM-DD)
    - **sort_by**: 排序字段(create_time/update_time/symbol/amount)
    - **sort_order**: 排序方向(asc/desc)
    """
    # 筛选用户订单
    user_orders = []
    
    for order in orders_db.values():
        if order.get("user_id") != current_user.id:
            continue
            
        # 应用筛选条件
        if status and order.get("status") != status:
            continue
        if symbol and order.get("symbol") != symbol:
            continue
        if side and order.get("side") != side:
            continue
            
        # 日期筛选
        if start_date:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            if order.get("create_time", datetime.now()) < start:
                continue
        if end_date:
            end = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            if order.get("create_time", datetime.now()) >= end:
                continue
        
        # 格式化订单数据
        formatted_order = {
            "order_id": order.get("order_id"),
            "symbol": order.get("symbol"),
            "name": order.get("name", ""),
            "side": order.get("side"),
            "order_type": order.get("order_type"),
            "status": order.get("status"),
            "price": order.get("price"),
            "quantity": order.get("quantity"),
            "filled_quantity": order.get("filled_quantity", 0),
            "filled_price": order.get("filled_price"),
            "amount": order.get("price", 0) * order.get("quantity", 0),
            "filled_amount": order.get("filled_price", 0) * order.get("filled_quantity", 0),
            "commission": order.get("commission", 0),
            "create_time": order.get("create_time", datetime.now()).isoformat(),
            "update_time": order.get("update_time", datetime.now()).isoformat(),
            "source": order.get("source", "WEB"),  # WEB/APP/API
            "remark": order.get("remark", "")
        }
        
        user_orders.append(formatted_order)
    
    # 排序
    reverse = (sort_order == "desc")
    if sort_by == "amount":
        user_orders.sort(key=lambda x: x["amount"], reverse=reverse)
    elif sort_by == "symbol":
        user_orders.sort(key=lambda x: x["symbol"], reverse=reverse)
    else:
        user_orders.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 分页
    total = len(user_orders)
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    orders_page = user_orders[start_idx:end_idx]
    
    # 计算统计信息
    stats = {
        "total_orders": total,
        "pending_count": len([o for o in user_orders if o["status"] == "PENDING"]),
        "filled_count": len([o for o in user_orders if o["status"] == "FILLED"]),
        "cancelled_count": len([o for o in user_orders if o["status"] == "CANCELLED"]),
        "total_amount": sum(o["amount"] for o in user_orders),
        "total_commission": sum(o["commission"] for o in user_orders)
    }
    
    return {
        "success": True,
        "data": orders_page,
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total": total,
            "total_pages": (total + page_size - 1) // page_size
        },
        "stats": stats
    }


@router.get("/orders/{order_id}/detail")
async def get_order_detail(
    order_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单详细信息
    
    包括订单状态历史、关联成交记录等
    """
    order = orders_db.get(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    if order.get("user_id") != current_user.id:
        raise HTTPException(status_code=403, detail="无权查看此订单")
    
    # 构建详细信息
    detail = {
        "order_info": {
            "order_id": order.get("order_id"),
            "symbol": order.get("symbol"),
            "name": order.get("name", ""),
            "side": order.get("side"),
            "order_type": order.get("order_type"),
            "status": order.get("status"),
            "price": order.get("price"),
            "quantity": order.get("quantity"),
            "filled_quantity": order.get("filled_quantity", 0),
            "filled_price": order.get("filled_price"),
            "amount": order.get("price", 0) * order.get("quantity", 0),
            "commission": order.get("commission", 0),
            "create_time": order.get("create_time", datetime.now()).isoformat(),
            "update_time": order.get("update_time", datetime.now()).isoformat()
        },
        "status_history": [
            {
                "status": "SUBMITTED",
                "time": order.get("create_time", datetime.now()).isoformat(),
                "message": "订单已提交"
            },
            {
                "status": order.get("status"),
                "time": order.get("update_time", datetime.now()).isoformat(),
                "message": f"订单{order.get('status')}"
            }
        ],
        "trades": [],  # 关联的成交记录
        "operations": []  # 可执行的操作
    }
    
    # 添加可执行操作
    if order.get("status") == "PENDING":
        detail["operations"].extend(["CANCEL", "MODIFY"])
    elif order.get("status") == "PARTIAL":
        detail["operations"].append("CANCEL")
    
    return {
        "success": True,
        "data": detail
    }


@router.put("/orders/{order_id}/modify")
async def modify_order(
    order_id: str,
    price: Optional[float] = Body(None, description="新价格"),
    quantity: Optional[int] = Body(None, description="新数量"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    修改订单
    
    只能修改未成交的订单的价格或数量
    """
    order = orders_db.get(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    if order.get("user_id") != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此订单")
    
    if order.get("status") not in ["PENDING", "PARTIAL"]:
        raise HTTPException(status_code=400, detail="订单状态不允许修改")
    
    # 修改订单
    modified = False
    
    if price is not None and price != order.get("price"):
        order["price"] = price
        modified = True
    
    if quantity is not None and quantity != order.get("quantity"):
        if quantity <= order.get("filled_quantity", 0):
            raise HTTPException(status_code=400, detail="新数量不能小于已成交数量")
        order["quantity"] = quantity
        modified = True
    
    if modified:
        order["update_time"] = datetime.now()
        
        # 添加修改记录
        if "modify_history" not in order:
            order["modify_history"] = []
        
        order["modify_history"].append({
            "time": datetime.now().isoformat(),
            "price": price,
            "quantity": quantity
        })
    
    return {
        "success": True,
        "message": "订单修改成功" if modified else "订单未修改",
        "data": {
            "order_id": order_id,
            "price": order.get("price"),
            "quantity": order.get("quantity"),
            "status": order.get("status")
        }
    }


@router.post("/orders/batch-cancel")
async def batch_cancel_orders(
    order_ids: List[str] = Body(..., description="订单ID列表"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    批量撤销订单
    
    - **order_ids**: 要撤销的订单ID列表
    """
    results = []
    success_count = 0
    
    for order_id in order_ids:
        order = orders_db.get(order_id)
        
        if not order:
            results.append({
                "order_id": order_id,
                "success": False,
                "message": "订单不存在"
            })
            continue
        
        if order.get("user_id") != current_user.id:
            results.append({
                "order_id": order_id,
                "success": False,
                "message": "无权操作"
            })
            continue
        
        if order.get("status") not in ["PENDING", "PARTIAL"]:
            results.append({
                "order_id": order_id,
                "success": False,
                "message": "订单状态不允许撤销"
            })
            continue
        
        # 撤销订单
        order["status"] = "CANCELLED"
        order["update_time"] = datetime.now()
        
        results.append({
            "order_id": order_id,
            "success": True,
            "message": "撤销成功"
        })
        success_count += 1
    
    return {
        "success": True,
        "message": f"成功撤销{success_count}个订单",
        "results": results,
        "summary": {
            "total": len(order_ids),
            "success": success_count,
            "failed": len(order_ids) - success_count
        }
    }


@router.post("/orders/cancel-all")
async def cancel_all_orders(
    symbol: Optional[str] = Body(None, description="股票代码，不填则撤销所有"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    撤销所有订单
    
    - **symbol**: 指定股票代码，不填则撤销所有未成交订单
    """
    cancelled_count = 0
    
    for order in orders_db.values():
        if order.get("user_id") != current_user.id:
            continue
        
        if order.get("status") not in ["PENDING", "PARTIAL"]:
            continue
        
        if symbol and order.get("symbol") != symbol:
            continue
        
        # 撤销订单
        order["status"] = "CANCELLED"
        order["update_time"] = datetime.now()
        cancelled_count += 1
    
    return {
        "success": True,
        "message": f"成功撤销{cancelled_count}个订单",
        "cancelled_count": cancelled_count
    }


@router.get("/orders/export")
async def export_orders(
    format: str = Query("csv", regex="^(csv|excel|json)$"),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    导出订单数据
    
    - **format**: 导出格式(csv/excel/json)
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    """
    # 获取用户订单
    user_orders = []
    
    for order in orders_db.values():
        if order.get("user_id") != current_user.id:
            continue
        
        # 日期筛选
        if start_date:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            if order.get("create_time", datetime.now()) < start:
                continue
        if end_date:
            end = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            if order.get("create_time", datetime.now()) >= end:
                continue
        
        user_orders.append({
            "订单号": order.get("order_id"),
            "股票代码": order.get("symbol"),
            "股票名称": order.get("name", ""),
            "买卖方向": "买入" if order.get("side") == "BUY" else "卖出",
            "订单类型": "市价" if order.get("order_type") == "MARKET" else "限价",
            "状态": order.get("status"),
            "委托价格": order.get("price"),
            "委托数量": order.get("quantity"),
            "成交数量": order.get("filled_quantity", 0),
            "成交均价": order.get("filled_price", 0),
            "手续费": order.get("commission", 0),
            "创建时间": order.get("create_time", datetime.now()).strftime("%Y-%m-%d %H:%M:%S")
        })
    
    # 根据格式返回数据
    if format == "json":
        return {
            "success": True,
            "data": user_orders,
            "count": len(user_orders)
        }
    else:
        # CSV/Excel格式需要实际实现文件生成
        return {
            "success": True,
            "message": f"导出{len(user_orders)}条订单数据",
            "download_url": f"/download/orders_{current_user.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.{format}"
        }


@router.get("/orders/statistics")
async def get_order_statistics(
    period: str = Query("today", regex="^(today|week|month|year|all)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单统计
    
    - **period**: 统计周期(today/week/month/year/all)
    """
    # 确定时间范围
    now = datetime.now()
    if period == "today":
        start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == "week":
        start_time = now - timedelta(days=7)
    elif period == "month":
        start_time = now - timedelta(days=30)
    elif period == "year":
        start_time = now - timedelta(days=365)
    else:
        start_time = datetime(2000, 1, 1)
    
    # 统计数据
    stats = {
        "period": period,
        "start_time": start_time.isoformat(),
        "total_orders": 0,
        "buy_orders": 0,
        "sell_orders": 0,
        "filled_orders": 0,
        "cancelled_orders": 0,
        "rejected_orders": 0,
        "partial_orders": 0,
        "total_amount": 0,
        "total_commission": 0,
        "success_rate": 0,
        "average_order_amount": 0,
        "most_traded_stock": None,
        "daily_distribution": []
    }
    
    stock_counts = {}
    
    for order in orders_db.values():
        if order.get("user_id") != current_user.id:
            continue
        
        if order.get("create_time", now) < start_time:
            continue
        
        # 基础统计
        stats["total_orders"] += 1
        
        if order.get("side") == "BUY":
            stats["buy_orders"] += 1
        else:
            stats["sell_orders"] += 1
        
        # 状态统计
        status = order.get("status")
        if status == "FILLED":
            stats["filled_orders"] += 1
        elif status == "CANCELLED":
            stats["cancelled_orders"] += 1
        elif status == "REJECTED":
            stats["rejected_orders"] += 1
        elif status == "PARTIAL":
            stats["partial_orders"] += 1
        
        # 金额统计
        amount = order.get("price", 0) * order.get("quantity", 0)
        stats["total_amount"] += amount
        stats["total_commission"] += order.get("commission", 0)
        
        # 股票统计
        symbol = order.get("symbol")
        if symbol:
            stock_counts[symbol] = stock_counts.get(symbol, 0) + 1
    
    # 计算衍生指标
    if stats["total_orders"] > 0:
        stats["success_rate"] = round(stats["filled_orders"] / stats["total_orders"] * 100, 2)
        stats["average_order_amount"] = round(stats["total_amount"] / stats["total_orders"], 2)
    
    # 最活跃股票
    if stock_counts:
        most_traded = max(stock_counts.items(), key=lambda x: x[1])
        stats["most_traded_stock"] = {
            "symbol": most_traded[0],
            "count": most_traded[1]
        }
    
    return {
        "success": True,
        "data": stats
    }


@router.post("/orders/template/save")
async def save_order_template(
    name: str = Body(..., description="模板名称"),
    template: Dict = Body(..., description="订单模板"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    保存订单模板
    
    保存常用的订单设置作为模板
    """
    # 实际应保存到数据库
    template_id = f"TPL{uuid.uuid4().hex[:8].upper()}"
    
    return {
        "success": True,
        "message": "模板保存成功",
        "data": {
            "template_id": template_id,
            "name": name,
            "created_at": datetime.now().isoformat()
        }
    }


@router.get("/orders/template/list")
async def get_order_templates(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单模板列表
    """
    # 模拟模板数据
    templates = [
        {
            "template_id": "TPL001",
            "name": "快速买入",
            "side": "BUY",
            "order_type": "MARKET",
            "quantity_type": "fixed",
            "quantity": 100
        },
        {
            "template_id": "TPL002",
            "name": "分批卖出",
            "side": "SELL",
            "order_type": "LIMIT",
            "quantity_type": "percent",
            "quantity": 50
        },
        {
            "template_id": "TPL003",
            "name": "定额买入",
            "side": "BUY",
            "order_type": "LIMIT",
            "quantity_type": "amount",
            "amount": 10000
        }
    ]
    
    return {
        "success": True,
        "data": templates,
        "count": len(templates)
    }


@router.get("/health")
async def health_check():
    """
    订单管理模块健康检查
    """
    return {
        "status": "healthy",
        "module": "order_management",
        "timestamp": datetime.now().isoformat(),
        "orders_count": len(orders_db)
    }