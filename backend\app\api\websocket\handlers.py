"""
WebSocket处理器
"""

import asyncio
import json
import logging
from typing import Any, Dict

from fastapi import Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_current_user_from_websocket
from app.core.database import get_db as get_async_session
from app.db.models.user import User
from app.services.market_data_service import MarketDataService
from app.services.trading_service import TradingService

from .connection import websocket_manager

logger = logging.getLogger(__name__)


async def handle_general_websocket(websocket: WebSocket, token: str = None):
    """
    处理通用WebSocket连接
    支持多种消息类型的路由和处理
    """
    client_id = None
    user = None

    try:
        # 验证认证token
        if token == "dev-token-for-testing":
            # 开发环境特殊处理
            user = {"id": "dev_user", "username": "dev_user"}
            logger.info("WebSocket开发认证成功")
        elif token:
            # 这里应该验证真实的token
            # user = await verify_websocket_token(token)
            logger.warning(f"WebSocket认证token验证跳过: {token[:10]}...")
        else:
            logger.warning("WebSocket连接缺少认证token，允许匿名连接")

        await websocket.accept()
        logger.info("通用WebSocket连接已建立")

        # 发送连接确认
        await websocket.send_json({
            "type": "connection",
            "status": "connected",
            "message": "WebSocket连接成功",
            "timestamp": asyncio.get_event_loop().time()
        })

        while True:
            # 接收消息
            data = await websocket.receive_text()
            message = json.loads(data)
            message_type = message.get("type")

            logger.info(f"收到WebSocket消息: {message_type}")

            # 处理认证消息
            if message_type == "auth":
                token = message.get("token")
                if token:
                    try:
                        # 这里应该验证token并获取用户信息
                        # user = await get_current_user_from_token(token)
                        client_id = message.get("client_id", "anonymous")
                        await websocket.send_json({
                            "type": "auth_response",
                            "status": "success",
                            "message": "认证成功"
                        })
                    except Exception as e:
                        await websocket.send_json({
                            "type": "auth_response",
                            "status": "error",
                            "message": f"认证失败: {str(e)}"
                        })
                        continue
                else:
                    # 允许匿名连接
                    client_id = message.get("client_id", "anonymous")
                    await websocket.send_json({
                        "type": "auth_response",
                        "status": "success",
                        "message": "匿名连接成功"
                    })

            # 处理心跳消息
            elif message_type == "ping":
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": message.get("timestamp")
                })

            # 处理订阅消息
            elif message_type == "subscribe":
                channel = message.get("channel")
                if channel:
                    await websocket_manager.subscribe(websocket, channel)
                    await websocket.send_json({
                        "type": "subscribe_response",
                        "status": "success",
                        "channel": channel,
                        "message": f"订阅频道 {channel} 成功"
                    })

            # 处理取消订阅消息
            elif message_type == "unsubscribe":
                channel = message.get("channel")
                if channel:
                    await websocket_manager.unsubscribe(websocket, channel)
                    await websocket.send_json({
                        "type": "unsubscribe_response",
                        "status": "success",
                        "channel": channel,
                        "message": f"取消订阅频道 {channel} 成功"
                    })

            # 处理未知消息类型
            else:
                await websocket.send_json({
                    "type": "error",
                    "message": f"未知消息类型: {message_type}"
                })

    except WebSocketDisconnect:
        logger.info("通用WebSocket连接正常断开")
    except Exception as e:
        logger.error(f"通用WebSocket连接错误: {e}")
        try:
            await websocket.send_json({
                "type": "error",
                "message": f"服务器错误: {str(e)}"
            })
        except:
            pass
    finally:
        if client_id:
            await websocket_manager.disconnect(websocket)
        logger.info("通用WebSocket连接已关闭")


async def _handle_subscribe_orders(
    websocket: WebSocket, user: User, trading_service: TradingService
):
    """处理订阅订单消息"""
    await websocket_manager.subscribe(websocket, f"orders_{user.id}")
    try:
        from app.schemas.trading import OrderQueryRequest
        query_request = OrderQueryRequest(
            user_id=user.id,
            limit=50,
            offset=0
        )
        orders, total = await trading_service.get_orders(query_request)
        await websocket_manager.send_to_connection(
            websocket,
            {
                "type": "orders_snapshot",
                "data": [
                    {
                        "id": order.id,
                        "symbol": order.symbol,
                        "side": order.side.value if order.side else None,
                        "type": order.type.value if order.type else None,
                        "status": order.status.value if order.status else None,
                        "quantity": float(order.quantity),
                        "price": float(order.price) if order.price else None,
                        "filled_quantity": float(order.filled_quantity),
                        "created_at": order.created_at.isoformat() if order.created_at else None,
                    }
                    for order in orders
                ],
                "total": total,
            },
        )
    except Exception as e:
        logger.error(f"获取订单失败: {e}")
        await websocket_manager.send_to_connection(
            websocket,
            {
                "type": "error",
                "message": f"获取订单失败: {str(e)}",
            },
        )


async def _handle_subscribe_positions(
    websocket: WebSocket, user: User, trading_service: TradingService
):
    """处理订阅持仓消息"""
    await websocket_manager.subscribe(websocket, f"positions_{user.id}")
    try:
        positions = await trading_service.get_positions(user_id=user.id)
        await websocket_manager.send_to_connection(
            websocket,
            {
                "type": "positions_snapshot",
                "data": [
                    {
                        "id": pos.id,
                        "symbol": pos.symbol,
                        "side": pos.side.value if pos.side else None,
                        "quantity": float(pos.quantity),
                        "price": float(pos.price) if pos.price else None,
                        "market_value": float(pos.market_value) if pos.market_value else None,
                        "unrealized_pnl": float(pos.unrealized_pnl) if pos.unrealized_pnl else None,
                        "updated_at": pos.updated_at.isoformat() if pos.updated_at else None,
                    }
                    for pos in positions
                ],
            },
        )
    except Exception as e:
        logger.error(f"获取持仓失败: {e}")
        await websocket_manager.send_to_connection(
            websocket,
            {
                "type": "error",
                "message": f"获取持仓失败: {str(e)}",
            },
        )


async def _handle_subscribe_trades(
    websocket: WebSocket, user: User, trading_service: TradingService
):
    """处理订阅成交消息"""
    await websocket_manager.subscribe(websocket, f"trades_{user.id}")
    try:
        from app.schemas.trading import TradeQueryRequest
        query_request = TradeQueryRequest(
            user_id=user.id,
            limit=50,
            offset=0
        )
        trades, total = await trading_service.get_trades(query_request)
        await websocket_manager.send_to_connection(
            websocket,
            {
                "type": "trades_snapshot",
                "data": [
                    {
                        "id": trade.id,
                        "order_id": trade.order_id,
                        "symbol": trade.symbol,
                        "side": trade.side.value if trade.side else None,
                        "quantity": float(trade.quantity),
                        "price": float(trade.price),
                        "commission": float(trade.commission) if trade.commission else None,
                        "trade_time": trade.trade_time.isoformat() if trade.trade_time else None,
                    }
                    for trade in trades
                ],
                "total": total,
            },
        )
    except Exception as e:
        logger.error(f"获取成交记录失败: {e}")
        await websocket_manager.send_to_connection(
            websocket,
            {
                "type": "error",
                "message": f"获取成交记录失败: {str(e)}",
            },
        )


async def _handle_submit_order(
    websocket: WebSocket, user: User, trading_service: TradingService, message: dict
):
    """处理提交订单消息"""
    try:
        order_data_dict = message.get("data")
        if not order_data_dict:
            raise ValueError("缺少订单数据")
        
        # 构造OrderData对象
        from app.schemas.trading import OrderData, Direction, Offset, OrderType
        
        order_data = OrderData(
            symbol=order_data_dict.get("symbol"),
            direction=Direction(order_data_dict.get("direction", "LONG")),
            offset=Offset(order_data_dict.get("offset", "OPEN")),
            type=OrderType(order_data_dict.get("type", "LIMIT")),
            volume=order_data_dict.get("volume", 0),
            price=order_data_dict.get("price"),
            user_id=user.id
        )
        
        order = await trading_service.create_order(order_data)

        await websocket_manager.send_to_connection(
            websocket, {
                "type": "order_submit_result", 
                "data": {
                    "success": True,
                    "order_id": order.id,
                    "message": "订单提交成功"
                }
            }
        )

        # 广播订单更新
        await websocket_manager.broadcast_to_topic(
            f"orders_{user.id}",
            {
                "type": "order_update", 
                "data": {
                    "id": order.id,
                    "symbol": order.symbol,
                    "side": order.side.value if order.side else None,
                    "type": order.type.value if order.type else None,
                    "status": order.status.value if order.status else None,
                    "quantity": float(order.quantity),
                    "price": float(order.price) if order.price else None,
                    "created_at": order.created_at.isoformat() if order.created_at else None,
                }
            },
        )
    except Exception as e:
        logger.error(f"订单提交失败: {e}")
        await websocket_manager.send_to_connection(
            websocket,
            {"type": "error", "message": f"订单提交失败: {str(e)}"},
        )


async def _handle_cancel_order(
    websocket: WebSocket, user: User, trading_service: TradingService, message: dict
):
    """处理撤销订单消息"""
    try:
        order_id = message.get("order_id")
        if not order_id:
            raise ValueError("缺少订单ID")
            
        success = await trading_service.cancel_order(order_id)

        await websocket_manager.send_to_connection(
            websocket, {
                "type": "order_cancel_result", 
                "data": {
                    "success": success,
                    "order_id": order_id,
                    "message": "订单撤销成功" if success else "订单撤销失败"
                }
            }
        )

        if success:
            await websocket_manager.broadcast_to_topic(
                f"orders_{user.id}",
                {"type": "order_cancelled", "order_id": order_id},
            )
    except Exception as e:
        logger.error(f"订单撤销失败: {e}")
        await websocket_manager.send_to_connection(
            websocket,
            {"type": "error", "message": f"订单撤销失败: {str(e)}"},
        )


async def handle_trading_websocket(
    websocket: WebSocket,
    user: User = Depends(get_current_user_from_websocket),
    db: AsyncSession = Depends(get_async_session),
):
    """处理交易WebSocket连接"""
    trading_service = TradingService(db)
    await websocket_manager.connect(websocket, user.id)

    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            message_type = message.get("type")

            # 使用字典映射处理不同消息类型
            handlers = {
                "subscribe_orders": lambda: _handle_subscribe_orders(
                    websocket, user, trading_service
                ),
                "subscribe_positions": lambda: _handle_subscribe_positions(
                    websocket, user, trading_service
                ),
                "subscribe_trades": lambda: _handle_subscribe_trades(
                    websocket, user, trading_service
                ),
                "submit_order": lambda: _handle_submit_order(
                    websocket, user, trading_service, message
                ),
                "cancel_order": lambda: _handle_cancel_order(
                    websocket, user, trading_service, message
                ),
                "ping": lambda: websocket_manager.send_to_connection(
                    websocket, {"type": "pong", "timestamp": message.get("timestamp")}
                ),
            }

            if message_type in handlers:
                await handlers[message_type]()
            else:
                await websocket_manager.send_to_connection(
                    websocket,
                    {"type": "error", "message": f"未知消息类型: {message_type}"},
                )

    except WebSocketDisconnect:
        logger.info(f"用户 {user.id} 交易WebSocket连接断开")
    except Exception as e:
        logger.error(f"交易WebSocket处理错误: {e}")
    finally:
        await websocket_manager.disconnect(websocket)


async def handle_market_data_websocket(
    websocket: WebSocket,
    user: User = Depends(get_current_user_from_websocket),
    db: AsyncSession = Depends(get_async_session),
):
    """处理行情数据WebSocket连接"""
    market_service = MarketDataService()

    await websocket_manager.connect(websocket, user.id)

    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            message_type = message.get("type")

            if message_type == "subscribe_tick":
                # 订阅实时行情
                symbols = message.get("symbols", [])
                for symbol in symbols:
                    await websocket_manager.subscribe(websocket, f"tick_{symbol}")

                await websocket_manager.send_to_connection(
                    websocket,
                    {"type": "tick_subscription_confirmed", "symbols": symbols},
                )

            elif message_type == "subscribe_kline":
                # 订阅K线数据
                symbol = message.get("symbol")
                interval = message.get("interval", "1m")

                await websocket_manager.subscribe(
                    websocket, f"kline_{symbol}_{interval}"
                )

                # TODO: 发送历史K线数据 - 需要实现get_kline_data方法
                # klines = await market_service.get_kline_data(symbol, interval, limit=100)
                klines = []  # 临时空数据，避免错误
                await websocket_manager.send_to_connection(
                    websocket,
                    {
                        "type": "kline_snapshot",
                        "symbol": symbol,
                        "interval": interval,
                        "data": klines,
                    },
                )

            elif message_type == "unsubscribe":
                # 取消订阅
                topic = message.get("topic")
                await websocket_manager.unsubscribe(websocket, topic)

            elif message_type == "ping":
                await websocket_manager.send_to_connection(
                    websocket, {"type": "pong", "timestamp": message.get("timestamp")}
                )

    except WebSocketDisconnect:
        logger.info(f"用户 {user.id} 行情WebSocket连接断开")
    except Exception as e:
        logger.error(f"行情WebSocket处理错误: {e}")
    finally:
        await websocket_manager.disconnect(websocket)


async def handle_strategy_websocket(
    websocket: WebSocket,
    user: User = Depends(get_current_user_from_websocket),
    db: AsyncSession = Depends(get_async_session),
):
    """处理策略WebSocket连接"""
    await websocket_manager.connect(websocket, user.id)

    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            message_type = message.get("type")

            if message_type == "subscribe_strategy_signals":
                # 订阅策略信号
                strategy_id = message.get("strategy_id")
                await websocket_manager.subscribe(
                    websocket, f"strategy_signals_{strategy_id}"
                )

            elif message_type == "subscribe_backtest_progress":
                # 订阅回测进度
                task_id = message.get("task_id")
                await websocket_manager.subscribe(
                    websocket, f"backtest_progress_{task_id}"
                )

            elif message_type == "ping":
                await websocket_manager.send_to_connection(
                    websocket, {"type": "pong", "timestamp": message.get("timestamp")}
                )

    except WebSocketDisconnect:
        logger.info(f"用户 {user.id} 策略WebSocket连接断开")
    except Exception as e:
        logger.error(f"策略WebSocket处理错误: {e}")
    finally:
        await websocket_manager.disconnect(websocket)


# 事件广播函数
async def broadcast_order_update(user_id: int, order_data: Dict[str, Any]):
    """广播订单更新"""
    await websocket_manager.broadcast_to_topic(
        f"orders_{user_id}", {"type": "order_update", "data": order_data}
    )


async def broadcast_trade_update(user_id: int, trade_data: Dict[str, Any]):
    """广播成交更新"""
    await websocket_manager.broadcast_to_topic(
        f"trades_{user_id}", {"type": "trade_update", "data": trade_data}
    )


async def broadcast_position_update(user_id: int, position_data: Dict[str, Any]):
    """广播持仓更新"""
    await websocket_manager.broadcast_to_topic(
        f"positions_{user_id}", {"type": "position_update", "data": position_data}
    )


async def broadcast_market_tick(symbol: str, tick_data: Dict[str, Any]):
    """广播行情TICK数据"""
    await websocket_manager.broadcast_to_topic(
        f"tick_{symbol}", {"type": "tick", "symbol": symbol, "data": tick_data}
    )


async def broadcast_kline_update(
    symbol: str, interval: str, kline_data: Dict[str, Any]
):
    """广播K线更新"""
    await websocket_manager.broadcast_to_topic(
        f"kline_{symbol}_{interval}",
        {
            "type": "kline_update",
            "symbol": symbol,
            "interval": interval,
            "data": kline_data,
        },
    )
