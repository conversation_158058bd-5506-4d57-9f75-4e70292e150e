{"timestamp": "2025-08-02T12:59:05.983Z", "summary": {"totalModules": 4, "workingModules": 4, "issueCount": 7, "healthScore": 100}, "moduleResults": [{"module": "首页和导航", "url": "http://localhost:5175", "result": {"title": "仪表盘 - 量化投资平台", "hasNavigation": true, "menuItems": [{"text": "仪表盘", "active": false}, {"text": "实时行情", "active": false}, {"text": "历史数据", "active": false}, {"text": "交易终端", "active": false}, {"text": "模拟交易", "active": false}, {"text": "MiniQMT实盘", "active": false}, {"text": "订单管理", "active": false}, {"text": "持仓管理", "active": false}, {"text": "策略中心", "active": false}, {"text": "策略开发", "active": false}, {"text": "策略监控", "active": false}, {"text": "策略文库", "active": false}, {"text": "回测分析", "active": false}, {"text": "投资组合", "active": false}, {"text": "风险管理", "active": false}, {"text": "组件展示", "active": false}], "hasLogo": true, "hasUserInfo": false, "contentLength": 393, "hasErrors": false}}, {"module": "模拟交易", "url": "http://localhost:5175/trading/simulated", "result": {"simulationBadge": true, "accountOverview": true, "stockSearch": true, "marketDepth": false, "tradingForm": true, "bottomPanel": true, "badgeText": "模拟交易", "searchPlaceholder": "输入股票代码或名称", "accountItems": [{"label": "总资产:", "value": "¥1,000,000"}, {"label": "可用资金:", "value": "¥800,000"}, {"label": "持仓市值:", "value": "¥200,000"}, {"label": "今日盈亏:", "value": "+¥5,000"}], "formInputs": 3, "tradingButtons": ["买入", "卖出", "买入"], "tabPanes": [null, null, null], "totalContent": 287, "isSimpleText": true, "contentPreview": "量化平台\n仪表盘\n行情中心\n交易中心\n交易终端\n模拟交易\nMiniQMT实盘\n订单管理\n持仓管理\n策略中心\n回测分析\n投资组合\n风险管理\n组件展示\n模拟交易\n模拟交易\n无风险练习\n总资产:\n¥1,000,000\n可用资金:\n¥800,000\n持仓市值:\n¥200,000\n今日盈亏:\n+¥5,000\n买入\n卖出\n股票代码:\n委托价格:\n委托数量:\n委托金额:\n¥0\n买入\n持仓\n委托\n成交\n股票代码\n\t\n股票名称\n\t\n持仓数量\n\t\n成本价\n\t\n现价\n\t\n市值\n\t\n盈亏\n000001\n\t\n平安银行\n\t\n1000\n\t\n10.5\n\t\n10.8\n\t\n10800\n\t\n+300.00", "searchInteractive": true}}, {"module": "实时行情", "url": "http://localhost:5175/market/realtime", "result": {"hasChart": false, "chartCanvas": false, "hasSidebar": false, "hasStockList": false, "stockItems": 0, "contentLength": 1428, "contentPreview": "量化平台\n仪表盘\n行情中心\n实时行情\n历史数据\n交易中心\n策略中心\n回测分析\n投资组合\n风险管理\n组件展示\n实时行情\n行情分析\n刷新\n深证成指\n10856.34\n-23.67 (-0.22%)\n创业板指\n2234.56\n+8.92 (+0.40%)\n上证指数\n3245.68\n+12.45 (+0.38%)\n科创50\n1045.23\n-5.67 (-0.54%)\n市场趋势\n 已就绪\n日线\n周线\n月线"}}, {"module": "历史数据", "url": "http://localhost:5175/market/historical", "result": {"hasSearchForm": false, "hasDataTable": false, "hasChartArea": false, "tableRows": 20, "contentLength": 1368, "contentPreview": "量化平台\n仪表盘\n行情中心\n实时行情\n历史数据\n交易中心\n策略中心\n回测分析\n投资组合\n风险管理\n组件展示\n历史数据中心\n历史行情数据\n\n提供A股、港股、美股等历史行情数据查询和分析工具\n\n热门股票\n银行股\n科技股\n白酒股\n4,500\n可查股票总数\n0\n覆盖交易所\n0\n涵盖行业\n0年\n历史数据跨度\n搜索\n交易所：\n全部\n上交所\n深交所\n北交所\n热门行业：\n全部\n银行\n医药\n食品\n电子\n软件\n地产\n"}}], "issues": [{"type": "<PERSON><PERSON><PERSON>", "message": "🚨 全局异常: JSHandle@object", "location": {"url": "http://localhost:5175/src/utils/resize-observer-fix.ts", "lineNumber": 10, "columnNumber": 25}}, {"type": "HTTP Error", "url": "http://localhost:8000/api/v1/health", "status": 404, "statusText": "Not Found"}, {"type": "<PERSON><PERSON><PERSON>", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "location": {"url": "http://localhost:8000/api/v1/health"}}, {"type": "HTTP Error", "url": "http://localhost:8000/api/v1/market/realtime/000001", "status": 404, "statusText": "Not Found"}, {"type": "<PERSON><PERSON><PERSON>", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "location": {"url": "http://localhost:8000/api/v1/market/realtime/000001"}}, {"type": "HTTP Error", "url": "http://localhost:8000/api/v1/strategy/list", "status": 404, "statusText": "Not Found"}, {"type": "<PERSON><PERSON><PERSON>", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "location": {"url": "http://localhost:8000/api/v1/strategy/list"}}]}