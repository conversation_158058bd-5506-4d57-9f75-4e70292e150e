{"test_time": "2025-08-01T19:32:22.837604", "security_warnings_count": 0, "security_warnings": [], "login_messages": [{"type": "log", "text": "[HTTP Request] POST /auth/login {headers: Object, data: Object, params: undefined}", "timestamp": "2025-08-01T19:32:16.627514"}, {"type": "log", "text": "[HTTP Response] POST /auth/login {status: 200, data: Object}", "timestamp": "2025-08-01T19:32:16.734044"}, {"type": "log", "text": "[HTTP Request] POST /auth/login {headers: Object, data: Object, params: undefined}", "timestamp": "2025-08-01T19:32:17.722268"}, {"type": "log", "text": "[HTTP Response] POST /auth/login {status: 200, data: Object}", "timestamp": "2025-08-01T19:32:17.737868"}], "user_state": {"isLoggedIn": true, "hasToken": false}, "test_result": "PASS"}