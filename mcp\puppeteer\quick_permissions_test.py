#!/usr/bin/env python3
"""
快速权限测试
"""

import asyncio
from puppeteer import BrowserManager

async def test():
    manager = BrowserManager()
    try:
        page = await manager.ensure_browser()
        print('🔧 测试权限修复...')
        
        # 监听控制台错误
        errors = []
        def handle_console(msg):
            if msg.type == 'error':
                errors.append(msg.text)
                print(f'[ERROR] {msg.text}')
        
        page.on('console', handle_console)
        
        await page.goto('http://localhost:5173/login')
        await page.wait_for_timeout(2000)
        
        await page.wait_for_selector('button:has-text("演示登录")')
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        
        await page.wait_for_timeout(5000)
        
        user_state = await page.evaluate('''
            () => {
                try {
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                    return {
                        isLoggedIn: userStore.isLoggedIn,
                        permissions: userStore.permissions,
                        hasPermissions: userStore.permissions !== undefined
                    };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f'👤 用户状态: {user_state}')
        print(f'❌ 控制台错误数: {len(errors)}')
        
        # 检查权限相关错误
        permission_errors = [e for e in errors if 'permissions' in e.lower()]
        print(f'🔒 权限错误数: {len(permission_errors)}')
        
        if len(permission_errors) == 0 and user_state.get('hasPermissions'):
            print('🎉 权限错误修复成功！')
            return True
        else:
            print('⚠️ 仍有问题需要修复')
            for error in permission_errors:
                print(f'   - {error}')
            return False
        
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    result = asyncio.run(test())
    exit(0 if result else 1)
