#!/usr/bin/env python3
"""
自动化注册和登录脚本
"""
import asyncio
import json
from playwright.async_api import async_playwright

async def auto_register_and_login():
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print("🚀 开始自动化注册流程...")
            
            # 导航到注册页面
            await page.goto('http://localhost:5174/register')
            await page.wait_for_load_state('networkidle')
            print("✅ 已导航到注册页面")
            
            # 填写注册表单
            await page.fill('input[placeholder="请输入用户名"]', 'testuser123')
            await page.fill('input[placeholder="请输入邮箱"]', '<EMAIL>')
            await page.fill('input[placeholder="请输入密码"]', 'password123')
            await page.fill('input[placeholder="请确认密码"]', 'password123')
            print("✅ 表单填写完成")
            
            # 等待滑块验证码加载
            await page.wait_for_selector('.slider-container', timeout=5000)
            print("✅ 滑块验证码已加载")
            
            # 处理滑块验证码
            slider_thumb = await page.query_selector('.slider-thumb')
            slider_track = await page.query_selector('.slider-track')
            
            if slider_thumb and slider_track:
                # 获取滑块和轨道的位置信息
                thumb_box = await slider_thumb.bounding_box()
                track_box = await slider_track.bounding_box()
                
                if thumb_box and track_box:
                    # 计算拖动距离（拖动到轨道的80%位置）
                    start_x = thumb_box['x'] + thumb_box['width'] / 2
                    start_y = thumb_box['y'] + thumb_box['height'] / 2
                    end_x = track_box['x'] + track_box['width'] * 0.8
                    end_y = start_y
                    
                    # 执行拖动操作
                    await page.mouse.move(start_x, start_y)
                    await page.mouse.down()
                    await page.mouse.move(end_x, end_y, steps=20)
                    await page.mouse.up()
                    
                    print("✅ 滑块验证码已完成")
                    await asyncio.sleep(1)
            
            # 点击注册按钮
            await page.click('button[type="submit"]')
            print("✅ 注册按钮已点击")
            
            # 等待注册结果
            await asyncio.sleep(3)
            
            # 检查是否注册成功（可能跳转到登录页面或显示成功消息）
            current_url = page.url
            print(f"📍 当前页面: {current_url}")
            
            # 如果注册成功，尝试登录
            if 'login' in current_url or current_url.endswith('/'):
                print("🔄 开始登录流程...")
                
                # 如果不在登录页面，导航到登录页面
                if 'login' not in current_url:
                    await page.goto('http://localhost:5174/login')
                    await page.wait_for_load_state('networkidle')
                
                # 填写登录表单
                await page.fill('input[placeholder="请输入用户名"]', 'testuser123')
                await page.fill('input[placeholder="请输入密码"]', 'password123')
                print("✅ 登录表单填写完成")
                
                # 处理登录页面的滑块验证码（如果有）
                try:
                    await page.wait_for_selector('.slider-container', timeout=2000)
                    slider_thumb = await page.query_selector('.slider-thumb')
                    slider_track = await page.query_selector('.slider-track')
                    
                    if slider_thumb and slider_track:
                        thumb_box = await slider_thumb.bounding_box()
                        track_box = await slider_track.bounding_box()
                        
                        if thumb_box and track_box:
                            start_x = thumb_box['x'] + thumb_box['width'] / 2
                            start_y = thumb_box['y'] + thumb_box['height'] / 2
                            end_x = track_box['x'] + track_box['width'] * 0.8
                            end_y = start_y
                            
                            await page.mouse.move(start_x, start_y)
                            await page.mouse.down()
                            await page.mouse.move(end_x, end_y, steps=20)
                            await page.mouse.up()
                            
                            print("✅ 登录滑块验证码已完成")
                            await asyncio.sleep(1)
                except:
                    print("ℹ️  登录页面无滑块验证码")
                
                # 点击登录按钮
                await page.click('button[type="submit"]')
                print("✅ 登录按钮已点击")
                
                # 等待登录结果
                await asyncio.sleep(3)
                final_url = page.url
                print(f"📍 最终页面: {final_url}")
                
                if 'dashboard' in final_url or final_url.endswith('/'):
                    print("🎉 登录成功！")
                else:
                    print("❌ 登录可能失败")
            
            # 保持浏览器打开一段时间以便查看结果
            print("⏳ 保持浏览器打开10秒以便查看结果...")
            await asyncio.sleep(10)
            
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(auto_register_and_login())
