/**
 * 实盘交易页面测试脚本
 */

const puppeteer = require('puppeteer');
const fs = require('fs');

async function testLiveTrading() {
    console.log('🚀 开始实盘交易页面测试...');
    
    const testResults = {
        timestamp: new Date().toISOString(),
        tests: [],
        screenshots: [],
        errors: []
    };
    
    try {
        const browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // 监听控制台输出
        page.on('console', msg => {
            console.log(`🖥️ [${msg.type()}]: ${msg.text()}`);
        });
        
        // 监听错误
        page.on('pageerror', error => {
            console.error('❌ 页面错误:', error.message);
            testResults.errors.push({
                type: 'page_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        });
        
        // 监听网络请求
        page.on('request', request => {
            if (request.url().includes('localhost:8000')) {
                console.log(`📤 API请求: ${request.method()} ${request.url()}`);
            }
        });
        
        page.on('response', response => {
            if (response.url().includes('localhost:8000')) {
                console.log(`📥 API响应: ${response.status()} ${response.url()}`);
            }
        });
        
        // 测试1: 访问实盘交易页面
        console.log('\n🎯 测试1: 访问实盘交易页面...');
        await page.goto('http://localhost:5173/trading/live', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 截图
        const screenshot1 = `live_trading_test_${Date.now()}.png`;
        await page.screenshot({ 
            path: screenshot1,
            fullPage: true 
        });
        testResults.screenshots.push(screenshot1);
        
        // 检查页面基本元素
        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                url: window.location.href,
                hasHeader: !!document.querySelector('.header-control-bar'),
                hasSearchBox: !!document.querySelector('.smart-search'),
                hasFundOverview: !!document.querySelector('.fund-overview'),
                hasChartSection: !!document.querySelector('.chart-section'),
                hasOrderPanel: !!document.querySelector('.order-panel'),
                hasDepthPanel: !!document.querySelector('.depth-panel'),
                hasPositionSection: !!document.querySelector('.position-section'),
                hasOrderQueue: !!document.querySelector('.order-queue-section'),
                hasStatusBar: !!document.querySelector('.bottom-status-bar'),
                contentLength: document.body.innerText.length
            };
        });
        
        testResults.tests.push({
            name: '页面基本结构检查',
            status: pageInfo.hasHeader && pageInfo.hasChartSection && pageInfo.hasOrderPanel ? 'PASS' : 'FAIL',
            details: pageInfo
        });
        
        console.log('📊 页面基本信息:', pageInfo);
        
        // 测试2: 检查智能搜索功能
        console.log('\n🔍 测试2: 检查智能搜索功能...');
        try {
            // 点击搜索框
            await page.click('.smart-search input');
            await page.type('.smart-search input', '平安银行');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查搜索建议
            const searchResults = await page.evaluate(() => {
                const suggestions = document.querySelectorAll('.search-suggestion');
                return {
                    hasSuggestions: suggestions.length > 0,
                    suggestionCount: suggestions.length,
                    firstSuggestion: suggestions[0] ? suggestions[0].innerText : null
                };
            });
            
            testResults.tests.push({
                name: '智能搜索功能',
                status: searchResults.hasSuggestions ? 'PASS' : 'FAIL',
                details: searchResults
            });
            
            console.log('🔍 搜索结果:', searchResults);
            
            // 选择第一个搜索结果
            if (searchResults.hasSuggestions) {
                await page.click('.search-suggestion');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
        } catch (error) {
            console.error('❌ 搜索测试失败:', error.message);
            testResults.tests.push({
                name: '智能搜索功能',
                status: 'ERROR',
                error: error.message
            });
        }
        
        // 测试3: 检查交易面板功能
        console.log('\n💰 测试3: 检查交易面板功能...');
        try {
            // 检查交易按钮
            const tradingButtons = await page.evaluate(() => {
                return {
                    hasBuyButton: !!document.querySelector('.buy-button'),
                    hasSellButton: !!document.querySelector('.sell-button'),
                    hasCancelButton: !!document.querySelector('.cancel-button'),
                    hasOrderForm: !!document.querySelector('.order-form-section'),
                    hasPriceInput: !!document.querySelector('input[type="number"]'),
                    hasQuantityInput: !!document.querySelectorAll('input[type="number"]')[1]
                };
            });
            
            testResults.tests.push({
                name: '交易面板功能',
                status: tradingButtons.hasBuyButton && tradingButtons.hasSellButton ? 'PASS' : 'FAIL',
                details: tradingButtons
            });
            
            console.log('💰 交易面板:', tradingButtons);
            
        } catch (error) {
            console.error('❌ 交易面板测试失败:', error.message);
            testResults.tests.push({
                name: '交易面板功能',
                status: 'ERROR',
                error: error.message
            });
        }
        
        // 测试4: 检查深度盘口
        console.log('\n📊 测试4: 检查深度盘口...');
        try {
            const depthData = await page.evaluate(() => {
                return {
                    hasDepthBook: !!document.querySelector('.depth-book'),
                    hasBuyOrders: !!document.querySelector('.buy-orders'),
                    hasSellOrders: !!document.querySelector('.sell-orders'),
                    buyOrderCount: document.querySelectorAll('.buy-row').length,
                    sellOrderCount: document.querySelectorAll('.sell-row').length,
                    hasLatestPrice: !!document.querySelector('.latest-price-divider')
                };
            });
            
            testResults.tests.push({
                name: '深度盘口功能',
                status: depthData.hasDepthBook && depthData.buyOrderCount > 0 ? 'PASS' : 'FAIL',
                details: depthData
            });
            
            console.log('📊 深度盘口:', depthData);
            
        } catch (error) {
            console.error('❌ 深度盘口测试失败:', error.message);
            testResults.tests.push({
                name: '深度盘口功能',
                status: 'ERROR',
                error: error.message
            });
        }
        
        // 测试5: 检查辅助功能区
        console.log('\n📋 测试5: 检查辅助功能区...');
        try {
            const auxiliaryData = await page.evaluate(() => {
                return {
                    hasPositionTable: !!document.querySelector('.position-table'),
                    hasOrderQueue: !!document.querySelector('.order-queue'),
                    hasSmartAlerts: !!document.querySelector('.smart-alerts-section'),
                    positionRows: document.querySelectorAll('.position-table .el-table__row').length,
                    orderRows: document.querySelectorAll('.order-queue .el-table__row').length
                };
            });
            
            testResults.tests.push({
                name: '辅助功能区',
                status: auxiliaryData.hasPositionTable && auxiliaryData.hasOrderQueue ? 'PASS' : 'FAIL',
                details: auxiliaryData
            });
            
            console.log('📋 辅助功能区:', auxiliaryData);
            
        } catch (error) {
            console.error('❌ 辅助功能区测试失败:', error.message);
            testResults.tests.push({
                name: '辅助功能区',
                status: 'ERROR',
                error: error.message
            });
        }
        
        // 最终截图
        const finalScreenshot = `live_trading_final_${Date.now()}.png`;
        await page.screenshot({ 
            path: finalScreenshot,
            fullPage: true 
        });
        testResults.screenshots.push(finalScreenshot);
        
        // 生成测试报告
        const reportFile = `live_trading_test_report_${Date.now()}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(testResults, null, 2));
        
        console.log('\n✅ 实盘交易页面测试完成！');
        console.log(`📄 测试报告已保存: ${reportFile}`);
        console.log(`📸 截图已保存: ${testResults.screenshots.join(', ')}`);
        
        // 统计测试结果
        const passCount = testResults.tests.filter(t => t.status === 'PASS').length;
        const failCount = testResults.tests.filter(t => t.status === 'FAIL').length;
        const errorCount = testResults.tests.filter(t => t.status === 'ERROR').length;
        
        console.log(`\n📊 测试统计:`);
        console.log(`✅ 通过: ${passCount}`);
        console.log(`❌ 失败: ${failCount}`);
        console.log(`⚠️ 错误: ${errorCount}`);
        
        // 保持浏览器打开以便查看
        console.log('\n🌐 浏览器保持打开状态，可以手动测试...');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        testResults.errors.push({
            type: 'test_error',
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
}

// 运行测试
testLiveTrading();
