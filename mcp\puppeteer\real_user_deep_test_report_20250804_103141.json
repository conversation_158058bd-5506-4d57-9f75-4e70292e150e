{"test_session": {"start_time": "2025-08-04T10:30:53.235321", "tester_profile": "Real User Simulation", "platform_url": "http://localhost:5173", "test_objectives": ["评估平台易用性", "发现功能性问题", "测试用户工作流", "检查性能表现", "验证数据准确性"]}, "user_journey": [{"name": "首次访问用户体验", "start_time": 1754274654.4692812, "steps": ["访问首页", "发现9个可点击元素"], "issues_found": ["缺少新用户引导或欢迎信息", "功能介绍不够清晰"], "user_feedback": ["没有看到任何帮助我了解平台功能的信息", "我无法快速了解这个平台能为我做什么"], "end_time": 1754274666.5453055, "duration": 12.076024293899536}, {"name": "市场数据探索", "start_time": 1754274666.5461037, "steps": ["导航到市场页面", "发现32个图表元素"], "issues_found": ["未发现数据表格", "未找到搜索功能"], "user_feedback": ["我期望看到股票或其他金融产品的数据表格", "我想搜索特定的股票或产品，但找不到搜索框"], "end_time": 1754274678.8591716, "duration": 12.31306791305542}, {"name": "交易工作流测试", "start_time": 1754274678.8600647, "steps": ["导航到交易页面"], "issues_found": ["未找到明确的模拟交易标识"], "user_feedback": ["我担心误操作进行真实交易"], "end_time": 1754274685.890549, "duration": 7.030484199523926}, {"name": "策略管理测试", "start_time": 1754274685.8910043, "steps": ["导航到策略页面", "发现55个策略卡片", "点击创建策略按钮"], "issues_found": ["未找到回测功能"], "user_feedback": ["我想测试策略的历史表现，但找不到回测功能"], "end_time": 1754274692.5633283, "duration": 6.672323942184448}, {"name": "投资组合管理测试", "start_time": 1754274692.5639544, "steps": ["导航到投资组合页面"], "issues_found": ["未找到资产概览信息"], "user_feedback": ["我无法看到总资产和收益情况"], "end_time": 1754274696.5951273, "duration": 4.03117299079895}, {"name": "风险管理测试", "start_time": 1754274696.5984888, "steps": ["导航到风险管理页面"], "issues_found": [], "user_feedback": [], "end_time": 1754274701.0126255, "duration": 4.414136648178101}, {"name": "移动端响应式测试", "start_time": 1754274701.013064, "steps": [], "issues_found": ["响应式测试异常: Page.set_viewport_size() takes 2 positional arguments but 3 were given"], "user_feedback": [], "end_time": 1754274701.0135143, "duration": 0.00045037269592285156}], "critical_issues": [{"type": "page_error", "message": "Cannot read properties of undefined (reading 'type')", "timestamp": "2025-08-04T10:31:22.885372", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流", "severity": "high"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'pointerdown')", "timestamp": "2025-08-04T10:31:30.290559", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理", "severity": "high"}], "usability_problems": [{"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:30:54.832296", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:30:55.789336", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/drawer.scss (3074.20ms)", "timestamp": "2025-08-04T10:31:03.212041", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/avatar.scss (3074.30ms)", "timestamp": "2025-08-04T10:31:03.213223", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/badge.scss (3062.40ms)", "timestamp": "2025-08-04T10:31:03.214923", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/breadcrumb.scss (3048.40ms)", "timestamp": "2025-08-04T10:31:03.215117", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/breadcrumb-item.scss (3043.40ms)", "timestamp": "2025-08-04T10:31:03.215324", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/menu.scss (3043.50ms)", "timestamp": "2025-08-04T10:31:03.215887", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/sub-menu.scss (3118.70ms)", "timestamp": "2025-08-04T10:31:03.288443", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/menu-item.scss (3108.00ms)", "timestamp": "2025-08-04T10:31:03.289428", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/row.scss (3056.10ms)", "timestamp": "2025-08-04T10:31:03.290607", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/progress.scss (3056.30ms)", "timestamp": "2025-08-04T10:31:03.290748", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/base.scss (3036.60ms)", "timestamp": "2025-08-04T10:31:03.290865", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/col.scss (3056.60ms)", "timestamp": "2025-08-04T10:31:03.291179", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/button.scss (3098.00ms)", "timestamp": "2025-08-04T10:31:03.352269", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/.vite/deps/decimal__js.js?v=0bafdf75 (3071.00ms)", "timestamp": "2025-08-04T10:31:03.352977", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/checkbox.scss (3071.30ms)", "timestamp": "2025-08-04T10:31:03.356615", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/tag.scss (3071.40ms)", "timestamp": "2025-08-04T10:31:03.358504", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/tooltip.scss (3055.00ms)", "timestamp": "2025-08-04T10:31:03.359486", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/scrollbar.scss (3055.20ms)", "timestamp": "2025-08-04T10:31:03.359650", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "🐌 慢资源加载: http://localhost:5173/node_modules/element-plus/theme-chalk/src/popper.scss (3025.80ms)", "timestamp": "2025-08-04T10:31:03.360882", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:06.526865", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:06.526954", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:06.603275", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:06.609352", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:08.398307", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 1/10", "timestamp": "2025-08-04T10:31:08.398782", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:08.897328", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 2/10", "timestamp": "2025-08-04T10:31:08.897465", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:09.398194", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 3/10", "timestamp": "2025-08-04T10:31:09.398487", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:09.898933", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 4/10", "timestamp": "2025-08-04T10:31:09.899523", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "WebSocket连接暂时不可用: Event", "timestamp": "2025-08-04T10:31:09.943229", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:10.401212", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 5/10", "timestamp": "2025-08-04T10:31:10.402073", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:10.903120", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 6/10", "timestamp": "2025-08-04T10:31:10.903932", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:11.417101", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 7/10", "timestamp": "2025-08-04T10:31:11.417756", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:11.918002", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 8/10", "timestamp": "2025-08-04T10:31:11.918786", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:12.417397", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 9/10", "timestamp": "2025-08-04T10:31:12.417530", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "⚠️ 市场图表容器引用未找到", "timestamp": "2025-08-04T10:31:12.920078", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "🔄 容器未准备好，重试 10/10", "timestamp": "2025-08-04T10:31:12.920968", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:12.952562", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:12.954012", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:16.316707", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:16.317079", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "WebSocket连接暂时不可用: Event", "timestamp": "2025-08-04T10:31:17.286384", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:19.179282", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:19.180997", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:19.607897", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:19.608356", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:19.609721", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:19.610439", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "WebSocket连接暂时不可用: Event", "timestamp": "2025-08-04T10:31:22.084786", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "交易WebSocket暂时不可用: Event", "timestamp": "2025-08-04T10:31:22.085546", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:25.131239", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:25.131344", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:25.913139", "url": "http://localhost:5173/strategy", "scenario": "策略管理"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:25.915803", "url": "http://localhost:5173/strategy", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:26.553282", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:26.559287", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:26.565395", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [props] [API] type.text is about to be deprecated in version 3.0.0, please use link instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/button.html#button-attributes\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:26.570834", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:29.636613", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:29.636757", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:30.307330", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:30.307870", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=0bafdf75:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=0bafdf75:6626:3)", "timestamp": "2025-08-04T10:31:30.308567", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:32.587126", "url": "http://localhost:5173/portfolio", "scenario": "投资组合管理"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:32.592912", "url": "http://localhost:5173/portfolio", "scenario": "投资组合管理"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:35.891745", "url": "http://localhost:5173/portfolio", "scenario": "投资组合管理"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:35.891835", "url": "http://localhost:5173/portfolio", "scenario": "投资组合管理"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/main.ts' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:36.635772", "url": "http://localhost:5173/risk", "scenario": "风险管理"}, {"type": "console_warning", "message": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "timestamp": "2025-08-04T10:31:36.640758", "url": "http://localhost:5173/risk", "scenario": "风险管理"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:40.294984", "url": "http://localhost:5173/risk", "scenario": "风险管理"}, {"type": "console_warning", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-04T10:31:40.295078", "url": "http://localhost:5173/risk", "scenario": "风险管理"}], "performance_issues": [{"action": "首页加载", "load_time": 0.012925386428833008, "performance_data": {"domContentLoaded": 0, "loadComplete": 0.8999999999068677, "firstPaint": 904, "firstContentfulPaint": 9224}, "timestamp": "2025-08-04T10:31:03.908892", "url": "http://localhost:5173/"}, {"action": "市场数据页面加载", "load_time": 0.004469633102416992, "performance_data": {"domContentLoaded": 0.10000000009313226, "loadComplete": 0.40000000037252903, "firstPaint": 296, "firstContentfulPaint": 296}, "timestamp": "2025-08-04T10:31:10.457138", "url": "http://localhost:5173/market"}, {"action": "交易页面加载", "load_time": 0.00511622428894043, "performance_data": {"domContentLoaded": 0.09999999962747097, "loadComplete": 0.5, "firstPaint": 400, "firstContentfulPaint": 400}, "timestamp": "2025-08-04T10:31:22.638603", "url": "http://localhost:5173/trading/terminal"}, {"action": "策略页面加载", "load_time": 0.0035381317138671875, "performance_data": {"domContentLoaded": 0.09999999962747097, "loadComplete": 0.3000000002793968, "firstPaint": 120, "firstContentfulPaint": 120}, "timestamp": "2025-08-04T10:31:27.020790", "url": "http://localhost:5173/strategy/center"}, {"action": "投资组合页面加载", "load_time": 0.0023736953735351562, "performance_data": {"domContentLoaded": 0, "loadComplete": 0.599999999627471, "firstPaint": 100, "firstContentfulPaint": 100}, "timestamp": "2025-08-04T10:31:33.258398", "url": "http://localhost:5173/portfolio"}, {"action": "风险管理页面加载", "load_time": 0.0034728050231933594, "performance_data": {"domContentLoaded": 0.10000000009313226, "loadComplete": 0.3000000002793968, "firstPaint": 144, "firstContentfulPaint": 144}, "timestamp": "2025-08-04T10:31:37.622702", "url": "http://localhost:5173/risk"}], "functional_bugs": [{"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:30:54.832138", "url": "http://localhost:5173/", "scenario": "首次访问用户"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:31:06.602878", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:31:09.941889", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:09.942005", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:31:09.942247", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:09.942345", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:31:09.942524", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:09.942601", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:09.942743", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:09.942814", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:09.942945", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:09.943014", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:09.943082", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "WebSocket错误详情: Event", "timestamp": "2025-08-04T10:31:09.943362", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "timestamp": "2025-08-04T10:31:09.944867", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-08-04T10:31:09.945058", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:31:13.282593", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:13.282780", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:31:13.283873", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:13.284086", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:31:13.284470", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:13.284631", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:13.284931", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:13.285083", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:13.299775", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:13.299978", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "❌ 容器检查失败，超过最大重试次数", "timestamp": "2025-08-04T10:31:13.430747", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "❌ 容器准备失败，尝试fallback方案", "timestamp": "2025-08-04T10:31:13.430887", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "⚠️ Fallback: 图表容器仍未找到，放弃初始化", "timestamp": "2025-08-04T10:31:14.647005", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:17.284858", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "WebSocket错误详情: Event", "timestamp": "2025-08-04T10:31:17.287465", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "重连失败: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-08-04T10:31:17.288308", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:31:17.620418", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:17.620954", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:31:17.624136", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:17.624596", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:31:17.636910", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:17.637495", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:17.638369", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:17.638811", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:17.669026", "scenario": "市场数据探索"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:17.669494", "url": "http://localhost:5173/market", "scenario": "市场数据探索"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:31:19.179172", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.084130", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "WebSocket错误详情: Event", "timestamp": "2025-08-04T10:31:22.085196", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "Error: {type: SYST<PERSON>, code: Error, message: Error: WebSocket连接暂时不可用, details: undefined, context: Object}", "timestamp": "2025-08-04T10:31:22.091170", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接暂时不可用\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-08-04T10:31:22.091347", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "Network error: XMLHttpRequest", "timestamp": "2025-08-04T10:31:22.115910", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "获取CTP状态失败: AxiosError", "timestamp": "2025-08-04T10:31:22.116313", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "刷新状态失败: AxiosError", "timestamp": "2025-08-04T10:31:22.116525", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/ctp/status", "method": "GET", "timestamp": "2025-08-04T10:31:22.116761", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.116941", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:31:22.117182", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.117292", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:31:22.117501", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.117589", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:31:22.117749", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.117852", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:22.118028", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.118110", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:22.118259", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:22.118341", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "🚨 页面错误: TypeError: Cannot read properties of undefined (reading 'type')\n    at Object.reset (http://localhost:5173/node_modules/.vite/deps/chunk-4K4XVGJT.js?v=0bafdf75:1964:34)\n    at Task2.seriesTaskReset [as _reset] (http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=0bafdf75:10712:70)\n    at Task2._doReset (http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=0bafdf75:6199:25)\n    at Task2.perform (http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=0bafdf75:6139:35)\n    at http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=0bafdf75:10550:22\n    at http://localhost:5173/node_modules/.vite/deps/chunk-N3SX5YXI.js?v=0bafdf75:653:10\n    at Map.forEach (<anonymous>)\n    at HashMap2.each (http://localhost:5173/node_modules/.vite/deps/chunk-N3SX5YXI.js?v=0bafdf75:652:15)\n    at http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=0bafdf75:10543:25\n    at Array.forEach (<anonymous>)", "timestamp": "2025-08-04T10:31:22.884659", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: TypeError, message: Cannot read properties of undefined (reading 'type'), details: undefined, context: Object}", "timestamp": "2025-08-04T10:31:22.884897", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "🚨 全局异常: {message: Uncaught TypeError: Cannot read properties of undefined (reading 'type'), filename: http://localhost:5173/node_modules/.vite/deps/chunk-URNRIFOG.js?v=0bafdf75, lineno: 12543, colno: 9, error: TypeError: Cannot read properties of undefined (reading 'type')\n    at Object.reset (http://localho…}", "timestamp": "2025-08-04T10:31:22.885100", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error in connection establishment: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:24.486884", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "CTP WebSocket连接错误: Event", "timestamp": "2025-08-04T10:31:24.488307", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "WebSocket连接失败: Event", "timestamp": "2025-08-04T10:31:24.488482", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "timestamp": "2025-08-04T10:31:25.488502", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:25.489067", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "timestamp": "2025-08-04T10:31:25.490230", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:25.490745", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/news?limit=10", "method": "GET", "timestamp": "2025-08-04T10:31:25.491490", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:25.491880", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=change_percent&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:25.492573", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:25.492942", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "request_failed", "url": "http://localhost:8000/api/v1/market/rankings?type=turnover&limit=50", "method": "GET", "timestamp": "2025-08-04T10:31:25.493637", "scenario": "交易工作流"}, {"type": "console_error", "message": "Failed to load resource: net::ERR_CONNECTION_REFUSED", "timestamp": "2025-08-04T10:31:25.494001", "url": "http://localhost:5173/trading/terminal", "scenario": "交易工作流"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:31:25.913010", "url": "http://localhost:5173/strategy", "scenario": "策略管理"}, {"type": "console_error", "message": "🚨 页面错误: TypeError: Cannot read properties of undefined (reading 'pointerdown')\n    at HTMLDocument.firstInputHandler (http://localhost:5173/src/services/performance-monitor.service.ts:70:47)", "timestamp": "2025-08-04T10:31:30.290150", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_error", "message": "Error: {type: <PERSON>Y<PERSON><PERSON>, code: TypeError, message: Cannot read properties of undefined (reading 'pointerdown'), details: undefined, context: Object}", "timestamp": "2025-08-04T10:31:30.290315", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_error", "message": "🚨 全局异常: {message: Uncaught TypeError: Cannot read properties of undefined (reading 'pointerdown'), filename: http://localhost:5173/src/services/performance-monitor.service.ts, lineno: 70, colno: 47, error: TypeError: Cannot read properties of undefined (reading 'pointerdown')\n    at HTMLDocument.firstInp…}", "timestamp": "2025-08-04T10:31:30.290465", "url": "http://localhost:5173/strategy/center", "scenario": "策略管理"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:31:32.587036", "url": "http://localhost:5173/portfolio", "scenario": "投资组合管理"}, {"type": "console_error", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-04T10:31:36.635615", "url": "http://localhost:5173/risk", "scenario": "风险管理"}], "ui_inconsistencies": [], "data_accuracy_issues": [], "accessibility_problems": [], "security_concerns": [], "report_summary": {"test_completion_time": "2025-08-04T10:31:41.013968", "total_test_duration": "46.54秒", "scenarios_tested": 7, "total_issues_found": 166, "critical_issues_count": 2, "usability_problems_count": 80, "functional_bugs_count": 84, "performance_issues_count": 6, "overall_assessment": "需要重要改进 - 功能性问题较多"}}