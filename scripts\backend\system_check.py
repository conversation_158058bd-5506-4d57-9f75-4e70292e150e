#!/usr/bin/env python3
"""
系统完整性检查脚本
使用MCP工具进行自动化检查，验证系统各组件的完整性
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

class SystemChecker:
    """系统检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'summary': {
                'total_checks': 0,
                'passed': 0,
                'failed': 0,
                'warnings': 0
            }
        }
    
    def check_file_exists(self, file_path: str, description: str) -> Dict[str, Any]:
        """检查文件是否存在"""
        full_path = self.project_root / file_path
        exists = full_path.exists()
        
        result = {
            'description': description,
            'path': str(full_path),
            'status': 'PASS' if exists else 'FAIL',
            'exists': exists
        }
        
        if exists:
            result['size'] = full_path.stat().st_size
            result['modified'] = datetime.fromtimestamp(full_path.stat().st_mtime).isoformat()
        
        return result
    
    def check_directory_structure(self) -> Dict[str, Any]:
        """检查目录结构"""
        required_dirs = [
            'app',
            'app/api',
            'app/api/v1',
            'app/services',
            'app/models',
            'app/core',
            'app/db',
            'app/schemas'
        ]
        
        results = {}
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            results[dir_path] = {
                'exists': full_path.exists(),
                'is_dir': full_path.is_dir() if full_path.exists() else False,
                'status': 'PASS' if full_path.exists() and full_path.is_dir() else 'FAIL'
            }
        
        return {
            'description': '目录结构检查',
            'details': results,
            'status': 'PASS' if all(r['status'] == 'PASS' for r in results.values()) else 'FAIL'
        }
    
    def check_api_routes(self) -> Dict[str, Any]:
        """检查API路由文件"""
        api_files = [
            'app/api/v1/__init__.py',
            'app/api/v1/auth_fixed.py',
            'app/api/v1/strategy_fixed.py',
            'app/api/v1/risk_fixed.py',
            'app/api/v1/backtest.py',
            'app/api/v1/backtest_enhanced.py',
            'app/api/v1/trading.py'
        ]
        
        results = {}
        for file_path in api_files:
            results[file_path] = self.check_file_exists(file_path, f'API路由文件: {file_path}')
        
        return {
            'description': 'API路由文件检查',
            'details': results,
            'status': 'PASS' if all(r['status'] == 'PASS' for r in results.values()) else 'FAIL'
        }
    
    def check_service_files(self) -> Dict[str, Any]:
        """检查服务文件"""
        service_files = [
            'app/services/backtest_engine_enhanced.py',
            'app/services/backtest_data_analyzer.py',
            'app/services/backtest_visualizer.py',
            'app/services/backtest_service.py',
            'app/services/trading_service_impl.py',
            'app/services/auth_service.py',
            'app/services/strategy_service.py',
            'app/services/risk_service.py'
        ]
        
        results = {}
        for file_path in service_files:
            results[file_path] = self.check_file_exists(file_path, f'服务文件: {file_path}')
        
        return {
            'description': '服务文件检查',
            'details': results,
            'status': 'PASS' if all(r['status'] == 'PASS' for r in results.values()) else 'FAIL'
        }
    
    def check_model_files(self) -> Dict[str, Any]:
        """检查模型文件"""
        model_files = [
            'app/models/trading.py'
        ]
        
        results = {}
        for file_path in model_files:
            results[file_path] = self.check_file_exists(file_path, f'模型文件: {file_path}')
        
        return {
            'description': '数据模型文件检查',
            'details': results,
            'status': 'PASS' if all(r['status'] == 'PASS' for r in results.values()) else 'FAIL'
        }
    
    def check_core_files(self) -> Dict[str, Any]:
        """检查核心文件"""
        core_files = [
            'app/core/fix_api_system.py'
        ]
        
        results = {}
        for file_path in core_files:
            results[file_path] = self.check_file_exists(file_path, f'核心文件: {file_path}')
        
        return {
            'description': '核心系统文件检查',
            'details': results,
            'status': 'PASS' if all(r['status'] == 'PASS' for r in results.values()) else 'FAIL'
        }
    
    def check_code_quality(self) -> Dict[str, Any]:
        """检查代码质量"""
        issues = []
        
        # 检查Python语法
        python_files = []
        for root, dirs, files in os.walk(self.project_root / 'app'):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        syntax_errors = 0
        for py_file in python_files[:10]:  # 检查前10个文件作为示例
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    compile(f.read(), py_file, 'exec')
            except SyntaxError as e:
                syntax_errors += 1
                issues.append(f'语法错误: {py_file} - {str(e)}')
            except Exception:
                pass  # 忽略其他错误
        
        return {
            'description': '代码质量检查',
            'syntax_errors': syntax_errors,
            'total_files_checked': len(python_files[:10]),
            'issues': issues,
            'status': 'PASS' if syntax_errors == 0 else 'WARN'
        }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有检查"""
        checks = [
            ('directory_structure', self.check_directory_structure),
            ('api_routes', self.check_api_routes),
            ('service_files', self.check_service_files),
            ('model_files', self.check_model_files),
            ('core_files', self.check_core_files),
            ('code_quality', self.check_code_quality)
        ]
        
        for check_name, check_func in checks:
            print(f"运行检查: {check_name}")
            try:
                result = check_func()
                self.results['checks'][check_name] = result
                self.results['summary']['total_checks'] += 1
                
                if result['status'] == 'PASS':
                    self.results['summary']['passed'] += 1
                elif result['status'] == 'FAIL':
                    self.results['summary']['failed'] += 1
                else:  # WARN
                    self.results['summary']['warnings'] += 1
                    
            except Exception as e:
                self.results['checks'][check_name] = {
                    'description': f'{check_name} 检查',
                    'status': 'FAIL',
                    'error': str(e)
                }
                self.results['summary']['failed'] += 1
                self.results['summary']['total_checks'] += 1
        
        return self.results
    
    def generate_report(self) -> str:
        """生成检查报告"""
        report = []
        report.append("=" * 60)
        report.append("系统完整性检查报告")
        report.append("=" * 60)
        report.append(f"检查时间: {self.results['timestamp']}")
        report.append(f"总检查项: {self.results['summary']['total_checks']}")
        report.append(f"通过: {self.results['summary']['passed']}")
        report.append(f"失败: {self.results['summary']['failed']}")
        report.append(f"警告: {self.results['summary']['warnings']}")
        report.append("")
        
        for check_name, check_result in self.results['checks'].items():
            report.append(f"检查项: {check_name}")
            report.append(f"描述: {check_result['description']}")
            report.append(f"状态: {check_result['status']}")
            
            if 'details' in check_result:
                report.append("详细信息:")
                for key, value in check_result['details'].items():
                    report.append(f"  {key}: {value['status']}")
            
            if 'issues' in check_result and check_result['issues']:
                report.append("问题:")
                for issue in check_result['issues']:
                    report.append(f"  - {issue}")
            
            report.append("-" * 40)
        
        return '\n'.join(report)

async def main():
    """主函数"""
    checker = SystemChecker()
    
    print("开始系统完整性检查...")
    results = await checker.run_all_checks()
    
    # 生成报告
    report = checker.generate_report()
    print(report)
    
    # 保存结果到文件
    results_file = checker.project_root / 'system_check_results.json'
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    report_file = checker.project_root / 'system_check_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n检查结果已保存到: {results_file}")
    print(f"检查报告已保存到: {report_file}")
    
    # 返回检查状态
    if results['summary']['failed'] > 0:
        return 1
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)