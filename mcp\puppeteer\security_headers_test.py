#!/usr/bin/env python3
"""
安全头部测试 - 验证安全头部警告是否已消除
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

async def test_security_headers():
    """测试安全头部是否正确设置"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔒 测试安全头部...")
        
        # 监听控制台消息
        console_messages = []
        
        def handle_console(msg):
            console_messages.append({
                "type": msg.type,
                "text": msg.text,
                "timestamp": datetime.now().isoformat()
            })
            print(f"[CONSOLE {msg.type.upper()}] {msg.text}")
        
        page.on('console', handle_console)
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 等待演示登录按钮
        await page.wait_for_selector('button:has-text("演示登录")', timeout=10000)
        print("✅ 找到演示登录按钮")
        
        # 点击演示登录按钮
        demo_button = await page.query_selector('button:has-text("演示登录")')
        await demo_button.click()
        print("🖱️ 已点击演示登录按钮")
        
        # 等待登录处理
        print("⏳ 等待登录处理...")
        await page.wait_for_timeout(5000)
        
        # 分析控制台消息
        security_warnings = []
        login_messages = []
        
        for msg in console_messages:
            if "Missing security headers" in msg["text"]:
                security_warnings.append(msg)
            elif "HTTP Request" in msg["text"] or "HTTP Response" in msg["text"]:
                login_messages.append(msg)
        
        print(f"\n📊 控制台消息分析:")
        print(f"   - 总消息数: {len(console_messages)}")
        print(f"   - 安全头部警告: {len(security_warnings)}")
        print(f"   - 登录相关消息: {len(login_messages)}")
        
        if security_warnings:
            print("\n❌ 仍有安全头部警告:")
            for warning in security_warnings:
                print(f"   - {warning['text']}")
        else:
            print("\n✅ 没有安全头部警告！")
        
        # 检查用户登录状态
        user_state = await page.evaluate('''
            () => {
                try {
                    const app = document.querySelector('#app').__vue_app__;
                    const pinia = app.config.globalProperties.$pinia;
                    if (pinia && pinia._s) {
                        const userStore = Array.from(pinia._s.values()).find(store => store.$id === 'user');
                        if (userStore) {
                            return {
                                isLoggedIn: userStore.isLoggedIn,
                                hasToken: !!userStore.token
                            };
                        }
                    }
                    return { error: 'User store not found' };
                } catch (e) {
                    return { error: e.message };
                }
            }
        ''')
        
        print(f"\n👤 用户状态: {user_state}")
        
        # 生成测试报告
        test_report = {
            "test_time": datetime.now().isoformat(),
            "security_warnings_count": len(security_warnings),
            "security_warnings": security_warnings,
            "login_messages": login_messages,
            "user_state": user_state,
            "test_result": "PASS" if len(security_warnings) == 0 else "FAIL"
        }
        
        with open('security_headers_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 测试报告已保存: security_headers_test_report.json")
        
        # 截图
        screenshot_name = f"security_test_{datetime.now().strftime('%H%M%S')}.png"
        await page.screenshot(path=screenshot_name, full_page=True)
        print(f"📸 测试截图已保存: {screenshot_name}")
        
        if len(security_warnings) == 0:
            print("\n🎉 安全头部修复成功！")
        else:
            print("\n⚠️ 仍需要进一步修复安全头部问题")
        
    except Exception as e:
        print(f"💥 测试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_security_headers())
