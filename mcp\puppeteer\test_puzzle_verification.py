#!/usr/bin/env python3
"""
专门测试拼图验证功能
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_puzzle_verification():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    try:
        # 1. 登录并跳转到拼图验证页面
        logger.info("1. 执行登录...")
        await page.goto("http://localhost:5173/login")
        await page.wait_for_load_state('networkidle')
        
        # 清除存储
        await page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
        await page.reload()
        await page.wait_for_load_state('networkidle')
        
        # 点击演示登录
        demo_btn = await page.query_selector('button:has-text("演示登录")')
        if demo_btn:
            await demo_btn.click()
            await asyncio.sleep(3)
            
            if 'puzzle-verify' in page.url:
                logger.info("✅ 成功跳转到拼图验证页面")
            else:
                logger.error("❌ 登录失败")
                return
        
        # 2. 分析拼图验证页面
        logger.info("2. 分析拼图验证页面...")
        await asyncio.sleep(2)
        
        # 获取拼图数据
        puzzle_info = await page.evaluate("""
            () => {
                const app = document.querySelector('#app');
                if (app && app.__vue__) {
                    const vm = app.__vue__;
                    // 尝试获取拼图数据
                    return {
                        hasVue: true,
                        // 这里可能需要根据实际的Vue组件结构调整
                    };
                }
                return { hasVue: false };
            }
        """)
        
        logger.info(f"Vue组件信息: {puzzle_info}")
        
        # 3. 测试滑动验证
        logger.info("3. 测试滑动验证...")
        
        slider_btn = await page.query_selector('.slider-btn')
        if not slider_btn:
            logger.error("❌ 未找到滑动按钮")
            return
        
        # 获取滑动按钮和轨道的位置
        btn_box = await slider_btn.bounding_box()
        track = await page.query_selector('.slider-track')
        track_box = await track.bounding_box()
        
        if btn_box and track_box:
            # 计算滑动距离 - 尝试滑动到中间位置
            start_x = btn_box['x'] + btn_box['width'] / 2
            start_y = btn_box['y'] + btn_box['height'] / 2
            
            # 尝试多个不同的目标位置
            target_positions = [
                track_box['x'] + track_box['width'] * 0.3,  # 30%位置
                track_box['x'] + track_box['width'] * 0.5,  # 50%位置
                track_box['x'] + track_box['width'] * 0.7,  # 70%位置
                track_box['x'] + track_box['width'] * 0.9,  # 90%位置
            ]
            
            for i, end_x in enumerate(target_positions):
                logger.info(f"   尝试位置 {i+1}: 滑动到 {(i+1)*20+10}% 位置")
                
                # 重置滑块（如果需要）
                if i > 0:
                    refresh_btn = await page.query_selector('.refresh-btn')
                    if refresh_btn:
                        await refresh_btn.click()
                        await asyncio.sleep(1)
                
                # 执行滑动
                await page.mouse.move(start_x, start_y)
                await page.mouse.down()
                
                # 平滑滑动
                steps = 15
                for step in range(steps + 1):
                    progress = step / steps
                    current_x = start_x + (end_x - start_x) * progress
                    await page.mouse.move(current_x, start_y)
                    await asyncio.sleep(0.03)
                
                await page.mouse.up()
                await asyncio.sleep(2)
                
                # 检查验证结果
                success_element = await page.query_selector('.slider-btn-success')
                success_message = await page.query_selector('.result-message.success')
                
                if success_element or success_message:
                    logger.info(f"✅ 验证成功！位置 {i+1} 有效")
                    
                    # 检查继续按钮
                    continue_btn = await page.query_selector('button:has-text("继续访问")')
                    if continue_btn:
                        is_disabled = await continue_btn.get_attribute('disabled')
                        if is_disabled is None:
                            logger.info("✅ 继续访问按钮已启用")
                            
                            # 测试继续访问
                            await continue_btn.click()
                            await asyncio.sleep(3)
                            
                            final_url = page.url
                            if final_url != "http://localhost:5173/puzzle-verify":
                                logger.info(f"✅ 成功跳转到主应用: {final_url}")
                            else:
                                logger.warning("⚠️ 点击继续访问但未跳转")
                            
                            break
                        else:
                            logger.warning("⚠️ 继续访问按钮仍然禁用")
                    break
                else:
                    logger.info(f"   位置 {i+1} 验证失败，尝试下一个位置")
            else:
                logger.error("❌ 所有位置都验证失败")
        
        # 4. 截图保存
        await page.screenshot(path="puzzle_verification_test.png")
        logger.info("📸 测试截图已保存: puzzle_verification_test.png")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    finally:
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_puzzle_verification())
