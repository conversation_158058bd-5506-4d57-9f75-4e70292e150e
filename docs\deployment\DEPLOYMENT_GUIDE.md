# 量化投资平台部署指南

## 概述

本指南说明如何部署和使用已修复的量化投资平台。平台现已包含完整的实时行情、交易终端、订单管理和策略开发功能。

## 新增模块说明

### 1. 实时行情模块 (market-v2)
- **路径前缀**: `/api/v1/market-v2`
- **功能**: 提供A股12只主要股票的实时行情、K线数据、市场深度等
- **主要端点**:
  - `/stocks/list` - 获取股票列表
  - `/quotes/realtime` - 获取实时行情
  - `/kline/{symbol}` - 获取K线数据
  - `/ws/quotes` - WebSocket实时推送

### 2. 交易终端模块 (terminal)
- **路径前缀**: `/api/v1/terminal`
- **功能**: 完整的交易界面功能，包括快速下单、持仓管理、订单跟踪
- **主要端点**:
  - `/terminal/overview` - 交易终端概览
  - `/terminal/quick-order` - 快速下单
  - `/terminal/positions/detail` - 持仓明细
  - `/terminal/ws` - 实时推送交易信息

### 3. 订单管理模块 (orders)
- **路径前缀**: `/api/v1/orders`
- **功能**: 订单查询、修改、批量操作、导出等
- **主要端点**:
  - `/orders/list` - 订单列表（支持筛选、排序、分页）
  - `/orders/{order_id}/modify` - 修改订单
  - `/orders/batch-cancel` - 批量撤销
  - `/orders/export` - 导出订单数据

### 4. 策略开发模块 (strategy-dev)
- **路径前缀**: `/api/v1/strategy-dev`
- **功能**: 策略编辑器、回测、优化、实盘交易
- **主要端点**:
  - `/strategies/create` - 创建策略
  - `/strategies/{id}/backtest` - 运行回测
  - `/strategies/{id}/optimize` - 参数优化
  - `/strategies/{id}/start` - 启动实盘

## 快速开始

### 1. 环境准备

```bash
# 进入后端目录
cd /Users/<USER>/Desktop/quant011/backend

# 创建虚拟环境（如果未创建）
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 方式1: 使用集成脚本（推荐）
./run_and_test.sh

# 方式2: 手动启动
python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问服务

- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/v1/health
- 调试路由: http://localhost:8000/api/v1/debug/routes

## 测试说明

### 运行集成测试

```bash
cd app/api/v1
python3 test_integration.py
```

### 测试单个模块

```python
# 测试实时行情
curl http://localhost:8000/api/v1/market-v2/quotes/realtime?symbols=000001,600036

# 测试交易终端
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/terminal/terminal/overview

# 测试订单管理
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/orders/orders/list

# 测试策略开发
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/v1/strategy-dev/strategies/template/list
```

## 前端集成

### 1. 更新API配置

在前端配置文件中更新API端点：

```javascript
// src/config/api.js
export const API_ENDPOINTS = {
  // 实时行情
  MARKET_QUOTES: '/api/v1/market-v2/quotes/realtime',
  MARKET_KLINE: '/api/v1/market-v2/kline',
  MARKET_DEPTH: '/api/v1/market-v2/depth',
  MARKET_WS: 'ws://localhost:8000/api/v1/market-v2/ws/quotes',
  
  // 交易终端
  TERMINAL_OVERVIEW: '/api/v1/terminal/terminal/overview',
  QUICK_ORDER: '/api/v1/terminal/terminal/quick-order',
  POSITIONS: '/api/v1/terminal/terminal/positions/detail',
  
  // 订单管理
  ORDER_LIST: '/api/v1/orders/orders/list',
  ORDER_DETAIL: '/api/v1/orders/orders/{id}/detail',
  ORDER_MODIFY: '/api/v1/orders/orders/{id}/modify',
  
  // 策略开发
  STRATEGY_CREATE: '/api/v1/strategy-dev/strategies/create',
  STRATEGY_BACKTEST: '/api/v1/strategy-dev/strategies/{id}/backtest',
  STRATEGY_MONITOR: '/api/v1/strategy-dev/strategies/{id}/monitor'
};
```

### 2. WebSocket连接示例

```javascript
// 实时行情订阅
const ws = new WebSocket('ws://localhost:8000/api/v1/market-v2/ws/quotes');

ws.onopen = () => {
  // 订阅股票
  ws.send(JSON.stringify({
    action: 'subscribe',
    symbols: ['000001', '000002', '600036']
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'quote') {
    // 更新行情显示
    updateQuote(data.data);
  }
};
```

## 性能优化建议

1. **缓存策略**
   - 实时行情数据缓存1秒
   - K线数据缓存5分钟
   - 股票列表缓存1小时

2. **批量请求**
   - 使用批量接口获取多只股票行情
   - 订单操作支持批量处理

3. **WebSocket使用**
   - 实时数据使用WebSocket推送
   - 避免频繁轮询

## 监控和维护

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

### 性能监控

- 访问 `/api/v1/monitoring/metrics` 查看性能指标
- 使用 `/api/v1/monitoring/alerts` 配置告警

### 数据备份

```bash
# 备份数据库
pg_dump -U postgres quant_platform > backup_$(date +%Y%m%d).sql

# 备份策略代码
tar -czf strategies_backup_$(date +%Y%m%d).tar.gz strategies/
```

## 常见问题

### 1. 服务启动失败
- 检查端口8000是否被占用
- 确认数据库连接配置正确
- 查看日志文件获取详细错误信息

### 2. WebSocket连接断开
- 检查防火墙设置
- 确认WebSocket路径正确
- 查看服务器日志

### 3. 性能问题
- 调整数据库连接池大小
- 启用Redis缓存
- 使用生产模式部署

## 技术支持

如遇到问题，请提供以下信息：
1. 错误日志
2. API请求和响应
3. 系统环境信息

---

最后更新: 2024-12-31