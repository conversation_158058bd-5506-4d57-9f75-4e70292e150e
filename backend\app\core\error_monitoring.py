"""
错误监控和告警系统
提供实时错误监控、统计分析和告警功能
"""

import asyncio
import json
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Callable
from dataclasses import dataclass
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from app.core.logging_config import get_contextual_logger
from app.core.exceptions import BaseCustomException, ErrorSeverity, ErrorCategory


class AlertLevel(Enum):
    """告警级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorEvent:
    """错误事件"""
    id: str
    timestamp: datetime
    exception_type: str
    error_code: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    request_id: str
    user_id: Optional[str]
    path: str
    method: str
    client_ip: str
    details: Dict[str, Any]
    stack_trace: Optional[str] = None


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    description: str
    condition: Callable[[List[ErrorEvent]], bool]
    alert_level: AlertLevel
    cooldown_minutes: int = 5
    enabled: bool = True
    last_triggered: Optional[datetime] = None


@dataclass
class AlertMessage:
    """告警消息"""
    rule_name: str
    level: AlertLevel
    title: str
    message: str
    events: List[ErrorEvent]
    timestamp: datetime
    metadata: Dict[str, Any]


class ErrorMonitor:
    """错误监控器"""
    
    def __init__(self, max_events: int = 10000, cleanup_interval: int = 3600):
        self.logger = get_contextual_logger("error_monitor")
        self.max_events = max_events
        self.cleanup_interval = cleanup_interval
        
        # 错误事件存储
        self.error_events: deque[ErrorEvent] = deque(maxlen=max_events)
        self.error_counts = defaultdict(int)
        self.error_rates = defaultdict(lambda: deque(maxlen=100))  # 最近100个事件的时间戳
        
        # 告警规则和处理器
        self.alert_rules: List[AlertRule] = []
        self.alert_handlers: List[Callable[[AlertMessage], None]] = []
        
        # 统计数据
        self.stats = {
            "total_events": 0,
            "events_by_severity": defaultdict(int),
            "events_by_category": defaultdict(int),
            "events_by_type": defaultdict(int),
            "most_common_errors": [],
            "error_trends": {},
            "last_updated": datetime.utcnow()
        }
        
        # 启动后台任务
        self._cleanup_task = None
        self._monitoring_task = None
        
        # 初始化默认告警规则
        self._setup_default_alert_rules()
    
    def add_error_event(self, event: ErrorEvent) -> None:
        """添加错误事件"""
        self.error_events.append(event)
        self.error_counts[event.exception_type] += 1
        self.error_rates[event.exception_type].append(event.timestamp)
        
        # 更新统计
        self._update_statistics(event)
        
        # 检查告警规则
        asyncio.create_task(self._check_alert_rules(event))
        
        self.logger.debug("Error event recorded", 
                         event_id=event.id,
                         exception_type=event.exception_type,
                         severity=event.severity.value)
    
    def add_alert_rule(self, rule: AlertRule) -> None:
        """添加告警规则"""
        self.alert_rules.append(rule)
        self.logger.info("Alert rule added", 
                        rule_name=rule.name,
                        alert_level=rule.alert_level.value)
    
    def add_alert_handler(self, handler: Callable[[AlertMessage], None]) -> None:
        """添加告警处理器"""
        self.alert_handlers.append(handler)
        self.logger.info("Alert handler added")
    
    def get_recent_events(self, minutes: int = 60) -> List[ErrorEvent]:
        """获取最近的错误事件"""
        cutoff = datetime.utcnow() - timedelta(minutes=minutes)
        return [event for event in self.error_events if event.timestamp >= cutoff]
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        self._refresh_statistics()
        return dict(self.stats)
    
    def get_error_trends(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误趋势分析"""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        recent_events = [event for event in self.error_events if event.timestamp >= cutoff]
        
        # 按小时分组
        hourly_counts = defaultdict(int)
        hourly_severities = defaultdict(lambda: defaultdict(int))
        
        for event in recent_events:
            hour = event.timestamp.replace(minute=0, second=0, microsecond=0)
            hour_str = hour.isoformat()
            hourly_counts[hour_str] += 1
            hourly_severities[hour_str][event.severity.value] += 1
        
        return {
            "time_range": {
                "start": (datetime.utcnow() - timedelta(hours=hours)).isoformat(),
                "end": datetime.utcnow().isoformat(),
                "hours": hours
            },
            "total_events": len(recent_events),
            "hourly_counts": dict(hourly_counts),
            "hourly_severities": {k: dict(v) for k, v in hourly_severities.items()},
            "top_errors": self._get_top_errors(recent_events, limit=10)
        }
    
    async def start_monitoring(self) -> None:
        """启动监控任务"""
        if self._monitoring_task and not self._monitoring_task.done():
            self.logger.warning("Monitoring already started")
            return
        
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self.logger.info("Error monitoring started")
    
    async def stop_monitoring(self) -> None:
        """停止监控任务"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        self.logger.info("Error monitoring stopped")
    
    def _setup_default_alert_rules(self) -> None:
        """设置默认告警规则"""
        
        # 高频错误告警
        def high_frequency_errors(events: List[ErrorEvent]) -> bool:
            recent_events = [e for e in events if (datetime.utcnow() - e.timestamp).seconds < 300]  # 5分钟内
            return len(recent_events) > 50
        
        self.add_alert_rule(AlertRule(
            name="high_frequency_errors",
            description="5分钟内出现超过50个错误",
            condition=high_frequency_errors,
            alert_level=AlertLevel.HIGH,
            cooldown_minutes=5
        ))
        
        # 关键错误告警
        def critical_errors(events: List[ErrorEvent]) -> bool:
            recent_critical = [e for e in events 
                             if e.severity == ErrorSeverity.CRITICAL 
                             and (datetime.utcnow() - e.timestamp).seconds < 60]  # 1分钟内
            return len(recent_critical) > 0
        
        self.add_alert_rule(AlertRule(
            name="critical_errors",
            description="出现关键错误",
            condition=critical_errors,
            alert_level=AlertLevel.CRITICAL,
            cooldown_minutes=1
        ))
        
        # 错误率飙升告警
        def error_rate_spike(events: List[ErrorEvent]) -> bool:
            if len(events) < 100:
                return False
            
            recent_30min = [e for e in events[-100:] 
                          if (datetime.utcnow() - e.timestamp).seconds < 1800]  # 30分钟内
            older_30min = [e for e in events[-200:-100] 
                         if (datetime.utcnow() - e.timestamp).seconds < 3600]  # 30-60分钟前
            
            if len(older_30min) == 0:
                return False
            
            recent_rate = len(recent_30min) / 30
            older_rate = len(older_30min) / 30
            
            return recent_rate > older_rate * 3  # 错误率增长3倍
        
        self.add_alert_rule(AlertRule(
            name="error_rate_spike",
            description="错误率急剧上升",
            condition=error_rate_spike,
            alert_level=AlertLevel.MEDIUM,
            cooldown_minutes=10
        ))
        
        # 数据库错误告警
        def database_errors(events: List[ErrorEvent]) -> bool:
            recent_db_errors = [e for e in events 
                              if e.category == ErrorCategory.DATA_ACCESS 
                              and (datetime.utcnow() - e.timestamp).seconds < 300]  # 5分钟内
            return len(recent_db_errors) > 10
        
        self.add_alert_rule(AlertRule(
            name="database_errors",
            description="数据库错误频发",
            condition=database_errors,
            alert_level=AlertLevel.HIGH,
            cooldown_minutes=5
        ))
    
    def _update_statistics(self, event: ErrorEvent) -> None:
        """更新统计信息"""
        self.stats["total_events"] += 1
        self.stats["events_by_severity"][event.severity.value] += 1
        self.stats["events_by_category"][event.category.value] += 1
        self.stats["events_by_type"][event.exception_type] += 1
        self.stats["last_updated"] = datetime.utcnow()
    
    def _refresh_statistics(self) -> None:
        """刷新统计信息"""
        # 更新最常见错误
        error_counts = defaultdict(int)
        for event in self.error_events:
            error_counts[event.exception_type] += 1
        
        self.stats["most_common_errors"] = sorted(
            error_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # 更新错误趋势
        self.stats["error_trends"] = self._calculate_trends()
    
    def _calculate_trends(self) -> Dict[str, Any]:
        """计算错误趋势"""
        if len(self.error_events) < 10:
            return {}
        
        recent_hour = datetime.utcnow() - timedelta(hours=1)
        previous_hour = datetime.utcnow() - timedelta(hours=2)
        
        recent_count = len([e for e in self.error_events if e.timestamp >= recent_hour])
        previous_count = len([e for e in self.error_events 
                            if previous_hour <= e.timestamp < recent_hour])
        
        trend = "stable"
        if previous_count > 0:
            change = (recent_count - previous_count) / previous_count
            if change > 0.5:
                trend = "increasing"
            elif change < -0.5:
                trend = "decreasing"
        
        return {
            "recent_hour_count": recent_count,
            "previous_hour_count": previous_count,
            "trend": trend,
            "change_percentage": ((recent_count - previous_count) / max(previous_count, 1)) * 100
        }
    
    def _get_top_errors(self, events: List[ErrorEvent], limit: int = 10) -> List[Dict[str, Any]]:
        """获取最常见的错误"""
        error_counts = defaultdict(int)
        error_details = {}
        
        for event in events:
            error_counts[event.exception_type] += 1
            if event.exception_type not in error_details:
                error_details[event.exception_type] = {
                    "latest_message": event.message,
                    "severity": event.severity.value,
                    "category": event.category.value
                }
        
        return [
            {
                "exception_type": error_type,
                "count": count,
                **error_details[error_type]
            }
            for error_type, count in sorted(error_counts.items(), 
                                          key=lambda x: x[1], 
                                          reverse=True)[:limit]
        ]
    
    async def _check_alert_rules(self, new_event: ErrorEvent) -> None:
        """检查告警规则"""
        current_events = list(self.error_events)
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            if (rule.last_triggered and 
                datetime.utcnow() - rule.last_triggered < timedelta(minutes=rule.cooldown_minutes)):
                continue
            
            try:
                if rule.condition(current_events):
                    await self._trigger_alert(rule, current_events, new_event)
                    rule.last_triggered = datetime.utcnow()
            except Exception as e:
                self.logger.error("Error checking alert rule", 
                                rule_name=rule.name,
                                error=str(e),
                                exc_info=True)
    
    async def _trigger_alert(self, rule: AlertRule, events: List[ErrorEvent], trigger_event: ErrorEvent) -> None:
        """触发告警"""
        alert_message = AlertMessage(
            rule_name=rule.name,
            level=rule.alert_level,
            title=f"告警: {rule.description}",
            message=self._generate_alert_message(rule, events, trigger_event),
            events=events[-10:],  # 最近10个事件
            timestamp=datetime.utcnow(),
            metadata={
                "rule_description": rule.description,
                "trigger_event_id": trigger_event.id,
                "total_recent_events": len(events)
            }
        )
        
        self.logger.warning("Alert triggered", 
                          rule_name=rule.name,
                          alert_level=rule.alert_level.value,
                          trigger_event=trigger_event.exception_type)
        
        # 发送告警到所有处理器
        for handler in self.alert_handlers:
            try:
                await asyncio.create_task(self._call_alert_handler(handler, alert_message))
            except Exception as e:
                self.logger.error("Error calling alert handler", 
                                error=str(e), exc_info=True)
    
    async def _call_alert_handler(self, handler: Callable, alert: AlertMessage) -> None:
        """调用告警处理器"""
        if asyncio.iscoroutinefunction(handler):
            await handler(alert)
        else:
            handler(alert)
    
    def _generate_alert_message(self, rule: AlertRule, events: List[ErrorEvent], trigger_event: ErrorEvent) -> str:
        """生成告警消息"""
        recent_events = [e for e in events if (datetime.utcnow() - e.timestamp).seconds < 300]
        
        message = f"""
错误监控告警

告警规则: {rule.name}
告警级别: {rule.alert_level.value.upper()}
触发时间: {datetime.utcnow().isoformat()}

触发事件:
- 类型: {trigger_event.exception_type}
- 消息: {trigger_event.message}
- 路径: {trigger_event.path}
- 用户: {trigger_event.user_id or 'Anonymous'}

最近5分钟统计:
- 总错误数: {len(recent_events)}
- 严重错误: {len([e for e in recent_events if e.severity == ErrorSeverity.HIGH])}
- 关键错误: {len([e for e in recent_events if e.severity == ErrorSeverity.CRITICAL])}

最常见错误类型:
"""
        
        top_errors = self._get_top_errors(recent_events, limit=5)
        for i, error in enumerate(top_errors, 1):
            message += f"{i}. {error['exception_type']}: {error['count']}次\n"
        
        return message
    
    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                self._refresh_statistics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in monitoring loop", error=str(e), exc_info=True)
    
    async def _cleanup_loop(self) -> None:
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                self._cleanup_old_events()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in cleanup loop", error=str(e), exc_info=True)
    
    def _cleanup_old_events(self) -> None:
        """清理旧事件"""
        cutoff = datetime.utcnow() - timedelta(hours=24)
        initial_count = len(self.error_events)
        
        # deque 会自动保持最大长度，这里主要是清理速率统计
        for error_type in list(self.error_rates.keys()):
            self.error_rates[error_type] = deque(
                [ts for ts in self.error_rates[error_type] if ts >= cutoff],
                maxlen=100
            )
        
        self.logger.debug("Cleaned up old error events", 
                         initial_count=initial_count,
                         current_count=len(self.error_events))


# 全局错误监控实例
_error_monitor: Optional[ErrorMonitor] = None


def get_error_monitor() -> ErrorMonitor:
    """获取全局错误监控实例"""
    global _error_monitor
    if _error_monitor is None:
        _error_monitor = ErrorMonitor()
    return _error_monitor


def setup_error_monitoring() -> ErrorMonitor:
    """设置错误监控"""
    monitor = get_error_monitor()
    
    # 添加默认告警处理器
    monitor.add_alert_handler(log_alert_handler)
    
    return monitor


# 默认告警处理器
async def log_alert_handler(alert: AlertMessage) -> None:
    """日志告警处理器"""
    logger = get_contextual_logger("alert_handler")
    
    log_level = {
        AlertLevel.LOW: "info",
        AlertLevel.MEDIUM: "warning", 
        AlertLevel.HIGH: "error",
        AlertLevel.CRITICAL: "critical"
    }.get(alert.level, "warning")
    
    getattr(logger, log_level)(
        alert.title,
        rule_name=alert.rule_name,
        alert_level=alert.level.value,
        message=alert.message,
        event_count=len(alert.events),
        metadata=alert.metadata
    )


# 邮件告警处理器（示例）
class EmailAlertHandler:
    """邮件告警处理器"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str, recipients: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.recipients = recipients
        self.logger = get_contextual_logger("email_alert")
    
    async def __call__(self, alert: AlertMessage) -> None:
        """发送邮件告警"""
        if alert.level in [AlertLevel.HIGH, AlertLevel.CRITICAL]:
            try:
                await self._send_email(alert)
            except Exception as e:
                self.logger.error("Failed to send email alert", error=str(e))
    
    async def _send_email(self, alert: AlertMessage) -> None:
        """发送邮件"""
        msg = MIMEMultipart()
        msg['From'] = self.username
        msg['To'] = ', '.join(self.recipients)
        msg['Subject'] = f"[{alert.level.value.upper()}] {alert.title}"
        
        body = alert.message
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # 异步发送邮件
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._send_smtp, msg)
    
    def _send_smtp(self, msg: MIMEMultipart) -> None:
        """SMTP发送"""
        with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)