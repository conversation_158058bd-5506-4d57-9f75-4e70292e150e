#!/usr/bin/env python3
"""
最终综合分析 - 量化交易平台完整功能测试
基于实际的滑动验证实现
"""

import asyncio
import logging
import time
import json
from datetime import datetime
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalPlatformAnalyzer:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.api_url = "http://localhost:8000"
        self.browser = None
        self.page = None
        self.analysis_results = {
            "platform_overview": {},
            "authentication_system": {},
            "puzzle_verification": {},
            "backend_services": {},
            "performance_metrics": {},
            "security_assessment": {},
            "user_experience": {},
            "technical_implementation": {},
            "issues_found": [],
            "recommendations": []
        }
        
    async def setup_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        # 设置监听器
        self.page.on("console", self._handle_console)
        self.page.on("pageerror", self._handle_page_error)
        self.page.on("requestfailed", self._handle_request_failed)
        
        logger.info("🚀 浏览器初始化完成")

    def _handle_console(self, msg):
        if msg.type == "error":
            self.analysis_results["issues_found"].append({
                "type": "console_error",
                "message": msg.text,
                "timestamp": datetime.now().isoformat()
            })

    def _handle_page_error(self, error):
        self.analysis_results["issues_found"].append({
            "type": "page_error", 
            "message": str(error),
            "timestamp": datetime.now().isoformat()
        })

    def _handle_request_failed(self, request):
        self.analysis_results["issues_found"].append({
            "type": "request_failed",
            "url": request.url,
            "method": request.method,
            "timestamp": datetime.now().isoformat()
        })

    async def analyze_platform_overview(self):
        """分析平台概览"""
        logger.info("🔍 分析平台概览...")
        
        try:
            # 访问主页
            start_time = time.time()
            await self.page.goto(self.base_url)
            await self.page.wait_for_load_state('networkidle')
            load_time = time.time() - start_time
            
            title = await self.page.title()
            url = self.page.url
            
            self.analysis_results["platform_overview"] = {
                "title": title,
                "main_url": url,
                "load_time": round(load_time, 2),
                "accessible": True
            }
            
            logger.info(f"✅ 平台主页可访问 - 标题: {title}, 加载时间: {load_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"❌ 平台概览分析失败: {e}")
            self.analysis_results["platform_overview"]["error"] = str(e)

    async def test_authentication_system(self):
        """测试认证系统"""
        logger.info("🔍 测试认证系统...")
        
        try:
            # 访问登录页面
            await self.page.goto(f"{self.base_url}/login")
            await self.page.wait_for_load_state('networkidle')
            
            # 清除存储
            await self.page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
            await self.page.reload()
            await self.page.wait_for_load_state('networkidle')
            
            # 检查登录页面元素
            form = await self.page.query_selector('form')
            username_input = await self.page.query_selector('input[type="text"], input[placeholder*="用户名"], input[placeholder*="邮箱"]')
            password_input = await self.page.query_selector('input[type="password"]')
            login_btn = await self.page.query_selector('button:has-text("登录")')
            demo_btn = await self.page.query_selector('button:has-text("演示登录")')
            
            auth_elements = {
                "login_form": form is not None,
                "username_input": username_input is not None,
                "password_input": password_input is not None,
                "login_button": login_btn is not None,
                "demo_login_button": demo_btn is not None
            }
            
            logger.info("✅ 登录页面元素检查完成:")
            for element, exists in auth_elements.items():
                logger.info(f"   - {element}: {'存在' if exists else '缺失'}")
            
            # 测试演示登录
            if demo_btn:
                await demo_btn.click()
                await asyncio.sleep(3)
                
                current_url = self.page.url
                login_success = 'puzzle-verify' in current_url
                
                self.analysis_results["authentication_system"] = {
                    "elements": auth_elements,
                    "demo_login_success": login_success,
                    "redirect_url": current_url
                }
                
                if login_success:
                    logger.info("✅ 演示登录成功，跳转到拼图验证页面")
                    return True
                else:
                    logger.error("❌ 演示登录失败")
                    return False
            else:
                logger.error("❌ 未找到演示登录按钮")
                return False
                
        except Exception as e:
            logger.error(f"❌ 认证系统测试失败: {e}")
            self.analysis_results["authentication_system"]["error"] = str(e)
            return False

    async def analyze_puzzle_verification(self):
        """分析拼图验证系统"""
        logger.info("🔍 分析拼图验证系统...")
        
        try:
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 检查页面元素
            title = await self.page.title()
            url = self.page.url
            
            # 检查Canvas元素
            canvases = await self.page.query_selector_all('canvas')
            puzzle_canvas = await self.page.query_selector('.puzzle-canvas')
            block_canvas = await self.page.query_selector('.puzzle-block')
            
            # 检查滑动控制元素
            slider_track = await self.page.query_selector('.slider-track')
            slider_btn = await self.page.query_selector('.slider-btn')
            slider_text = await self.page.query_selector('.slider-text')
            
            # 检查按钮
            continue_btn = await self.page.query_selector('button:has-text("继续访问")')
            back_btn = await self.page.query_selector('button:has-text("返回登录")')
            refresh_btn = await self.page.query_selector('.refresh-btn')
            
            puzzle_elements = {
                "page_title": title,
                "page_url": url,
                "canvas_count": len(canvases),
                "puzzle_canvas": puzzle_canvas is not None,
                "block_canvas": block_canvas is not None,
                "slider_track": slider_track is not None,
                "slider_button": slider_btn is not None,
                "slider_text": slider_text is not None,
                "continue_button": continue_btn is not None,
                "back_button": back_btn is not None,
                "refresh_button": refresh_btn is not None
            }
            
            logger.info("✅ 拼图验证页面元素检查:")
            for element, exists in puzzle_elements.items():
                if isinstance(exists, bool):
                    logger.info(f"   - {element}: {'存在' if exists else '缺失'}")
                else:
                    logger.info(f"   - {element}: {exists}")
            
            # 测试滑动验证
            if slider_btn:
                await self.test_slider_verification()
            
            self.analysis_results["puzzle_verification"] = puzzle_elements
            
        except Exception as e:
            logger.error(f"❌ 拼图验证分析失败: {e}")
            self.analysis_results["puzzle_verification"]["error"] = str(e)

    async def test_slider_verification(self):
        """测试滑动验证功能"""
        logger.info("🔍 测试滑动验证功能...")
        
        try:
            slider_btn = await self.page.query_selector('.slider-btn')
            if not slider_btn:
                logger.error("❌ 未找到滑动按钮")
                return
            
            # 获取滑动按钮和轨道的位置信息
            btn_box = await slider_btn.bounding_box()
            track = await self.page.query_selector('.slider-track')
            track_box = await track.bounding_box()
            
            if btn_box and track_box:
                # 计算滑动距离
                start_x = btn_box['x'] + btn_box['width'] / 2
                start_y = btn_box['y'] + btn_box['height'] / 2
                end_x = track_box['x'] + track_box['width'] - btn_box['width'] / 2 - 10
                end_y = start_y
                
                logger.info(f"   - 开始滑动: ({start_x:.1f}, {start_y:.1f}) -> ({end_x:.1f}, {end_y:.1f})")
                
                # 执行滑动操作
                await self.page.mouse.move(start_x, start_y)
                await self.page.mouse.down()
                
                # 分步滑动，模拟真实用户行为
                steps = 20
                for i in range(steps + 1):
                    progress = i / steps
                    current_x = start_x + (end_x - start_x) * progress
                    await self.page.mouse.move(current_x, end_y)
                    await asyncio.sleep(0.05)  # 每步间隔50ms
                
                await self.page.mouse.up()
                
                # 等待验证结果
                await asyncio.sleep(2)
                
                # 检查验证结果
                success_indicators = [
                    '.slider-btn-success',
                    '.result-message.success',
                    'text="验证成功"',
                    'text="验证通过"'
                ]
                
                verification_success = False
                for indicator in success_indicators:
                    element = await self.page.query_selector(indicator)
                    if element:
                        verification_success = True
                        logger.info(f"✅ 验证成功 - 发现成功指示器: {indicator}")
                        break
                
                # 检查继续按钮是否可用
                continue_btn = await self.page.query_selector('button:has-text("继续访问")')
                if continue_btn:
                    is_disabled = await continue_btn.get_attribute('disabled')
                    btn_enabled = is_disabled is None
                    logger.info(f"   - 继续按钮状态: {'可用' if btn_enabled else '禁用'}")
                    
                    if btn_enabled and verification_success:
                        # 测试继续访问功能
                        await continue_btn.click()
                        await asyncio.sleep(3)
                        
                        new_url = self.page.url
                        if new_url != "http://localhost:5173/puzzle-verify":
                            logger.info(f"✅ 继续访问成功，跳转到: {new_url}")
                            self.analysis_results["puzzle_verification"]["continue_access_success"] = True
                            self.analysis_results["puzzle_verification"]["final_url"] = new_url
                        else:
                            logger.warning("⚠️ 点击继续访问但未发生跳转")
                            self.analysis_results["puzzle_verification"]["continue_access_success"] = False
                
                self.analysis_results["puzzle_verification"]["slider_verification_success"] = verification_success
                
            else:
                logger.error("❌ 无法获取滑动元素位置信息")
                
        except Exception as e:
            logger.error(f"❌ 滑动验证测试失败: {e}")

    async def test_backend_services(self):
        """测试后端服务"""
        logger.info("🔍 测试后端服务...")
        
        try:
            import aiohttp
            
            backend_tests = {}
            
            async with aiohttp.ClientSession() as session:
                # 测试健康检查
                try:
                    async with session.get(f"{self.api_url}/", timeout=5) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            backend_tests["health_check"] = {"status": "success", "data": data}
                            logger.info("✅ 后端健康检查通过")
                        else:
                            backend_tests["health_check"] = {"status": "failed", "code": resp.status}
                except Exception as e:
                    backend_tests["health_check"] = {"status": "error", "message": str(e)}
                
                # 测试登录API
                try:
                    login_data = {"username": "admin", "password": "admin123"}
                    async with session.post(f"{self.api_url}/api/v1/auth/login", json=login_data, timeout=5) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            backend_tests["login_api"] = {"status": "success", "has_token": "token" in data}
                            logger.info("✅ 登录API测试通过")
                        else:
                            backend_tests["login_api"] = {"status": "failed", "code": resp.status}
                except Exception as e:
                    backend_tests["login_api"] = {"status": "error", "message": str(e)}
                
                # 测试其他API端点
                api_endpoints = [
                    "/api/v1/auth/register",
                    "/api/v1/user/profile", 
                    "/api/v1/trading/accounts"
                ]
                
                for endpoint in api_endpoints:
                    try:
                        async with session.get(f"{self.api_url}{endpoint}", timeout=3) as resp:
                            backend_tests[f"endpoint_{endpoint.split('/')[-1]}"] = {
                                "status": "accessible" if resp.status < 500 else "error",
                                "code": resp.status
                            }
                    except Exception as e:
                        backend_tests[f"endpoint_{endpoint.split('/')[-1]}"] = {
                            "status": "error", 
                            "message": str(e)
                        }
            
            self.analysis_results["backend_services"] = backend_tests
            
            # 统计后端服务状态
            success_count = sum(1 for test in backend_tests.values() if test.get("status") == "success")
            total_count = len(backend_tests)
            logger.info(f"✅ 后端服务测试完成: {success_count}/{total_count} 通过")
            
        except Exception as e:
            logger.error(f"❌ 后端服务测试失败: {e}")
            self.analysis_results["backend_services"]["error"] = str(e)

    async def assess_performance_metrics(self):
        """评估性能指标"""
        logger.info("🔍 评估性能指标...")
        
        try:
            # 页面性能指标
            performance_metrics = await self.page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const memory = performance.memory || {};
                    
                    return {
                        page_load_time: navigation ? navigation.loadEventEnd - navigation.fetchStart : 0,
                        dom_content_loaded: navigation ? navigation.domContentLoadedEventEnd - navigation.fetchStart : 0,
                        memory_used: memory.usedJSHeapSize || 0,
                        memory_total: memory.totalJSHeapSize || 0,
                        memory_limit: memory.jsHeapSizeLimit || 0
                    };
                }
            """)
            
            # 网络请求统计
            network_stats = await self.page.evaluate("""
                () => {
                    const resources = performance.getEntriesByType('resource');
                    return {
                        total_requests: resources.length,
                        avg_response_time: resources.length > 0 ? 
                            resources.reduce((sum, r) => sum + r.duration, 0) / resources.length : 0
                    };
                }
            """)
            
            performance_metrics.update(network_stats)
            
            self.analysis_results["performance_metrics"] = performance_metrics
            
            logger.info("✅ 性能指标评估完成:")
            logger.info(f"   - 页面加载时间: {performance_metrics.get('page_load_time', 0):.2f}ms")
            logger.info(f"   - DOM加载时间: {performance_metrics.get('dom_content_loaded', 0):.2f}ms")
            logger.info(f"   - 内存使用: {performance_metrics.get('memory_used', 0) / 1024 / 1024:.2f}MB")
            logger.info(f"   - 网络请求数: {performance_metrics.get('total_requests', 0)}")
            
        except Exception as e:
            logger.error(f"❌ 性能指标评估失败: {e}")
            self.analysis_results["performance_metrics"]["error"] = str(e)

    async def generate_final_report(self):
        """生成最终报告"""
        logger.info("📊 生成最终综合报告...")
        
        # 计算总体评分
        total_score = 0
        max_score = 0
        
        # 认证系统评分
        auth_score = 0
        if self.analysis_results["authentication_system"].get("demo_login_success"):
            auth_score += 30
        if self.analysis_results["authentication_system"].get("elements", {}).get("demo_login_button"):
            auth_score += 10
        total_score += auth_score
        max_score += 40
        
        # 拼图验证评分
        puzzle_score = 0
        if self.analysis_results["puzzle_verification"].get("slider_verification_success"):
            puzzle_score += 25
        if self.analysis_results["puzzle_verification"].get("continue_access_success"):
            puzzle_score += 15
        total_score += puzzle_score
        max_score += 40
        
        # 后端服务评分
        backend_score = 0
        backend_tests = self.analysis_results["backend_services"]
        if isinstance(backend_tests, dict):
            success_tests = [t for t in backend_tests.values() if isinstance(t, dict) and t.get("status") == "success"]
            backend_score = len(success_tests) * 5
        total_score += backend_score
        max_score += 20
        
        overall_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        # 生成建议
        recommendations = []
        
        if not self.analysis_results["puzzle_verification"].get("slider_verification_success"):
            recommendations.append("优化拼图验证算法，提高验证成功率")
        
        if not self.analysis_results["puzzle_verification"].get("continue_access_success"):
            recommendations.append("检查继续访问功能的路由配置")
        
        if len(self.analysis_results["issues_found"]) > 0:
            recommendations.append("修复发现的JavaScript错误和网络请求问题")
        
        performance = self.analysis_results.get("performance_metrics", {})
        if performance.get("page_load_time", 0) > 3000:
            recommendations.append("优化页面加载性能，减少加载时间")
        
        if not recommendations:
            recommendations.append("平台功能完整，建议进行更深入的安全测试和性能优化")
        
        self.analysis_results["recommendations"] = recommendations
        
        # 输出最终报告
        logger.info("=" * 80)
        logger.info("📋 量化交易平台深度分析报告")
        logger.info("=" * 80)
        logger.info(f"🎯 总体评分: {overall_score:.1f}/100")
        logger.info("")
        
        logger.info("🏗️  平台概览:")
        overview = self.analysis_results["platform_overview"]
        logger.info(f"   - 平台标题: {overview.get('title', 'N/A')}")
        logger.info(f"   - 主页加载时间: {overview.get('load_time', 0):.2f}秒")
        logger.info(f"   - 可访问性: {'✅ 正常' if overview.get('accessible') else '❌ 异常'}")
        logger.info("")
        
        logger.info("🔐 认证系统:")
        auth = self.analysis_results["authentication_system"]
        logger.info(f"   - 演示登录: {'✅ 成功' if auth.get('demo_login_success') else '❌ 失败'}")
        logger.info(f"   - 页面跳转: {'✅ 正常' if 'puzzle-verify' in auth.get('redirect_url', '') else '❌ 异常'}")
        logger.info("")
        
        logger.info("🧩 拼图验证:")
        puzzle = self.analysis_results["puzzle_verification"]
        logger.info(f"   - Canvas元素: {puzzle.get('canvas_count', 0)}个")
        logger.info(f"   - 滑动验证: {'✅ 成功' if puzzle.get('slider_verification_success') else '❌ 失败'}")
        logger.info(f"   - 继续访问: {'✅ 正常' if puzzle.get('continue_access_success') else '❌ 异常'}")
        logger.info("")
        
        logger.info("🔧 后端服务:")
        backend = self.analysis_results["backend_services"]
        if isinstance(backend, dict):
            for service, result in backend.items():
                if isinstance(result, dict):
                    status = result.get("status", "unknown")
                    icon = "✅" if status == "success" else "⚠️" if status == "accessible" else "❌"
                    logger.info(f"   - {service}: {icon} {status}")
        logger.info("")
        
        logger.info("⚡ 性能指标:")
        perf = self.analysis_results["performance_metrics"]
        logger.info(f"   - 页面加载: {perf.get('page_load_time', 0):.0f}ms")
        logger.info(f"   - 内存使用: {perf.get('memory_used', 0) / 1024 / 1024:.1f}MB")
        logger.info(f"   - 网络请求: {perf.get('total_requests', 0)}个")
        logger.info("")
        
        if self.analysis_results["issues_found"]:
            logger.info("⚠️  发现的问题:")
            for issue in self.analysis_results["issues_found"][:5]:  # 只显示前5个
                logger.info(f"   - {issue['type']}: {issue.get('message', issue.get('url', 'N/A'))}")
            if len(self.analysis_results["issues_found"]) > 5:
                logger.info(f"   - ... 还有 {len(self.analysis_results['issues_found']) - 5} 个问题")
            logger.info("")
        
        logger.info("💡 改进建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
        logger.info("")
        
        if overall_score >= 80:
            logger.info("🎉 平台功能完整，运行状态良好！")
        elif overall_score >= 60:
            logger.info("✅ 平台基本功能正常，有一些改进空间")
        else:
            logger.info("⚠️ 平台存在一些问题，建议优先修复")
        
        # 保存详细报告
        report_data = {
            "analysis_timestamp": datetime.now().isoformat(),
            "overall_score": overall_score,
            "detailed_results": self.analysis_results
        }
        
        report_file = f"platform_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 详细报告已保存: {report_file}")
        
        return report_data

    async def run_comprehensive_analysis(self):
        """运行全面分析"""
        logger.info("🚀 开始量化交易平台最终深度分析...")
        logger.info("=" * 80)
        
        try:
            await self.setup_browser()
            
            # 1. 平台概览分析
            await self.analyze_platform_overview()
            
            # 2. 认证系统测试
            auth_success = await self.test_authentication_system()
            
            # 3. 拼图验证分析（如果认证成功）
            if auth_success:
                await self.analyze_puzzle_verification()
            
            # 4. 后端服务测试
            await self.test_backend_services()
            
            # 5. 性能指标评估
            await self.assess_performance_metrics()
            
            # 6. 生成最终报告
            await self.generate_final_report()
            
        except Exception as e:
            logger.error(f"❌ 分析过程中发生严重错误: {e}")
        finally:
            if self.browser:
                await self.browser.close()
                logger.info("🔚 浏览器已关闭")

async def main():
    analyzer = FinalPlatformAnalyzer()
    await analyzer.run_comprehensive_analysis()

if __name__ == "__main__":
    asyncio.run(main())
