#!/usr/bin/env python3

"""
简单的 MCP Server 测试脚本
测试 Puppeteer MCP Server 的基本功能
"""

import asyncio
import json
import sys
from mcp.types import Tool

# 导入我们的 puppeteer server
from puppeteer import app

async def test_list_tools():
    """测试工具列表功能"""
    print("🔧 测试工具列表...")
    try:
        # 手动创建工具列表，因为这是装饰器定义的
        from puppeteer import list_tools
        tools = await list_tools()
        print(f"✅ 成功获取 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        return True
    except Exception as e:
        print(f"❌ 工具列表测试失败: {e}")
        return False

async def test_server_info():
    """测试服务器基本信息"""
    print("\n📋 测试服务器信息...")
    try:
        # 检查服务器是否正确初始化
        print(f"✅ 服务器名称: {app.name}")
        print(f"✅ 服务器类型: {type(app).__name__}")
        return True
    except Exception as e:
        print(f"❌ 服务器信息测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 Puppeteer MCP Server...")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_server_info,
        test_list_tools,
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    passed = sum(results)
    total = len(results)
    print(f"✅ 通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Puppeteer MCP Server 基本功能正常。")
        print("\n📝 可用工具:")
        print("  - puppeteer_navigate: 导航到指定URL")
        print("  - puppeteer_screenshot: 截取页面或元素截图")
        print("  - puppeteer_click: 点击页面元素")
        print("  - puppeteer_fill: 填写表单字段")
        print("  - puppeteer_evaluate: 执行JavaScript代码")
        print("\n⚠️  注意: 需要完成 Playwright 浏览器下载才能使用完整功能")
    else:
        print("❌ 部分测试失败，请检查配置。")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)
