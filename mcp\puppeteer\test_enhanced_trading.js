const puppeteer = require('puppeteer');

async function testEnhancedTradingInterface() {
    console.log('🚀 测试增强版模拟交易界面...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 },
        args: ['--start-maximized']
    });
    
    try {
        const page = await browser.newPage();
        await page.goto('http://localhost:5175/trading/simulated', {
            waitUntil: 'networkidle0',
            timeout: 30000 
        });
        
        console.log('📄 页面加载完成');
        
        // 等待Vue组件渲染
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查页面基本结构
        const pageStructure = await page.evaluate(() => {
            return {
                title: document.title,
                hasHeader: !!document.querySelector('.trading-header'),
                hasSimulationBadge: !!document.querySelector('.simulation-badge'),
                hasSearchInput: !!document.querySelector('.search-input'),
                hasAccountStats: !!document.querySelector('.account-stats'),
                hasMarketDisplay: !!document.querySelector('.market-display-area'),
                hasTradingOperation: !!document.querySelector('.trading-operation-area'),
                hasBottomTabs: !!document.querySelector('.bottom-panel'),
                
                // 检查具体元素
                simulationBadgeText: document.querySelector('.badge-text')?.textContent || '',
                searchPlaceholder: document.querySelector('.search-input input')?.placeholder || '',
                accountItems: Array.from(document.querySelectorAll('.stat-item')).map(item => ({
                    label: item.querySelector('.stat-label')?.textContent || '',
                    value: item.querySelector('.stat-value')?.textContent || ''
                })),
                
                // 检查交易按钮
                tradingButtons: Array.from(document.querySelectorAll('.trade-button')).map(btn => btn.textContent?.trim() || ''),
                
                // 检查是否有错误
                hasErrors: !!document.querySelector('.error, .el-message--error'),
                errorMessages: Array.from(document.querySelectorAll('.error, .el-message--error')).map(el => el.textContent)
            };
        });
        
        console.log('\n📊 增强版模拟交易界面检查结果:');
        console.log('============================================================');
        console.log(`📄 页面标题: ${pageStructure.title}`);
        console.log(`🎯 模拟标识: ${pageStructure.hasSimulationBadge ? '✅' : '❌'} (${pageStructure.simulationBadgeText})`);
        console.log(`🔍 智能搜索: ${pageStructure.hasSearchInput ? '✅' : '❌'} (${pageStructure.searchPlaceholder})`);
        console.log(`💰 账户统计: ${pageStructure.hasAccountStats ? '✅' : '❌'}`);
        console.log(`📈 行情展示: ${pageStructure.hasMarketDisplay ? '✅' : '❌'}`);
        console.log(`💼 交易操作: ${pageStructure.hasTradingOperation ? '✅' : '❌'}`);
        console.log(`📑 底部标签: ${pageStructure.hasBottomTabs ? '✅' : '❌'}`);
        
        if (pageStructure.accountItems.length > 0) {
            console.log('\n💰 账户信息:');
            pageStructure.accountItems.forEach(item => {
                console.log(`   ${item.label}: ${item.value}`);
            });
        }
        
        if (pageStructure.tradingButtons.length > 0) {
            console.log('\n🔄 交易按钮:');
            pageStructure.tradingButtons.forEach(btn => {
                console.log(`   - ${btn}`);
            });
        }
        
        if (pageStructure.hasErrors) {
            console.log('\n❌ 发现错误:');
            pageStructure.errorMessages.forEach(msg => {
                console.log(`   - ${msg}`);
            });
        }
        
        console.log('============================================================');
        
        // 测试搜索功能
        console.log('\n🔍 测试智能搜索功能...');
        try {
            // 尝试不同的选择器
            const searchSelectors = [
                '.search-input input',
                '.el-input__inner',
                'input[placeholder*="股票代码"]',
                '.smart-search input'
            ];
            
            let searchInput = null;
            for (const selector of searchSelectors) {
                try {
                    searchInput = await page.$(selector);
                    if (searchInput) {
                        console.log(`✅ 找到搜索框: ${selector}`);
                        break;
                    }
                } catch (e) {
                    // 继续尝试下一个选择器
                }
            }
            
            if (searchInput) {
                await searchInput.type('PAYH');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const hasDropdown = await page.$('.search-dropdown');
                console.log(`🔍 搜索下拉框: ${hasDropdown ? '✅' : '❌'}`);
                
                if (hasDropdown) {
                    const searchResults = await page.$$('.search-item');
                    console.log(`📋 搜索结果数量: ${searchResults.length}`);
                }
            } else {
                console.log('❌ 未找到搜索输入框');
            }
            
        } catch (error) {
            console.log(`❌ 搜索测试失败: ${error.message}`);
        }
        
        // 截图保存
        await page.screenshot({ 
            path: 'enhanced_trading_interface.png', 
            fullPage: true 
        });
        console.log('\n📸 界面截图已保存: enhanced_trading_interface.png');
        
        // 计算界面完整度评分
        let score = 0;
        const checks = [
            pageStructure.hasHeader,
            pageStructure.hasSimulationBadge,
            pageStructure.hasSearchInput,
            pageStructure.hasAccountStats,
            pageStructure.hasMarketDisplay,
            pageStructure.hasTradingOperation,
            pageStructure.hasBottomTabs,
            pageStructure.accountItems.length >= 3,
            pageStructure.tradingButtons.length >= 2,
            !pageStructure.hasErrors
        ];
        
        score = checks.filter(check => check).length;
        const maxScore = checks.length;
        const percentage = Math.round((score / maxScore) * 100);
        
        console.log(`\n🎯 界面完整度评分: ${score}/${maxScore} (${percentage}%)`);
        
        if (percentage >= 80) {
            console.log('🎉 界面开发状态: 优秀');
        } else if (percentage >= 60) {
            console.log('👍 界面开发状态: 良好');
        } else {
            console.log('⚠️ 界面开发状态: 需要改进');
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error.message);
    } finally {
        await browser.close();
    }
}

// 运行测试
testEnhancedTradingInterface().catch(console.error);
