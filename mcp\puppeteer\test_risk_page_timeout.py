#!/usr/bin/env python3
"""
测试风控页面超时问题
"""

import asyncio
import logging
from playwright.async_api import async_playwright
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_risk_page_timeout():
    """测试风控页面加载和超时问题"""
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    try:
        # 监听网络请求
        requests = []
        responses = []
        
        def handle_request(request):
            requests.append({
                'url': request.url,
                'method': request.method,
                'timestamp': time.time()
            })
            logger.info(f"📤 请求: {request.method} {request.url}")
        
        def handle_response(response):
            responses.append({
                'url': response.url,
                'status': response.status,
                'timestamp': time.time()
            })
            if response.status >= 400:
                logger.error(f"❌ 响应错误: {response.status} {response.url}")
            else:
                logger.info(f"✅ 响应成功: {response.status} {response.url}")
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        # 监听控制台错误
        def handle_console(msg):
            if msg.type == 'error':
                logger.error(f"🔴 控制台错误: {msg.text}")
            elif 'timeout' in msg.text.lower() or 'error' in msg.text.lower():
                logger.warning(f"⚠️ 控制台警告: {msg.text}")
        
        page.on('console', handle_console)
        
        # 1. 先登录
        logger.info("🚀 开始测试风控页面超时问题...")
        await page.goto("http://localhost:5173/login")
        await page.wait_for_load_state('networkidle')
        
        # 清除存储
        await page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
        await page.reload()
        await page.wait_for_load_state('networkidle')
        
        # 点击演示登录
        demo_btn = await page.query_selector('button:has-text("演示登录")')
        if demo_btn:
            await demo_btn.click()
            await asyncio.sleep(3)
            
            if 'puzzle-verify' in page.url:
                logger.info("✅ 成功跳转到拼图验证页面")
                
                # 快速完成拼图验证
                slider_btn = await page.query_selector('.slider-btn')
                track = await page.query_selector('.slider-track')
                
                if slider_btn and track:
                    btn_box = await slider_btn.bounding_box()
                    track_box = await track.bounding_box()
                    
                    if btn_box and track_box:
                        start_x = btn_box['x'] + btn_box['width'] / 2
                        start_y = btn_box['y'] + btn_box['height'] / 2
                        end_x = track_box['x'] + track_box['width'] * 0.7
                        
                        await page.mouse.move(start_x, start_y)
                        await page.mouse.down()
                        await page.mouse.move(end_x, start_y)
                        await page.mouse.up()
                        await asyncio.sleep(2)
                        
                        # 点击继续访问
                        continue_btn = await page.query_selector('button:has-text("继续访问")')
                        if continue_btn:
                            await continue_btn.click()
                            await asyncio.sleep(2)
        
        # 2. 导航到风控页面
        logger.info("\n🎯 导航到风控页面...")
        start_time = time.time()
        
        try:
            await page.goto("http://localhost:5173/risk", timeout=30000)  # 30秒超时
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            load_time = time.time() - start_time
            logger.info(f"✅ 风控页面加载成功，耗时: {load_time:.2f}秒")
            
        except Exception as e:
            load_time = time.time() - start_time
            logger.error(f"❌ 风控页面加载失败，耗时: {load_time:.2f}秒，错误: {e}")
            
            # 截图保存错误状态
            await page.screenshot(path="risk_page_timeout_error.png")
            logger.info("📸 错误截图已保存: risk_page_timeout_error.png")
        
        # 3. 检查页面元素加载情况
        logger.info("\n🔍 检查页面元素加载情况...")
        
        # 检查主要元素
        elements_to_check = [
            ('.page-header', '页面头部'),
            ('.risk-overview', '风险概览'),
            ('.content-card', '内容卡片'),
            ('.risk-metrics-table', '风险指标表格'),
            ('.chart-container', '图表容器')
        ]
        
        for selector, name in elements_to_check:
            try:
                element = await page.query_selector(selector)
                if element:
                    logger.info(f"✅ {name} 加载成功")
                else:
                    logger.warning(f"⚠️ {name} 未找到")
            except Exception as e:
                logger.error(f"❌ 检查 {name} 时出错: {e}")
        
        # 4. 分析网络请求
        logger.info("\n📊 分析网络请求...")
        
        # 统计请求
        api_requests = [req for req in requests if '/api/' in req['url']]
        failed_responses = [resp for resp in responses if resp['status'] >= 400]
        slow_requests = []
        
        # 计算请求响应时间
        for req in requests:
            matching_resp = next((resp for resp in responses if resp['url'] == req['url']), None)
            if matching_resp:
                response_time = matching_resp['timestamp'] - req['timestamp']
                if response_time > 5:  # 超过5秒的请求
                    slow_requests.append({
                        'url': req['url'],
                        'time': response_time
                    })
        
        logger.info(f"   总请求数: {len(requests)}")
        logger.info(f"   API请求数: {len(api_requests)}")
        logger.info(f"   失败请求数: {len(failed_responses)}")
        logger.info(f"   慢请求数: {len(slow_requests)}")
        
        if failed_responses:
            logger.error("❌ 失败的请求:")
            for resp in failed_responses:
                logger.error(f"   {resp['status']} {resp['url']}")
        
        if slow_requests:
            logger.warning("⚠️ 慢请求:")
            for req in slow_requests:
                logger.warning(f"   {req['time']:.2f}s {req['url']}")
        
        # 最终截图
        await page.screenshot(path="risk_page_final_state.png")
        logger.info("📸 最终状态截图已保存: risk_page_final_state.png")
        
        # 总结
        logger.info("\n📋 风控页面诊断总结:")
        if load_time < 10:
            logger.info("✅ 页面加载速度正常")
        elif load_time < 20:
            logger.warning("⚠️ 页面加载较慢")
        else:
            logger.error("❌ 页面加载超时")
            
        if len(failed_responses) == 0:
            logger.info("✅ 所有API请求成功")
        else:
            logger.error(f"❌ 有 {len(failed_responses)} 个API请求失败")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    finally:
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_risk_page_timeout())
