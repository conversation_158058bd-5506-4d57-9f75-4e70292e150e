const puppeteer = require('puppeteer');
const fs = require('fs');

class DeepPlatformAnalysis {
    constructor() {
        this.browser = null;
        this.page = null;
        this.issues = [];
        this.successes = [];
        this.baseUrl = 'http://localhost:5175';
        this.apiUrl = 'http://localhost:8000';
    }

    async init() {
        console.log('🔍 启动量化投资平台深度分析...');
        
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox', '--start-maximized']
        });
        
        this.page = await this.browser.newPage();
        
        // 监听所有类型的错误和问题
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.issues.push({
                    type: 'Console Error',
                    message: msg.text(),
                    location: msg.location()
                });
            } else if (msg.type() === 'warning') {
                this.issues.push({
                    type: 'Console Warning',
                    message: msg.text(),
                    location: msg.location()
                });
            }
        });
        
        this.page.on('pageerror', error => {
            this.issues.push({
                type: 'Page Error',
                message: error.message,
                stack: error.stack
            });
        });
        
        this.page.on('requestfailed', request => {
            this.issues.push({
                type: 'Request Failed',
                url: request.url(),
                error: request.failure().errorText,
                method: request.method()
            });
        });

        this.page.on('response', response => {
            if (response.status() >= 400) {
                this.issues.push({
                    type: 'HTTP Error',
                    url: response.url(),
                    status: response.status(),
                    statusText: response.statusText()
                });
            }
        });
    }

    async analyzeModule(moduleName, url, testFunction) {
        console.log(`\n🔍 分析模块: ${moduleName}`);
        console.log(`📍 URL: ${url}`);
        
        try {
            await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 15000 });
            await new Promise(resolve => setTimeout(resolve, 3000)); // 等待Vue组件完全渲染
            
            const result = await testFunction();
            
            this.successes.push({
                module: moduleName,
                url,
                result
            });
            
            console.log(`✅ ${moduleName} - 分析完成`);
            return result;
            
        } catch (error) {
            this.issues.push({
                type: 'Module Analysis Error',
                module: moduleName,
                url,
                error: error.message
            });
            console.log(`❌ ${moduleName} - 分析失败: ${error.message}`);
            return null;
        }
    }

    // 分析1: 首页和导航
    async analyzeHomepage() {
        return await this.analyzeModule('首页和导航', this.baseUrl, async () => {
            const analysis = await this.page.evaluate(() => {
                return {
                    title: document.title,
                    hasNavigation: !!document.querySelector('.el-menu'),
                    menuItems: Array.from(document.querySelectorAll('.el-menu-item')).map(item => ({
                        text: item.textContent.trim(),
                        active: item.classList.contains('is-active')
                    })),
                    hasLogo: !!document.querySelector('.logo'),
                    hasUserInfo: !!document.querySelector('.user-info'),
                    contentLength: document.body.innerText.length,
                    hasErrors: document.body.innerText.includes('Error') || document.body.innerText.includes('404')
                };
            });

            // 截图
            await this.page.screenshot({ path: 'analysis_homepage.png', fullPage: true });
            
            return analysis;
        });
    }

    // 分析2: 模拟交易页面
    async analyzeSimulatedTrading() {
        return await this.analyzeModule('模拟交易', `${this.baseUrl}/trading/simulated`, async () => {
            const analysis = await this.page.evaluate(() => {
                const elements = {
                    // 检查关键组件
                    simulationBadge: !!document.querySelector('.simulation-badge'),
                    accountOverview: !!document.querySelector('.account-overview'),
                    stockSearch: !!document.querySelector('.stock-search'),
                    marketDepth: !!document.querySelector('.market-depth'),
                    tradingForm: !!document.querySelector('.trading-form'),
                    bottomPanel: !!document.querySelector('.bottom-panel'),
                    
                    // 检查具体内容
                    badgeText: document.querySelector('.badge-text')?.textContent || '',
                    searchPlaceholder: document.querySelector('.search-input input')?.placeholder || '',
                    
                    // 检查账户信息
                    accountItems: Array.from(document.querySelectorAll('.account-item')).map(item => ({
                        label: item.querySelector('.label')?.textContent || '',
                        value: item.querySelector('.value')?.textContent || ''
                    })),
                    
                    // 检查交易表单
                    formInputs: Array.from(document.querySelectorAll('.trading-form input')).length,
                    tradingButtons: Array.from(document.querySelectorAll('.trading-form button')).map(btn => btn.textContent?.trim()),
                    
                    // 检查底部面板
                    tabPanes: Array.from(document.querySelectorAll('.el-tab-pane')).map(pane => pane.getAttribute('label')),
                    
                    // 页面内容分析
                    totalContent: document.body.innerText.length,
                    isSimpleText: document.body.innerText.length < 500,
                    contentPreview: document.body.innerText.substring(0, 300)
                };
                
                return elements;
            });

            // 测试交互功能
            try {
                const searchInput = await this.page.$('.search-input input');
                if (searchInput) {
                    await searchInput.type('000001');
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    analysis.searchInteractive = true;
                }
            } catch (error) {
                analysis.searchInteractive = false;
                analysis.searchError = error.message;
            }

            // 截图
            await this.page.screenshot({ path: 'analysis_simulated_trading.png', fullPage: true });
            
            return analysis;
        });
    }

    // 分析3: 实时行情页面
    async analyzeRealTimeMarket() {
        return await this.analyzeModule('实时行情', `${this.baseUrl}/market/realtime`, async () => {
            const analysis = await this.page.evaluate(() => {
                return {
                    hasChart: !!document.querySelector('#market-trend-chart'),
                    chartCanvas: !!document.querySelector('#market-trend-chart canvas'),
                    hasSidebar: !!document.querySelector('.sidebar'),
                    hasStockList: !!document.querySelector('.stock-list'),
                    stockItems: Array.from(document.querySelectorAll('.stock-item')).length,
                    contentLength: document.body.innerText.length,
                    contentPreview: document.body.innerText.substring(0, 200)
                };
            });

            await this.page.screenshot({ path: 'analysis_realtime_market.png', fullPage: true });
            return analysis;
        });
    }

    // 分析4: 历史数据页面
    async analyzeHistoricalData() {
        return await this.analyzeModule('历史数据', `${this.baseUrl}/market/historical`, async () => {
            const analysis = await this.page.evaluate(() => {
                return {
                    hasSearchForm: !!document.querySelector('.search-form'),
                    hasDataTable: !!document.querySelector('.data-table'),
                    hasChartArea: !!document.querySelector('.chart-area'),
                    tableRows: Array.from(document.querySelectorAll('.el-table__row')).length,
                    contentLength: document.body.innerText.length,
                    contentPreview: document.body.innerText.substring(0, 200)
                };
            });

            // 测试搜索功能
            try {
                const symbolInput = await this.page.$('input[placeholder*="股票代码"]');
                if (symbolInput) {
                    await symbolInput.type('000001');
                    const searchButton = await this.page.$('button[type="primary"]');
                    if (searchButton) {
                        await searchButton.click();
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        analysis.searchFunctional = true;
                    }
                }
            } catch (error) {
                analysis.searchFunctional = false;
                analysis.searchError = error.message;
            }

            await this.page.screenshot({ path: 'analysis_historical_data.png', fullPage: true });
            return analysis;
        });
    }

    // 分析5: API连接状态
    async analyzeAPIConnectivity() {
        console.log('\n🔍 分析API连接状态...');
        
        const endpoints = [
            '/api/v1/health',
            '/api/v1/market/stocks',
            '/api/v1/market/realtime/000001',
            '/api/v1/trading/account',
            '/api/v1/strategy/list'
        ];

        const apiResults = [];
        
        for (const endpoint of endpoints) {
            try {
                const response = await this.page.evaluate(async (url) => {
                    const res = await fetch(url);
                    const text = await res.text();
                    return {
                        status: res.status,
                        ok: res.ok,
                        statusText: res.statusText,
                        responseLength: text.length,
                        responsePreview: text.substring(0, 200)
                    };
                }, `${this.apiUrl}${endpoint}`);

                apiResults.push({
                    endpoint,
                    ...response,
                    working: response.ok
                });
                
                console.log(`${response.ok ? '✅' : '❌'} ${endpoint} - ${response.status}`);
                
            } catch (error) {
                apiResults.push({
                    endpoint,
                    error: error.message,
                    working: false
                });
                console.log(`❌ ${endpoint} - ${error.message}`);
            }
        }

        return { apiResults };
    }

    // 生成综合分析报告
    async generateComprehensiveReport() {
        console.log('\n📊 ===== 量化投资平台深度分析报告 =====');
        
        const totalModules = this.successes.length;
        const workingModules = this.successes.filter(s => s.result && !s.result.hasErrors).length;
        
        console.log(`\n📈 平台概览:`);
        console.log(`🔧 测试模块: ${totalModules}`);
        console.log(`✅ 正常工作: ${workingModules}`);
        console.log(`❌ 发现问题: ${this.issues.length}`);
        console.log(`🎯 健康度: ${Math.round(workingModules / totalModules * 100)}%`);

        console.log(`\n📋 模块分析结果:`);
        this.successes.forEach(success => {
            console.log(`\n🔍 ${success.module}:`);
            if (success.result) {
                Object.entries(success.result).forEach(([key, value]) => {
                    if (typeof value === 'boolean') {
                        console.log(`  ${value ? '✅' : '❌'} ${key}`);
                    } else if (typeof value === 'number') {
                        console.log(`  📊 ${key}: ${value}`);
                    } else if (typeof value === 'string' && value.length < 100) {
                        console.log(`  📝 ${key}: ${value}`);
                    }
                });
            }
        });

        if (this.issues.length > 0) {
            console.log(`\n🚨 发现的问题:`);
            const groupedIssues = {};
            this.issues.forEach(issue => {
                if (!groupedIssues[issue.type]) {
                    groupedIssues[issue.type] = [];
                }
                groupedIssues[issue.type].push(issue);
            });

            Object.entries(groupedIssues).forEach(([type, issues]) => {
                console.log(`\n❌ ${type} (${issues.length}个):`);
                issues.forEach((issue, index) => {
                    console.log(`  ${index + 1}. ${issue.message || issue.error || issue.url}`);
                });
            });
        }

        // 保存详细报告
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                totalModules,
                workingModules,
                issueCount: this.issues.length,
                healthScore: Math.round(workingModules / totalModules * 100)
            },
            moduleResults: this.successes,
            issues: this.issues
        };

        fs.writeFileSync('deep_platform_analysis_report.json', JSON.stringify(reportData, null, 2));
        console.log('\n💾 详细报告已保存到: deep_platform_analysis_report.json');
    }

    async runFullAnalysis() {
        try {
            await this.init();

            // 执行所有分析
            await this.analyzeHomepage();
            await this.analyzeSimulatedTrading();
            await this.analyzeRealTimeMarket();
            await this.analyzeHistoricalData();
            await this.analyzeAPIConnectivity();

            await this.generateComprehensiveReport();

        } catch (error) {
            console.error('❌ 分析执行失败:', error);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }
}

// 运行深度分析
const analyzer = new DeepPlatformAnalysis();
analyzer.runFullAnalysis()
    .then(() => {
        console.log('\n🎉 深度分析完成！');
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 分析异常:', error);
        process.exit(1);
    });
