"""add composite indexes to orders table

Revision ID: 005_add_composite_indexes
Revises: 004_add_ctp_tables
Create Date: 2025-07-08
"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "005_add_composite_indexes"
down_revision = "004_add_ctp_tables"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        "ix_orders_user_status_symbol_created",
        "orders",
        ["user_id", "status", "symbol_code", "created_at"],
    )


def downgrade():
    op.drop_index("ix_orders_user_status_symbol_created", table_name="orders")
