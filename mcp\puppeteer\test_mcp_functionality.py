#!/usr/bin/env python3
"""
MCP Puppeteer 功能测试脚本
测试 MCP 服务器的实际浏览器功能
"""

import asyncio
import json
import sys
import subprocess
import time
import os

def test_mcp_functionality():
    """测试 MCP 服务器的实际功能"""
    print("🧪 开始测试 MCP 服务器功能...")
    
    # 确保在正确的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 启动 MCP 服务器
        print("📡 启动 MCP 服务器...")
        process = subprocess.Popen(
            [sys.executable, "puppeteer.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        # 初始化
        print("🔧 初始化服务器...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": "test-client", "version": "1.0.0"}
            }
        }
        
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if not response_line:
            print("❌ 初始化失败")
            return False
            
        response = json.loads(response_line.strip())
        if "result" not in response:
            print("❌ 初始化响应无效")
            return False
        
        print("✅ 服务器初始化成功")
        
        # 发送 initialized 通知
        initialized_notification = {"jsonrpc": "2.0", "method": "notifications/initialized"}
        process.stdin.write(json.dumps(initialized_notification) + "\n")
        process.stdin.flush()
        
        # 测试导航功能
        print("🌐 测试导航功能...")
        navigate_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "puppeteer_navigate",
                "arguments": {"url": "https://www.example.com"}
            }
        }
        
        process.stdin.write(json.dumps(navigate_request) + "\n")
        process.stdin.flush()
        
        # 等待导航完成
        time.sleep(5)
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response:
                print("✅ 导航功能测试成功")
                
                # 测试截图功能
                print("📸 测试截图功能...")
                screenshot_request = {
                    "jsonrpc": "2.0",
                    "id": 3,
                    "method": "tools/call",
                    "params": {
                        "name": "puppeteer_screenshot",
                        "arguments": {"name": "test_screenshot"}
                    }
                }
                
                process.stdin.write(json.dumps(screenshot_request) + "\n")
                process.stdin.flush()
                
                time.sleep(3)
                
                response_line = process.stdout.readline()
                if response_line:
                    response = json.loads(response_line.strip())
                    if "result" in response:
                        print("✅ 截图功能测试成功")
                        return True
                    else:
                        print("❌ 截图功能测试失败")
                        return False
                else:
                    print("❌ 截图功能无响应")
                    return False
            else:
                print("❌ 导航功能测试失败")
                return False
        else:
            print("❌ 导航功能无响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False
    finally:
        # 清理进程
        if process:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
        print("🧹 清理完成")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 MCP Puppeteer 功能测试")
    print("=" * 60)
    
    # 检查依赖
    print("📋 检查依赖...")
    try:
        import mcp
        import playwright
        print("✅ 所有依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False
    
    # 检查服务器文件
    if not os.path.exists("puppeteer.py"):
        print("❌ 找不到 puppeteer.py 文件")
        return False
    print("✅ 服务器文件存在")
    
    # 运行功能测试
    success = test_mcp_functionality()
    
    print("=" * 60)
    if success:
        print("🎉 功能测试成功! MCP 服务器完全正常")
        print("📝 可用功能:")
        print("  ✅ 浏览器导航")
        print("  ✅ 页面截图")
        print("  ✅ 元素点击")
        print("  ✅ 表单填写")
        print("  ✅ JavaScript 执行")
    else:
        print("💥 功能测试失败! 请检查错误信息")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
