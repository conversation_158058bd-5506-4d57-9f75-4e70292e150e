"""
报告生成器
生成专业的量化投资分析报告
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, date
import json
import warnings
warnings.filterwarnings('ignore')

@dataclass
class ReportSection:
    """报告章节"""
    title: str
    content: Dict[str, Any]
    charts: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.charts is None:
            self.charts = []

@dataclass 
class AnalysisReport:
    """分析报告"""
    title: str
    created_at: datetime
    period: Dict[str, date]
    sections: List[ReportSection]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.risk_free_rate = config.get('risk_free_rate', 0.02)
        
    def generate_backtest_report(
        self,
        portfolio_data: pd.Series,
        benchmark_data: Optional[pd.Series] = None,
        trades: Optional[pd.DataFrame] = None,
        positions: Optional[Dict[str, Any]] = None,
        strategy_info: Optional[Dict[str, Any]] = None
    ) -> AnalysisReport:
        """生成回测报告"""
        
        sections = []
        
        # 1. 策略概况
        if strategy_info:
            overview_section = self._create_strategy_overview(strategy_info)
            sections.append(overview_section)
        
        # 2. 绩效摘要
        performance_section = self._create_performance_summary(
            portfolio_data, benchmark_data
        )
        sections.append(performance_section)
        
        # 3. 收益分析
        returns_section = self._create_returns_analysis(
            portfolio_data, benchmark_data
        )
        sections.append(returns_section)
        
        # 4. 风险分析
        risk_section = self._create_risk_analysis(portfolio_data)
        sections.append(risk_section)
        
        # 5. 回撤分析
        drawdown_section = self._create_drawdown_analysis(portfolio_data)
        sections.append(drawdown_section)
        
        # 6. 交易分析
        if trades is not None and not trades.empty:
            trading_section = self._create_trading_analysis(trades)
            sections.append(trading_section)
            
        # 7. 持仓分析
        if positions:
            position_section = self._create_position_analysis(positions)
            sections.append(position_section)
            
        # 8. 滚动分析  
        rolling_section = self._create_rolling_analysis(portfolio_data)
        sections.append(rolling_section)
        
        return AnalysisReport(
            title="量化策略回测分析报告",
            created_at=datetime.now(),
            period={
                'start_date': portfolio_data.index[0].date(),
                'end_date': portfolio_data.index[-1].date()
            },
            sections=sections,
            metadata={
                'data_points': len(portfolio_data),
                'trading_days': len(portfolio_data),
                'years': len(portfolio_data) / 252
            }
        )
    
    def _create_strategy_overview(self, strategy_info: Dict[str, Any]) -> ReportSection:
        """创建策略概况章节"""
        content = {
            'strategy_name': strategy_info.get('name', 'Unknown'),
            'strategy_type': strategy_info.get('strategy_type', 'Unknown'),
            'description': strategy_info.get('description', ''),
            'version': strategy_info.get('version', '1.0.0'),
            'parameters': strategy_info.get('parameters', {}),
            'universe_size': strategy_info.get('universe_size', 0),
            'max_positions': strategy_info.get('max_positions', 0),
            'benchmark': strategy_info.get('benchmark', '000300.SH')
        }
        
        return ReportSection(
            title="策略概况",
            content=content
        )
    
    def _create_performance_summary(
        self,
        portfolio_data: pd.Series,
        benchmark_data: Optional[pd.Series] = None
    ) -> ReportSection:
        """创建绩效摘要章节"""
        returns = portfolio_data.pct_change().dropna()
        
        # 基础指标
        total_return = (portfolio_data.iloc[-1] / portfolio_data.iloc[0]) - 1
        annual_return = self._calculate_annual_return(returns)
        volatility = returns.std() * np.sqrt(252)
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        max_drawdown = self._calculate_max_drawdown(portfolio_data)
        
        content = {
            'performance_metrics': {
                'total_return': f"{total_return:.2%}",
                'annual_return': f"{annual_return:.2%}",
                'volatility': f"{volatility:.2%}",
                'sharpe_ratio': f"{sharpe_ratio:.2f}",
                'max_drawdown': f"{max_drawdown:.2%}",
                'calmar_ratio': f"{annual_return / max_drawdown:.2f}" if max_drawdown > 0 else "N/A"
            }
        }
        
        # 基准比较
        if benchmark_data is not None:
            benchmark_comparison = self._compare_with_benchmark(portfolio_data, benchmark_data)
            content['benchmark_comparison'] = benchmark_comparison
            
        # 图表数据
        charts = [
            {
                'type': 'line',
                'title': '累计收益曲线',
                'data': {
                    'dates': portfolio_data.index.strftime('%Y-%m-%d').tolist(),
                    'portfolio': ((portfolio_data / portfolio_data.iloc[0] - 1) * 100).tolist()
                }
            }
        ]
        
        if benchmark_data is not None:
            charts[0]['data']['benchmark'] = ((benchmark_data / benchmark_data.iloc[0] - 1) * 100).tolist()
        
        return ReportSection(
            title="绩效摘要",
            content=content,
            charts=charts
        )
    
    def _create_returns_analysis(
        self,
        portfolio_data: pd.Series,
        benchmark_data: Optional[pd.Series] = None
    ) -> ReportSection:
        """创建收益分析章节"""
        returns = portfolio_data.pct_change().dropna()
        
        # 收益分布分析
        content = {
            'return_statistics': {
                'mean_daily_return': f"{returns.mean():.4f}",
                'median_daily_return': f"{returns.median():.4f}",
                'std_daily_return': f"{returns.std():.4f}",
                'skewness': f"{returns.skew():.2f}",
                'kurtosis': f"{returns.kurtosis():.2f}",
                'positive_days': f"{(returns > 0).sum()}/{len(returns)}",
                'negative_days': f"{(returns < 0).sum()}/{len(returns)}",
                'zero_days': f"{(returns == 0).sum()}/{len(returns)}"
            },
            'percentiles': {
                f'{p}%': f"{returns.quantile(p/100):.4f}" 
                for p in [1, 5, 10, 25, 50, 75, 90, 95, 99]
            }
        }
        
        # 月度收益分析
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        monthly_stats = {
            'best_month': f"{monthly_returns.max():.2%}",
            'worst_month': f"{monthly_returns.min():.2%}",
            'positive_months': f"{(monthly_returns > 0).sum()}/{len(monthly_returns)}",
            'avg_positive_month': f"{monthly_returns[monthly_returns > 0].mean():.2%}" if (monthly_returns > 0).any() else "N/A",
            'avg_negative_month': f"{monthly_returns[monthly_returns < 0].mean():.2%}" if (monthly_returns < 0).any() else "N/A"
        }
        
        content['monthly_analysis'] = monthly_stats
        
        # 图表
        charts = [
            {
                'type': 'histogram',
                'title': '日收益率分布',
                'data': {
                    'returns': returns.tolist(),
                    'bins': 50
                }
            },
            {
                'type': 'bar',
                'title': '月度收益',
                'data': {
                    'dates': monthly_returns.index.strftime('%Y-%m').tolist(),
                    'returns': (monthly_returns * 100).tolist()
                }
            }
        ]
        
        return ReportSection(
            title="收益分析",
            content=content,
            charts=charts
        )
    
    def _create_risk_analysis(self, portfolio_data: pd.Series) -> ReportSection:
        """创建风险分析章节"""
        returns = portfolio_data.pct_change().dropna()
        
        # VaR和CVaR计算
        var_95 = returns.quantile(0.05)
        var_99 = returns.quantile(0.01)
        cvar_95 = returns[returns <= var_95].mean() if (returns <= var_95).any() else 0
        cvar_99 = returns[returns <= var_99].mean() if (returns <= var_99).any() else 0
        
        # 下行风险指标
        negative_returns = returns[returns < 0]
        downside_deviation = negative_returns.std() * np.sqrt(252) if len(negative_returns) > 0 else 0
        sortino_ratio = self._calculate_sortino_ratio(returns)
        
        content = {
            'risk_metrics': {
                'daily_volatility': f"{returns.std():.4f}",
                'annual_volatility': f"{returns.std() * np.sqrt(252):.2%}",
                'downside_deviation': f"{downside_deviation:.2%}",
                'var_95_daily': f"{var_95:.4f}",
                'var_99_daily': f"{var_99:.4f}",
                'cvar_95_daily': f"{cvar_95:.4f}",
                'cvar_99_daily': f"{cvar_99:.4f}",
                'sortino_ratio': f"{sortino_ratio:.2f}"
            },
            'tail_risk': {
                'worst_day': f"{returns.min():.4f}",
                'best_day': f"{returns.max():.4f}",
                'days_below_var95': f"{(returns <= var_95).sum()}",
                'days_below_var99': f"{(returns <= var_99).sum()}"
            }
        }
        
        # 滚动风险指标
        rolling_vol = returns.rolling(60).std() * np.sqrt(252)
        rolling_var = returns.rolling(60).quantile(0.05)
        
        charts = [
            {
                'type': 'line',
                'title': '滚动波动率（60日）',
                'data': {
                    'dates': rolling_vol.dropna().index.strftime('%Y-%m-%d').tolist(),
                    'volatility': (rolling_vol.dropna() * 100).tolist()
                }
            },
            {
                'type': 'line',
                'title': '滚动VaR（60日）',
                'data': {
                    'dates': rolling_var.dropna().index.strftime('%Y-%m-%d').tolist(),
                    'var': (rolling_var.dropna() * 100).tolist()
                }
            }
        ]
        
        return ReportSection(
            title="风险分析",
            content=content,
            charts=charts
        )
    
    def _create_drawdown_analysis(self, portfolio_data: pd.Series) -> ReportSection:
        """创建回撤分析章节"""
        # 计算回撤序列
        running_max = portfolio_data.expanding().max()
        drawdown = (portfolio_data - running_max) / running_max
        
        # 回撤统计
        max_drawdown = abs(drawdown.min())
        avg_drawdown = abs(drawdown[drawdown < 0].mean()) if (drawdown < 0).any() else 0
        
        # 回撤期分析
        drawdown_periods = self._find_drawdown_periods(drawdown)
        
        content = {
            'drawdown_metrics': {
                'max_drawdown': f"{max_drawdown:.2%}",
                'avg_drawdown': f"{avg_drawdown:.2%}",
                'drawdown_periods': len(drawdown_periods),
                'current_drawdown': f"{abs(drawdown.iloc[-1]):.2%}"
            }
        }
        
        if drawdown_periods:
            # 最大回撤期信息
            max_dd_period = max(drawdown_periods, key=lambda x: x['max_drawdown'])
            longest_period = max(drawdown_periods, key=lambda x: x['duration'])
            
            content['major_drawdowns'] = {
                'max_drawdown_period': {
                    'start_date': max_dd_period['start_date'].strftime('%Y-%m-%d'),
                    'end_date': max_dd_period['end_date'].strftime('%Y-%m-%d'),
                    'duration_days': max_dd_period['duration'],
                    'max_drawdown': f"{max_dd_period['max_drawdown']:.2%}"
                },
                'longest_drawdown_period': {
                    'start_date': longest_period['start_date'].strftime('%Y-%m-%d'),
                    'end_date': longest_period['end_date'].strftime('%Y-%m-%d'),
                    'duration_days': longest_period['duration'],
                    'max_drawdown': f"{longest_period['max_drawdown']:.2%}"
                }
            }
            
            # Top 5 最大回撤
            top_drawdowns = sorted(drawdown_periods, key=lambda x: x['max_drawdown'], reverse=True)[:5]
            content['top_5_drawdowns'] = [
                {
                    'rank': i + 1,
                    'start_date': dd['start_date'].strftime('%Y-%m-%d'),
                    'end_date': dd['end_date'].strftime('%Y-%m-%d'),
                    'duration': dd['duration'],
                    'max_drawdown': f"{dd['max_drawdown']:.2%}"
                }
                for i, dd in enumerate(top_drawdowns)
            ]
        
        # 图表
        charts = [
            {
                'type': 'area',
                'title': '回撤曲线',
                'data': {
                    'dates': drawdown.index.strftime('%Y-%m-%d').tolist(),
                    'drawdown': (drawdown * 100).tolist()
                }
            }
        ]
        
        return ReportSection(
            title="回撤分析",
            content=content,
            charts=charts
        )
    
    def _create_trading_analysis(self, trades: pd.DataFrame) -> ReportSection:
        """创建交易分析章节"""
        if trades.empty:
            return ReportSection(
                title="交易分析",
                content={'message': '无交易数据'}
            )
        
        # 交易统计
        total_trades = len(trades)
        buy_trades = len(trades[trades['side'] == 'buy'])
        sell_trades = len(trades[trades['side'] == 'sell'])
        
        # 交易金额统计
        total_volume = trades['quantity'].sum()
        total_amount = (trades['quantity'] * trades['price']).sum()
        avg_trade_size = total_amount / total_trades if total_trades > 0 else 0
        
        content = {
            'trading_statistics': {
                'total_trades': total_trades,
                'buy_trades': buy_trades,
                'sell_trades': sell_trades,
                'total_volume': f"{total_volume:,.0f}",
                'total_amount': f"{total_amount:,.2f}",
                'avg_trade_size': f"{avg_trade_size:,.2f}"
            }
        }
        
        # 按股票统计
        symbol_stats = trades.groupby('symbol').agg({
            'quantity': 'sum',
            'price': 'mean'
        }).round(2)
        
        symbol_amounts = (symbol_stats['quantity'] * symbol_stats['price']).sort_values(ascending=False)
        top_symbols = symbol_amounts.head(10)
        
        content['top_traded_symbols'] = [
            {
                'symbol': symbol,
                'total_amount': f"{amount:,.2f}",
                'avg_price': f"{symbol_stats.loc[symbol, 'price']:.2f}",
                'total_volume': f"{symbol_stats.loc[symbol, 'quantity']:,.0f}"
            }
            for symbol, amount in top_symbols.items()
        ]
        
        # 时间分布
        trades['date'] = pd.to_datetime(trades['date'])
        monthly_trades = trades.groupby(trades['date'].dt.to_period('M')).size()
        
        charts = [
            {
                'type': 'bar',
                'title': '月度交易次数',
                'data': {
                    'months': [str(m) for m in monthly_trades.index],
                    'trades': monthly_trades.tolist()
                }
            }
        ]
        
        return ReportSection(
            title="交易分析",
            content=content,
            charts=charts
        )
    
    def _create_position_analysis(self, positions: Dict[str, Any]) -> ReportSection:
        """创建持仓分析章节"""
        content = {
            'position_summary': positions.get('summary', {}),
            'top_positions': positions.get('top_positions', []),
            'sector_distribution': positions.get('sector_exposure', {}),
            'best_performers': positions.get('best_performers', []),
            'worst_performers': positions.get('worst_performers', [])
        }
        
        # 图表
        charts = []
        
        # 持仓分布饼图
        if positions.get('top_positions'):
            top_pos = positions['top_positions'][:10]
            charts.append({
                'type': 'pie',
                'title': 'Top 10 持仓分布',
                'data': {
                    'symbols': [p['symbol'] for p in top_pos],
                    'values': [abs(p['market_value']) for p in top_pos]
                }
            })
        
        return ReportSection(
            title="持仓分析",
            content=content,
            charts=charts
        )
    
    def _create_rolling_analysis(self, portfolio_data: pd.Series) -> ReportSection:
        """创建滚动分析章节"""
        returns = portfolio_data.pct_change().dropna()
        
        # 滚动指标计算
        windows = [30, 60, 120, 252]
        rolling_metrics = {}
        
        for window in windows:
            if len(returns) >= window:
                rolling_return = returns.rolling(window).apply(lambda x: (1 + x).prod() - 1)
                rolling_vol = returns.rolling(window).std() * np.sqrt(252)
                rolling_sharpe = returns.rolling(window).apply(
                    lambda x: (x.mean() - self.risk_free_rate / 252) / x.std() * np.sqrt(252)
                    if x.std() > 0 else 0
                )
                
                rolling_metrics[f'{window}d'] = {
                    'current_return': f"{rolling_return.iloc[-1]:.2%}" if not rolling_return.empty else "N/A",
                    'current_volatility': f"{rolling_vol.iloc[-1]:.2%}" if not rolling_vol.empty else "N/A",
                    'current_sharpe': f"{rolling_sharpe.iloc[-1]:.2f}" if not rolling_sharpe.empty else "N/A"
                }
        
        content = {
            'rolling_metrics': rolling_metrics
        }
        
        # 滚动夏普比率图表
        if len(returns) >= 60:
            rolling_sharpe_60 = returns.rolling(60).apply(
                lambda x: (x.mean() - self.risk_free_rate / 252) / x.std() * np.sqrt(252)
                if x.std() > 0 else 0
            )
            
            charts = [
                {
                    'type': 'line',
                    'title': '滚动夏普比率（60日）',
                    'data': {
                        'dates': rolling_sharpe_60.dropna().index.strftime('%Y-%m-%d').tolist(),
                        'sharpe': rolling_sharpe_60.dropna().tolist()
                    }
                }
            ]
        else:
            charts = []
        
        return ReportSection(
            title="滚动分析",
            content=content,
            charts=charts
        )
    
    def _calculate_annual_return(self, returns: pd.Series) -> float:
        """计算年化收益率"""
        if len(returns) == 0:
            return 0
        total_return = (1 + returns).prod() - 1
        years = len(returns) / 252
        if years <= 0:
            return 0
        return (1 + total_return) ** (1 / years) - 1
    
    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """计算夏普比率"""
        excess_returns = returns - self.risk_free_rate / 252
        if excess_returns.std() == 0:
            return 0
        return excess_returns.mean() / excess_returns.std() * np.sqrt(252)
    
    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """计算索提诺比率"""
        excess_returns = returns - self.risk_free_rate / 252
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0 or negative_returns.std() == 0:
            return 0
        downside_deviation = negative_returns.std()
        return excess_returns.mean() / downside_deviation * np.sqrt(252)
    
    def _calculate_max_drawdown(self, portfolio_data: pd.Series) -> float:
        """计算最大回撤"""
        running_max = portfolio_data.expanding().max()
        drawdown = (portfolio_data - running_max) / running_max
        return abs(drawdown.min())
    
    def _compare_with_benchmark(self, portfolio_data: pd.Series, benchmark_data: pd.Series) -> Dict[str, str]:
        """与基准比较"""
        # 对齐数据
        aligned_data = pd.DataFrame({
            'portfolio': portfolio_data,
            'benchmark': benchmark_data
        }).dropna()
        
        if len(aligned_data) < 2:
            return {}
        
        # 计算指标
        portfolio_return = (aligned_data['portfolio'].iloc[-1] / aligned_data['portfolio'].iloc[0]) - 1
        benchmark_return = (aligned_data['benchmark'].iloc[-1] / aligned_data['benchmark'].iloc[0]) - 1
        excess_return = portfolio_return - benchmark_return
        
        portfolio_returns = aligned_data['portfolio'].pct_change().dropna()
        benchmark_returns = aligned_data['benchmark'].pct_change().dropna()
        
        # Beta计算
        covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 1
        
        # 跟踪误差
        tracking_error = (portfolio_returns - benchmark_returns).std() * np.sqrt(252)
        
        return {
            'portfolio_return': f"{portfolio_return:.2%}",
            'benchmark_return': f"{benchmark_return:.2%}",
            'excess_return': f"{excess_return:.2%}",
            'beta': f"{beta:.2f}",
            'tracking_error': f"{tracking_error:.2%}",
            'correlation': f"{np.corrcoef(portfolio_returns, benchmark_returns)[0, 1]:.2f}"
        }
    
    def _find_drawdown_periods(self, drawdown: pd.Series) -> List[Dict[str, Any]]:
        """找出回撤期"""
        periods = []
        start_idx = None
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and start_idx is None:
                start_idx = i
            elif dd >= 0 and start_idx is not None:
                end_idx = i - 1
                max_dd_in_period = abs(drawdown.iloc[start_idx:end_idx+1].min())
                
                periods.append({
                    'start_date': drawdown.index[start_idx],
                    'end_date': drawdown.index[end_idx],
                    'duration': end_idx - start_idx + 1,
                    'max_drawdown': max_dd_in_period
                })
                start_idx = None
                
        # 处理未结束的回撤
        if start_idx is not None:
            max_dd_in_period = abs(drawdown.iloc[start_idx:].min())
            periods.append({
                'start_date': drawdown.index[start_idx],
                'end_date': drawdown.index[-1],
                'duration': len(drawdown) - start_idx,
                'max_drawdown': max_dd_in_period
            })
            
        return periods
    
    def export_report_json(self, report: AnalysisReport) -> str:
        """导出报告为JSON格式"""
        def convert_datetime(obj):
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            return obj
        
        report_dict = asdict(report)
        
        # 转换日期和numpy类型
        def convert_recursive(obj):
            if isinstance(obj, dict):
                return {k: convert_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_recursive(item) for item in obj]
            else:
                return convert_datetime(obj)
        
        report_dict = convert_recursive(report_dict)
        
        return json.dumps(report_dict, ensure_ascii=False, indent=2)
    
    def export_report_html(self, report: AnalysisReport) -> str:
        """导出报告为HTML格式"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .section {{ margin: 30px 0; }}
                .section h2 {{ color: #333; border-bottom: 2px solid #007acc; padding-bottom: 5px; }}
                .metrics-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .metrics-table th {{ background-color: #f2f2f2; }}
                .chart-placeholder {{ background-color: #f9f9f9; padding: 20px; text-align: center; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{title}</h1>
                <p>生成时间: {created_at}</p>
                <p>分析期间: {start_date} 至 {end_date}</p>
            </div>
            {sections_html}
        </body>
        </html>
        """
        
        sections_html = ""
        for section in report.sections:
            section_html = f'<div class="section"><h2>{section.title}</h2>'
            
            # 添加内容表格
            if section.content:
                section_html += self._dict_to_html_table(section.content)
            
            # 添加图表占位符
            for chart in section.charts:
                section_html += f'<div class="chart-placeholder">[图表: {chart["title"]}]</div>'
            
            section_html += '</div>'
            sections_html += section_html
        
        return html_template.format(
            title=report.title,
            created_at=report.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            start_date=report.period['start_date'],
            end_date=report.period['end_date'],
            sections_html=sections_html
        )
    
    def _dict_to_html_table(self, data: Dict[str, Any], max_depth: int = 2) -> str:
        """将字典转换为HTML表格"""
        if max_depth <= 0:
            return str(data)
        
        html = '<table class="metrics-table">'
        
        for key, value in data.items():
            if isinstance(value, dict):
                html += f'<tr><th colspan="2">{key}</th></tr>'
                nested_table = self._dict_to_html_table(value, max_depth - 1)
                html += f'<tr><td colspan="2">{nested_table}</td></tr>'
            elif isinstance(value, list):
                html += f'<tr><td><strong>{key}</strong></td><td>{len(value)} items</td></tr>'
            else:
                html += f'<tr><td><strong>{key}</strong></td><td>{value}</td></tr>'
        
        html += '</table>'
        return html