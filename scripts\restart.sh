#!/bin/bash

# 量化投资平台重启脚本
# 支持 Linux、macOS、Windows (Git Bash)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔄 重启量化投资平台..."
echo "========================"

# 进入项目根目录
cd "$(dirname "$0")/.."

# 停止服务
log_info "停止现有服务..."
./scripts/stop.sh

# 等待服务完全停止
log_info "等待服务完全停止..."
sleep 3

# 启动服务
log_info "启动服务..."
./scripts/start.sh

log_success "重启完成！"
