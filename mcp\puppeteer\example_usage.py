#!/usr/bin/env python3

"""
Puppeteer MCP Server 使用示例
演示如何使用 MCP Server 进行网页自动化操作
"""

import asyncio
import json
import sys
from puppeteer import call_tool

async def example_navigate_and_screenshot():
    """示例：导航到网页并截图"""
    print("🌐 示例：导航到网页并截图")
    
    # 导航到网页
    print("  📍 导航到 example.com...")
    nav_result = await call_tool("puppeteer_navigate", {
        "url": "https://example.com",
        "timeout": 30000
    })
    print(f"  ✅ 导航结果: {nav_result[0].text}")
    
    # 截取页面截图
    print("  📸 截取页面截图...")
    screenshot_result = await call_tool("puppeteer_screenshot", {
        "name": "example_page",
        "width": 1280,
        "height": 720
    })
    print(f"  ✅ 截图结果: {screenshot_result[0].text}")
    
    return True

async def example_search_interaction():
    """示例：搜索引擎交互"""
    print("\n🔍 示例：搜索引擎交互")
    
    # 导航到搜索引擎
    print("  📍 导航到 DuckDuckGo...")
    nav_result = await call_tool("puppeteer_navigate", {
        "url": "https://duckduckgo.com",
        "timeout": 30000
    })
    print(f"  ✅ 导航结果: {nav_result[0].text}")
    
    # 填写搜索框
    print("  ✏️  填写搜索框...")
    fill_result = await call_tool("puppeteer_fill", {
        "selector": "input[name='q']",
        "value": "MCP Model Context Protocol",
        "timeout": 10000
    })
    print(f"  ✅ 填写结果: {fill_result[0].text}")
    
    # 点击搜索按钮
    print("  🔍 点击搜索按钮...")
    click_result = await call_tool("puppeteer_click", {
        "selector": "button[type='submit']",
        "timeout": 10000
    })
    print(f"  ✅ 点击结果: {click_result[0].text}")
    
    # 等待搜索结果加载并截图
    await asyncio.sleep(3)
    print("  📸 截取搜索结果...")
    screenshot_result = await call_tool("puppeteer_screenshot", {
        "name": "search_results",
        "width": 1280,
        "height": 720
    })
    print(f"  ✅ 截图结果: {screenshot_result[0].text}")
    
    return True

async def example_javascript_execution():
    """示例：执行 JavaScript 代码"""
    print("\n💻 示例：执行 JavaScript 代码")
    
    # 导航到测试页面
    print("  📍 导航到测试页面...")
    nav_result = await call_tool("puppeteer_navigate", {
        "url": "https://httpbin.org/html",
        "timeout": 30000
    })
    print(f"  ✅ 导航结果: {nav_result[0].text}")
    
    # 执行 JavaScript 获取页面信息
    print("  🔧 执行 JavaScript 获取页面标题...")
    js_result = await call_tool("puppeteer_evaluate", {
        "script": "document.title",
        "timeout": 10000
    })
    print(f"  ✅ 页面标题: {js_result[0].text}")
    
    # 执行更复杂的 JavaScript
    print("  🔧 执行 JavaScript 获取页面统计...")
    js_result = await call_tool("puppeteer_evaluate", {
        "script": """
        ({
            title: document.title,
            url: window.location.href,
            elementCount: document.querySelectorAll('*').length,
            hasH1: document.querySelector('h1') !== null,
            bodyText: document.body.innerText.substring(0, 100) + '...'
        })
        """,
        "timeout": 10000
    })
    print(f"  ✅ 页面统计: {js_result[0].text}")
    
    return True

async def main():
    """主函数"""
    print("🚀 Puppeteer MCP Server 使用示例")
    print("=" * 50)
    
    examples = [
        ("基础导航和截图", example_navigate_and_screenshot),
        ("搜索引擎交互", example_search_interaction),
        ("JavaScript 执行", example_javascript_execution),
    ]
    
    for name, example_func in examples:
        try:
            print(f"\n📋 运行示例: {name}")
            success = await example_func()
            if success:
                print(f"✅ 示例 '{name}' 完成")
            else:
                print(f"❌ 示例 '{name}' 失败")
        except Exception as e:
            print(f"❌ 示例 '{name}' 出错: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 所有示例运行完成！")
    print("\n📝 提示:")
    print("  - 确保网络连接正常")
    print("  - 某些网站可能有反爬虫机制")
    print("  - 可以根据需要调整超时时间")
    print("  - 截图文件会保存在内存中，可以通过 MCP 协议获取")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  示例被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 运行示例时发生错误: {e}")
        sys.exit(1)
