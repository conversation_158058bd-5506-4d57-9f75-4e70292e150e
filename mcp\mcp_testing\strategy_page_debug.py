#!/usr/bin/env python3
"""
策略页面调试工具 - 详细分析页面状态和元素
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StrategyPageDebug:
    def __init__(self):
        self.session_id = f"debug_{int(datetime.now().timestamp())}"
        self.browser = None
        self.page = None
        
    async def initialize(self):
        """初始化浏览器"""
        logger.info("初始化浏览器...")
        
        Path('screenshots').mkdir(exist_ok=True)
        Path('debug_reports').mkdir(exist_ok=True)
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
    async def analyze_page(self):
        """分析页面详细信息"""
        logger.info("分析策略页面...")
        
        try:
            # 导航到页面
            await self.page.goto('http://localhost:5173/strategy/detail/mock-1', 
                                wait_until='networkidle', timeout=30000)
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            # 截图
            await self.page.screenshot(path=f'screenshots/{self.session_id}_full_page.png', full_page=True)
            
            # 获取页面基本信息
            page_info = await self.get_page_info()
            
            # 获取所有元素信息
            elements_info = await self.get_all_elements()
            
            # 检查特定的可点击元素
            clickable_info = await self.check_clickable_elements()
            
            # 生成报告
            report = {
                'session_id': self.session_id,
                'timestamp': datetime.now().isoformat(),
                'page_info': page_info,
                'elements_info': elements_info,
                'clickable_info': clickable_info
            }
            
            # 保存报告
            with open(f'debug_reports/strategy_debug_{self.session_id}.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            # 打印摘要
            await self.print_summary(report)
            
        except Exception as e:
            logger.error(f"页面分析失败: {e}")
    
    async def get_page_info(self):
        """获取页面基本信息"""
        logger.info("获取页面基本信息...")
        
        try:
            page_info = await self.page.evaluate("""
                () => {
                    return {
                        title: document.title,
                        url: window.location.href,
                        readyState: document.readyState,
                        bodyContent: document.body ? document.body.innerHTML.length : 0,
                        hasVue: typeof window.Vue !== 'undefined',
                        hasVueApp: !!document.querySelector('#app'),
                        vueAppContent: document.querySelector('#app') ? document.querySelector('#app').innerHTML.length : 0,
                        totalElements: document.querySelectorAll('*').length,
                        visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
                            const style = window.getComputedStyle(el);
                            return style.display !== 'none' && style.visibility !== 'hidden';
                        }).length
                    };
                }
            """)
            
            return page_info
            
        except Exception as e:
            logger.error(f"获取页面信息失败: {e}")
            return {}
    
    async def get_all_elements(self):
        """获取所有元素的统计信息"""
        logger.info("统计页面元素...")
        
        try:
            elements_stats = await self.page.evaluate("""
                () => {
                    const stats = {
                        total: 0,
                        byTag: {},
                        withClass: 0,
                        withId: 0,
                        withHref: 0,
                        withOnclick: 0,
                        buttons: 0,
                        links: 0,
                        divs: 0,
                        spans: 0
                    };
                    
                    const allElements = document.querySelectorAll('*');
                    stats.total = allElements.length;
                    
                    allElements.forEach(el => {
                        const tagName = el.tagName.toLowerCase();
                        
                        // 按标签统计
                        stats.byTag[tagName] = (stats.byTag[tagName] || 0) + 1;
                        
                        // 特殊属性统计
                        if (el.className) stats.withClass++;
                        if (el.id) stats.withId++;
                        if (el.href) stats.withHref++;
                        if (el.onclick) stats.withOnclick++;
                        
                        // 特定标签统计
                        if (tagName === 'button') stats.buttons++;
                        if (tagName === 'a') stats.links++;
                        if (tagName === 'div') stats.divs++;
                        if (tagName === 'span') stats.spans++;
                    });
                    
                    return stats;
                }
            """)
            
            return elements_stats
            
        except Exception as e:
            logger.error(f"获取元素统计失败: {e}")
            return {}
    
    async def check_clickable_elements(self):
        """检查可点击元素"""
        logger.info("检查可点击元素...")
        
        try:
            clickable_elements = await self.page.evaluate("""
                () => {
                    const clickableElements = [];
                    
                    // 查找所有可能可点击的元素
                    const selectors = [
                        'a[href]',
                        'button',
                        '[role="button"]',
                        '[onclick]',
                        '.el-button',
                        '.btn',
                        'div[class*="card"]',
                        'div[class*="item"]',
                        'div[class*="clickable"]',
                        'span[class*="link"]',
                        '*[cursor="pointer"]'
                    ];
                    
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach((el, index) => {
                                const rect = el.getBoundingClientRect();
                                const style = window.getComputedStyle(el);
                                
                                // 只记录可见的元素
                                if (rect.width > 0 && rect.height > 0 && 
                                    style.display !== 'none' && 
                                    style.visibility !== 'hidden') {
                                    
                                    clickableElements.push({
                                        selector: selector,
                                        tagName: el.tagName.toLowerCase(),
                                        className: el.className || '',
                                        id: el.id || '',
                                        href: el.href || '',
                                        onclick: el.onclick ? 'has onclick' : '',
                                        textContent: (el.textContent || '').trim().substring(0, 100),
                                        position: {
                                            x: Math.round(rect.x),
                                            y: Math.round(rect.y),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        cursor: style.cursor,
                                        isVisible: true
                                    });
                                }
                            });
                        } catch (e) {
                            console.warn('Error with selector:', selector, e);
                        }
                    });
                    
                    // 去重
                    const uniqueElements = [];
                    const seen = new Set();
                    
                    clickableElements.forEach(el => {
                        const key = `${el.position.x},${el.position.y},${el.textContent.substring(0, 20)}`;
                        if (!seen.has(key)) {
                            seen.add(key);
                            uniqueElements.push(el);
                        }
                    });
                    
                    return {
                        total: uniqueElements.length,
                        elements: uniqueElements.slice(0, 50) // 限制返回数量
                    };
                }
            """)
            
            return clickable_elements
            
        except Exception as e:
            logger.error(f"检查可点击元素失败: {e}")
            return {'total': 0, 'elements': []}
    
    async def print_summary(self, report):
        """打印分析摘要"""
        print("\n" + "="*80)
        print("🔍 策略页面详细分析报告")
        print("="*80)
        
        page_info = report.get('page_info', {})
        elements_info = report.get('elements_info', {})
        clickable_info = report.get('clickable_info', {})
        
        print(f"📄 页面信息:")
        print(f"  标题: {page_info.get('title', 'N/A')}")
        print(f"  URL: {page_info.get('url', 'N/A')}")
        print(f"  加载状态: {page_info.get('readyState', 'N/A')}")
        print(f"  页面内容长度: {page_info.get('bodyContent', 0)}")
        print(f"  Vue应用: {'是' if page_info.get('hasVueApp') else '否'}")
        print(f"  Vue应用内容长度: {page_info.get('vueAppContent', 0)}")
        
        print(f"\n📊 元素统计:")
        print(f"  总元素数: {elements_info.get('total', 0)}")
        print(f"  可见元素数: {page_info.get('visibleElements', 0)}")
        print(f"  按钮数: {elements_info.get('buttons', 0)}")
        print(f"  链接数: {elements_info.get('links', 0)}")
        print(f"  带href属性: {elements_info.get('withHref', 0)}")
        print(f"  带onclick事件: {elements_info.get('withOnclick', 0)}")
        
        print(f"\n🖱️ 可点击元素:")
        print(f"  找到可点击元素: {clickable_info.get('total', 0)}个")
        
        if clickable_info.get('elements'):
            print(f"  前10个可点击元素:")
            for i, el in enumerate(clickable_info['elements'][:10], 1):
                print(f"    {i}. {el['tagName']} - {el['textContent'][:30]}...")
                print(f"       位置: ({el['position']['x']}, {el['position']['y']})")
                print(f"       大小: {el['position']['width']}x{el['position']['height']}")
                if el['href']:
                    print(f"       链接: {el['href']}")
                print()
        
        print(f"📋 详细报告已保存: debug_reports/strategy_debug_{self.session_id}.json")
    
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
    
    async def run_debug(self):
        """运行调试分析"""
        try:
            await self.initialize()
            await self.analyze_page()
        finally:
            await self.cleanup()

async def main():
    debugger = StrategyPageDebug()
    await debugger.run_debug()

if __name__ == "__main__":
    asyncio.run(main())
