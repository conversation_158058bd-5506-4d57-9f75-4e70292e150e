"""
修复所有导入问题的脚本
"""
import os
import re
from pathlib import Path


def fix_imports_in_file(file_path: Path):
    """修复单个文件的导入"""
    if not file_path.exists() or file_path.suffix != '.py':
        return
    
    content = file_path.read_text(encoding='utf-8')
    original_content = content
    
    # 修复导入路径
    replacements = [
        # 修复User模型导入
        (r'from app\.models\.user import User', 'from app.db.models.user import User'),
        
        # 修复dependencies导入
        (r'from app\.core\.dependencies import \((.*?)\)', 
         lambda m: f'from app.core.dependencies_fixed import {m.group(1).replace(chr(10), "").strip()}'),
        
        # 修复trading models导入
        (r'from app\.models\.trading import', 'from app.models.trading import'),
        
        # 修复schemas导入
        (r'from app\.schemas\.trading import \((.*?)\)', 
         lambda m: 'from app.schemas.trading import (\n' + m.group(1) + '\n)'),
         
        # 添加缺失的导入
        (r'from app\.core\.dependencies import', 'from app.core.dependencies_fixed import'),
    ]
    
    for pattern, replacement in replacements:
        if callable(replacement):
            content = re.sub(pattern, replacement, content, flags=re.DOTALL | re.MULTILINE)
        else:
            content = re.sub(pattern, replacement, content)
    
    # 只有内容发生变化时才写入
    if content != original_content:
        file_path.write_text(content, encoding='utf-8')
        print(f"Fixed imports in: {file_path}")


def main():
    """主函数"""
    backend_dir = Path(__file__).parent
    
    # 需要修复的文件模式
    patterns = [
        'app/api/v1/*.py',
        'app/services/*.py',
        'app/core/*.py'
    ]
    
    for pattern in patterns:
        for file_path in backend_dir.glob(pattern):
            try:
                fix_imports_in_file(file_path)
            except Exception as e:
                print(f"Error fixing {file_path}: {e}")
    
    print("Import fixes completed!")


if __name__ == "__main__":
    main()