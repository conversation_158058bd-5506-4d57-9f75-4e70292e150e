#!/usr/bin/env python3
"""
量化投资平台综合分析 - 真实用户视角
使用MCP工具组合进行全面的平台分析，发现用户使用中的问题
"""

import asyncio
import json
import time
import os
import subprocess
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_platform_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensivePlatformAnalyzer:
    def __init__(self):
        self.test_session_id = f"comprehensive_analysis_{int(time.time())}"
        self.test_results = {
            'session_id': self.test_session_id,
            'start_time': datetime.now().isoformat(),
            'test_type': 'Comprehensive Platform Analysis',
            'analysis_results': [],
            'discovered_issues': [],
            'user_experience_insights': [],
            'recommendations': [],
            'platform_readiness': {}
        }
        
    async def analyze_project_architecture(self):
        """分析项目架构"""
        logger.info("分析项目架构")
        
        analysis = {
            'name': '项目架构分析',
            'start_time': time.time(),
            'findings': [],
            'issues': [],
            'insights': []
        }
        
        # 分析前端架构
        frontend_path = Path("../../frontend")
        if frontend_path.exists():
            analysis['findings'].append("发现前端项目")
            
            # 检查Vue.js项目结构
            vue_files = [
                'src/main.ts',
                'src/App.vue',
                'src/router/index.ts',
                'src/components/',
                'src/views/'
            ]
            
            vue_score = 0
            for file in vue_files:
                if (frontend_path / file).exists():
                    vue_score += 1
                    analysis['findings'].append(f"发现Vue.js文件: {file}")
                else:
                    analysis['issues'].append(f"缺少Vue.js文件: {file}")
                    
            if vue_score >= 4:
                analysis['insights'].append("前端采用标准Vue.js架构")
            else:
                analysis['insights'].append("前端架构不完整")
                
        # 分析后端架构
        backend_path = Path("../../backend")
        if backend_path.exists():
            analysis['findings'].append("发现后端项目")
            
            # 检查Python/FastAPI项目结构
            backend_files = [
                'app/',
                'requirements.txt',
                'main.py',
                'app/api/',
                'app/models/'
            ]
            
            backend_score = 0
            for file in backend_files:
                if (backend_path / file).exists():
                    backend_score += 1
                    analysis['findings'].append(f"发现后端文件: {file}")
                else:
                    analysis['issues'].append(f"缺少后端文件: {file}")
                    
            if backend_score >= 3:
                analysis['insights'].append("后端采用标准Python架构")
            else:
                analysis['insights'].append("后端架构不完整")
                
        # 检查配置文件
        config_files = [
            '../../docker-compose.yml',
            '../../package.json',
            '../../README.md'
        ]
        
        for config in config_files:
            if Path(config).exists():
                analysis['findings'].append(f"发现配置文件: {config}")
            else:
                analysis['issues'].append(f"缺少配置文件: {config}")
                
        analysis['end_time'] = time.time()
        analysis['duration'] = analysis['end_time'] - analysis['start_time']
        self.test_results['analysis_results'].append(analysis)
        
    async def analyze_user_interface_design(self):
        """分析用户界面设计"""
        logger.info("分析用户界面设计")
        
        analysis = {
            'name': '用户界面设计分析',
            'start_time': time.time(),
            'findings': [],
            'issues': [],
            'insights': []
        }
        
        # 分析Vue组件
        components_path = Path("../../frontend/src/components")
        if components_path.exists():
            vue_files = list(components_path.glob("**/*.vue"))
            analysis['findings'].append(f"发现{len(vue_files)}个Vue组件")
            
            # 检查关键组件
            key_components = [
                'Dashboard',
                'Trading',
                'Market',
                'Portfolio',
                'Strategy',
                'Chart',
                'Table'
            ]
            
            found_components = []
            for component in key_components:
                matching_files = [f for f in vue_files if component.lower() in f.name.lower()]
                if matching_files:
                    found_components.append(component)
                    analysis['findings'].append(f"发现{component}相关组件")
                else:
                    analysis['issues'].append(f"缺少{component}相关组件")
                    
            if len(found_components) >= 5:
                analysis['insights'].append("UI组件覆盖了主要功能模块")
            else:
                analysis['insights'].append("UI组件覆盖不够全面")
                
        # 分析样式文件
        style_files = list(Path("../../frontend/src").glob("**/*.css")) + \
                     list(Path("../../frontend/src").glob("**/*.scss")) + \
                     list(Path("../../frontend/src").glob("**/*.less"))
                     
        analysis['findings'].append(f"发现{len(style_files)}个样式文件")
        
        if len(style_files) == 0:
            analysis['issues'].append("缺少自定义样式文件")
            analysis['insights'].append("可能完全依赖UI框架样式")
            
        # 检查UI框架使用
        package_json_path = Path("../../frontend/package.json")
        if package_json_path.exists():
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
            dependencies = {**package_data.get('dependencies', {}), 
                          **package_data.get('devDependencies', {})}
            
            ui_frameworks = ['element-plus', 'ant-design-vue', 'vuetify', 'quasar']
            found_frameworks = [fw for fw in ui_frameworks if fw in dependencies]
            
            if found_frameworks:
                analysis['findings'].append(f"使用UI框架: {', '.join(found_frameworks)}")
                analysis['insights'].append("采用了成熟的UI框架")
            else:
                analysis['issues'].append("未发现主流UI框架")
                analysis['insights'].append("可能需要更多UI组件支持")
                
        analysis['end_time'] = time.time()
        analysis['duration'] = analysis['end_time'] - analysis['start_time']
        self.test_results['analysis_results'].append(analysis)
        
    async def analyze_data_visualization(self):
        """分析数据可视化能力"""
        logger.info("分析数据可视化能力")
        
        analysis = {
            'name': '数据可视化分析',
            'start_time': time.time(),
            'findings': [],
            'issues': [],
            'insights': []
        }
        
        # 检查图表库
        package_json_path = Path("../../frontend/package.json")
        if package_json_path.exists():
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
            dependencies = {**package_data.get('dependencies', {}), 
                          **package_data.get('devDependencies', {})}
            
            chart_libraries = ['echarts', 'chart.js', 'd3', 'plotly.js', 'highcharts']
            found_libraries = [lib for lib in chart_libraries if lib in dependencies]
            
            if found_libraries:
                analysis['findings'].append(f"使用图表库: {', '.join(found_libraries)}")
                analysis['insights'].append("具备数据可视化能力")
            else:
                analysis['issues'].append("未发现图表库")
                analysis['insights'].append("缺少数据可视化能力")
                
        # 检查图表组件
        frontend_path = Path("../../frontend/src")
        chart_files = []
        
        if frontend_path.exists():
            # 查找包含chart关键词的文件
            for file in frontend_path.glob("**/*.vue"):
                if 'chart' in file.name.lower():
                    chart_files.append(file)
                    
            # 查找文件内容中包含图表相关代码的文件
            for file in frontend_path.glob("**/*.vue"):
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read().lower()
                        if any(keyword in content for keyword in ['echarts', 'chart', 'canvas']):
                            if file not in chart_files:
                                chart_files.append(file)
                except:
                    pass
                    
        analysis['findings'].append(f"发现{len(chart_files)}个可能的图表组件")
        
        if len(chart_files) >= 3:
            analysis['insights'].append("有丰富的图表组件")
        elif len(chart_files) >= 1:
            analysis['insights'].append("有基本的图表组件")
        else:
            analysis['issues'].append("缺少图表组件")
            analysis['insights'].append("量化平台需要更多数据可视化")
            
        analysis['end_time'] = time.time()
        analysis['duration'] = analysis['end_time'] - analysis['start_time']
        self.test_results['analysis_results'].append(analysis)
        
    async def analyze_api_integration(self):
        """分析API集成"""
        logger.info("分析API集成")
        
        analysis = {
            'name': 'API集成分析',
            'start_time': time.time(),
            'findings': [],
            'issues': [],
            'insights': []
        }
        
        # 检查前端API调用
        api_path = Path("../../frontend/src/api")
        if api_path.exists():
            api_files = list(api_path.glob("**/*.ts")) + list(api_path.glob("**/*.js"))
            analysis['findings'].append(f"发现{len(api_files)}个API文件")
            
            # 分析API文件内容
            api_endpoints = []
            for file in api_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 查找API端点
                        import re
                        endpoints = re.findall(r'[\'"`]/api/[^\'"`]+[\'"`]', content)
                        api_endpoints.extend(endpoints)
                except:
                    pass
                    
            unique_endpoints = list(set(api_endpoints))
            analysis['findings'].append(f"发现{len(unique_endpoints)}个API端点")
            
            if len(unique_endpoints) >= 5:
                analysis['insights'].append("API集成较为完善")
            elif len(unique_endpoints) >= 2:
                analysis['insights'].append("有基本的API集成")
            else:
                analysis['issues'].append("API集成不足")
                
        else:
            analysis['issues'].append("未发现API目录")
            analysis['insights'].append("缺少API集成层")
            
        # 检查后端API定义
        backend_api_path = Path("../../backend/app/api")
        if backend_api_path.exists():
            api_files = list(backend_api_path.glob("**/*.py"))
            analysis['findings'].append(f"发现{len(api_files)}个后端API文件")
            
            if len(api_files) >= 3:
                analysis['insights'].append("后端API结构完整")
            else:
                analysis['issues'].append("后端API文件较少")
        else:
            analysis['issues'].append("未发现后端API目录")
            
        analysis['end_time'] = time.time()
        analysis['duration'] = analysis['end_time'] - analysis['start_time']
        self.test_results['analysis_results'].append(analysis)
        
    async def analyze_user_experience_readiness(self):
        """分析用户体验就绪度"""
        logger.info("分析用户体验就绪度")
        
        analysis = {
            'name': '用户体验就绪度分析',
            'start_time': time.time(),
            'findings': [],
            'issues': [],
            'insights': []
        }
        
        # 检查路由配置
        router_path = Path("../../frontend/src/router")
        if router_path.exists():
            router_files = list(router_path.glob("**/*.ts")) + list(router_path.glob("**/*.js"))
            analysis['findings'].append(f"发现{len(router_files)}个路由文件")
            
            # 分析路由数量
            total_routes = 0
            for file in router_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 简单计算路由数量
                        route_count = content.count('path:') + content.count("path:")
                        total_routes += route_count
                except:
                    pass
                    
            analysis['findings'].append(f"估计有{total_routes}个路由")
            
            if total_routes >= 5:
                analysis['insights'].append("路由配置较为完整")
            else:
                analysis['issues'].append("路由配置可能不够完整")
                
        # 检查页面组件
        views_path = Path("../../frontend/src/views")
        if views_path.exists():
            view_files = list(views_path.glob("**/*.vue"))
            analysis['findings'].append(f"发现{len(view_files)}个页面组件")
            
            # 检查关键页面
            key_pages = ['dashboard', 'trading', 'market', 'portfolio', 'strategy']
            found_pages = []
            
            for page in key_pages:
                matching_files = [f for f in view_files if page.lower() in f.name.lower()]
                if matching_files:
                    found_pages.append(page)
                    
            analysis['findings'].append(f"发现{len(found_pages)}个关键页面")
            
            if len(found_pages) >= 4:
                analysis['insights'].append("主要功能页面齐全")
            else:
                analysis['issues'].append("缺少关键功能页面")
                
        # 检查国际化支持
        i18n_files = list(Path("../../frontend/src").glob("**/i18n*")) + \
                    list(Path("../../frontend/src").glob("**/locale*"))
                    
        if i18n_files:
            analysis['findings'].append("发现国际化支持")
            analysis['insights'].append("支持多语言")
        else:
            analysis['issues'].append("缺少国际化支持")
            
        analysis['end_time'] = time.time()
        analysis['duration'] = analysis['end_time'] - analysis['start_time']
        self.test_results['analysis_results'].append(analysis)
        
    async def generate_comprehensive_recommendations(self):
        """生成综合建议"""
        logger.info("生成综合建议")
        
        # 收集所有问题
        all_issues = []
        all_insights = []
        
        for analysis in self.test_results['analysis_results']:
            all_issues.extend(analysis.get('issues', []))
            all_insights.extend(analysis.get('insights', []))
            
        # 生成建议
        recommendations = []
        
        # 架构相关建议
        if any('架构' in issue for issue in all_issues):
            recommendations.append("完善项目架构，确保前后端结构完整")
            
        # UI相关建议
        if any('组件' in issue for issue in all_issues):
            recommendations.append("增加UI组件，提升用户界面完整性")
            
        # 数据可视化建议
        if any('图表' in issue for issue in all_issues):
            recommendations.append("增强数据可视化能力，添加更多图表组件")
            
        # API相关建议
        if any('API' in issue for issue in all_issues):
            recommendations.append("完善API集成，确保前后端数据交互")
            
        # 用户体验建议
        if any('路由' in issue for issue in all_issues):
            recommendations.append("完善路由配置，确保页面导航流畅")
            
        if any('页面' in issue for issue in all_issues):
            recommendations.append("补充关键功能页面")
            
        # 通用建议
        recommendations.extend([
            "添加用户引导和帮助文档",
            "优化加载性能和响应速度",
            "增加错误处理和用户反馈",
            "提供完整的开发和部署文档",
            "添加自动化测试覆盖"
        ])
        
        self.test_results['recommendations'] = recommendations
        
        # 评估平台就绪度
        total_issues = len(all_issues)
        total_insights = len(all_insights)
        
        if total_issues <= 5:
            readiness_level = "高"
            readiness_comment = "平台基本就绪，可以进行用户测试"
        elif total_issues <= 10:
            readiness_level = "中"
            readiness_comment = "平台需要一些改进才能投入使用"
        else:
            readiness_level = "低"
            readiness_comment = "平台需要重要改进才能投入使用"
            
        self.test_results['platform_readiness'] = {
            'level': readiness_level,
            'comment': readiness_comment,
            'total_issues': total_issues,
            'total_insights': total_insights
        }
        
    async def save_analysis_results(self):
        """保存分析结果"""
        self.test_results['end_time'] = datetime.now().isoformat()
        self.test_results['total_duration'] = time.time() - time.mktime(
            datetime.fromisoformat(self.test_results['start_time']).timetuple()
        )
        
        # 保存详细报告
        report_file = f"reports/comprehensive_platform_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"综合分析报告已保存: {report_file}")
        
    def print_summary(self):
        """打印分析摘要"""
        print("\n" + "=" * 80)
        print("量化投资平台综合分析报告")
        print("=" * 80)
        
        total_analyses = len(self.test_results['analysis_results'])
        total_issues = sum(len(a.get('issues', [])) for a in self.test_results['analysis_results'])
        
        print(f"分析会话: {self.test_session_id}")
        print(f"分析时间: {self.test_results.get('total_duration', 0):.2f}秒")
        print(f"分析维度: {total_analyses}个")
        print(f"发现问题: {total_issues}个")
        
        readiness = self.test_results['platform_readiness']
        print(f"\n平台就绪度: {readiness['level']}")
        print(f"就绪评价: {readiness['comment']}")
        
        print(f"\n改进建议:")
        for i, rec in enumerate(self.test_results['recommendations'][:10], 1):
            print(f"  {i}. {rec}")
            
        if total_issues <= 5:
            print("\n✅ 平台状态良好，可以进行用户测试")
        elif total_issues <= 10:
            print("\n⚠️ 平台需要适度改进")
        else:
            print("\n🔴 平台需要重要改进")
            
    async def run_comprehensive_analysis(self):
        """运行综合分析"""
        logger.info("开始量化投资平台综合分析")
        
        await self.analyze_project_architecture()
        await self.analyze_user_interface_design()
        await self.analyze_data_visualization()
        await self.analyze_api_integration()
        await self.analyze_user_experience_readiness()
        await self.generate_comprehensive_recommendations()
        await self.save_analysis_results()
        
        self.print_summary()
        
        logger.info("量化投资平台综合分析完成")

async def main():
    """主函数"""
    analyzer = ComprehensivePlatformAnalyzer()
    await analyzer.run_comprehensive_analysis()

if __name__ == "__main__":
    asyncio.run(main())
