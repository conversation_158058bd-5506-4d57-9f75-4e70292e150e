#!/usr/bin/env python3
"""
测试策略文件导入API
"""

import requests
import json
from urllib.parse import quote

def test_strategy_import():
    """测试策略文件导入API"""

    # 测试URL - 使用实际存在的文件，进行URL编码
    filename = "2024 (1).txt"
    encoded_filename = quote(filename)
    url = f"http://localhost:8000/api/v1/strategy-files/2024/{encoded_filename}/import"

    # 请求数据
    data = {
        "strategy_name": "测试导入策略"
    }
    
    try:
        print(f"🔍 测试API: {url}")
        print(f"📤 请求数据: {data}")
        
        # 发送POST请求
        response = requests.post(url, json=data, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败!")
            print(f"📋 错误内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 后端服务可能未启动")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_strategy_files_list():
    """测试获取策略文件列表API"""
    
    url = "http://localhost:8000/api/v1/strategy-files/2024"
    
    try:
        print(f"\n🔍 测试API: {url}")
        
        response = requests.get(url, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📋 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 请求失败!")
            print(f"📋 错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_health_check():
    """测试健康检查API"""
    
    url = "http://localhost:8000/health"
    
    try:
        print(f"\n🔍 测试健康检查: {url}")
        
        response = requests.get(url, timeout=5)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务健康!")
            print(f"📋 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 服务异常!")
            print(f"📋 错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")

if __name__ == "__main__":
    print("🚀 开始API测试...")
    
    # 1. 健康检查
    test_health_check()
    
    # 2. 测试文件列表
    test_strategy_files_list()
    
    # 3. 测试导入功能
    test_strategy_import()
    
    print("\n✅ API测试完成!")
