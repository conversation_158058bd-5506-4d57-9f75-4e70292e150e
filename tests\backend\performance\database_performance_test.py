"""
数据库性能测试套件
验证数据库优化效果，包括负载测试、并发测试和性能基准测试
"""

import asyncio
import json
import logging
import random
import statistics
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import pytest
from sqlalchemy import select, text, func, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import db_manager
from app.core.advanced_query_optimizer import query_optimizer
from app.core.async_query_executor import async_query_executor, QueryPriority
from app.core.smart_cache_manager import smart_cache_manager
from app.core.connection_pool_optimizer import pool_optimizer
from app.db.models.market import MarketData, KLineData, Symbol
from app.db.models.trading import Order, Trade, Position
from app.db.models.user import User

logger = logging.getLogger(__name__)


@dataclass
class PerformanceTestResult:
    """性能测试结果"""
    test_name: str
    total_operations: int
    success_count: int
    error_count: int
    total_time: float
    avg_time: float
    min_time: float
    max_time: float
    p95_time: float
    p99_time: float
    throughput: float  # 每秒操作数
    concurrent_users: int
    error_rate: float


class DatabasePerformanceTest:
    """数据库性能测试"""
    
    def __init__(self):
        self.test_results: List[PerformanceTestResult] = []
        self.setup_complete = False
    
    async def setup_test_data(self, num_users: int = 100, num_symbols: int = 50):
        """设置测试数据"""
        if self.setup_complete:
            return
        
        async with db_manager.get_session() as session:
            try:
                # 创建测试用户
                users = []
                for i in range(num_users):
                    user = User(
                        username=f"test_user_{i}",
                        email=f"test_{i}@example.com",
                        hashed_password="hashed_password"
                    )
                    users.append(user)
                    session.add(user)
                
                await session.flush()  # 获取用户ID
                
                # 创建测试股票
                symbols = []
                for i in range(num_symbols):
                    symbol = Symbol(
                        code=f"00000{i:02d}",
                        name=f"测试股票{i}",
                        market_type="stock",
                        exchange="SSE"
                    )
                    symbols.append(symbol)
                    session.add(symbol)
                
                await session.flush()
                
                # 创建测试市场数据
                market_data_list = []
                base_time = datetime.now() - timedelta(days=30)
                
                for symbol in symbols[:10]:  # 只为前10个股票创建市场数据
                    for day in range(30):
                        for hour in range(8):  # 每天8个小时的数据
                            timestamp = base_time + timedelta(days=day, hours=hour)
                            market_data = MarketData(
                                symbol_code=symbol.code,
                                last_price=random.uniform(10.0, 100.0),
                                open_price=random.uniform(10.0, 100.0),
                                high_price=random.uniform(10.0, 100.0),
                                low_price=random.uniform(10.0, 100.0),
                                volume=random.randint(1000, 100000),
                                turnover=random.uniform(100000, 10000000),
                                trading_date=timestamp.date(),
                                timestamp=timestamp
                            )
                            market_data_list.append(market_data)
                            session.add(market_data)
                
                # 创建测试订单
                orders = []
                for user in users[:20]:  # 只为前20个用户创建订单
                    for i in range(10):  # 每个用户10个订单
                        order = Order(
                            user_id=user.id,
                            order_id=f"ORD_{user.id}_{i}",
                            symbol_code=random.choice(symbols[:10]).code,
                            order_type="limit",
                            side="buy" if random.random() > 0.5 else "sell",
                            quantity=random.randint(100, 1000),
                            price=random.uniform(10.0, 100.0),
                            status="filled" if random.random() > 0.3 else "submitted"
                        )
                        orders.append(order)
                        session.add(order)
                
                await session.commit()
                logger.info(f"测试数据创建完成: {num_users}个用户, {num_symbols}个股票, {len(market_data_list)}条市场数据, {len(orders)}个订单")
                self.setup_complete = True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"创建测试数据失败: {e}")
                raise
    
    async def test_basic_query_performance(self, iterations: int = 1000) -> PerformanceTestResult:
        """基础查询性能测试"""
        test_name = "basic_query_performance"
        execution_times = []
        error_count = 0
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                query_start = time.time()
                
                async with db_manager.get_session() as session:
                    # 简单查询
                    result = await session.execute(
                        select(MarketData).where(
                            MarketData.symbol_code == "000001"
                        ).limit(10)
                    )
                    rows = result.scalars().all()
                
                execution_time = time.time() - query_start
                execution_times.append(execution_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"查询失败: {e}")
        
        total_time = time.time() - start_time
        
        return self._create_result(
            test_name, iterations, len(execution_times), error_count,
            total_time, execution_times, 1
        )
    
    async def test_complex_query_performance(self, iterations: int = 500) -> PerformanceTestResult:
        """复杂查询性能测试"""
        test_name = "complex_query_performance"
        execution_times = []
        error_count = 0
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                query_start = time.time()
                
                async with db_manager.get_session() as session:
                    # 复杂关联查询
                    result = await session.execute(
                        select(Order, User.username).join(
                            User, Order.user_id == User.id
                        ).where(
                            and_(
                                Order.status == "filled",
                                Order.created_at >= datetime.now() - timedelta(days=7)
                            )
                        ).order_by(Order.created_at.desc()).limit(50)
                    )
                    rows = result.fetchall()
                
                execution_time = time.time() - query_start
                execution_times.append(execution_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"复杂查询失败: {e}")
        
        total_time = time.time() - start_time
        
        return self._create_result(
            test_name, iterations, len(execution_times), error_count,
            total_time, execution_times, 1
        )
    
    async def test_concurrent_query_performance(
        self, 
        concurrent_users: int = 20, 
        queries_per_user: int = 50
    ) -> PerformanceTestResult:
        """并发查询性能测试"""
        test_name = f"concurrent_query_performance_{concurrent_users}_users"
        all_execution_times = []
        total_error_count = 0
        
        async def user_workload(user_id: int):
            """单个用户的工作负载"""
            execution_times = []
            error_count = 0
            
            for i in range(queries_per_user):
                try:
                    query_start = time.time()
                    
                    async with db_manager.get_session() as session:
                        # 模拟用户查询订单
                        result = await session.execute(
                            select(Order).where(
                                Order.user_id == user_id % 20 + 1  # 循环使用用户ID
                            ).limit(10)
                        )
                        rows = result.scalars().all()
                    
                    execution_time = time.time() - query_start
                    execution_times.append(execution_time)
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"用户 {user_id} 查询失败: {e}")
            
            return execution_times, error_count
        
        start_time = time.time()
        
        # 并发执行用户工作负载
        tasks = [user_workload(i) for i in range(concurrent_users)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 收集结果
        for result in results:
            if isinstance(result, Exception):
                total_error_count += queries_per_user
            else:
                execution_times, error_count = result
                all_execution_times.extend(execution_times)
                total_error_count += error_count
        
        total_operations = concurrent_users * queries_per_user
        
        return self._create_result(
            test_name, total_operations, len(all_execution_times), total_error_count,
            total_time, all_execution_times, concurrent_users
        )
    
    async def test_optimized_query_performance(self, iterations: int = 500) -> PerformanceTestResult:
        """优化查询性能测试"""
        test_name = "optimized_query_performance"
        execution_times = []
        error_count = 0
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                query_start = time.time()
                
                # 使用优化的查询
                result = await query_optimizer.execute_optimized_query(
                    None,  # 这里需要传入session，但为了测试简化处理
                    'user_orders',
                    user_ids=[1, 2, 3],
                    limit=10
                )
                
                execution_time = time.time() - query_start
                execution_times.append(execution_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"优化查询失败: {e}")
        
        total_time = time.time() - start_time
        
        return self._create_result(
            test_name, iterations, len(execution_times), error_count,
            total_time, execution_times, 1
        )
    
    async def test_cache_performance(self, iterations: int = 1000) -> PerformanceTestResult:
        """缓存性能测试"""
        test_name = "cache_performance"
        execution_times = []
        error_count = 0
        
        # 预热缓存
        test_key = "test_cache_key"
        test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                query_start = time.time()
                
                if i % 10 == 0:
                    # 每10次操作中有1次是设置缓存
                    await smart_cache_manager.set_smart(
                        'test_data',
                        {'key': f'{test_key}_{i}'},
                        test_data
                    )
                else:
                    # 其他是获取缓存
                    result = await smart_cache_manager.get_smart(
                        'test_data',
                        {'key': f'{test_key}_{i - (i % 10)}'}
                    )
                
                execution_time = time.time() - query_start
                execution_times.append(execution_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"缓存操作失败: {e}")
        
        total_time = time.time() - start_time
        
        return self._create_result(
            test_name, iterations, len(execution_times), error_count,
            total_time, execution_times, 1
        )
    
    async def test_async_executor_performance(
        self, 
        total_queries: int = 1000,
        concurrent_submits: int = 50
    ) -> PerformanceTestResult:
        """异步执行器性能测试"""
        test_name = "async_executor_performance"
        
        # 启动异步执行器
        await async_query_executor.start()
        
        try:
            execution_times = []
            error_count = 0
            
            start_time = time.time()
            
            # 批量提交查询
            task_ids = []
            for i in range(total_queries):
                try:
                    task_id = await async_query_executor.submit_query(
                        query="SELECT 1 as test_value",
                        priority=QueryPriority.NORMAL
                    )
                    task_ids.append(task_id)
                except Exception as e:
                    error_count += 1
                    logger.error(f"提交查询失败: {e}")
            
            # 获取所有结果
            for task_id in task_ids:
                try:
                    query_start = time.time()
                    result = await async_query_executor.get_result(task_id, timeout=30.0)
                    execution_time = time.time() - query_start
                    execution_times.append(execution_time)
                except Exception as e:
                    error_count += 1
                    logger.error(f"获取结果失败: {e}")
            
            total_time = time.time() - start_time
            
            return self._create_result(
                test_name, total_queries, len(execution_times), error_count,
                total_time, execution_times, concurrent_submits
            )
        
        finally:
            await async_query_executor.stop()
    
    def _create_result(
        self,
        test_name: str,
        total_operations: int,
        success_count: int,
        error_count: int,
        total_time: float,
        execution_times: List[float],
        concurrent_users: int
    ) -> PerformanceTestResult:
        """创建性能测试结果"""
        if not execution_times:
            execution_times = [0.0]
        
        avg_time = statistics.mean(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)
        
        # 计算百分位数
        sorted_times = sorted(execution_times)
        p95_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0.0
        p99_time = sorted_times[int(len(sorted_times) * 0.99)] if sorted_times else 0.0
        
        throughput = success_count / max(total_time, 0.001)
        error_rate = error_count / max(total_operations, 1)
        
        result = PerformanceTestResult(
            test_name=test_name,
            total_operations=total_operations,
            success_count=success_count,
            error_count=error_count,
            total_time=total_time,
            avg_time=avg_time,
            min_time=min_time,
            max_time=max_time,
            p95_time=p95_time,
            p99_time=p99_time,
            throughput=throughput,
            concurrent_users=concurrent_users,
            error_rate=error_rate
        )
        
        self.test_results.append(result)
        return result
    
    async def run_full_performance_suite(self) -> Dict[str, Any]:
        """运行完整的性能测试套件"""
        logger.info("开始数据库性能测试套件...")
        
        # 设置测试数据
        await self.setup_test_data()
        
        # 执行所有测试
        tests = [
            self.test_basic_query_performance(1000),
            self.test_complex_query_performance(300),
            self.test_concurrent_query_performance(10, 30),
            self.test_concurrent_query_performance(20, 30),
            self.test_cache_performance(500),
            self.test_async_executor_performance(500, 25)
        ]
        
        results = []
        for test in tests:
            try:
                result = await test
                results.append(result)
                logger.info(f"测试完成: {result.test_name}, 吞吐量: {result.throughput:.2f} ops/s")
            except Exception as e:
                logger.error(f"测试失败: {e}")
        
        # 生成综合报告
        report = self._generate_comprehensive_report(results)
        logger.info("数据库性能测试套件完成")
        
        return report
    
    def _generate_comprehensive_report(self, results: List[PerformanceTestResult]) -> Dict[str, Any]:
        """生成综合性能报告"""
        if not results:
            return {"error": "没有测试结果"}
        
        # 总体统计
        total_operations = sum(r.total_operations for r in results)
        total_success = sum(r.success_count for r in results)
        total_errors = sum(r.error_count for r in results)
        total_time = sum(r.total_time for r in results)
        
        # 性能指标统计
        avg_throughput = statistics.mean([r.throughput for r in results])
        avg_response_time = statistics.mean([r.avg_time for r in results])
        max_response_time = max([r.max_time for r in results])
        avg_error_rate = statistics.mean([r.error_rate for r in results])
        
        # 性能评级
        performance_grade = self._calculate_performance_grade(results)
        
        # 优化建议
        recommendations = self._generate_optimization_recommendations(results)
        
        return {
            'summary': {
                'total_tests': len(results),
                'total_operations': total_operations,
                'total_success': total_success,
                'total_errors': total_errors,
                'overall_success_rate': total_success / max(total_operations, 1),
                'total_test_time': total_time,
                'avg_throughput': avg_throughput,
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'avg_error_rate': avg_error_rate,
                'performance_grade': performance_grade
            },
            'test_results': [asdict(result) for result in results],
            'optimization_recommendations': recommendations,
            'report_generated_at': datetime.now().isoformat()
        }
    
    def _calculate_performance_grade(self, results: List[PerformanceTestResult]) -> str:
        """计算性能评级"""
        # 简单的评级算法
        avg_throughput = statistics.mean([r.throughput for r in results])
        avg_error_rate = statistics.mean([r.error_rate for r in results])
        avg_response_time = statistics.mean([r.avg_time for r in results])
        
        score = 100
        
        # 吞吐量评分
        if avg_throughput > 1000:
            score += 20
        elif avg_throughput > 500:
            score += 10
        elif avg_throughput < 100:
            score -= 20
        
        # 错误率评分
        if avg_error_rate > 0.05:
            score -= 30
        elif avg_error_rate > 0.01:
            score -= 10
        
        # 响应时间评分
        if avg_response_time > 1.0:
            score -= 20
        elif avg_response_time > 0.5:
            score -= 10
        elif avg_response_time < 0.1:
            score += 10
        
        if score >= 120:
            return "A+"
        elif score >= 100:
            return "A"
        elif score >= 80:
            return "B"
        elif score >= 60:
            return "C"
        else:
            return "D"
    
    def _generate_optimization_recommendations(self, results: List[PerformanceTestResult]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 分析各项指标
        high_error_tests = [r for r in results if r.error_rate > 0.05]
        slow_tests = [r for r in results if r.avg_time > 0.5]
        low_throughput_tests = [r for r in results if r.throughput < 100]
        
        if high_error_tests:
            recommendations.append(
                f"错误率较高的测试: {', '.join([t.test_name for t in high_error_tests])}，"
                "建议检查查询逻辑和数据库连接稳定性"
            )
        
        if slow_tests:
            recommendations.append(
                f"响应时间较慢的测试: {', '.join([t.test_name for t in slow_tests])}，"
                "建议优化查询语句和添加索引"
            )
        
        if low_throughput_tests:
            recommendations.append(
                f"吞吐量较低的测试: {', '.join([t.test_name for t in low_throughput_tests])}，"
                "建议增加连接池大小或优化并发处理"
            )
        
        # 并发性能分析
        concurrent_tests = [r for r in results if r.concurrent_users > 1]
        if concurrent_tests:
            avg_concurrent_throughput = statistics.mean([t.throughput for t in concurrent_tests])
            single_thread_tests = [r for r in results if r.concurrent_users == 1]
            if single_thread_tests:
                avg_single_throughput = statistics.mean([t.throughput for t in single_thread_tests])
                if avg_concurrent_throughput < avg_single_throughput * 0.5:
                    recommendations.append("并发性能较差，建议优化连接池配置和查询并发处理")
        
        if not recommendations:
            recommendations.append("性能表现良好，建议继续监控和定期测试")
        
        return recommendations
    
    async def export_performance_report(self, filepath: str):
        """导出性能报告"""
        if not self.test_results:
            await self.run_full_performance_suite()
        
        report = self._generate_comprehensive_report(self.test_results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"性能报告已导出到: {filepath}")


# 便捷测试函数
async def run_quick_performance_test() -> Dict[str, Any]:
    """快速性能测试"""
    test_suite = DatabasePerformanceTest()
    
    # 只运行关键测试
    await test_suite.setup_test_data(50, 20)
    
    results = []
    
    # 基础查询测试
    result = await test_suite.test_basic_query_performance(100)
    results.append(result)
    
    # 并发测试
    result = await test_suite.test_concurrent_query_performance(5, 20)
    results.append(result)
    
    # 缓存测试
    result = await test_suite.test_cache_performance(100)
    results.append(result)
    
    return test_suite._generate_comprehensive_report(results)


async def run_stress_test(duration_minutes: int = 10) -> Dict[str, Any]:
    """压力测试"""
    test_suite = DatabasePerformanceTest()
    await test_suite.setup_test_data()
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    stress_results = []
    iteration = 0
    
    while time.time() < end_time:
        iteration += 1
        logger.info(f"压力测试迭代 {iteration}")
        
        # 随机选择测试类型
        test_type = random.choice([
            'basic_query',
            'complex_query',
            'concurrent_query'
        ])
        
        try:
            if test_type == 'basic_query':
                result = await test_suite.test_basic_query_performance(50)
            elif test_type == 'complex_query':
                result = await test_suite.test_complex_query_performance(25)
            else:
                result = await test_suite.test_concurrent_query_performance(5, 10)
            
            stress_results.append(result)
            
        except Exception as e:
            logger.error(f"压力测试迭代 {iteration} 失败: {e}")
        
        # 短暂休息
        await asyncio.sleep(1)
    
    return test_suite._generate_comprehensive_report(stress_results)


if __name__ == "__main__":
    async def main():
        """主测试函数"""
        print("开始数据库性能测试...")
        
        # 初始化数据库
        db_manager.initialize()
        await db_manager.create_tables()
        
        # 运行性能测试套件
        test_suite = DatabasePerformanceTest()
        report = await test_suite.run_full_performance_suite()
        
        print(f"\n性能测试完成!")
        print(f"总体评级: {report['summary']['performance_grade']}")
        print(f"平均吞吐量: {report['summary']['avg_throughput']:.2f} ops/s")
        print(f"平均响应时间: {report['summary']['avg_response_time']:.3f}s")
        print(f"错误率: {report['summary']['avg_error_rate']:.2%}")
        
        print("\n优化建议:")
        for rec in report['optimization_recommendations']:
            print(f"- {rec}")
        
        # 导出报告
        await test_suite.export_performance_report(
            '/Users/<USER>/Desktop/quant-platform/backend/logs/performance_test_report.json'
        )
        
        print("\n性能报告已导出")
    
    asyncio.run(main())