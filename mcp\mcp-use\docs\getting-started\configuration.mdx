---
title: "Configuration Overview"
description: "Complete guide to configuring mcp_use"
icon: "cog"
---

# Configuration Overview

<Info>
mcp_use configuration is organized into two main areas: **Client Configuration** for connecting to MCP servers, and **Agent Configuration** for customizing agent behavior and LLM integration.
</Info>

## Configuration Architecture

mcp_use follows a clear separation between client-side and agent-side concerns:

<CardGroup cols={2}>
  <Card title="Client Configuration" icon="server" href="/client/client-configuration">
    **MCPClient Setup**
    - MCP server connections
    - Multi-server configurations
    - Sandboxed execution
    - Connection types
  </Card>
  <Card title="Agent Configuration" icon="brain" href="/agent/agent-configuration">
    **MCPAgent Setup**
    - API key management
    - LLM integration
    - Server manager
    - Tool access control
    - Memory and prompts
    - Adapter usage
  </Card>
</CardGroup>

## Quick Start Configuration

For a basic setup, you need both client and agent configuration:

### 1. Client Setup

```python
from mcp_use import MC<PERSON>lient

# Configure your MCP servers
config = {
    "mcpServers": {
        "playwright": {
            "command": "npx",
            "args": ["@playwright/mcp@latest"],
            "env": {"DISPLAY": ":1"}
        }
    }
}

client = MCPClient.from_dict(config)
```

### 2. Agent Setup

```python
from mcp_use import MCPAgent
from langchain_openai import ChatOpenAI

# Configure your agent with an LLM
llm = ChatOpenAI(model="gpt-4o")
agent = MCPAgent(llm=llm, client=client)
```

### 3. Basic Usage

```python
import asyncio

async def main():
    result = await agent.run("Search for information about climate change")
    print(result)

asyncio.run(main())
```

## Configuration Paths

<Steps>
  <Step title="Client Configuration">
    Set up your MCPClient to connect to MCP servers. This includes configuring server connections, managing API keys, and setting up multi-server environments.

    **Start here:** [Client Configuration Guide →](/client/client-configuration)
  </Step>

  <Step title="Agent Configuration">
    Configure your MCPAgent's behavior, including LLM integration, tool restrictions, memory settings, and custom prompts.

    **Continue with:** [Agent Configuration Guide →](/agent/agent-configuration)
  </Step>

  <Step title="Advanced Topics">
    Explore connection types, server management, and LLM integration patterns for complex use cases.

    **Learn more:** [Connection Types](/client/connection-types) | [Server Manager](/agent/server-manager) | [LLM Integration](/agent/llm-integration)
  </Step>
</Steps>

## Common Configuration Patterns

### Development Setup
```python
# Simple development configuration
from dotenv import load_dotenv
load_dotenv()

client = MCPClient.from_config_file("dev-config.json")
agent = MCPAgent(
    llm=ChatOpenAI(model="gpt-4o"),
    client=client,
    max_steps=10,
    verbose=True
)
```

### Production Setup
```python
# Production configuration with restrictions
agent = MCPAgent(
    llm=ChatOpenAI(model="gpt-4o", temperature=0.1),
    client=client,
    max_steps=30,
    disallowed_tools=["file_system", "shell"],
    use_server_manager=True,
    memory_enabled=True
)
```

### Multi-Server Setup
```python
# Complex multi-server configuration
client = MCPClient.from_config_file("multi-server.json")
agent = MCPAgent(
    llm=llm,
    client=client,
    use_server_manager=True,  # Auto-select servers
    system_prompt="You have access to web browsing, file operations, and API tools."
)
```

## What's Next?

<CardGroup cols={3}>
  <Card title="Client Configuration" icon="server" href="/client/client-configuration">
    Learn how to configure MCPClient and connect to MCP servers
  </Card>
  <Card title="Agent Configuration" icon="brain" href="/agent/agent-configuration">
    Discover how to customize MCPAgent behavior and LLM integration
  </Card>
  <Card title="Examples" icon="code" href="https://github.com/mcp-use/mcp-use/tree/main/examples">
    Explore real-world configuration examples
  </Card>
</CardGroup>

<Tip>
**New to mcp_use?** Start with the [Quickstart Guide](/quickstart) for a basic introduction, then return here for detailed configuration options.
</Tip>
