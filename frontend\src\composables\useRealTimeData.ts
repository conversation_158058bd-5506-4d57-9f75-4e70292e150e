/**
 * 实时数据流管理
 *
 * 提供统一的实时数据更新管理，包括：
 * - WebSocket连接管理
 * - 实时行情数据
 * - 账户资金变动
 * - 交易订单状态
 * - 风险监控指标
 */

import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { MarketService } from '@/services/market.service'
import { useTradingStore } from '@/stores/modules/trading'
import { useMarketStore } from '@/stores/modules/market'
import { usePortfolioStore } from '@/stores/modules/portfolio'
import { getWebSocketUrl } from '@/config/websocket'
import type { QuoteData } from '@/types/market'

interface RealTimeMetrics {
  totalAssets: number
  dailyProfit: number
  dailyProfitPercent: number
  totalProfit: number
  totalProfitPercent: number
  positionCount: number
  activeStrategies: number
  riskLevel: 'low' | 'medium' | 'high'
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  lastUpdate: Date | null
}

interface PriceFlashEffect {
  [symbol: string]: {
    direction: 'up' | 'down' | 'neutral'
    intensity: number
    timestamp: number
  }
}

export function useRealTimeData() {
  // 核心状态
  const isConnected = ref(false)
  const connectionStatus = ref<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const lastHeartbeat = ref<Date | null>(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5

  // 实时指标数据
  const realTimeMetrics = reactive<RealTimeMetrics>({
    totalAssets: 0,
    dailyProfit: 0,
    dailyProfitPercent: 0,
    totalProfit: 0,
    totalProfitPercent: 0,
    positionCount: 0,
    activeStrategies: 0,
    riskLevel: 'low',
    connectionStatus: 'disconnected',
    lastUpdate: null
  })

  // 价格闪烁效果状态
  const priceFlashEffects = reactive<PriceFlashEffect>({})

  // 实时新闻和公告
  const realtimeNews = ref<Array<{
    id: string
    title: string
    content: string
    importance: 'low' | 'medium' | 'high'
    timestamp: Date
    category: 'market' | 'trading' | 'risk' | 'system'
  }>>([])

  // Store引用
  const tradingStore = useTradingStore()
  const marketStore = useMarketStore()
  const portfolioStore = usePortfolioStore()

  // WebSocket连接
  let ws: WebSocket | null = null
  let heartbeatTimer: NodeJS.Timeout | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // 连接WebSocket
  const connect = async () => {
    if (ws?.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接，跳过重复连接')
      return
    }

    try {
      connectionStatus.value = 'connecting'

      // 使用统一的WebSocket配置
      const wsUrl = getWebSocketUrl('general')
      ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('🚀 实时数据WebSocket连接成功')
        isConnected.value = true
        connectionStatus.value = 'connected'
        reconnectAttempts.value = 0

        // 发送认证和订阅消息
        authenticate()
        subscribeToStreams()
        startHeartbeat()

        ElMessage.success('实时数据连接成功')
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('📡 WebSocket连接关闭:', event.code, event.reason)
        isConnected.value = false
        connectionStatus.value = 'disconnected'
        stopHeartbeat()

        // 自动重连（除非是主动关闭）
        if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }

      ws.onerror = (error) => {
        console.error('❌ WebSocket连接错误:', error)
        connectionStatus.value = 'error'
        ElMessage.error('实时数据连接失败')
      }

    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      connectionStatus.value = 'error'
    }
  }

  // 断开连接
  const disconnect = () => {
    if (ws) {
      ws.close(1000, '用户主动断开')
      ws = null
    }
    stopHeartbeat()
    stopReconnect()
    isConnected.value = false
    connectionStatus.value = 'disconnected'
  }

  // 认证
  const authenticate = () => {
    const token = localStorage.getItem('access_token')
    if (ws && token) {
      const authMessage = {
        type: 'auth',
        data: { token }
      }
      ws.send(JSON.stringify(authMessage))
    }
  }

  // 订阅数据流
  const subscribeToStreams = () => {
    if (!ws) return

    const subscriptions = {
      type: 'subscribe',
      data: {
        streams: [
          'account_updates',    // 账户资金变动
          'position_updates',   // 持仓变化
          'order_updates',      // 订单状态
          'market_quotes',      // 实时行情
          'risk_monitoring',    // 风险监控
          'news_feed',          // 新闻推送
          'system_notifications' // 系统通知
        ]
      }
    }

    ws.send(JSON.stringify(subscriptions))
    console.log('📊 已订阅实时数据流')
  }

  // 处理接收到的消息
  const handleMessage = (message: any) => {
    lastHeartbeat.value = new Date()

    switch (message.type) {
      case 'auth_response':
        if (message.success) {
          console.log('✅ WebSocket认证成功')
        } else {
          console.error('❌ WebSocket认证失败:', message.error)
          ElMessage.error('认证失败，请重新登录')
        }
        break

      case 'account_update':
        updateAccountMetrics(message.data)
        break

      case 'position_update':
        updatePositionData(message.data)
        break

      case 'market_quote':
        updateMarketQuote(message.data)
        break

      case 'risk_alert':
        handleRiskAlert(message.data)
        break

      case 'news_update':
        addRealtimeNews(message.data)
        break

      case 'system_notification':
        handleSystemNotification(message.data)
        break

      case 'heartbeat':
      case 'pong':
        // 心跳响应，更新最后心跳时间
        break

      default:
        console.log('收到未知消息类型:', message.type)
    }
  }

  // 更新账户指标
  const updateAccountMetrics = (data: any) => {
    Object.assign(realTimeMetrics, {
      ...data,
      lastUpdate: new Date()
    })

    // 同步更新store
    tradingStore.updateAccount(data)

    console.log('💰 账户指标已更新:', data)
  }

  // 更新持仓数据
  const updatePositionData = (data: any) => {
    realTimeMetrics.positionCount = data.count || 0
    tradingStore.updatePositions(data.positions || [])

    console.log('📈 持仓数据已更新')
  }

  // 更新市场行情
  const updateMarketQuote = (quote: QuoteData) => {
    // 计算价格变动方向用于闪烁效果
    const symbol = quote.symbol
    const previousPrice = marketStore.quotes[symbol]?.currentPrice || quote.currentPrice

    let direction: 'up' | 'down' | 'neutral' = 'neutral'
    if (quote.currentPrice > previousPrice) {
      direction = 'up'
    } else if (quote.currentPrice < previousPrice) {
      direction = 'down'
    }

    // 设置闪烁效果
    priceFlashEffects[symbol] = {
      direction,
      intensity: Math.abs(quote.currentPrice - previousPrice) / previousPrice * 100,
      timestamp: Date.now()
    }

    // 更新市场数据
    marketStore.updateQuote(quote)

    // 清除闪烁效果
    setTimeout(() => {
      if (priceFlashEffects[symbol]) {
        delete priceFlashEffects[symbol]
      }
    }, 1500)
  }

  // 处理风险预警
  const handleRiskAlert = (alert: any) => {
    realTimeMetrics.riskLevel = alert.level || 'low'

    if (alert.level === 'high') {
      ElMessage.error(`🚨 高风险预警: ${alert.message}`)
    } else if (alert.level === 'medium') {
      ElMessage.warning(`⚠️ 中等风险提醒: ${alert.message}`)
    }

    console.log('⚠️ 风险预警:', alert)
  }

  // 添加实时新闻
  const addRealtimeNews = (news: any) => {
    const newsItem = {
      id: news.id || `news_${Date.now()}`,
      title: news.title,
      content: news.content,
      importance: news.importance || 'medium',
      timestamp: new Date(news.timestamp || Date.now()),
      category: news.category || 'market'
    }

    realtimeNews.value.unshift(newsItem)

    // 保持最新50条新闻
    if (realtimeNews.value.length > 50) {
      realtimeNews.value = realtimeNews.value.slice(0, 50)
    }

    // 重要新闻弹窗提醒
    if (news.importance === 'high') {
      ElMessage.info(`📰 重要新闻: ${news.title}`)
    }
  }

  // 处理系统通知
  const handleSystemNotification = (notification: any) => {
    switch (notification.level) {
      case 'error':
        ElMessage.error(notification.message)
        break
      case 'warning':
        ElMessage.warning(notification.message)
        break
      case 'info':
        ElMessage.info(notification.message)
        break
      default:
        ElMessage.success(notification.message)
    }
  }

  // 心跳机制
  const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
      if (ws?.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // 30秒心跳
  }

  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectTimer) return

    reconnectAttempts.value++
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000)

    console.log(`🔄 ${delay/1000}秒后尝试第${reconnectAttempts.value}次重连...`)

    reconnectTimer = setTimeout(() => {
      reconnectTimer = null
      connect()
    }, delay)
  }

  const stopReconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
  }

  // 计算属性
  const connectionQuality = computed(() => {
    if (!isConnected.value) return 'poor'

    const now = Date.now()
    const lastHeartbeatTime = lastHeartbeat.value?.getTime() || 0
    const timeSinceLastHeartbeat = now - lastHeartbeatTime

    if (timeSinceLastHeartbeat < 35000) return 'excellent'
    if (timeSinceLastHeartbeat < 60000) return 'good'
    return 'poor'
  })

  // 生命周期管理
  onMounted(() => {
    connect()
  })

  onUnmounted(() => {
    disconnect()
  })

  // 返回接口
  return {
    // 状态
    isConnected,
    connectionStatus,
    connectionQuality,
    lastHeartbeat,
    reconnectAttempts,

    // 数据
    realTimeMetrics,
    priceFlashEffects,
    realtimeNews,

    // 方法
    connect,
    disconnect,

    // 工具方法
    getPriceFlashClass: (symbol: string) => {
      const effect = priceFlashEffects[symbol]
      if (!effect) return ''

      const age = Date.now() - effect.timestamp
      if (age > 1500) return ''

      const opacity = Math.max(0, 1 - age / 1500)
      return `price-flash price-flash--${effect.direction} opacity-${Math.floor(opacity * 10)}`
    }
  }
}
