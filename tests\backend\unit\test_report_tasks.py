"""
报告任务单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal
import pandas as pd
import tempfile
import os

from app.tasks.report_tasks import (
    generate_daily_report,
    generate_weekly_report,
    generate_monthly_report,
    generate_performance_report,
    generate_risk_report,
    generate_custom_report,
    send_report_email,
    cleanup_old_reports,
    ReportConfig,
    ReportType,
    ReportStatus,
    ReportGenerator,
    PerformanceAnalyzer,
    RiskAnalyzer,
    ReportEmailSender,
)


@pytest.mark.unit
@pytest.mark.report_tasks
class TestReportTasks:
    """报告任务测试类"""

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = AsyncMock()
        session.execute = AsyncMock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.close = AsyncMock()
        return session

    @pytest.fixture
    def mock_redis(self):
        """模拟Redis连接"""
        redis = AsyncMock()
        redis.get = AsyncMock(return_value=None)
        redis.set = AsyncMock()
        redis.delete = AsyncMock()
        redis.publish = AsyncMock()
        return redis

    @pytest.fixture
    def sample_trading_data(self):
        """样本交易数据"""
        return pd.DataFrame(
            {
                "date": pd.date_range("2024-01-01", periods=30, freq="D"),
                "symbol": ["rb2405"] * 30,
                "price": [3800 + i * 10 for i in range(30)],
                "volume": [100 + i * 5 for i in range(30)],
                "profit": [50 + i * 2 for i in range(30)],
                "commission": [10] * 30,
                "position": [1000 + i * 50 for i in range(30)],
            }
        )

    @pytest.fixture
    def sample_backtest_data(self):
        """样本回测数据"""
        return {
            "strategy_id": "test_strategy",
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "initial_capital": 100000,
            "final_capital": 120000,
            "total_return": 0.2,
            "sharpe_ratio": 1.5,
            "max_drawdown": 0.05,
            "win_rate": 0.65,
            "total_trades": 50,
            "daily_returns": [0.01, -0.005, 0.02, -0.01, 0.015] * 10,
        }

    @pytest.fixture
    def report_config(self):
        """报告配置"""
        return ReportConfig(
            report_type=ReportType.DAILY,
            output_format="pdf",
            include_charts=True,
            include_analysis=True,
            email_recipients=["<EMAIL>"],
            storage_path="/tmp/reports",
        )

    @pytest.fixture
    def report_generator(self):
        """报告生成器"""
        return ReportGenerator()

    @pytest.fixture
    def performance_analyzer(self):
        """绩效分析器"""
        return PerformanceAnalyzer()

    @pytest.fixture
    def risk_analyzer(self):
        """风险分析器"""
        return RiskAnalyzer()

    @pytest.fixture
    def email_sender(self):
        """邮件发送器"""
        return ReportEmailSender()

    @pytest.mark.asyncio
    async def test_generate_daily_report(
        self, mock_db_session, mock_redis, sample_trading_data
    ):
        """测试生成日报"""
        target_date = datetime(2024, 1, 15)

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = sample_trading_data.to_dict("records")
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_daily_report(target_date)

        assert result is not None
        assert "report_id" in result
        assert "status" in result
        assert result["status"] == ReportStatus.COMPLETED

        # 验证数据库查询被调用
        mock_db_session.execute.assert_called()

    @pytest.mark.asyncio
    async def test_generate_weekly_report(
        self, mock_db_session, mock_redis, sample_trading_data
    ):
        """测试生成周报"""
        target_date = datetime(2024, 1, 15)

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = sample_trading_data.to_dict("records")
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_weekly_report(target_date)

        assert result is not None
        assert "report_id" in result
        assert "status" in result
        assert result["status"] == ReportStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_generate_monthly_report(
        self, mock_db_session, mock_redis, sample_trading_data
    ):
        """测试生成月报"""
        target_date = datetime(2024, 1, 15)

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = sample_trading_data.to_dict("records")
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_monthly_report(target_date)

        assert result is not None
        assert "report_id" in result
        assert "status" in result
        assert result["status"] == ReportStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_generate_performance_report(
        self, mock_db_session, mock_redis, sample_backtest_data
    ):
        """测试生成绩效报告"""
        strategy_id = "test_strategy"
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = [sample_backtest_data]
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_performance_report(
                    strategy_id, start_date, end_date
                )

        assert result is not None
        assert "report_id" in result
        assert "status" in result
        assert result["status"] == ReportStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_generate_risk_report(
        self, mock_db_session, mock_redis, sample_trading_data
    ):
        """测试生成风险报告"""
        portfolio_id = "test_portfolio"

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = sample_trading_data.to_dict("records")
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_risk_report(portfolio_id)

        assert result is not None
        assert "report_id" in result
        assert "status" in result
        assert result["status"] == ReportStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_generate_custom_report(
        self, mock_db_session, mock_redis, report_config
    ):
        """测试生成自定义报告"""
        parameters = {
            "symbols": ["rb2405", "hc2405"],
            "start_date": "2024-01-01",
            "end_date": "2024-01-31",
            "metrics": ["return", "volatility", "sharpe_ratio"],
        }

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_custom_report(report_config, parameters)

        assert result is not None
        assert "report_id" in result
        assert "status" in result
        assert result["status"] == ReportStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_send_report_email(self, email_sender):
        """测试发送报告邮件"""
        report_data = {
            "report_id": "test_report_123",
            "title": "Daily Trading Report",
            "content": "Test report content",
            "file_path": "/tmp/test_report.pdf",
        }

        recipients = ["<EMAIL>", "<EMAIL>"]

        with patch.object(email_sender, "send_email", return_value=True) as mock_send:
            result = await send_report_email(report_data, recipients)

        assert result is True
        mock_send.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_old_reports(self, mock_db_session):
        """测试清理旧报告"""
        max_age_days = 30

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.rowcount = 5
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("os.path.exists", return_value=True):
                with patch("os.remove") as mock_remove:
                    result = await cleanup_old_reports(max_age_days)

        assert result is not None
        assert "deleted_count" in result
        assert result["deleted_count"] == 5

    def test_report_generator_create_daily_report(
        self, report_generator, sample_trading_data
    ):
        """测试报告生成器创建日报"""
        target_date = datetime(2024, 1, 15)

        with patch.object(
            report_generator, "_fetch_daily_data", return_value=sample_trading_data
        ):
            report = report_generator.create_daily_report(target_date)

        assert report is not None
        assert "summary" in report
        assert "charts" in report
        assert "analysis" in report
        assert report["date"] == target_date.date()

    def test_report_generator_create_weekly_report(
        self, report_generator, sample_trading_data
    ):
        """测试报告生成器创建周报"""
        target_date = datetime(2024, 1, 15)

        with patch.object(
            report_generator, "_fetch_weekly_data", return_value=sample_trading_data
        ):
            report = report_generator.create_weekly_report(target_date)

        assert report is not None
        assert "summary" in report
        assert "charts" in report
        assert "analysis" in report
        assert "week_start" in report
        assert "week_end" in report

    def test_report_generator_create_monthly_report(
        self, report_generator, sample_trading_data
    ):
        """测试报告生成器创建月报"""
        target_date = datetime(2024, 1, 15)

        with patch.object(
            report_generator, "_fetch_monthly_data", return_value=sample_trading_data
        ):
            report = report_generator.create_monthly_report(target_date)

        assert report is not None
        assert "summary" in report
        assert "charts" in report
        assert "analysis" in report
        assert "month" in report
        assert "year" in report

    def test_performance_analyzer_calculate_metrics(
        self, performance_analyzer, sample_backtest_data
    ):
        """测试绩效分析器计算指标"""
        daily_returns = sample_backtest_data["daily_returns"]

        metrics = performance_analyzer.calculate_metrics(daily_returns)

        assert "total_return" in metrics
        assert "annual_return" in metrics
        assert "volatility" in metrics
        assert "sharpe_ratio" in metrics
        assert "max_drawdown" in metrics
        assert "calmar_ratio" in metrics
        assert "sortino_ratio" in metrics

    def test_performance_analyzer_calculate_drawdown(self, performance_analyzer):
        """测试绩效分析器计算回撤"""
        returns = [0.01, -0.02, 0.03, -0.01, 0.02, -0.03, 0.01]

        drawdown = performance_analyzer.calculate_drawdown(returns)

        assert isinstance(drawdown, dict)
        assert "max_drawdown" in drawdown
        assert "drawdown_duration" in drawdown
        assert "recovery_time" in drawdown
        assert drawdown["max_drawdown"] < 0

    def test_performance_analyzer_calculate_rolling_metrics(self, performance_analyzer):
        """测试绩效分析器计算滚动指标"""
        returns = [0.01, -0.02, 0.03, -0.01, 0.02, -0.03, 0.01] * 10
        window = 20

        rolling_metrics = performance_analyzer.calculate_rolling_metrics(
            returns, window
        )

        assert "rolling_return" in rolling_metrics
        assert "rolling_volatility" in rolling_metrics
        assert "rolling_sharpe" in rolling_metrics
        assert len(rolling_metrics["rolling_return"]) == len(returns) - window + 1

    def test_risk_analyzer_calculate_var(self, risk_analyzer):
        """测试风险分析器计算VaR"""
        returns = [0.01, -0.02, 0.03, -0.01, 0.02, -0.03, 0.01] * 20
        confidence_level = 0.95

        var = risk_analyzer.calculate_var(returns, confidence_level)

        assert isinstance(var, float)
        assert var < 0  # VaR应该是负数

    def test_risk_analyzer_calculate_cvar(self, risk_analyzer):
        """测试风险分析器计算CVaR"""
        returns = [0.01, -0.02, 0.03, -0.01, 0.02, -0.03, 0.01] * 20
        confidence_level = 0.95

        cvar = risk_analyzer.calculate_cvar(returns, confidence_level)

        assert isinstance(cvar, float)
        assert cvar < 0  # CVaR应该是负数

    def test_risk_analyzer_calculate_beta(self, risk_analyzer):
        """测试风险分析器计算Beta"""
        portfolio_returns = [0.01, -0.02, 0.03, -0.01, 0.02, -0.03, 0.01] * 10
        benchmark_returns = [0.008, -0.015, 0.025, -0.008, 0.015, -0.025, 0.008] * 10

        beta = risk_analyzer.calculate_beta(portfolio_returns, benchmark_returns)

        assert isinstance(beta, float)
        assert beta > 0  # Beta应该是正数

    def test_risk_analyzer_calculate_correlation_matrix(self, risk_analyzer):
        """测试风险分析器计算相关性矩阵"""
        returns_data = {
            "asset1": [0.01, -0.02, 0.03, -0.01, 0.02] * 10,
            "asset2": [0.008, -0.015, 0.025, -0.008, 0.015] * 10,
            "asset3": [0.012, -0.018, 0.028, -0.012, 0.018] * 10,
        }

        correlation_matrix = risk_analyzer.calculate_correlation_matrix(returns_data)

        assert isinstance(correlation_matrix, pd.DataFrame)
        assert correlation_matrix.shape == (3, 3)
        assert (correlation_matrix.values.diagonal() == 1.0).all()

    def test_email_sender_send_email(self, email_sender):
        """测试邮件发送器发送邮件"""
        subject = "Daily Trading Report"
        body = "Please find attached the daily trading report."
        recipients = ["<EMAIL>"]
        attachments = ["/tmp/test_report.pdf"]

        with patch("smtplib.SMTP") as mock_smtp:
            mock_server = Mock()
            mock_smtp.return_value.__enter__.return_value = mock_server

            result = email_sender.send_email(subject, body, recipients, attachments)

        assert result is True
        mock_server.send_message.assert_called_once()

    def test_email_sender_send_email_failure(self, email_sender):
        """测试邮件发送失败"""
        subject = "Test Report"
        body = "Test body"
        recipients = ["<EMAIL>"]

        with patch("smtplib.SMTP") as mock_smtp:
            mock_smtp.side_effect = Exception("SMTP connection failed")

            result = email_sender.send_email(subject, body, recipients)

        assert result is False

    def test_report_config_validation(self):
        """测试报告配置验证"""
        # 有效配置
        valid_config = ReportConfig(
            report_type=ReportType.DAILY,
            output_format="pdf",
            include_charts=True,
            include_analysis=True,
            email_recipients=["<EMAIL>"],
            storage_path="/tmp/reports",
        )

        assert valid_config.is_valid() is True

        # 无效配置 - 空邮件列表
        invalid_config = ReportConfig(
            report_type=ReportType.DAILY,
            output_format="pdf",
            include_charts=True,
            include_analysis=True,
            email_recipients=[],
            storage_path="/tmp/reports",
        )

        assert invalid_config.is_valid() is False

    @pytest.mark.asyncio
    async def test_report_generation_with_error(self, mock_db_session, mock_redis):
        """测试报告生成错误处理"""
        target_date = datetime(2024, 1, 15)

        # 模拟数据库错误
        mock_db_session.execute.side_effect = Exception("Database connection failed")

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                result = await generate_daily_report(target_date)

        assert result is not None
        assert "status" in result
        assert result["status"] == ReportStatus.FAILED
        assert "error" in result

    @pytest.mark.asyncio
    async def test_report_generation_with_retry(
        self, mock_db_session, mock_redis, sample_trading_data
    ):
        """测试报告生成重试机制"""
        target_date = datetime(2024, 1, 15)

        # 模拟第一次失败，第二次成功
        mock_result = Mock()
        mock_result.fetchall.return_value = sample_trading_data.to_dict("records")
        mock_db_session.execute.side_effect = [
            Exception("Temporary failure"),
            mock_result,
        ]

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                with patch(
                    "app.tasks.report_tasks.retry_on_failure", return_value=True
                ):
                    result = await generate_daily_report(target_date)

        assert result is not None
        assert result["status"] == ReportStatus.COMPLETED

    def test_report_file_generation(self, report_generator, sample_trading_data):
        """测试报告文件生成"""
        report_data = {
            "title": "Test Report",
            "date": datetime.now().date(),
            "summary": {"total_trades": 10, "profit": 1000},
            "charts": ["chart1.png", "chart2.png"],
            "analysis": "Test analysis content",
        }

        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_report.pdf")

            with patch.object(
                report_generator, "_generate_pdf", return_value=file_path
            ):
                result = report_generator.generate_report_file(report_data, "pdf")

            assert result == file_path

    def test_chart_generation(self, report_generator, sample_trading_data):
        """测试图表生成"""
        chart_config = {
            "type": "line",
            "title": "Price Trend",
            "x_column": "date",
            "y_column": "price",
        }

        with tempfile.TemporaryDirectory() as temp_dir:
            chart_path = os.path.join(temp_dir, "chart.png")

            with patch("matplotlib.pyplot.savefig") as mock_savefig:
                result = report_generator.generate_chart(
                    sample_trading_data, chart_config, chart_path
                )

            assert result == chart_path
            mock_savefig.assert_called_once()

    @pytest.mark.asyncio
    async def test_concurrent_report_generation(
        self, mock_db_session, mock_redis, sample_trading_data
    ):
        """测试并发报告生成"""
        target_dates = [
            datetime(2024, 1, 15),
            datetime(2024, 1, 16),
            datetime(2024, 1, 17),
        ]

        # 模拟数据库查询
        mock_result = Mock()
        mock_result.fetchall.return_value = sample_trading_data.to_dict("records")
        mock_db_session.execute.return_value = mock_result

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            with patch("app.tasks.report_tasks.get_redis", return_value=mock_redis):
                # 并发生成多个报告
                tasks = [generate_daily_report(date) for date in target_dates]
                results = await asyncio.gather(*tasks)

        assert len(results) == 3
        for result in results:
            assert result["status"] == ReportStatus.COMPLETED

    def test_data_aggregation(self, report_generator, sample_trading_data):
        """测试数据聚合"""
        # 测试日数据聚合
        daily_summary = report_generator.aggregate_daily_data(sample_trading_data)

        assert "total_volume" in daily_summary
        assert "total_profit" in daily_summary
        assert "avg_price" in daily_summary
        assert "trade_count" in daily_summary

        # 测试周数据聚合
        weekly_summary = report_generator.aggregate_weekly_data(sample_trading_data)

        assert "weekly_return" in weekly_summary
        assert "weekly_volume" in weekly_summary
        assert "weekly_trades" in weekly_summary

    def test_performance_comparison(self, performance_analyzer):
        """测试绩效比较"""
        strategy_returns = [0.01, -0.02, 0.03, -0.01, 0.02] * 10
        benchmark_returns = [0.008, -0.015, 0.025, -0.008, 0.015] * 10

        comparison = performance_analyzer.compare_performance(
            strategy_returns, benchmark_returns
        )

        assert "alpha" in comparison
        assert "beta" in comparison
        assert "information_ratio" in comparison
        assert "tracking_error" in comparison
        assert "correlation" in comparison

    def test_risk_metrics_calculation(self, risk_analyzer):
        """测试风险指标计算"""
        returns = [0.01, -0.02, 0.03, -0.01, 0.02, -0.03, 0.01] * 20

        risk_metrics = risk_analyzer.calculate_risk_metrics(returns)

        assert "var_95" in risk_metrics
        assert "var_99" in risk_metrics
        assert "cvar_95" in risk_metrics
        assert "cvar_99" in risk_metrics
        assert "volatility" in risk_metrics
        assert "downside_deviation" in risk_metrics

    @pytest.mark.asyncio
    async def test_report_storage_and_retrieval(self, mock_db_session):
        """测试报告存储和检索"""
        report_data = {
            "report_id": "test_report_123",
            "type": ReportType.DAILY,
            "date": datetime.now().date(),
            "file_path": "/tmp/test_report.pdf",
            "status": ReportStatus.COMPLETED,
        }

        with patch(
            "app.tasks.report_tasks.get_db_session", return_value=mock_db_session
        ):
            # 存储报告
            await report_generator.save_report_metadata(report_data)

            # 检索报告
            retrieved_report = await report_generator.get_report_metadata(
                "test_report_123"
            )

        mock_db_session.execute.assert_called()
        mock_db_session.commit.assert_called()

    def test_memory_efficiency(self, report_generator):
        """测试内存效率"""
        # 生成大量数据
        large_data = pd.DataFrame(
            {
                "date": pd.date_range("2024-01-01", periods=10000, freq="H"),
                "price": range(10000),
                "volume": range(10000),
                "profit": range(10000),
            }
        )

        # 测试内存使用
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 生成报告
        report = report_generator.create_daily_report(datetime.now(), large_data)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该控制在合理范围内
        assert memory_increase < 100  # 少于100MB
        assert report is not None
