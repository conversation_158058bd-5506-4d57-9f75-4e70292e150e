#!/usr/bin/env python3
"""
量化投资平台深度用户体验测试
使用Puppeteer进行全面的功能测试
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

class PlatformTester:
    def __init__(self):
        self.manager = BrowserManager()
        self.page = None
        self.test_results = []
        self.screenshots = []
        
    async def setup(self):
        """初始化浏览器"""
        self.page = await self.manager.ensure_browser()
        print("🚀 浏览器已启动，开始测试量化投资平台...")
        
    async def teardown(self):
        """清理资源"""
        if self.manager.browser:
            await self.manager.browser.close()
            print("🔚 浏览器已关闭")
            
    async def take_screenshot(self, name, description=""):
        """截图并记录"""
        filename = f"test_{name}_{datetime.now().strftime('%H%M%S')}.png"
        await self.page.screenshot(path=filename, full_page=True)
        self.screenshots.append({
            "name": name,
            "filename": filename,
            "description": description,
            "timestamp": datetime.now().isoformat()
        })
        print(f"📸 截图已保存: {filename}")
        
    async def log_test_result(self, test_name, status, details="", error=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "status": status,  # "PASS", "FAIL", "WARNING"
            "details": details,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {details}")
        
    async def test_homepage_access(self):
        """测试首页访问"""
        try:
            print("\n📋 测试1: 首页访问和基础功能")
            
            # 访问首页
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            await self.page.wait_for_timeout(2000)
            
            # 获取页面标题
            title = await self.page.title()
            await self.log_test_result("页面标题获取", "PASS", f"标题: {title}")
            
            # 截图
            await self.take_screenshot("homepage", "平台首页")
            
            # 检查页面是否加载完成
            body_text = await self.page.evaluate('() => document.body.innerText')
            if len(body_text) > 100:
                await self.log_test_result("页面内容加载", "PASS", f"内容长度: {len(body_text)} 字符")
            else:
                await self.log_test_result("页面内容加载", "WARNING", "页面内容较少，可能未完全加载")
                
            # 检查控制台错误
            console_errors = await self.page.evaluate('''() => {
                const errors = [];
                const originalError = console.error;
                console.error = (...args) => {
                    errors.push(args.join(' '));
                    originalError(...args);
                };
                return window.consoleErrors || [];
            }''')
            
            if not console_errors:
                await self.log_test_result("控制台错误检查", "PASS", "无控制台错误")
            else:
                await self.log_test_result("控制台错误检查", "WARNING", f"发现 {len(console_errors)} 个错误")
                
        except Exception as e:
            await self.log_test_result("首页访问", "FAIL", "首页访问失败", e)
            
    async def test_navigation_menu(self):
        """测试导航菜单"""
        try:
            print("\n📋 测试2: 导航菜单和页面跳转")
            
            # 查找导航菜单
            nav_items = await self.page.query_selector_all('nav a, .nav-item, .menu-item')
            await self.log_test_result("导航菜单检测", "PASS", f"发现 {len(nav_items)} 个导航项")
            
            # 测试主要导航链接
            main_pages = [
                ('/dashboard', '仪表盘'),
                ('/market', '市场数据'),
                ('/trading', '交易终端'),
                ('/strategy', '策略中心'),
                ('/portfolio', '投资组合'),
                ('/risk', '风险管理')
            ]
            
            for path, name in main_pages:
                try:
                    # 尝试点击导航或直接访问
                    current_url = self.page.url
                    await self.page.goto(f'http://localhost:5173{path}', wait_until='networkidle')
                    await self.page.wait_for_timeout(1000)
                    
                    # 检查页面是否正确加载
                    new_title = await self.page.title()
                    await self.take_screenshot(f"page_{name.replace(' ', '_')}", f"{name}页面")
                    await self.log_test_result(f"{name}页面访问", "PASS", f"成功访问，标题: {new_title}")
                    
                except Exception as e:
                    await self.log_test_result(f"{name}页面访问", "FAIL", f"访问失败: {path}", e)
                    
        except Exception as e:
            await self.log_test_result("导航菜单测试", "FAIL", "导航测试失败", e)
            
    async def test_login_functionality(self):
        """测试登录功能"""
        try:
            print("\n📋 测试3: 用户登录功能")
            
            # 访问登录页面
            await self.page.goto('http://localhost:5173/login', wait_until='networkidle')
            await self.page.wait_for_timeout(2000)
            await self.take_screenshot("login_page", "登录页面")
            
            # 查找登录表单元素
            username_input = await self.page.query_selector('input[type="text"], input[name="username"], input[placeholder*="用户名"]')
            password_input = await self.page.query_selector('input[type="password"], input[name="password"]')
            login_button = await self.page.query_selector('button[type="submit"], .login-btn, .submit-btn')
            
            if username_input and password_input and login_button:
                await self.log_test_result("登录表单检测", "PASS", "登录表单元素完整")
                
                # 测试演示登录
                demo_button = await self.page.query_selector('.demo-login, .demo-btn, button:has-text("演示登录")')
                if demo_button:
                    await demo_button.click()
                    await self.page.wait_for_timeout(2000)
                    await self.take_screenshot("after_demo_login", "演示登录后")
                    await self.log_test_result("演示登录", "PASS", "演示登录功能正常")
                else:
                    # 尝试手动填写登录信息
                    await username_input.fill('admin')
                    await password_input.fill('admin123')
                    await login_button.click()
                    await self.page.wait_for_timeout(3000)
                    await self.take_screenshot("manual_login", "手动登录后")
                    await self.log_test_result("手动登录", "PASS", "手动登录测试完成")
                    
            else:
                await self.log_test_result("登录表单检测", "FAIL", "登录表单元素不完整")
                
        except Exception as e:
            await self.log_test_result("登录功能测试", "FAIL", "登录测试失败", e)
            
    async def test_interactive_elements(self):
        """测试交互元素"""
        try:
            print("\n📋 测试4: 交互元素和按钮")
            
            # 回到首页
            await self.page.goto('http://localhost:5173', wait_until='networkidle')
            await self.page.wait_for_timeout(2000)
            
            # 查找所有按钮
            buttons = await self.page.query_selector_all('button, .btn, .el-button')
            await self.log_test_result("按钮检测", "PASS", f"发现 {len(buttons)} 个按钮")
            
            # 查找所有输入框
            inputs = await self.page.query_selector_all('input, textarea, .el-input')
            await self.log_test_result("输入框检测", "PASS", f"发现 {len(inputs)} 个输入框")
            
            # 查找下拉菜单
            selects = await self.page.query_selector_all('select, .el-select, .dropdown')
            await self.log_test_result("下拉菜单检测", "PASS", f"发现 {len(selects)} 个下拉菜单")
            
            # 测试一些按钮点击（安全的按钮）
            safe_buttons = await self.page.query_selector_all('button:not([type="submit"]):not(.delete):not(.remove)')
            clicked_count = 0
            for i, button in enumerate(safe_buttons[:5]):  # 只测试前5个按钮
                try:
                    await button.click()
                    await self.page.wait_for_timeout(500)
                    clicked_count += 1
                except:
                    pass
                    
            await self.log_test_result("按钮交互测试", "PASS", f"成功点击 {clicked_count} 个按钮")
            
        except Exception as e:
            await self.log_test_result("交互元素测试", "FAIL", "交互元素测试失败", e)
            
    async def test_responsive_design(self):
        """测试响应式设计"""
        try:
            print("\n📋 测试5: 响应式设计")
            
            # 测试不同屏幕尺寸
            screen_sizes = [
                (1920, 1080, "桌面大屏"),
                (1366, 768, "桌面标准"),
                (768, 1024, "平板竖屏"),
                (375, 667, "手机")
            ]
            
            for width, height, device_name in screen_sizes:
                await self.page.set_viewport_size(width, height)
                await self.page.wait_for_timeout(1000)
                await self.take_screenshot(f"responsive_{device_name.replace(' ', '_')}", f"{device_name}显示效果")
                await self.log_test_result(f"响应式-{device_name}", "PASS", f"尺寸: {width}x{height}")
                
            # 恢复默认尺寸
            await self.page.set_viewport_size(1280, 720)
            
        except Exception as e:
            await self.log_test_result("响应式设计测试", "FAIL", "响应式测试失败", e)
            
    async def generate_report(self):
        """生成测试报告"""
        report = {
            "test_summary": {
                "total_tests": len(self.test_results),
                "passed": len([r for r in self.test_results if r["status"] == "PASS"]),
                "failed": len([r for r in self.test_results if r["status"] == "FAIL"]),
                "warnings": len([r for r in self.test_results if r["status"] == "WARNING"]),
                "timestamp": datetime.now().isoformat()
            },
            "test_results": self.test_results,
            "screenshots": self.screenshots
        }
        
        # 保存报告
        with open(f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 打印总结
        print("\n" + "="*60)
        print("📊 测试总结报告")
        print("="*60)
        print(f"总测试数: {report['test_summary']['total_tests']}")
        print(f"✅ 通过: {report['test_summary']['passed']}")
        print(f"❌ 失败: {report['test_summary']['failed']}")
        print(f"⚠️ 警告: {report['test_summary']['warnings']}")
        print(f"📸 截图数: {len(self.screenshots)}")
        print("="*60)
        
        return report

async def main():
    """主测试函数"""
    tester = PlatformTester()
    
    try:
        await tester.setup()
        
        # 执行所有测试
        await tester.test_homepage_access()
        await tester.test_navigation_menu()
        await tester.test_login_functionality()
        await tester.test_interactive_elements()
        await tester.test_responsive_design()
        
        # 生成报告
        report = await tester.generate_report()
        
    except Exception as e:
        print(f"💥 测试过程中发生严重错误: {e}")
    finally:
        await tester.teardown()

if __name__ == "__main__":
    asyncio.run(main())
