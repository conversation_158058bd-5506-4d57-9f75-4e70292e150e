{"session_id": "comprehensive_audit_1754287309", "start_time": "2025-08-04T14:01:49.437078", "pages_tested": [{"name": "策略中心", "url": "http://localhost:5173/strategy/center", "expected_title": "策略中心", "actual_title": "策略中心 - 量化投资平台", "status": "working", "content_length": 68448, "clickable_elements": 67, "issues": [], "screenshot_path": "screenshots/comprehensive_audit_1754287309_策略中心.png"}, {"name": "策略详情-mock-1", "url": "http://localhost:5173/strategy/detail/mock-1", "expected_title": "策略详情", "actual_title": "策略详情 - 量化投资平台", "status": "error", "content_length": 47596, "clickable_elements": 17, "issues": [{"type": "navigation_error", "severity": "high", "message": "页面访问失败: Page.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - taking page screenshot\n  - waiting for fonts to load...\n  - fonts loaded\n", "page": "策略详情-mock-1"}], "screenshot_path": ""}, {"name": "策略详情-mock-2", "url": "http://localhost:5173/strategy/detail/mock-2", "expected_title": "策略详情", "actual_title": "策略详情 - 量化投资平台", "status": "error", "content_length": 47583, "clickable_elements": 17, "issues": [{"type": "navigation_error", "severity": "high", "message": "页面访问失败: Page.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - taking page screenshot\n  - waiting for fonts to load...\n  - fonts loaded\n", "page": "策略详情-mock-2"}], "screenshot_path": ""}, {"name": "策略详情-test-1", "url": "http://localhost:5173/strategy/detail/test-1", "expected_title": "策略详情", "actual_title": "策略详情 - 量化投资平台", "status": "error", "content_length": 47599, "clickable_elements": 17, "issues": [{"type": "navigation_error", "severity": "high", "message": "页面访问失败: Page.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - taking page screenshot\n  - waiting for fonts to load...\n  - fonts loaded\n", "page": "策略详情-test-1"}], "screenshot_path": ""}, {"name": "策略开发", "url": "http://localhost:5173/strategy/development", "expected_title": "策略开发", "actual_title": "策略开发 - 量化投资平台", "status": "error", "content_length": 44499, "clickable_elements": 40, "issues": [{"type": "navigation_error", "severity": "high", "message": "页面访问失败: Page.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - taking page screenshot\n  - waiting for fonts to load...\n  - fonts loaded\n", "page": "策略开发"}], "screenshot_path": ""}, {"name": "策略监控", "url": "http://localhost:5173/strategy/monitor", "expected_title": "策略监控", "actual_title": "策略监控 - 量化投资平台", "status": "working", "content_length": 38155, "clickable_elements": 31, "issues": [], "screenshot_path": "screenshots/comprehensive_audit_1754287309_策略监控.png"}, {"name": "策略文库", "url": "http://localhost:5173/strategy/library", "expected_title": "策略文库", "actual_title": "策略文库 - 量化投资平台", "status": "error", "content_length": 32345, "clickable_elements": 15, "issues": [{"type": "navigation_error", "severity": "high", "message": "页面访问失败: Page.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - taking page screenshot\n  - waiting for fonts to load...\n  - fonts loaded\n", "page": "策略文库"}], "screenshot_path": ""}], "total_links_found": 7, "working_links": 7, "broken_links": 5, "issues_summary": {"total": 5, "high": 5, "medium": 0, "low": 0}, "recommendations": ["🚨 立即修复 5 个高优先级问题", "💡 建议全面检查路由配置和页面组件", "💡 建议添加策略页面的自动化测试", "💡 建议建立页面健康监控机制", "💡 建议为所有策略详情页面添加统一的错误处理"], "success_rate": 28.57142857142857, "end_time": "2025-08-04T14:05:00.883143"}