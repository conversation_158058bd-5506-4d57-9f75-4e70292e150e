{"test_summary": {"total_tests": 15, "total_issues": 107, "high_severity": 0, "medium_severity": 27, "low_severity": 80, "test_time": "2025-08-03T13:04:38.142278"}, "issues_by_category": {"代码质量": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.643285", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.643710", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.643916", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.644090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.644402", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T13:04:22.644617", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T13:04:22.644808", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.644942", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.645076", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.645253", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.645428", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.645635", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.645820", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.645996", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.646152", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.646293", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.646433", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.646602", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T13:04:22.646749", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T13:04:22.646940", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T13:04:22.647083", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647228", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647411", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647602", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647788", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.648001", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.648184", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.648394", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.648597", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.648767", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T13:04:08.636355, total: 8}", "evidence": null, "timestamp": "2025-08-03T13:04:22.648969", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649113", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649273", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649469", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649671", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649861", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T13:04:22.650048", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.650331", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.650553", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.651611", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.652628", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.653694", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.654756", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.654919", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655099", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T13:04:11.604187, total: 8}", "evidence": null, "timestamp": "2025-08-03T13:04:22.655256", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655430", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655588", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655747", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655972", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.656182", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.656407", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T13:04:22.656618", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.656934", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.657191", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.657448", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.658173", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.658340", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.659159", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.659357", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.660071", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.660245", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.661082", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.661353", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.662295", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.662484", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.663173", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.663394", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.664079", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.664248", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.664920", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.665130", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.665854", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.666014", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.666739", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.666968", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.667642", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.667803", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.668457", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.668680", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.669398", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.669552", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.670431", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.670652", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.671333", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.671485", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.672174", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.672413", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.673162", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.673349", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.674090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.674314", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.675104", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.675279", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.676029", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.676268", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.676932", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.677076", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.677725", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.677925", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.678564", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.678801", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.679992", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.680247", "url": "http://localhost:5173/risk"}], "UI/UX": [{"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T13:04:31.001204", "url": "http://localhost:5173/"}], "可访问性": [{"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T13:04:31.702386", "url": "http://localhost:5173/"}], "安全": [{"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T13:04:38.141619", "url": "http://localhost:5173/"}]}, "detailed_issues": [{"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.643285", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.643710", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.643916", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.644090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.644402", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "API Error 404: {detail: Not Found}", "evidence": null, "timestamp": "2025-08-03T13:04:22.644617", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Resource not found", "evidence": null, "timestamp": "2025-08-03T13:04:22.644808", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "获取CTP状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.644942", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "刷新状态失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.645076", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.645253", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.645428", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.645635", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.645820", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.645996", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Get trades error: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.646152", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "Failed to load resource: the server responded with a status of 404 (Not Found)", "evidence": null, "timestamp": "2025-08-03T13:04:22.646293", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.646433", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "❌ 交易终端初始化失败: AxiosError", "evidence": null, "timestamp": "2025-08-03T13:04:22.646602", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket connection to 'ws://localhost:8000/ctp/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "evidence": null, "timestamp": "2025-08-03T13:04:22.646749", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "CTP WebSocket连接错误: Event", "evidence": null, "timestamp": "2025-08-03T13:04:22.646940", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "WebSocket连接失败: Event", "evidence": null, "timestamp": "2025-08-03T13:04:22.647083", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647228", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647411", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "中", "title": "控制台错误", "description": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647602", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.647788", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.648001", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.648184", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.648394", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.648597", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.648767", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T13:04:08.636355, total: 8}", "evidence": null, "timestamp": "2025-08-03T13:04:22.648969", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649113", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649273", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649469", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649671", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.649861", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T13:04:22.650048", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.650331", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.650553", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.651611", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.652628", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "ElementPlusError: [el-radio] [API] label act as value is about to be deprecated in version 3.0.0, please use value instead.\nFor more detail, please visit: https://element-plus.org/en-US/component/radio.html\n\n    at debugWarn (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:632:37)\n    at watch.immediate (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6628:7)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2289:19)\n    at callWithAsyncErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2296:17)\n    at baseWatchOptions.call (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8365:47)\n    at job (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2019:18)\n    at watch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:2054:7)\n    at doWatch (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8393:23)\n    at watch2 (http://localhost:5173/node_modules/.vite/deps/chunk-DBOTBDL2.js?v=dd06b969:8326:10)\n    at useDeprecated (http://localhost:5173/node_modules/.vite/deps/chunk-NLWRA436.js?v=dd06b969:6626:3)", "evidence": null, "timestamp": "2025-08-03T13:04:22.653694", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.654756", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 指数数据格式异常或为空，使用模拟数据: {success: true, data: Object}", "evidence": null, "timestamp": "2025-08-03T13:04:22.654919", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655099", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 板块数据格式异常或为空: {success: true, data: Array(8), timestamp: 2025-08-03T13:04:11.604187, total: 8}", "evidence": null, "timestamp": "2025-08-03T13:04:22.655256", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655430", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655588", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655747", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.655972", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ 排行榜数据格式异常或为空: [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]", "evidence": null, "timestamp": "2025-08-03T13:04:22.656182", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "Missing security headers: [x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy]", "evidence": null, "timestamp": "2025-08-03T13:04:22.656407", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.", "evidence": null, "timestamp": "2025-08-03T13:04:22.656618", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.656934", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.657191", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.657448", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.658173", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.658340", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.659159", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.659357", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.660071", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.660245", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.661082", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.661353", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.662295", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.662484", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.663173", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.663394", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.664079", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.664248", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.664920", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.665130", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.665854", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.666014", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.666739", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.666968", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.667642", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.667803", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.668457", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.668680", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.669398", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.669552", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.670431", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.670652", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.671333", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.671485", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.672174", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.672413", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.673162", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.673349", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.674090", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.674314", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.675104", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.675279", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.676029", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.676268", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.676932", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.677076", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: validation failed for prop \"type\". Expected one of [\"default\", \"primary\", \"success\", \"warning\", \"info\", \"danger\", \"text\", \"\"], got value \"link\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.677725", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.677925", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "⚠️ Vue警告: Invalid prop: custom validator check failed for prop \"type\".", "evidence": null, "timestamp": "2025-08-03T13:04:22.678564", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "组件追踪: at <ElButton>\nat <ElOnlyChild>\nat <ElOnlyChild>\nat <ElPopperTrigger>\nat <ElTooltipTrigger>\nat <ElPopper>\nat <ElTooltip>\nat <ElDropdown>\nat <ElCard>\nat <Index>\nat <StrategyHub>\nat <KeepAlive>\nat <BaseTransition>\nat <Transition>\nat <RouterView>\nat <DefaultLayout>\nat <RouterView>\nat <App>", "evidence": null, "timestamp": "2025-08-03T13:04:22.678801", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.679992", "url": "http://localhost:5173/risk"}, {"category": "代码质量", "severity": "低", "title": "控制台警告", "description": "A preload for 'http://localhost:5173/src/App.vue' is found, but is not used because the request credentials mode does not match. Consider taking a look at crossorigin attribute.", "evidence": null, "timestamp": "2025-08-03T13:04:22.680247", "url": "http://localhost:5173/risk"}, {"category": "UI/UX", "severity": "中", "title": "移动端文字过小", "description": "发现 48 个小于14px的文字元素", "evidence": null, "timestamp": "2025-08-03T13:04:31.001204", "url": "http://localhost:5173/"}, {"category": "可访问性", "severity": "中", "title": "表单元素缺少标签", "description": "5 个表单元素缺少适当标签", "evidence": null, "timestamp": "2025-08-03T13:04:31.702386", "url": "http://localhost:5173/"}, {"category": "安全", "severity": "中", "title": "缺少安全头信息", "description": "缺少: x-frame-options, x-content-type-options, x-xss-protection, strict-transport-security, content-security-policy", "evidence": null, "timestamp": "2025-08-03T13:04:38.141619", "url": "http://localhost:5173/"}], "test_results": [{"test": "首页性能测试", "status": "PASS", "details": "加载时间: 1.38秒", "timestamp": "2025-08-03T13:03:57.414332"}, {"test": "仪表盘性能测试", "status": "PASS", "details": "加载时间: 0.86秒", "timestamp": "2025-08-03T13:03:58.271090"}, {"test": "市场数据性能测试", "status": "PASS", "details": "加载时间: 1.21秒", "timestamp": "2025-08-03T13:03:59.477091"}, {"test": "交易终端性能测试", "status": "PASS", "details": "加载时间: 1.27秒", "timestamp": "2025-08-03T13:04:00.751914"}, {"test": "策略中心性能测试", "status": "PASS", "details": "加载时间: 0.69秒", "timestamp": "2025-08-03T13:04:01.446335"}, {"test": "投资组合性能测试", "status": "PASS", "details": "加载时间: 0.67秒", "timestamp": "2025-08-03T13:04:02.116336"}, {"test": "风险管理性能测试", "status": "PASS", "details": "加载时间: 0.67秒", "timestamp": "2025-08-03T13:04:02.786726"}, {"test": "控制台错误检查", "status": "PASS", "details": "发现 24 个错误, 80 个警告", "timestamp": "2025-08-03T13:04:22.680474"}, {"test": "网络请求分析", "status": "PASS", "details": "发现 0 个失败请求", "timestamp": "2025-08-03T13:04:28.814143"}, {"test": "桌面端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T13:04:29.619911"}, {"test": "平板端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T13:04:30.302888"}, {"test": "手机端响应性", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T13:04:31.001426"}, {"test": "可访问性检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T13:04:31.713469"}, {"test": "数据加载检查", "status": "PASS", "details": "检查完成", "timestamp": "2025-08-03T13:04:37.474911"}, {"test": "安全头检查", "status": "PASS", "details": "缺少 5 个安全头", "timestamp": "2025-08-03T13:04:38.142008"}], "recommendations": [{"priority": "中", "title": "用户体验改进", "description": "提升界面响应性和用户体验", "actions": ["优化移动端适配", "改进加载状态显示", "统一UI组件"]}]}