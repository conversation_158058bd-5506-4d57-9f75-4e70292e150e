#!/usr/bin/env python3
"""
前端用户体验测试 - 使用浏览器自动化测试回测功能
模拟真实用户在前端界面上的操作流程
"""

import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class FrontendUserExperienceTester:
    def __init__(self):
        self.driver = None
        self.base_url = "http://localhost:5174"
        self.issues = []
        
    def setup_browser(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            self.log_success("浏览器启动成功")
            return True
        except Exception as e:
            self.log_issue("浏览器设置", f"浏览器启动失败: {str(e)}", "high")
            return False
    
    def log_issue(self, category, description, severity="medium"):
        """记录发现的问题"""
        issue = {
            "category": category,
            "description": description,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        }
        self.issues.append(issue)
        print(f"❌ [{severity.upper()}] {category}: {description}")
        
    def log_success(self, message):
        """记录成功的操作"""
        print(f"✅ {message}")
    
    def test_page_load(self):
        """测试页面加载"""
        print("\n🌐 测试页面加载...")
        
        try:
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查页面标题
            title = self.driver.title
            if title:
                self.log_success(f"页面加载成功，标题: {title}")
            else:
                self.log_issue("页面加载", "页面标题为空", "medium")
            
            # 检查是否有错误信息
            try:
                error_elements = self.driver.find_elements(By.CLASS_NAME, "error")
                if error_elements:
                    self.log_issue("页面加载", f"页面显示错误信息: {len(error_elements)}个错误", "medium")
            except:
                pass
            
            return True
            
        except TimeoutException:
            self.log_issue("页面加载", "页面加载超时", "high")
            return False
        except Exception as e:
            self.log_issue("页面加载", f"页面加载异常: {str(e)}", "high")
            return False
    
    def test_navigation(self):
        """测试导航功能"""
        print("\n🧭 测试导航功能...")
        
        try:
            # 查找导航菜单
            nav_items = [
                ("回测", ["回测", "backtest", "策略回测"]),
                ("策略", ["策略", "strategy", "策略管理"]),
                ("数据", ["数据", "data", "历史数据"]),
                ("用户", ["用户", "user", "个人中心"])
            ]
            
            found_nav_items = 0
            
            for nav_name, keywords in nav_items:
                found = False
                for keyword in keywords:
                    try:
                        # 尝试通过文本内容查找导航项
                        nav_element = self.driver.find_element(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                        if nav_element.is_displayed():
                            self.log_success(f"找到导航项: {nav_name}")
                            found = True
                            found_nav_items += 1
                            break
                    except NoSuchElementException:
                        continue
                
                if not found:
                    self.log_issue("导航", f"未找到导航项: {nav_name}", "medium")
            
            if found_nav_items == 0:
                self.log_issue("导航", "未找到任何导航菜单", "high")
                return False
            
            return True
            
        except Exception as e:
            self.log_issue("导航", f"导航测试异常: {str(e)}", "medium")
            return False
    
    def test_user_registration_ui(self):
        """测试用户注册界面"""
        print("\n📝 测试用户注册界面...")
        
        try:
            # 查找注册相关元素
            registration_keywords = ["注册", "register", "sign up", "创建账户"]
            registration_found = False
            
            for keyword in registration_keywords:
                try:
                    reg_element = self.driver.find_element(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                    if reg_element.is_displayed():
                        self.log_success(f"找到注册入口: {keyword}")
                        registration_found = True
                        
                        # 尝试点击注册
                        reg_element.click()
                        time.sleep(2)
                        
                        # 检查是否出现注册表单
                        form_fields = ["username", "email", "password"]
                        found_fields = 0
                        
                        for field in form_fields:
                            try:
                                field_element = self.driver.find_element(By.NAME, field)
                                if field_element.is_displayed():
                                    found_fields += 1
                            except NoSuchElementException:
                                try:
                                    field_element = self.driver.find_element(By.ID, field)
                                    if field_element.is_displayed():
                                        found_fields += 1
                                except NoSuchElementException:
                                    pass
                        
                        if found_fields >= 2:
                            self.log_success(f"注册表单正常，找到{found_fields}个字段")
                        else:
                            self.log_issue("注册界面", f"注册表单不完整，只找到{found_fields}个字段", "medium")
                        
                        break
                        
                except NoSuchElementException:
                    continue
            
            if not registration_found:
                self.log_issue("注册界面", "未找到注册入口", "high")
                return False
            
            return True
            
        except Exception as e:
            self.log_issue("注册界面", f"注册界面测试异常: {str(e)}", "medium")
            return False
    
    def test_backtest_interface(self):
        """测试回测界面"""
        print("\n📊 测试回测界面...")
        
        try:
            # 查找回测相关元素
            backtest_keywords = ["回测", "backtest", "策略回测", "回测分析"]
            backtest_found = False
            
            for keyword in backtest_keywords:
                try:
                    backtest_element = self.driver.find_element(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                    if backtest_element.is_displayed():
                        self.log_success(f"找到回测入口: {keyword}")
                        backtest_found = True
                        
                        # 尝试点击进入回测页面
                        backtest_element.click()
                        time.sleep(3)
                        
                        # 检查回测配置表单
                        config_fields = [
                            ("策略选择", ["strategy", "策略"]),
                            ("股票代码", ["symbol", "股票", "代码"]),
                            ("开始日期", ["start", "开始", "date"]),
                            ("结束日期", ["end", "结束", "date"]),
                            ("初始资金", ["capital", "资金", "money"])
                        ]
                        
                        found_config_fields = 0
                        
                        for field_name, keywords in config_fields:
                            found = False
                            for kw in keywords:
                                try:
                                    # 查找包含关键词的输入框或选择框
                                    elements = self.driver.find_elements(By.XPATH, f"//input[contains(@placeholder, '{kw}') or contains(@name, '{kw}') or contains(@id, '{kw}')]")
                                    elements.extend(self.driver.find_elements(By.XPATH, f"//select[contains(@name, '{kw}') or contains(@id, '{kw}')]"))
                                    
                                    if elements:
                                        found = True
                                        found_config_fields += 1
                                        self.log_success(f"找到配置字段: {field_name}")
                                        break
                                except:
                                    continue
                            
                            if not found:
                                self.log_issue("回测界面", f"未找到配置字段: {field_name}", "medium")
                        
                        # 查找开始回测按钮
                        start_buttons = ["开始回测", "运行回测", "start", "run"]
                        start_button_found = False
                        
                        for btn_text in start_buttons:
                            try:
                                btn_element = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{btn_text}')]")
                                if btn_element.is_displayed():
                                    self.log_success(f"找到开始回测按钮: {btn_text}")
                                    start_button_found = True
                                    break
                            except NoSuchElementException:
                                continue
                        
                        if not start_button_found:
                            self.log_issue("回测界面", "未找到开始回测按钮", "high")
                        
                        if found_config_fields < 3:
                            self.log_issue("回测界面", f"回测配置不完整，只找到{found_config_fields}个配置项", "medium")
                        
                        break
                        
                except NoSuchElementException:
                    continue
            
            if not backtest_found:
                self.log_issue("回测界面", "未找到回测功能入口", "high")
                return False
            
            return True
            
        except Exception as e:
            self.log_issue("回测界面", f"回测界面测试异常: {str(e)}", "medium")
            return False
    
    def test_responsive_design(self):
        """测试响应式设计"""
        print("\n📱 测试响应式设计...")
        
        try:
            # 测试不同屏幕尺寸
            screen_sizes = [
                ("桌面", 1920, 1080),
                ("平板", 768, 1024),
                ("手机", 375, 667)
            ]
            
            for size_name, width, height in screen_sizes:
                self.driver.set_window_size(width, height)
                time.sleep(2)
                
                # 检查页面是否正常显示
                body = self.driver.find_element(By.TAG_NAME, "body")
                if body.is_displayed():
                    self.log_success(f"{size_name}尺寸({width}x{height})显示正常")
                else:
                    self.log_issue("响应式设计", f"{size_name}尺寸显示异常", "medium")
            
            # 恢复桌面尺寸
            self.driver.set_window_size(1920, 1080)
            return True
            
        except Exception as e:
            self.log_issue("响应式设计", f"响应式测试异常: {str(e)}", "medium")
            return False
    
    def run_full_frontend_test(self):
        """运行完整的前端用户体验测试"""
        print("🚀 开始前端用户体验测试...")
        print("=" * 50)
        
        if not self.setup_browser():
            return False
        
        try:
            # 1. 页面加载测试
            if not self.test_page_load():
                print("❌ 页面加载失败，终止测试")
                return False
            
            # 2. 导航功能测试
            self.test_navigation()
            
            # 3. 用户注册界面测试
            self.test_user_registration_ui()
            
            # 4. 回测界面测试
            self.test_backtest_interface()
            
            # 5. 响应式设计测试
            self.test_responsive_design()
            
            return True
            
        finally:
            if self.driver:
                self.driver.quit()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📋 前端用户体验测试报告")
        print("=" * 50)
        
        if not self.issues:
            print("🎉 恭喜！前端用户体验良好，未发现问题！")
        else:
            print(f"⚠️ 发现 {len(self.issues)} 个用户体验问题:")
            
            # 按严重程度分类
            high_issues = [i for i in self.issues if i["severity"] == "high"]
            medium_issues = [i for i in self.issues if i["severity"] == "medium"]
            low_issues = [i for i in self.issues if i["severity"] == "low"]
            
            if high_issues:
                print(f"\n🔴 高优先级问题 ({len(high_issues)}个):")
                for issue in high_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
            
            if medium_issues:
                print(f"\n🟡 中优先级问题 ({len(medium_issues)}个):")
                for issue in medium_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
            
            if low_issues:
                print(f"\n🟢 低优先级问题 ({len(low_issues)}个):")
                for issue in low_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    tester = FrontendUserExperienceTester()
    
    try:
        success = tester.run_full_frontend_test()
        tester.generate_report()
        
        if success:
            print("✅ 前端测试完成")
        else:
            print("❌ 前端测试发现严重问题")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        tester.generate_report()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")
        tester.generate_report()
