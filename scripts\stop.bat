@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 量化投资平台停止脚本 (Windows版本)
echo 🛑 停止量化投资平台...
echo.

:: 进入项目根目录
cd /d "%~dp0\.."

:: 停止后端服务
echo 📡 停止后端服务...

:: 按进程名停止
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im python3.exe >nul 2>&1

:: 按端口停止
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo ✅ 后端服务已停止

:: 停止前端服务
echo 🎨 停止前端服务...

:: 按进程名停止
taskkill /f /im node.exe >nul 2>&1

:: 按端口停止
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo ✅ 前端服务已停止

:: 清理进程ID文件
if exist .backend.pid del .backend.pid
if exist .frontend.pid del .frontend.pid

echo.
echo ✅ 量化投资平台已完全停止
echo.
pause
