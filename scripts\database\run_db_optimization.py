#!/usr/bin/env python3
"""
数据库性能优化启动脚本
用于启动和管理数据库性能优化系统
"""

import asyncio
import argparse
import json
import logging
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.db_performance_config import db_performance_manager
from scripts.db_index_optimizer import DatabaseIndexOptimizer


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/db_optimization.log')
    ]
)

logger = logging.getLogger(__name__)


async def run_index_optimization():
    """运行索引优化"""
    print("=" * 60)
    print("执行数据库索引优化")
    print("=" * 60)
    
    try:
        async with DatabaseIndexOptimizer() as optimizer:
            report = await optimizer.generate_optimization_report()
            
            print(f"优化完成时间: {report['timestamp']}")
            
            summary = report.get('optimization_summary', {})
            print(f"总表数: {summary.get('total_tables', 0)}")
            print(f"创建索引数: {summary.get('total_indexes_created', 0)}")
            print(f"统计信息已更新: {summary.get('statistics_updated', False)}")
            
            created_indexes = report.get('created_indexes', [])
            if created_indexes:
                print(f"\n新创建的索引 ({len(created_indexes)}个):")
                for idx in created_indexes:
                    print(f"  - {idx}")
            
            recommendations = summary.get('recommendations', [])
            if recommendations:
                print(f"\n优化建议:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec}")
            
            # 保存报告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"logs/index_optimization_report_{timestamp}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n详细报告已保存到: {report_file}")
            
    except Exception as e:
        logger.error(f"索引优化失败: {e}")
        return False
    
    return True


async def start_performance_monitoring():
    """启动性能监控"""
    print("=" * 60)
    print("启动数据库性能监控系统")
    print("=" * 60)
    
    try:
        # 初始化性能管理器
        await db_performance_manager.initialize()
        
        print("性能监控系统已启动，按 Ctrl+C 停止...")
        
        # 定期输出状态
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟输出一次状态
                
                status = await db_performance_manager.get_performance_status()
                if status.get('initialized'):
                    components = status.get('components', {})
                    
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 性能状态:")
                    
                    # 查询缓存状态
                    if 'query_cache' in components:
                        cache = components['query_cache']
                        print(f"  缓存命中率: {cache.get('hit_ratio', 0):.2%}")
                    
                    # 连接池状态
                    if 'connection_pool' in components:
                        pool = components['connection_pool']
                        print(f"  连接池利用率: {pool.get('utilization', 0):.2%}")
                        print(f"  活跃连接数: {pool.get('active_connections', 0)}")
                    
                    # 慢查询状态
                    if 'slow_query_analyzer' in components:
                        slow = components['slow_query_analyzer']
                        print(f"  慢查询比例: {slow.get('slow_ratio', 0):.2%}")
                    
                    # 性能监控状态
                    if 'performance_monitor' in components:
                        perf = components['performance_monitor']
                        print(f"  健康状态: {perf.get('health_status', 'unknown')}")
                        print(f"  QPS: {perf.get('qps', 0):.2f}")
                        print(f"  CPU使用率: {perf.get('cpu_usage', 0):.1f}%")
                        print(f"  内存使用率: {perf.get('memory_usage', 0):.1f}%")
                
            except KeyboardInterrupt:
                print("\n正在停止性能监控...")
                break
            except Exception as e:
                logger.error(f"性能监控循环错误: {e}")
                await asyncio.sleep(60)
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        logger.error(f"启动性能监控失败: {e}")
        return False
    finally:
        await db_performance_manager.cleanup()
        print("性能监控系统已停止")
    
    return True


async def generate_performance_report(hours: int = 24):
    """生成性能报告"""
    print("=" * 60)
    print(f"生成 {hours} 小时性能报告")
    print("=" * 60)
    
    try:
        # 初始化（如果尚未初始化）
        if not db_performance_manager.initialized:
            await db_performance_manager.initialize()
        
        # 生成报告
        report = await db_performance_manager.generate_comprehensive_report(hours)
        
        print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"报告周期: {hours} 小时")
        
        # 显示组件状态
        components = report.get('components', {})
        print(f"包含组件: {', '.join(components.keys())}")
        
        # 显示关键指标
        if 'performance_monitoring' in components:
            perf = components['performance_monitoring']
            summary = perf.get('performance_summary', {})
            
            if 'queries' in summary:
                queries = summary['queries']
                print(f"\n查询性能:")
                print(f"  平均QPS: {queries.get('avg_qps', 0):.2f}")
                print(f"  最高QPS: {queries.get('max_qps', 0):.2f}")
                print(f"  平均查询时间: {queries.get('avg_query_time', 0):.3f}s")
                print(f"  慢查询总数: {queries.get('total_slow_queries', 0)}")
            
            if 'connections' in summary:
                conns = summary['connections']
                print(f"\n连接池性能:")
                print(f"  平均利用率: {conns.get('avg_pool_utilization', 0):.2%}")
                print(f"  最高利用率: {conns.get('max_pool_utilization', 0):.2%}")
                print(f"  连接错误数: {conns.get('total_connection_errors', 0)}")
            
            # 显示活跃告警
            active_alerts = perf.get('active_alerts', [])
            if active_alerts:
                print(f"\n活跃告警 ({len(active_alerts)}个):")
                for alert in active_alerts[:5]:  # 只显示前5个
                    print(f"  - [{alert['severity'].upper()}] {alert['message']}")
            
            print(f"\n系统健康状态: {perf.get('health_status', 'unknown').upper()}")
        
        # 显示优化建议
        recommendations = report.get('optimization_recommendations', [])
        if recommendations:
            print(f"\n优化建议:")
            for i, rec in enumerate(recommendations[:5], 1):
                print(f"  {i}. {rec}")
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"logs/comprehensive_performance_report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n详细报告已保存到: {report_file}")
        
    except Exception as e:
        logger.error(f"生成性能报告失败: {e}")
        return False
    finally:
        if db_performance_manager.initialized:
            await db_performance_manager.cleanup()
    
    return True


async def run_quick_optimization():
    """运行快速优化"""
    print("=" * 60)
    print("执行数据库快速优化")
    print("=" * 60)
    
    success = True
    
    # 1. 索引优化
    print("1. 运行索引优化...")
    if not await run_index_optimization():
        success = False
    
    # 2. 初始化性能监控收集一些数据
    print("\n2. 初始化性能监控系统...")
    try:
        await db_performance_manager.initialize()
        
        # 收集一些初始指标
        await asyncio.sleep(5)
        
        status = await db_performance_manager.get_performance_status()
        if status.get('initialized'):
            print("   性能监控系统初始化成功")
        else:
            print("   性能监控系统初始化失败")
            success = False
        
        await db_performance_manager.cleanup()
        
    except Exception as e:
        logger.error(f"性能监控初始化失败: {e}")
        success = False
    
    # 3. 生成初始报告
    print("\n3. 生成性能报告...")
    if not await generate_performance_report(hours=1):
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("快速优化完成！")
        print("\n后续建议:")
        print("  1. 定期运行索引优化: python scripts/run_db_optimization.py --optimize-indexes")
        print("  2. 启动持续监控: python scripts/run_db_optimization.py --monitor")
        print("  3. 定期生成报告: python scripts/run_db_optimization.py --report --hours 24")
    else:
        print("快速优化过程中遇到错误，请检查日志")
    print("=" * 60)
    
    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库性能优化工具')
    parser.add_argument('--optimize-indexes', action='store_true', help='运行索引优化')
    parser.add_argument('--monitor', action='store_true', help='启动性能监控')
    parser.add_argument('--report', action='store_true', help='生成性能报告')
    parser.add_argument('--quick', action='store_true', help='执行快速优化')
    parser.add_argument('--hours', type=int, default=24, help='报告时间范围（小时）')
    
    args = parser.parse_args()
    
    # 确保日志目录存在
    Path('logs').mkdir(exist_ok=True)
    
    # 根据参数执行相应操作
    if args.optimize_indexes:
        asyncio.run(run_index_optimization())
    elif args.monitor:
        asyncio.run(start_performance_monitoring())
    elif args.report:
        asyncio.run(generate_performance_report(args.hours))
    elif args.quick:
        asyncio.run(run_quick_optimization())
    else:
        print("数据库性能优化工具")
        print("使用方法:")
        print("  --optimize-indexes  运行索引优化")
        print("  --monitor          启动性能监控")
        print("  --report           生成性能报告")
        print("  --quick            执行快速优化")
        print("  --hours N          报告时间范围（默认24小时）")
        print("")
        print("示例:")
        print("  python scripts/run_db_optimization.py --quick")
        print("  python scripts/run_db_optimization.py --monitor")
        print("  python scripts/run_db_optimization.py --report --hours 12")


if __name__ == "__main__":
    main()