"""
监控集成配置
统一管理Sentry、Prometheus等监控服务的集成
"""

import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime

import sentry_sdk
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.loguru import LoguruIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.integrations.fastapi import FastApiIntegration

from prometheus_client import (
    Counter, Histogram, Gauge, CollectorRegistry, 
    multiprocess, generate_latest, CONTENT_TYPE_LATEST
)
from loguru import logger

from app.core.config import settings


@dataclass
class SentryConfig:
    """Sentry配置"""
    dsn: Optional[str] = None
    environment: str = "development"
    release: Optional[str] = None
    traces_sample_rate: float = 0.1
    profiles_sample_rate: float = 0.1
    max_breadcrumbs: int = 100
    debug: bool = False
    send_default_pii: bool = False
    attach_stacktrace: bool = True
    include_local_variables: bool = False
    include_source_context: bool = True
    ca_certs: Optional[str] = None
    before_send_transaction_rate: float = 1.0
    enabled_integrations: List[str] = field(default_factory=lambda: [
        "sqlalchemy", "asyncio", "redis", "loguru", "starlette", "fastapi"
    ])


@dataclass
class PrometheusConfig:
    """Prometheus配置"""
    enabled: bool = True
    metrics_path: str = "/metrics"
    registry: Optional[CollectorRegistry] = None
    multiprocess_mode: bool = False
    include_default_metrics: bool = True
    custom_labels: Dict[str, str] = field(default_factory=dict)


class SentryIntegration:
    """Sentry集成管理"""
    
    def __init__(self, config: SentryConfig = None):
        self.config = config or SentryConfig()
        self.is_initialized = False

    def initialize(self) -> bool:
        """初始化Sentry"""
        if self.is_initialized:
            return True
            
        if not self.config.dsn:
            self.config.dsn = getattr(settings, "SENTRY_DSN", None)
            
        if not self.config.dsn:
            logger.warning("Sentry DSN未配置，跳过Sentry初始化")
            return False
            
        try:
            # 配置集成
            integrations = self._get_integrations()
            
            # 初始化Sentry
            sentry_sdk.init(
                dsn=self.config.dsn,
                environment=self.config.environment,
                release=self.config.release or getattr(settings, "APP_VERSION", "1.0.0"),
                traces_sample_rate=self.config.traces_sample_rate,
                profiles_sample_rate=self.config.profiles_sample_rate,
                max_breadcrumbs=self.config.max_breadcrumbs,
                debug=self.config.debug,
                send_default_pii=self.config.send_default_pii,
                attach_stacktrace=self.config.attach_stacktrace,
                include_local_variables=self.config.include_local_variables,
                include_source_context=self.config.include_source_context,
                ca_certs=self.config.ca_certs,
                integrations=integrations,
                before_send=self._before_send,
                before_send_transaction=self._before_send_transaction,
            )
            
            # 设置全局标签
            sentry_sdk.set_tag("service", "quant-platform-backend")
            sentry_sdk.set_tag("component", "backend")
            
            # 设置全局上下文
            sentry_sdk.set_context("application", {
                "name": "quant-platform",
                "version": self.config.release or "1.0.0",
                "environment": self.config.environment
            })
            
            self.is_initialized = True
            logger.info(f"Sentry已初始化，环境: {self.config.environment}")
            return True
            
        except Exception as e:
            logger.error(f"Sentry初始化失败: {e}")
            return False

    def _get_integrations(self) -> List[Any]:
        """获取Sentry集成"""
        integrations = []
        
        integration_map = {
            "sqlalchemy": SqlalchemyIntegration(),
            "asyncio": AsyncioIntegration(),
            "redis": RedisIntegration(),
            "loguru": LoguruIntegration(),
            "starlette": StarletteIntegration(transaction_style="endpoint"),
            "fastapi": FastApiIntegration(transaction_style="endpoint"),
        }
        
        for integration_name in self.config.enabled_integrations:
            if integration_name in integration_map:
                integrations.append(integration_map[integration_name])
        
        return integrations

    def _before_send(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """事件发送前的处理"""
        # 过滤敏感信息
        if 'request' in event:
            request = event['request']
            if 'data' in request:
                request['data'] = self._sanitize_data(request['data'])
            if 'headers' in request:
                request['headers'] = self._sanitize_headers(request['headers'])
        
        # 开发环境不发送某些错误
        if self.config.environment == "development":
            if event.get('level') == 'info':
                return None
        
        return event

    def _before_send_transaction(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """事务发送前的处理"""
        # 过滤短时间事务
        if event.get('type') == 'transaction':
            duration = event.get('timestamp', 0) - event.get('start_timestamp', 0)
            if duration < 0.1:  # 小于100ms的事务不发送
                return None
        
        return event

    def _sanitize_data(self, data: Any) -> Any:
        """清理敏感数据"""
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in ['password', 'token', 'secret', 'key']):
                    sanitized[key] = '[Filtered]'
                else:
                    sanitized[key] = self._sanitize_data(value)
            return sanitized
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        return data

    def _sanitize_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """清理敏感头部"""
        sanitized = {}
        for key, value in headers.items():
            if any(sensitive in key.lower() for sensitive in ['authorization', 'cookie', 'token']):
                sanitized[key] = '[Filtered]'
            else:
                sanitized[key] = value
        return sanitized

    def capture_exception(self, exception: Exception, **kwargs):
        """捕获异常"""
        if self.is_initialized:
            sentry_sdk.capture_exception(exception, **kwargs)

    def capture_message(self, message: str, level: str = "info", **kwargs):
        """捕获消息"""
        if self.is_initialized:
            sentry_sdk.capture_message(message, level=level, **kwargs)

    def set_user(self, user_info: Dict[str, Any]):
        """设置用户信息"""
        if self.is_initialized:
            sentry_sdk.set_user(user_info)

    def set_context(self, key: str, context: Dict[str, Any]):
        """设置上下文"""
        if self.is_initialized:
            sentry_sdk.set_context(key, context)

    def add_breadcrumb(self, message: str, category: str = "default", level: str = "info", data: Dict[str, Any] = None):
        """添加面包屑"""
        if self.is_initialized:
            sentry_sdk.add_breadcrumb(
                message=message,
                category=category,
                level=level,
                data=data or {},
                timestamp=datetime.utcnow().timestamp()
            )


class PrometheusMetrics:
    """Prometheus指标管理"""
    
    def __init__(self, config: PrometheusConfig = None):
        self.config = config or PrometheusConfig()
        self.registry = config.registry if config else None
        self.metrics = {}
        
        if self.config.multiprocess_mode:
            self.registry = CollectorRegistry()
            multiprocess.MultiProcessCollector(self.registry)
        
        self._initialize_metrics()

    def _initialize_metrics(self):
        """初始化基础指标"""
        # HTTP请求指标
        self.metrics['http_requests_total'] = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code'],
            registry=self.registry
        )
        
        self.metrics['http_request_duration'] = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # 数据库指标
        self.metrics['db_connections_active'] = Gauge(
            'db_connections_active',
            'Active database connections',
            registry=self.registry
        )
        
        self.metrics['db_query_duration'] = Histogram(
            'db_query_duration_seconds',
            'Database query duration',
            ['query_type'],
            registry=self.registry
        )
        
        # 业务指标
        self.metrics['trading_orders_total'] = Counter(
            'trading_orders_total',
            'Total trading orders',
            ['order_type', 'status'],
            registry=self.registry
        )
        
        self.metrics['active_strategies'] = Gauge(
            'active_strategies',
            'Number of active trading strategies',
            registry=self.registry
        )
        
        self.metrics['market_data_updates'] = Counter(
            'market_data_updates_total',
            'Total market data updates',
            ['symbol', 'data_type'],
            registry=self.registry
        )
        
        # 系统指标
        self.metrics['errors_total'] = Counter(
            'errors_total',
            'Total errors',
            ['error_type', 'severity'],
            registry=self.registry
        )
        
        self.metrics['cache_operations'] = Counter(
            'cache_operations_total',
            'Total cache operations',
            ['operation', 'status'],
            registry=self.registry
        )
        
        # CTP指标
        self.metrics['ctp_connections'] = Gauge(
            'ctp_connections',
            'CTP connection status',
            ['connection_type'],
            registry=self.registry
        )
        
        logger.info("Prometheus指标已初始化")

    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """记录HTTP请求"""
        self.metrics['http_requests_total'].labels(
            method=method, endpoint=endpoint, status_code=status_code
        ).inc()
        
        self.metrics['http_request_duration'].labels(
            method=method, endpoint=endpoint
        ).observe(duration)

    def record_db_operation(self, query_type: str, duration: float):
        """记录数据库操作"""
        self.metrics['db_query_duration'].labels(query_type=query_type).observe(duration)

    def record_trading_order(self, order_type: str, status: str):
        """记录交易订单"""
        self.metrics['trading_orders_total'].labels(
            order_type=order_type, status=status
        ).inc()

    def set_active_strategies(self, count: int):
        """设置活跃策略数量"""
        self.metrics['active_strategies'].set(count)

    def record_market_data_update(self, symbol: str, data_type: str):
        """记录市场数据更新"""
        self.metrics['market_data_updates'].labels(
            symbol=symbol, data_type=data_type
        ).inc()

    def record_error(self, error_type: str, severity: str):
        """记录错误"""
        self.metrics['errors_total'].labels(
            error_type=error_type, severity=severity
        ).inc()

    def record_cache_operation(self, operation: str, status: str):
        """记录缓存操作"""
        self.metrics['cache_operations'].labels(
            operation=operation, status=status
        ).inc()

    def set_ctp_connection_status(self, connection_type: str, status: int):
        """设置CTP连接状态"""
        self.metrics['ctp_connections'].labels(connection_type=connection_type).set(status)

    def get_metrics(self) -> str:
        """获取指标数据"""
        return generate_latest(self.registry)

    def get_content_type(self) -> str:
        """获取内容类型"""
        return CONTENT_TYPE_LATEST


class MonitoringIntegration:
    """监控集成管理器"""
    
    def __init__(self, sentry_config: SentryConfig = None, prometheus_config: PrometheusConfig = None):
        self.sentry = SentryIntegration(sentry_config)
        self.prometheus = PrometheusMetrics(prometheus_config) if prometheus_config and prometheus_config.enabled else None
        self.is_initialized = False

    async def initialize(self) -> bool:
        """初始化监控集成"""
        if self.is_initialized:
            return True
            
        success = True
        
        # 初始化Sentry
        if not self.sentry.initialize():
            success = False
            
        # 初始化Prometheus（如果启用）
        if self.prometheus:
            logger.info("Prometheus指标收集已启用")
        
        self.is_initialized = success
        
        if success:
            logger.info("监控集成初始化完成")
        else:
            logger.warning("监控集成初始化部分失败")
            
        return success

    def capture_exception(self, exception: Exception, **kwargs):
        """捕获异常到所有监控服务"""
        self.sentry.capture_exception(exception, **kwargs)
        
        if self.prometheus:
            error_type = type(exception).__name__
            severity = kwargs.get('level', 'error')
            self.prometheus.record_error(error_type, severity)

    def capture_message(self, message: str, level: str = "info", **kwargs):
        """捕获消息到所有监控服务"""
        self.sentry.capture_message(message, level, **kwargs)

    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """记录HTTP请求"""
        if self.prometheus:
            self.prometheus.record_http_request(method, endpoint, status_code, duration)

    def record_business_metric(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """记录业务指标"""
        if self.prometheus and hasattr(self.prometheus.metrics, metric_name):
            metric = self.prometheus.metrics[metric_name]
            if labels:
                metric.labels(**labels).set(value) if hasattr(metric, 'set') else metric.labels(**labels).inc(value)
            else:
                metric.set(value) if hasattr(metric, 'set') else metric.inc(value)

    def set_user_context(self, user_info: Dict[str, Any]):
        """设置用户上下文"""
        self.sentry.set_user(user_info)

    def add_breadcrumb(self, message: str, category: str = "default", level: str = "info", data: Dict[str, Any] = None):
        """添加面包屑"""
        self.sentry.add_breadcrumb(message, category, level, data)

    def get_prometheus_metrics(self) -> Optional[str]:
        """获取Prometheus指标"""
        if self.prometheus:
            return self.prometheus.get_metrics()
        return None


# 全局监控集成实例
monitoring = MonitoringIntegration(
    sentry_config=SentryConfig(
        environment=getattr(settings, "ENVIRONMENT", "development"),
        debug=getattr(settings, "DEBUG", False),
        traces_sample_rate=getattr(settings, "SENTRY_TRACES_SAMPLE_RATE", 0.1),
    ),
    prometheus_config=PrometheusConfig(
        enabled=getattr(settings, "PROMETHEUS_ENABLED", True),
    )
)


async def setup_monitoring():
    """设置监控集成"""
    await monitoring.initialize()


def get_monitoring() -> MonitoringIntegration:
    """获取监控集成实例"""
    return monitoring