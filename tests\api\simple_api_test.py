"""
简单的API测试服务
用于测试历史股票数据的API接口
"""

from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Dict, Any
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.historical_stock_service import historical_stock_service

app = FastAPI(title="历史股票数据测试API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {"message": "历史股票数据测试API", "status": "running"}

@app.get("/api/v1/market-data/stocks")
async def get_stocks(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(50, ge=1, le=200, description="每页数量"),
    market: Optional[str] = Query(None, description="市场代码")
) -> List[Dict[str, Any]]:
    """获取股票列表"""
    try:
        result = await historical_stock_service.get_stock_list(
            page=page,
            page_size=pageSize,
            market=market
        )
        return result["stocks"]
    except Exception as e:
        return {"error": str(e), "stocks": []}

@app.get("/api/v1/market-data/overview")
async def get_market_overview():
    """获取市场概览"""
    try:
        # 获取各市场的股票数据
        sh_stocks = await historical_stock_service.get_stock_list(page=1, page_size=1000, market="SH")
        sz_stocks = await historical_stock_service.get_stock_list(page=1, page_size=1000, market="SZ")
        
        # 计算统计数据
        sh_up = sum(1 for stock in sh_stocks["stocks"] if stock.get("changePercent", 0) > 0)
        sh_down = sum(1 for stock in sh_stocks["stocks"] if stock.get("changePercent", 0) < 0)
        sh_flat = len(sh_stocks["stocks"]) - sh_up - sh_down
        sh_volume = sum(stock.get("volume", 0) for stock in sh_stocks["stocks"])
        sh_turnover = sum(stock.get("turnover", 0) for stock in sh_stocks["stocks"])
        
        sz_up = sum(1 for stock in sz_stocks["stocks"] if stock.get("changePercent", 0) > 0)
        sz_down = sum(1 for stock in sz_stocks["stocks"] if stock.get("changePercent", 0) < 0)
        sz_flat = len(sz_stocks["stocks"]) - sz_up - sz_down
        sz_volume = sum(stock.get("volume", 0) for stock in sz_stocks["stocks"])
        sz_turnover = sum(stock.get("turnover", 0) for stock in sz_stocks["stocks"])
        
        return {
            "marketStats": [
                {
                    "market": "SH",
                    "name": "上证主板",
                    "upCount": sh_up,
                    "downCount": sh_down,
                    "flatCount": sh_flat,
                    "totalVolume": sh_volume,
                    "totalTurnover": sh_turnover,
                },
                {
                    "market": "SZ",
                    "name": "深证主板", 
                    "upCount": sz_up,
                    "downCount": sz_down,
                    "flatCount": sz_flat,
                    "totalVolume": sz_volume,
                    "totalTurnover": sz_turnover,
                },
            ],
            "timestamp": 1640995200  # 示例时间戳
        }
    except Exception as e:
        return {"error": str(e), "marketStats": []}

@app.get("/api/v1/market-data/quote/{symbol}")
async def get_quote(symbol: str):
    """获取单只股票行情"""
    try:
        quote = await historical_stock_service.get_stock_quote(symbol)
        if quote:
            return quote
        else:
            return {"error": f"未找到股票 {symbol} 的数据"}
    except Exception as e:
        return {"error": str(e)}

@app.get("/api/v1/market-data/symbols")
async def get_available_symbols():
    """获取所有可用的股票代码"""
    try:
        symbols = await historical_stock_service.get_available_symbols()
        return {"symbols": symbols, "count": len(symbols)}
    except Exception as e:
        return {"error": str(e), "symbols": []}

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 测试历史股票服务是否正常
        result = await historical_stock_service.get_stock_list(page=1, page_size=1)
        return {
            "status": "healthy",
            "historical_service": "ok" if result["stocks"] else "no_data",
            "total_stocks": result["total"]
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动历史股票数据测试API服务...")
    print("📊 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    uvicorn.run(app, host="0.0.0.0", port=8000)
