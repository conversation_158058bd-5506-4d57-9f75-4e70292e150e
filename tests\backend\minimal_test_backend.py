#!/usr/bin/env python3
"""
最简版本的测试后端 - 无外部依赖
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import random
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
import json

app = FastAPI(title="量化投资测试平台", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
MOCK_STOCKS = [
    {"symbol": "000001", "name": "平安银行", "market": "sz"},
    {"symbol": "000002", "name": "万科A", "market": "sz"},
    {"symbol": "600000", "name": "浦发银行", "market": "sh"},
    {"symbol": "600036", "name": "招商银行", "market": "sh"},
    {"symbol": "600519", "name": "贵州茅台", "market": "sh"},
    {"symbol": "000858", "name": "五粮液", "market": "sz"},
    {"symbol": "002415", "name": "海康威视", "market": "sz"},
    {"symbol": "002594", "name": "比亚迪", "market": "sz"},
    {"symbol": "300059", "name": "东方财富", "market": "sz"},
    {"symbol": "002230", "name": "科大讯飞", "market": "sz"}
]

MOCK_ORDERS = []
MOCK_POSITIONS = []
MOCK_STRATEGIES = []

def generate_mock_price(base_price: float = 15.0) -> float:
    """生成模拟价格"""
    return round(base_price * (0.9 + random.random() * 0.2), 2)

def generate_mock_kline_data(symbol: str, days: int = 100) -> List[Dict]:
    """生成模拟K线数据"""
    data = []
    base_price = random.uniform(10, 50)
    
    for i in range(days):
        date = (datetime.now() - timedelta(days=days-i)).strftime("%Y-%m-%d")
        
        # 生成OHLC数据
        open_price = base_price * (0.98 + random.random() * 0.04)
        close_price = open_price * (0.95 + random.random() * 0.1)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.05)
        low_price = min(open_price, close_price) * (0.95 + random.random() * 0.05)
        volume = random.randint(1000000, 10000000)
        
        data.append({
            "date": date,
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume
        })
        
        base_price = close_price  # 下一天的基础价格
    
    return data

# ==================== 基础路由 ====================
@app.get("/")
async def root():
    return {
        "project": "量化投资测试平台",
        "version": "1.0.0",
        "description": "量化投资平台API接口",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "operational",
            "database": "mock",
            "market_data": "mock"
        }
    }

# ==================== 市场数据接口 ====================
@app.get("/api/v1/market/stocks")
async def get_stocks():
    return {
        "status": "success",
        "data": MOCK_STOCKS,
        "total": len(MOCK_STOCKS)
    }

@app.get("/api/v1/market/quotes/{symbol}")
async def get_stock_quote(symbol: str):
    # 查找股票
    stock = next((s for s in MOCK_STOCKS if s["symbol"] == symbol), None)
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    price = generate_mock_price()
    return {
        "status": "success",
        "data": {
            "symbol": symbol,
            "name": stock["name"],
            "price": price,
            "change": round(random.uniform(-2, 2), 2),
            "change_percent": round(random.uniform(-5, 5), 2),
            "volume": random.randint(1000000, 50000000),
            "turnover": round(random.uniform(100000000, 1000000000), 2),
            "high": round(price * 1.05, 2),
            "low": round(price * 0.95, 2),
            "open": round(price * (0.98 + random.random() * 0.04), 2),
            "pre_close": round(price * (0.99 + random.random() * 0.02), 2),
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/v1/market/kline/{symbol}")
async def get_kline_data(symbol: str, period: str = "1d", limit: int = 100):
    stock = next((s for s in MOCK_STOCKS if s["symbol"] == symbol), None)
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    kline_data = generate_mock_kline_data(symbol, min(limit, 252))
    
    return {
        "status": "success",
        "data": {
            "symbol": symbol,
            "period": period,
            "klines": kline_data[-limit:]
        }
    }

@app.get("/api/v1/market/depth/{symbol}")
async def get_market_depth(symbol: str):
    stock = next((s for s in MOCK_STOCKS if s["symbol"] == symbol), None)
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    base_price = generate_mock_price()
    
    # 生成买盘和卖盘数据
    bids = []
    asks = []
    
    for i in range(5):
        bid_price = round(base_price * (0.998 - i * 0.001), 2)
        bid_volume = random.randint(100, 10000)
        bids.append({"price": bid_price, "volume": bid_volume})
        
        ask_price = round(base_price * (1.002 + i * 0.001), 2)
        ask_volume = random.randint(100, 10000)
        asks.append({"price": ask_price, "volume": ask_volume})
    
    return {
        "status": "success",
        "data": {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "timestamp": datetime.now().isoformat()
        }
    }

# ==================== 交易终端接口 ====================
@app.get("/api/v1/trading/overview")
async def get_trading_overview():
    return {
        "status": "success",
        "data": {
            "account": {
                "total_assets": 1000000.00,
                "available_cash": 200000.00,
                "market_value": 800000.00,
                "profit_loss": 50000.00,
                "profit_loss_percent": 5.26
            },
            "positions_summary": {
                "total_positions": 8,
                "profit_positions": 5,
                "loss_positions": 3
            },
            "today_trading": {
                "buy_orders": 3,
                "sell_orders": 2,
                "total_volume": 15000,
                "total_amount": 450000.00
            }
        }
    }

@app.get("/api/v1/trading/positions")
async def get_positions():
    # 生成模拟持仓数据
    positions = []
    for i, stock in enumerate(MOCK_STOCKS[:6]):
        price = generate_mock_price()
        quantity = random.randint(100, 5000)
        cost_price = price * (0.9 + random.random() * 0.2)
        
        positions.append({
            "symbol": stock["symbol"],
            "name": stock["name"],
            "quantity": quantity,
            "available_quantity": quantity - random.randint(0, quantity//2),
            "cost_price": round(cost_price, 2),
            "current_price": price,
            "market_value": round(price * quantity, 2),
            "profit_loss": round((price - cost_price) * quantity, 2),
            "profit_loss_percent": round((price - cost_price) / cost_price * 100, 2)
        })
    
    return {
        "status": "success",
        "data": positions
    }

@app.post("/api/v1/trading/quick-order")
async def quick_order(order_data: dict):
    # 模拟下单
    order_id = random.randint(100000, 999999)
    
    order = {
        "order_id": order_id,
        "symbol": order_data.get("symbol"),
        "side": order_data.get("side"),
        "order_type": order_data.get("order_type"),
        "quantity": order_data.get("quantity"),
        "price": order_data.get("price"),
        "status": "pending",
        "create_time": datetime.now().isoformat()
    }
    
    MOCK_ORDERS.append(order)
    
    return {
        "status": "success",
        "message": "订单提交成功",
        "data": order
    }

# ==================== 订单管理接口 ====================
@app.get("/api/v1/orders")
async def get_orders(page: int = 1, size: int = 20):
    # 如果没有模拟订单，生成一些
    if not MOCK_ORDERS:
        for i in range(10):
            stock = random.choice(MOCK_STOCKS)
            order = {
                "order_id": 100000 + i,
                "symbol": stock["symbol"],
                "name": stock["name"],
                "side": random.choice(["buy", "sell"]),
                "order_type": random.choice(["limit", "market"]),
                "quantity": random.randint(100, 2000),
                "price": generate_mock_price(),
                "filled_quantity": 0,
                "status": random.choice(["pending", "filled", "cancelled"]),
                "create_time": (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat()
            }
            MOCK_ORDERS.append(order)
    
    start = (page - 1) * size
    end = start + size
    
    return {
        "status": "success",
        "data": MOCK_ORDERS[start:end],
        "pagination": {
            "page": page,
            "size": size,
            "total": len(MOCK_ORDERS),
            "pages": (len(MOCK_ORDERS) + size - 1) // size
        }
    }

@app.get("/api/v1/orders/{order_id}")
async def get_order_detail(order_id: int):
    order = next((o for o in MOCK_ORDERS if o["order_id"] == order_id), None)
    if not order:
        # 创建一个模拟订单
        stock = random.choice(MOCK_STOCKS)
        order = {
            "order_id": order_id,
            "symbol": stock["symbol"],
            "name": stock["name"],
            "side": random.choice(["buy", "sell"]),
            "order_type": "limit",
            "quantity": 1000,
            "price": generate_mock_price(),
            "filled_quantity": 800,
            "status": "partial_filled",
            "create_time": datetime.now().isoformat(),
            "update_time": datetime.now().isoformat()
        }
    
    return {
        "status": "success",
        "data": order
    }

@app.put("/api/v1/orders/{order_id}")
async def modify_order(order_id: int, modify_data: dict):
    return {
        "status": "success",
        "message": "订单修改成功",
        "data": {
            "order_id": order_id,
            "modified_fields": modify_data,
            "update_time": datetime.now().isoformat()
        }
    }

# ==================== 策略开发接口 ====================
@app.get("/api/v1/strategies")
async def get_strategies():
    if not MOCK_STRATEGIES:
        # 生成模拟策略
        strategies = [
            {
                "id": 1,
                "name": "均线策略",
                "description": "基于移动平均线的交易策略",
                "status": "running",
                "total_return": 12.5,
                "annual_return": 15.8,
                "max_drawdown": -5.2,
                "sharpe_ratio": 1.45,
                "create_time": "2024-01-01T00:00:00"
            },
            {
                "id": 2,
                "name": "RSI策略",
                "description": "基于相对强弱指标的策略",
                "status": "stopped",
                "total_return": 8.3,
                "annual_return": 10.2,
                "max_drawdown": -3.8,
                "sharpe_ratio": 1.28,
                "create_time": "2024-02-01T00:00:00"
            }
        ]
        MOCK_STRATEGIES.extend(strategies)
    
    return {
        "status": "success",
        "data": MOCK_STRATEGIES
    }

@app.get("/api/v1/strategies/templates")
async def get_strategy_templates():
    templates = [
        {
            "id": 1,
            "name": "双均线策略",
            "description": "使用快慢均线交叉信号进行交易",
            "category": "trend_following",
            "code_template": "# 双均线策略模板\ndef strategy():\n    pass"
        },
        {
            "id": 2,
            "name": "布林带策略",
            "description": "基于布林带指标的均值回归策略",
            "category": "mean_reversion",
            "code_template": "# 布林带策略模板\ndef strategy():\n    pass"
        }
    ]
    
    return {
        "status": "success",
        "data": templates
    }

@app.post("/api/v1/strategies")
async def create_strategy(strategy_data: dict):
    strategy_id = len(MOCK_STRATEGIES) + 1
    strategy = {
        "id": strategy_id,
        "name": strategy_data.get("name"),
        "description": strategy_data.get("description"),
        "code": strategy_data.get("code"),
        "parameters": strategy_data.get("parameters", {}),
        "status": "created",
        "create_time": datetime.now().isoformat()
    }
    
    MOCK_STRATEGIES.append(strategy)
    
    return {
        "status": "success",
        "message": "策略创建成功",
        "data": strategy
    }

@app.post("/api/v1/strategies/backtest")
async def backtest_strategy(backtest_data: dict):
    # 模拟回测结果
    return {
        "status": "success",
        "message": "回测完成",
        "data": {
            "strategy_id": backtest_data.get("strategy_id"),
            "start_date": backtest_data.get("start_date"),
            "end_date": backtest_data.get("end_date"),
            "initial_capital": backtest_data.get("initial_capital"),
            "final_capital": 120000.0,
            "total_return": 20.0,
            "annual_return": 25.5,
            "max_drawdown": -8.5,
            "sharpe_ratio": 1.65,
            "win_rate": 65.5,
            "total_trades": 45,
            "backtest_time": datetime.now().isoformat()
        }
    }

# ==================== 历史数据接口 ====================
@app.get("/api/v1/historical/stocks")
async def get_historical_stocks():
    return {
        "status": "success",
        "data": MOCK_STOCKS,
        "message": "历史股票数据列表"
    }

@app.get("/api/v1/historical/data/{symbol}")
async def get_historical_data(symbol: str, start_date: str = None, end_date: str = None):
    stock = next((s for s in MOCK_STOCKS if s["symbol"] == symbol), None)
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    # 生成历史数据
    historical_data = generate_mock_kline_data(symbol, 365)
    
    return {
        "status": "success",
        "data": {
            "symbol": symbol,
            "name": stock["name"],
            "start_date": start_date or "2024-01-01",
            "end_date": end_date or datetime.now().strftime("%Y-%m-%d"),
            "data": historical_data
        }
    }

if __name__ == "__main__":
    print("🚀 启动量化投资测试后端...")
    print("📊 模拟数据已加载")
    print("🔗 API文档: http://localhost:8000/docs")
    print("🏥 健康检查: http://localhost:8000/health")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)