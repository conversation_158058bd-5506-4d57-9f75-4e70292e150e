import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getCurrentAccount,
  switchAccountType,
  createSimulatedAccount as createAccount,
  resetSimulatedAccount as resetAccount
} from '@/api/simulated'

export interface Account {
  id: string
  account_type: 'REAL' | 'SIMULATED'
  available_cash: number
  total_assets: number
  total_profit_loss: number
  total_profit_rate: number
  day_profit_loss: number
  day_profit_rate: number
}

export const useAccountStore = defineStore('account', () => {
  // 状态
  const currentAccount = ref<Account | null>(null)
  const realAccount = ref<Account | null>(null)
  const simulatedAccount = ref<Account | null>(null)
  const isSimulated = ref(false)
  const loading = ref(false)

  // 计算属性
  const availableCash = computed(() => {
    return currentAccount.value ? currentAccount.value.available_cash : 0
  })

  const totalAssets = computed(() => {
    return currentAccount.value ? currentAccount.value.total_assets : 0
  })

  const hasRealAccount = computed(() => {
    return !!realAccount.value
  })

  const hasSimulatedAccount = computed(() => {
    return !!simulatedAccount.value
  })

  const accountPnL = computed(() => {
    if (!currentAccount.value) return null

    return {
      total: currentAccount.value.total_profit_loss,
      totalRate: currentAccount.value.total_profit_rate,
      daily: currentAccount.value.day_profit_loss,
      dailyRate: currentAccount.value.day_profit_rate
    }
  })

  // 方法
  const setCurrentAccount = (account: Account | null) => {
    currentAccount.value = account
    isSimulated.value = account ? account.account_type === 'SIMULATED' : false

    // 更新对应类型的账户
    if (account) {
      if (account.account_type === 'SIMULATED') {
        simulatedAccount.value = account
      } else {
        realAccount.value = account
      }
    }
  }

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const clearAccounts = () => {
    currentAccount.value = null
    realAccount.value = null
    simulatedAccount.value = null
    isSimulated.value = false
  }

  // 异步操作
  const loadCurrentAccount = async () => {
    setLoading(true)
    try {
      const response = await getCurrentAccount()
      if (response.code === 200 && response.data) {
        setCurrentAccount(response.data)
      }
    } catch (error) {
      console.error('加载账户失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const switchAccount = async (accountType: 'REAL' | 'SIMULATED') => {
    setLoading(true)
    try {
      const response = await switchAccountType(accountType)
      if (response.code === 200 && response.data) {
        setCurrentAccount(response.data)

        // 触发全局事件
        window.dispatchEvent(new CustomEvent('account-switched', {
          detail: { accountType, account: response.data }
        }))
      }
      return response
    } catch (error) {
      console.error('切换账户失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const createSimulatedAccount = async (data: any) => {
    try {
      const response = await createAccount(data)
      if (response.code === 200) {
        // 创建成功后重新加载账户
        await loadCurrentAccount()
      }
      return response
    } catch (error) {
      console.error('创建模拟账户失败:', error)
      throw error
    }
  }

  const resetSimulatedAccount = async (accountId: string) => {
    try {
      const response = await resetAccount(accountId)
      if (response.code === 200) {
        // 重置成功后更新当前账户信息
        if (currentAccount.value && currentAccount.value.id === accountId) {
          const accountResponse = await getCurrentAccount()
          if (accountResponse.code === 200) {
            setCurrentAccount(accountResponse.data)
          }
        }

        // 触发全局事件
        window.dispatchEvent(new CustomEvent('account-reset', {
          detail: { accountId }
        }))
      }
      return response
    } catch (error) {
      console.error('重置账户失败:', error)
      throw error
    }
  }

  return {
    // 状态
    currentAccount,
    realAccount,
    simulatedAccount,
    isSimulated,
    loading,

    // 计算属性
    availableCash,
    totalAssets,
    hasRealAccount,
    hasSimulatedAccount,
    accountPnL,

    // 方法
    setCurrentAccount,
    setLoading,
    clearAccounts,
    loadCurrentAccount,
    switchAccount,
    createSimulatedAccount,
    resetSimulatedAccount
  }
})
