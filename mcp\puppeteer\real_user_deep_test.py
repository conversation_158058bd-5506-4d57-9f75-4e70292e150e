#!/usr/bin/env python3
"""
真实用户深度测试脚本 - 使用Puppeteer MCP
模拟真实用户使用量化投资平台的完整体验，发现实际使用中的问题
"""

import asyncio
import json
import time
import random
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealUserDeepTest:
    def __init__(self):
        self.browser: Browser = None
        self.page: Page = None
        self.test_results = {
            'test_session': {
                'start_time': datetime.now().isoformat(),
                'tester_profile': 'Real User Simulation',
                'platform_url': 'http://localhost:5173',
                'test_objectives': [
                    '评估平台易用性',
                    '发现功能性问题',
                    '测试用户工作流',
                    '检查性能表现',
                    '验证数据准确性'
                ]
            },
            'user_journey': [],
            'critical_issues': [],
            'usability_problems': [],
            'performance_issues': [],
            'functional_bugs': [],
            'ui_inconsistencies': [],
            'data_accuracy_issues': [],
            'accessibility_problems': [],
            'security_concerns': []
        }
        self.screenshot_counter = 0
        self.current_scenario = None
        
    async def setup(self):
        """初始化测试环境"""
        logger.info("🚀 启动真实用户深度测试...")
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器观察真实用户行为
            args=[
                '--no-sandbox', 
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--start-maximized'
            ]
        )
        
        # 创建真实用户环境
        context = await self.browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport={'width': 1920, 'height': 1080},
            locale='zh-CN',
            timezone_id='Asia/Shanghai'
        )
        self.page = await context.new_page()
        
        # 监听各种事件
        self.page.on('console', self.handle_console_message)
        self.page.on('pageerror', self.handle_page_error)
        self.page.on('requestfailed', self.handle_request_failed)
        self.page.on('response', self.handle_response)
        
        logger.info("✅ 测试环境初始化完成")

    async def handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type == 'error':
            self.test_results['functional_bugs'].append({
                'type': 'console_error',
                'message': msg.text,
                'timestamp': datetime.now().isoformat(),
                'url': self.page.url,
                'scenario': self.current_scenario
            })
        elif msg.type == 'warning':
            self.test_results['usability_problems'].append({
                'type': 'console_warning',
                'message': msg.text,
                'timestamp': datetime.now().isoformat(),
                'url': self.page.url,
                'scenario': self.current_scenario
            })

    async def handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['critical_issues'].append({
            'type': 'page_error',
            'message': str(error),
            'timestamp': datetime.now().isoformat(),
            'url': self.page.url,
            'scenario': self.current_scenario,
            'severity': 'high'
        })

    async def handle_request_failed(self, request):
        """处理请求失败"""
        self.test_results['functional_bugs'].append({
            'type': 'request_failed',
            'url': request.url,
            'method': request.method,
            'timestamp': datetime.now().isoformat(),
            'scenario': self.current_scenario
        })

    async def handle_response(self, response):
        """处理响应"""
        if response.status >= 400:
            self.test_results['functional_bugs'].append({
                'type': 'http_error',
                'url': response.url,
                'status': response.status,
                'timestamp': datetime.now().isoformat(),
                'scenario': self.current_scenario
            })

    async def take_screenshot(self, name: str):
        """截图"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%H%M%S")
        filename = f"real_user_test_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        await self.page.screenshot(path=filename, full_page=True)
        logger.info(f"📸 截图保存: {filename}")
        return filename

    async def measure_performance(self, action_name: str):
        """测量性能"""
        start_time = time.time()
        
        # 等待网络空闲
        try:
            await self.page.wait_for_load_state('networkidle', timeout=10000)
        except:
            pass
            
        end_time = time.time()
        load_time = end_time - start_time
        
        # 获取性能指标
        try:
            performance_data = await self.page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    return {
                        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                        firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-contentful-paint')?.startTime || 0
                    }
                }
            """)
            
            self.test_results['performance_issues'].append({
                'action': action_name,
                'load_time': load_time,
                'performance_data': performance_data,
                'timestamp': datetime.now().isoformat(),
                'url': self.page.url
            })
            
            if load_time > 3:
                self.test_results['usability_problems'].append({
                    'type': 'slow_loading',
                    'message': f'{action_name} 加载时间过长: {load_time:.2f}秒',
                    'timestamp': datetime.now().isoformat(),
                    'url': self.page.url
                })
                
        except Exception as e:
            logger.warning(f"性能测量失败: {e}")

    async def scenario_first_time_user(self):
        """场景1: 首次访问用户体验"""
        self.current_scenario = "首次访问用户"
        logger.info("🎯 开始场景: 首次访问用户体验")
        
        scenario_data = {
            'name': '首次访问用户体验',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }
        
        try:
            # 1. 访问首页
            logger.info("📍 步骤1: 访问平台首页")
            try:
                await self.page.goto('http://localhost:5173', wait_until='networkidle', timeout=10000)
                await self.measure_performance('首页加载')
                await self.take_screenshot('01_homepage_first_visit')
                scenario_data['steps'].append('访问首页')
            except Exception as e:
                logger.warning(f"无法访问localhost:5173，尝试访问本地HTML文件: {e}")
                # 尝试访问本地HTML文件
                html_path = Path(__file__).parent.parent / 'frontend' / 'index.html'
                if html_path.exists():
                    await self.page.goto(f'file://{html_path.absolute()}')
                    await self.take_screenshot('01_homepage_local_file')
                    scenario_data['steps'].append('访问本地HTML文件')
                else:
                    scenario_data['issues_found'].append('无法访问平台首页')
                    return
            
            # 等待页面完全加载
            await asyncio.sleep(2)
            
            # 2. 检查首页内容和导航
            logger.info("📍 步骤2: 检查首页内容和导航")
            
            # 检查是否有明显的导航菜单
            nav_elements = await self.page.query_selector_all('nav, .nav, .navigation, .menu')
            if not nav_elements:
                scenario_data['issues_found'].append('未找到明显的导航菜单')
                scenario_data['user_feedback'].append('作为新用户，我不知道如何浏览平台的不同功能')
            
            # 检查是否有欢迎信息或引导
            welcome_elements = await self.page.query_selector_all('[class*="welcome"], [class*="intro"], [class*="guide"]')
            if not welcome_elements:
                scenario_data['issues_found'].append('缺少新用户引导或欢迎信息')
                scenario_data['user_feedback'].append('没有看到任何帮助我了解平台功能的信息')
            
            # 3. 尝试理解平台功能
            logger.info("📍 步骤3: 尝试理解平台功能")
            
            # 查找功能介绍或说明
            feature_descriptions = await self.page.query_selector_all('[class*="feature"], [class*="description"], .card')
            if len(feature_descriptions) < 3:
                scenario_data['issues_found'].append('功能介绍不够清晰')
                scenario_data['user_feedback'].append('我无法快速了解这个平台能为我做什么')
            
            await self.take_screenshot('02_homepage_explored')
            
            # 4. 尝试找到主要功能入口
            logger.info("📍 步骤4: 寻找主要功能入口")
            
            # 查找主要功能按钮或链接
            main_buttons = await self.page.query_selector_all('button, .btn, a[href*="/"]')
            clickable_count = len(main_buttons)
            
            if clickable_count < 5:
                scenario_data['issues_found'].append('主要功能入口不够明显')
                scenario_data['user_feedback'].append('我需要花时间寻找如何开始使用平台')
            
            scenario_data['steps'].append(f'发现{clickable_count}个可点击元素')
            
        except Exception as e:
            scenario_data['issues_found'].append(f'页面访问异常: {str(e)}')
            logger.error(f"首次用户场景测试失败: {e}")
        
        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)
        
        logger.info(f"✅ 首次用户场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def scenario_market_data_exploration(self):
        """场景2: 市场数据探索"""
        self.current_scenario = "市场数据探索"
        logger.info("🎯 开始场景: 市场数据探索")

        scenario_data = {
            'name': '市场数据探索',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 导航到市场数据页面
            logger.info("📍 步骤1: 导航到市场数据页面")
            await self.page.goto('http://localhost:5173/market', wait_until='networkidle')
            await self.measure_performance('市场数据页面加载')
            await self.take_screenshot('03_market_page_loaded')
            scenario_data['steps'].append('导航到市场页面')

            await asyncio.sleep(3)  # 等待数据加载

            # 2. 检查数据表格
            logger.info("📍 步骤2: 检查市场数据表格")

            tables = await self.page.query_selector_all('table, .table, .data-table')
            if not tables:
                scenario_data['issues_found'].append('未发现数据表格')
                scenario_data['user_feedback'].append('我期望看到股票或其他金融产品的数据表格')
            else:
                # 检查表格内容
                for i, table in enumerate(tables[:3]):  # 检查前3个表格
                    rows = await table.query_selector_all('tr')
                    if len(rows) < 2:
                        scenario_data['issues_found'].append(f'表格{i+1}数据不足')

            # 3. 检查图表
            logger.info("📍 步骤3: 检查市场图表")

            charts = await self.page.query_selector_all('canvas, svg, .chart, [class*="chart"]')
            if not charts:
                scenario_data['issues_found'].append('未发现图表元素')
                scenario_data['user_feedback'].append('我希望看到价格走势图或其他可视化数据')
            else:
                scenario_data['steps'].append(f'发现{len(charts)}个图表元素')

            # 4. 测试搜索功能
            logger.info("📍 步骤4: 测试搜索功能")

            search_inputs = await self.page.query_selector_all('input[type="search"], input[placeholder*="搜索"], input[placeholder*="search"]')
            if not search_inputs:
                scenario_data['issues_found'].append('未找到搜索功能')
                scenario_data['user_feedback'].append('我想搜索特定的股票或产品，但找不到搜索框')
            else:
                # 尝试使用搜索功能
                search_input = search_inputs[0]
                await search_input.fill('AAPL')
                await search_input.press('Enter')
                await asyncio.sleep(2)
                scenario_data['steps'].append('测试搜索功能')

            # 5. 检查实时数据更新
            logger.info("📍 步骤5: 检查实时数据更新")

            # 等待一段时间看是否有数据更新
            initial_content = await self.page.content()
            await asyncio.sleep(5)
            updated_content = await self.page.content()

            if initial_content == updated_content:
                scenario_data['issues_found'].append('数据似乎没有实时更新')
                scenario_data['user_feedback'].append('我无法确定数据是否是实时的')

        except Exception as e:
            scenario_data['issues_found'].append(f'市场数据页面异常: {str(e)}')
            logger.error(f"市场数据探索场景测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        logger.info(f"✅ 市场数据探索场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def scenario_trading_workflow(self):
        """场景3: 交易工作流测试"""
        self.current_scenario = "交易工作流"
        logger.info("🎯 开始场景: 交易工作流测试")

        scenario_data = {
            'name': '交易工作流测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 导航到交易页面
            logger.info("📍 步骤1: 导航到交易页面")
            await self.page.goto('http://localhost:5173/trading/terminal', wait_until='networkidle')
            await self.measure_performance('交易页面加载')
            await self.take_screenshot('04_trading_page_loaded')
            scenario_data['steps'].append('导航到交易页面')

            await asyncio.sleep(3)

            # 2. 检查交易界面元素
            logger.info("📍 步骤2: 检查交易界面元素")

            # 查找买入/卖出按钮
            buy_buttons = await self.page.query_selector_all('button:has-text("买入"), button:has-text("买"), .buy-btn')
            sell_buttons = await self.page.query_selector_all('button:has-text("卖出"), button:has-text("卖"), .sell-btn')

            if not buy_buttons and not sell_buttons:
                scenario_data['issues_found'].append('未找到买入/卖出按钮')
                scenario_data['user_feedback'].append('我无法找到如何进行交易的按钮')

            # 查找价格输入框
            price_inputs = await self.page.query_selector_all('input[placeholder*="价格"], input[placeholder*="price"]')
            quantity_inputs = await self.page.query_selector_all('input[placeholder*="数量"], input[placeholder*="quantity"]')

            if not price_inputs:
                scenario_data['issues_found'].append('未找到价格输入框')
                scenario_data['user_feedback'].append('我不知道在哪里输入交易价格')

            if not quantity_inputs:
                scenario_data['issues_found'].append('未找到数量输入框')
                scenario_data['user_feedback'].append('我不知道在哪里输入交易数量')

            # 3. 测试模拟交易
            logger.info("📍 步骤3: 测试模拟交易功能")

            # 查找模拟交易相关元素
            simulation_elements = await self.page.query_selector_all('[class*="simulation"], [class*="demo"], [class*="mock"]')
            if simulation_elements:
                scenario_data['steps'].append('发现模拟交易功能')
            else:
                scenario_data['issues_found'].append('未找到明确的模拟交易标识')
                scenario_data['user_feedback'].append('我担心误操作进行真实交易')

            # 4. 检查持仓信息
            logger.info("📍 步骤4: 检查持仓信息")

            position_elements = await self.page.query_selector_all('[class*="position"], [class*="holding"]')
            if not position_elements:
                scenario_data['issues_found'].append('未找到持仓信息显示')
                scenario_data['user_feedback'].append('我无法查看当前的持仓情况')

        except Exception as e:
            scenario_data['issues_found'].append(f'交易页面异常: {str(e)}')
            logger.error(f"交易工作流场景测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        logger.info(f"✅ 交易工作流场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def scenario_strategy_management(self):
        """场景4: 策略管理测试"""
        self.current_scenario = "策略管理"
        logger.info("🎯 开始场景: 策略管理测试")

        scenario_data = {
            'name': '策略管理测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 导航到策略页面
            logger.info("📍 步骤1: 导航到策略页面")
            await self.page.goto('http://localhost:5173/strategy', wait_until='networkidle')
            await self.measure_performance('策略页面加载')
            await self.take_screenshot('05_strategy_page_loaded')
            scenario_data['steps'].append('导航到策略页面')

            await asyncio.sleep(3)

            # 2. 检查策略列表
            logger.info("📍 步骤2: 检查策略列表")

            strategy_cards = await self.page.query_selector_all('.strategy-card, .card, [class*="strategy"]')
            if not strategy_cards:
                scenario_data['issues_found'].append('未找到策略列表')
                scenario_data['user_feedback'].append('我期望看到可用的交易策略列表')
            else:
                scenario_data['steps'].append(f'发现{len(strategy_cards)}个策略卡片')

            # 3. 测试创建新策略
            logger.info("📍 步骤3: 测试创建新策略")

            create_buttons = await self.page.query_selector_all('button:has-text("创建"), button:has-text("新建"), .create-btn')
            if not create_buttons:
                scenario_data['issues_found'].append('未找到创建策略按钮')
                scenario_data['user_feedback'].append('我不知道如何创建自己的交易策略')
            else:
                # 尝试点击创建按钮
                await create_buttons[0].click()
                await asyncio.sleep(2)
                await self.take_screenshot('06_strategy_create_form')
                scenario_data['steps'].append('点击创建策略按钮')

                # 检查表单
                form_inputs = await self.page.query_selector_all('input, textarea, select')
                if len(form_inputs) < 3:
                    scenario_data['issues_found'].append('策略创建表单过于简单')
                    scenario_data['user_feedback'].append('表单字段太少，无法配置复杂的策略')

            # 4. 检查策略回测功能
            logger.info("📍 步骤4: 检查策略回测功能")

            backtest_elements = await self.page.query_selector_all('[class*="backtest"], button:has-text("回测")')
            if not backtest_elements:
                scenario_data['issues_found'].append('未找到回测功能')
                scenario_data['user_feedback'].append('我想测试策略的历史表现，但找不到回测功能')

        except Exception as e:
            scenario_data['issues_found'].append(f'策略页面异常: {str(e)}')
            logger.error(f"策略管理场景测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        logger.info(f"✅ 策略管理场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def scenario_portfolio_management(self):
        """场景5: 投资组合管理测试"""
        self.current_scenario = "投资组合管理"
        logger.info("🎯 开始场景: 投资组合管理测试")

        scenario_data = {
            'name': '投资组合管理测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 导航到投资组合页面
            logger.info("📍 步骤1: 导航到投资组合页面")
            await self.page.goto('http://localhost:5173/portfolio', wait_until='networkidle')
            await self.measure_performance('投资组合页面加载')
            await self.take_screenshot('07_portfolio_page_loaded')
            scenario_data['steps'].append('导航到投资组合页面')

            await asyncio.sleep(3)

            # 2. 检查资产概览
            logger.info("📍 步骤2: 检查资产概览")

            # 查找总资产、收益等关键指标
            asset_elements = await self.page.query_selector_all('[class*="asset"], [class*="balance"], [class*="total"]')
            if not asset_elements:
                scenario_data['issues_found'].append('未找到资产概览信息')
                scenario_data['user_feedback'].append('我无法看到总资产和收益情况')

            # 3. 检查持仓详情
            logger.info("📍 步骤3: 检查持仓详情")

            holdings_table = await self.page.query_selector_all('table, .holdings, [class*="position"]')
            if not holdings_table:
                scenario_data['issues_found'].append('未找到持仓详情表格')
                scenario_data['user_feedback'].append('我想查看具体的持仓明细')

            # 4. 检查收益分析图表
            logger.info("📍 步骤4: 检查收益分析图表")

            charts = await self.page.query_selector_all('canvas, svg, .chart')
            if not charts:
                scenario_data['issues_found'].append('未找到收益分析图表')
                scenario_data['user_feedback'].append('我希望看到收益趋势的可视化展示')

        except Exception as e:
            scenario_data['issues_found'].append(f'投资组合页面异常: {str(e)}')
            logger.error(f"投资组合管理场景测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        logger.info(f"✅ 投资组合管理场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def scenario_risk_management(self):
        """场景6: 风险管理测试"""
        self.current_scenario = "风险管理"
        logger.info("🎯 开始场景: 风险管理测试")

        scenario_data = {
            'name': '风险管理测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 导航到风险管理页面
            logger.info("📍 步骤1: 导航到风险管理页面")
            await self.page.goto('http://localhost:5173/risk', wait_until='networkidle')
            await self.measure_performance('风险管理页面加载')
            await self.take_screenshot('08_risk_page_loaded')
            scenario_data['steps'].append('导航到风险管理页面')

            await asyncio.sleep(3)

            # 2. 检查风险指标
            logger.info("📍 步骤2: 检查风险指标")

            risk_metrics = await self.page.query_selector_all('[class*="risk"], [class*="var"], [class*="metric"]')
            if not risk_metrics:
                scenario_data['issues_found'].append('未找到风险指标显示')
                scenario_data['user_feedback'].append('我无法了解当前的风险水平')

            # 3. 检查风险控制设置
            logger.info("📍 步骤3: 检查风险控制设置")

            risk_controls = await self.page.query_selector_all('input[type="number"], .slider, [class*="limit"]')
            if not risk_controls:
                scenario_data['issues_found'].append('未找到风险控制设置')
                scenario_data['user_feedback'].append('我想设置止损和风险限制，但找不到相关功能')

        except Exception as e:
            scenario_data['issues_found'].append(f'风险管理页面异常: {str(e)}')
            logger.error(f"风险管理场景测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        logger.info(f"✅ 风险管理场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def scenario_mobile_responsiveness(self):
        """场景7: 移动端响应式测试"""
        self.current_scenario = "移动端响应式"
        logger.info("🎯 开始场景: 移动端响应式测试")

        scenario_data = {
            'name': '移动端响应式测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 测试不同屏幕尺寸
            viewports = [
                {'width': 375, 'height': 667, 'name': 'iPhone SE'},
                {'width': 414, 'height': 896, 'name': 'iPhone 11'},
                {'width': 768, 'height': 1024, 'name': 'iPad'},
                {'width': 1024, 'height': 768, 'name': 'iPad横屏'}
            ]

            for viewport in viewports:
                logger.info(f"📍 测试 {viewport['name']} 视口")
                await self.page.set_viewport_size(viewport['width'], viewport['height'])
                await asyncio.sleep(2)

                # 检查导航菜单是否适配
                nav_elements = await self.page.query_selector_all('nav, .nav, .menu')
                if nav_elements:
                    nav_rect = await nav_elements[0].bounding_box()
                    if nav_rect and nav_rect['width'] > viewport['width']:
                        scenario_data['issues_found'].append(f'{viewport["name"]}导航菜单溢出')

                # 检查文字大小
                small_text = await self.page.evaluate("""
                    () => {
                        const elements = document.querySelectorAll('*');
                        let smallTextCount = 0;
                        elements.forEach(el => {
                            const style = window.getComputedStyle(el);
                            const fontSize = parseFloat(style.fontSize);
                            if (fontSize < 14 && el.textContent.trim()) {
                                smallTextCount++;
                            }
                        });
                        return smallTextCount;
                    }
                """)

                if small_text > 10:
                    scenario_data['issues_found'].append(f'{viewport["name"]}存在{small_text}个小文字元素')
                    scenario_data['user_feedback'].append(f'在{viewport["name"]}上文字太小，难以阅读')

                await self.take_screenshot(f'09_responsive_{viewport["name"].replace(" ", "_")}')
                scenario_data['steps'].append(f'测试{viewport["name"]}视口')

            # 恢复桌面视口
            await self.page.set_viewport_size(1920, 1080)

        except Exception as e:
            scenario_data['issues_found'].append(f'响应式测试异常: {str(e)}')
            logger.error(f"移动端响应式场景测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        logger.info(f"✅ 移动端响应式场景完成，耗时: {scenario_data['duration']:.2f}秒")

    async def generate_comprehensive_report(self):
        """生成综合测试报告"""
        logger.info("📊 生成综合测试报告...")

        # 统计问题
        total_issues = (
            len(self.test_results['critical_issues']) +
            len(self.test_results['usability_problems']) +
            len(self.test_results['functional_bugs']) +
            len(self.test_results['ui_inconsistencies'])
        )

        # 计算总测试时间
        total_duration = sum(scenario['duration'] for scenario in self.test_results['user_journey'])

        # 生成报告摘要
        report_summary = {
            'test_completion_time': datetime.now().isoformat(),
            'total_test_duration': f"{total_duration:.2f}秒",
            'scenarios_tested': len(self.test_results['user_journey']),
            'total_issues_found': total_issues,
            'critical_issues_count': len(self.test_results['critical_issues']),
            'usability_problems_count': len(self.test_results['usability_problems']),
            'functional_bugs_count': len(self.test_results['functional_bugs']),
            'performance_issues_count': len(self.test_results['performance_issues']),
            'overall_assessment': self.assess_platform_quality()
        }

        self.test_results['report_summary'] = report_summary

        # 保存详细报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"real_user_deep_test_report_{timestamp}.json"

        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)

        logger.info(f"📋 详细报告已保存: {report_filename}")

        # 打印摘要
        self.print_report_summary(report_summary)

        return report_filename

    def assess_platform_quality(self):
        """评估平台质量"""
        critical_count = len(self.test_results['critical_issues'])
        usability_count = len(self.test_results['usability_problems'])
        functional_count = len(self.test_results['functional_bugs'])

        if critical_count > 5:
            return "需要紧急修复 - 存在严重问题"
        elif functional_count > 10:
            return "需要重要改进 - 功能性问题较多"
        elif usability_count > 15:
            return "需要优化 - 用户体验有待提升"
        else:
            return "基本可用 - 建议持续改进"

    def print_report_summary(self, summary):
        """打印报告摘要"""
        print("\n" + "="*60)
        print("🎯 真实用户深度测试报告摘要")
        print("="*60)
        print(f"📅 测试完成时间: {summary['test_completion_time']}")
        print(f"⏱️  总测试时长: {summary['total_test_duration']}")
        print(f"🧪 测试场景数: {summary['scenarios_tested']}")
        print(f"🐛 发现问题总数: {summary['total_issues_found']}")
        print(f"🚨 严重问题: {summary['critical_issues_count']}")
        print(f"😕 可用性问题: {summary['usability_problems_count']}")
        print(f"🔧 功能性错误: {summary['functional_bugs_count']}")
        print(f"⚡ 性能问题: {summary['performance_issues_count']}")
        print(f"📊 整体评估: {summary['overall_assessment']}")
        print("="*60)

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        logger.info("🧹 测试环境已清理")

if __name__ == "__main__":
    async def main():
        tester = RealUserDeepTest()
        try:
            await tester.setup()
            await tester.scenario_first_time_user()
            await tester.scenario_market_data_exploration()
            await tester.scenario_trading_workflow()
            await tester.scenario_strategy_management()
            await tester.scenario_portfolio_management()
            await tester.scenario_risk_management()
            await tester.scenario_mobile_responsiveness()

            # 生成报告
            report_file = await tester.generate_comprehensive_report()
            logger.info(f"🎉 真实用户深度测试完成！报告文件: {report_file}")

        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
        finally:
            await tester.cleanup()

    asyncio.run(main())
