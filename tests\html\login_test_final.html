<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示登录功能验证</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 30px; 
            background: #f5f5f5;
        }
        .card { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 12px rgba(0,0,0,0.1); 
            margin: 20px 0;
        }
        .success { color: #22c55e; font-weight: bold; }
        .error { color: #ef4444; font-weight: bold; }
        button { 
            background: #3b82f6; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 16px;
            margin: 10px 0;
            width: 100%;
        }
        button:hover { background: #2563eb; }
        .demo-btn { background: #22c55e; }
        .demo-btn:hover { background: #16a34a; }
        #result { 
            background: #f8f9fa; 
            border: 1px solid #e9ecef; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status-good { color: #22c55e; }
        .status-bad { color: #ef4444; }
        h1 { color: #1f2937; margin-bottom: 10px; }
        h2 { color: #374151; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="card">
        <h1>🧪 演示登录功能验证</h1>
        <p>测试量化交易平台的演示登录功能是否正常工作</p>
        
        <h2>📡 服务状态</h2>
        <p>前端服务 (5173): <span id="frontend-status">检查中...</span></p>
        <p>后端服务 (8001): <span id="backend-status">检查中...</span></p>
        
        <h2>🔐 登录测试</h2>
        <button class="demo-btn" onclick="testDemoLogin()">测试演示登录 (admin/admin123)</button>
        <button onclick="testWrongPassword()">测试错误密码 (应失败)</button>
        
        <div id="result"></div>
    </div>

    <script>
        // 检查服务状态
        async function checkServices() {
            // 检查前端
            try {
                const response = await fetch('http://localhost:5173/', { 
                    method: 'HEAD', 
                    mode: 'no-cors',
                    timeout: 5000
                });
                document.getElementById('frontend-status').innerHTML = '<span class="status-good">✅ 正常</span>';
            } catch (e) {
                document.getElementById('frontend-status').innerHTML = '<span class="status-bad">❌ 离线</span>';
            }

            // 检查后端
            try {
                const response = await fetch('http://localhost:8001/health', { timeout: 5000 });
                if (response.ok) {
                    document.getElementById('backend-status').innerHTML = '<span class="status-good">✅ 正常</span>';
                } else {
                    document.getElementById('backend-status').innerHTML = '<span class="status-bad">⚠️ 异常</span>';
                }
            } catch (e) {
                document.getElementById('backend-status').innerHTML = '<span class="status-bad">❌ 离线</span>';
            }
        }

        // 测试演示登录
        async function testDemoLogin() {
            await testLogin('admin', 'admin123', '演示登录');
        }

        // 测试错误密码
        async function testWrongPassword() {
            await testLogin('admin', 'wrongpassword', '错误密码测试');
        }

        // 通用登录测试函数
        async function testLogin(username, password, testName) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = `正在测试 ${testName}...`;
            
            try {
                const startTime = Date.now();
                const response = await fetch('http://localhost:8001/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                const data = await response.json();
                
                let result = `\\n=== ${testName} 结果 ===\\n`;
                result += `时间: ${new Date().toLocaleTimeString()}\\n`;
                result += `响应时间: ${responseTime}ms\\n`;
                result += `HTTP状态: ${response.status}\\n`;
                result += `成功状态: ${data.success}\\n`;
                result += `消息: ${data.message}\\n\\n`;
                
                if (data.success && response.status === 200) {
                    result += `✅ 登录成功！\\n`;
                    result += `用户ID: ${data.data.user.id}\\n`;
                    result += `用户名: ${data.data.user.username}\\n`;
                    result += `邮箱: ${data.data.user.email}\\n`;
                    result += `权限: ${JSON.stringify(data.data.user.permissions)}\\n`;
                    result += `角色: ${data.data.user.role}\\n`;
                    result += `Token: ${data.data.token.substring(0, 50)}...\\n`;
                    
                    if (testName === '演示登录') {
                        result += `\\n🎉 演示登录功能正常！`;
                        result += `\\n前端页面应该能正常使用此账户登录。`;
                    }
                } else {
                    result += `❌ 登录失败\\n`;
                    result += `详细信息: ${JSON.stringify(data, null, 2)}`;
                    
                    if (testName === '错误密码测试') {
                        result += `\\n✅ 错误密码正确被拒绝，安全验证通过！`;
                    }
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `❌ 网络错误: ${error.message}\\n\\n请检查：\\n1. 后端服务是否在8001端口运行\\n2. 网络连接是否正常\\n3. CORS配置是否正确`;
            }
        }

        // 页面加载时检查服务
        window.onload = checkServices;
    </script>

    <div class="card">
        <h2>💡 使用说明</h2>
        <ol>
            <li><strong>确保服务运行:</strong> 前端(5173)和后端(8001)都应显示正常</li>
            <li><strong>测试演示登录:</strong> 点击绿色按钮测试admin/admin123</li>
            <li><strong>访问前端:</strong> <a href="http://localhost:5173/login" target="_blank">http://localhost:5173/login</a></li>
            <li><strong>使用演示账户:</strong> 在前端页面点击"演示登录"按钮</li>
        </ol>
    </div>
</body>
</html>