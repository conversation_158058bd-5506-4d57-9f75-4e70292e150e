"""
测试历史股票服务的简单脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.historical_stock_service import historical_stock_service

async def test_historical_service():
    """测试历史股票服务"""
    print("🧪 测试历史股票服务...")
    
    try:
        # 测试1: 获取股票列表
        print("\n📊 测试1: 获取股票列表...")
        result = await historical_stock_service.get_stock_list(page=1, page_size=10)
        
        print(f"   总股票数: {result['total']}")
        print(f"   当前页股票数: {len(result['stocks'])}")
        
        if result['stocks']:
            print("   前3只股票:")
            for i, stock in enumerate(result['stocks'][:3]):
                print(f"     {i+1}. {stock['symbol']} - {stock['name']}")
                print(f"        交易所: {stock['exchange']}")
                print(f"        行业: {stock['industry']}")
                print(f"        当前价格: {stock.get('currentPrice', 'N/A')}")
        
        # 测试2: 按市场筛选
        print("\n📊 测试2: 按市场筛选...")
        sh_result = await historical_stock_service.get_stock_list(page=1, page_size=5, market="SH")
        sz_result = await historical_stock_service.get_stock_list(page=1, page_size=5, market="SZ")
        
        print(f"   上海市场股票数: {len(sh_result['stocks'])}")
        print(f"   深圳市场股票数: {len(sz_result['stocks'])}")
        
        # 测试3: 获取单只股票行情
        print("\n📊 测试3: 获取单只股票行情...")
        if result['stocks']:
            test_symbol = result['stocks'][0]['symbol']
            quote = await historical_stock_service.get_stock_quote(test_symbol)
            
            if quote:
                print(f"   股票: {quote['symbol']} - {quote['name']}")
                print(f"   当前价格: {quote['currentPrice']}")
                print(f"   涨跌幅: {quote['changePercent']}%")
                print(f"   成交量: {quote['volume']}")
            else:
                print(f"   ❌ 无法获取股票 {test_symbol} 的行情")
        
        # 测试4: 获取可用股票代码
        print("\n📊 测试4: 获取可用股票代码...")
        symbols = await historical_stock_service.get_available_symbols()
        print(f"   可用股票代码数量: {len(symbols)}")
        if symbols:
            print(f"   前10个代码: {symbols[:10]}")
        
        print("\n✅ 历史股票服务测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_historical_service())
    if success:
        print("🎉 所有测试通过！")
    else:
        print("💥 测试失败！")
