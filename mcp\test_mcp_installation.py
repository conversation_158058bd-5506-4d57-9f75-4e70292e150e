#!/usr/bin/env python3
"""
MCP工具安装验证脚本
测试BrowserTools MCP、FileSystem MCP和mcp-use的安装状态
"""

import asyncio
import os
import sys
import subprocess
from pathlib import Path

def check_node_installation():
    """检查Node.js安装"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ Node.js已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js未找到")
        return False

def check_npm_installation():
    """检查npm安装"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ npm已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ npm未安装")
            return False
    except FileNotFoundError:
        print("❌ npm未找到")
        return False

def check_directory_structure():
    """检查目录结构"""
    base_path = Path(__file__).parent
    
    directories_to_check = [
        "browser-tools-mcp",
        "browser-tools-mcp/browser-tools-mcp",
        "browser-tools-mcp/browser-tools-server", 
        "browser-tools-mcp/chrome-extension",
        "servers/src/filesystem",
        "mcp-use"
    ]
    
    print("\n📁 检查目录结构:")
    all_exist = True
    
    for dir_path in directories_to_check:
        full_path = base_path / dir_path
        if full_path.exists():
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} - 不存在")
            all_exist = False
    
    return all_exist

def check_package_json_files():
    """检查package.json文件"""
    base_path = Path(__file__).parent
    
    package_json_paths = [
        "browser-tools-mcp/browser-tools-mcp/package.json",
        "browser-tools-mcp/browser-tools-server/package.json",
        "servers/src/filesystem/package.json"
    ]
    
    print("\n📦 检查package.json文件:")
    all_exist = True
    
    for pkg_path in package_json_paths:
        full_path = base_path / pkg_path
        if full_path.exists():
            print(f"✅ {pkg_path}")
        else:
            print(f"❌ {pkg_path} - 不存在")
            all_exist = False
    
    return all_exist

def check_node_modules():
    """检查node_modules目录"""
    base_path = Path(__file__).parent
    
    node_modules_paths = [
        "browser-tools-mcp/browser-tools-mcp/node_modules",
        "browser-tools-mcp/browser-tools-server/node_modules",
        "servers/src/filesystem/node_modules"
    ]
    
    print("\n📚 检查node_modules目录:")
    all_exist = True
    
    for nm_path in node_modules_paths:
        full_path = base_path / nm_path
        if full_path.exists() and full_path.is_dir():
            # 计算包数量
            try:
                package_count = len([d for d in full_path.iterdir() if d.is_dir() and not d.name.startswith('.')])
                print(f"✅ {nm_path} ({package_count} packages)")
            except:
                print(f"✅ {nm_path}")
        else:
            print(f"❌ {nm_path} - 不存在")
            all_exist = False
    
    return all_exist

def check_python_mcp_use():
    """检查mcp-use Python包"""
    print("\n🐍 检查mcp-use Python包:")
    
    try:
        import mcp_use
        print(f"✅ mcp-use已安装: {mcp_use.__version__ if hasattr(mcp_use, '__version__') else '版本未知'}")
        return True
    except ImportError:
        print("❌ mcp-use未安装")
        return False

def check_mcp_dependencies():
    """检查MCP相关依赖"""
    print("\n🔗 检查MCP相关依赖:")
    
    dependencies = [
        'mcp',
        'langchain', 
        'websockets',
        'aiohttp',
        'pydantic'
    ]
    
    all_installed = True
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - 未安装")
            all_installed = False
    
    return all_installed

async def test_mcp_use_basic():
    """测试mcp-use基本功能"""
    print("\n🧪 测试mcp-use基本功能:")
    
    try:
        from mcp_use import MCPClient
        
        # 创建客户端
        client = MCPClient()
        print("✅ MCPClient创建成功")
        
        # 这里不实际连接，只测试导入和创建
        print("✅ mcp-use基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ mcp-use测试失败: {e}")
        return False

def generate_startup_scripts():
    """生成启动脚本"""
    base_path = Path(__file__).parent
    
    # BrowserTools启动脚本
    browser_tools_script = """@echo off
echo 启动BrowserTools MCP服务器...
cd /d "%~dp0browser-tools-mcp\\browser-tools-server"
start "BrowserTools Server" cmd /k "npm start"

timeout /t 3 /nobreak > nul

cd /d "%~dp0browser-tools-mcp\\browser-tools-mcp"  
start "BrowserTools MCP" cmd /k "npm start"

echo BrowserTools MCP服务器已启动
pause
"""
    
    # FileSystem启动脚本
    filesystem_script = """@echo off
echo 启动FileSystem MCP服务器...
cd /d "%~dp0servers\\src\\filesystem"
start "FileSystem MCP" cmd /k "npm start"

echo FileSystem MCP服务器已启动
pause
"""
    
    # 保存启动脚本
    with open(base_path / "start_browser_tools.bat", "w", encoding="utf-8") as f:
        f.write(browser_tools_script)
    
    with open(base_path / "start_filesystem.bat", "w", encoding="utf-8") as f:
        f.write(filesystem_script)
    
    print("\n📜 生成启动脚本:")
    print("✅ start_browser_tools.bat")
    print("✅ start_filesystem.bat")

def main():
    """主函数"""
    print("🔍 MCP工具安装验证")
    print("=" * 50)
    
    # 检查基础环境
    node_ok = check_node_installation()
    npm_ok = check_npm_installation()
    
    if not (node_ok and npm_ok):
        print("\n❌ 基础环境检查失败，请先安装Node.js和npm")
        return False
    
    # 检查目录结构
    dirs_ok = check_directory_structure()
    
    # 检查package.json文件
    pkg_ok = check_package_json_files()
    
    # 检查node_modules
    modules_ok = check_node_modules()
    
    # 检查Python包
    python_ok = check_python_mcp_use()
    deps_ok = check_mcp_dependencies()
    
    # 测试基本功能
    asyncio.run(test_mcp_use_basic())
    
    # 生成启动脚本
    generate_startup_scripts()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 安装验证总结:")
    
    checks = [
        ("Node.js环境", node_ok and npm_ok),
        ("目录结构", dirs_ok),
        ("配置文件", pkg_ok), 
        ("依赖包", modules_ok),
        ("Python包", python_ok and deps_ok)
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有检查通过！MCP工具安装成功")
        print("\n📋 下一步:")
        print("1. 安装Chrome扩展 (browser-tools-mcp/chrome-extension/)")
        print("2. 运行启动脚本测试服务器")
        print("3. 开始使用MCP工具")
    else:
        print("\n⚠️ 部分检查失败，请检查安装")
    
    return all_passed

if __name__ == "__main__":
    main()
