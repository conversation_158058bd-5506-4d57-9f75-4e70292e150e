"""
WebSocket接口
提供实时数据推送和交互功能
"""
from fastapi import WebSocket, WebSocketDisconnect, Depends
from fastapi.routing import APIRouter
from typing import Dict, List, Any, Optional
import json
import asyncio
import logging
from datetime import datetime
import uuid

from .connection_manager import ConnectionManager

router = APIRouter()
manager = ConnectionManager()

logger = logging.getLogger(__name__)

class WebSocketMessage:
    """WebSocket消息格式"""
    
    def __init__(self, message_type: str, data: Dict[str, Any], client_id: Optional[str] = None):
        self.message_type = message_type
        self.data = data
        self.client_id = client_id
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': self.message_type,
            'data': self.data,
            'client_id': self.client_id,
            'timestamp': self.timestamp.isoformat()
        }
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)

@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点"""
    await manager.connect(websocket, client_id)
    logger.info(f"Client {client_id} connected")
    
    # 发送连接成功消息
    welcome_msg = WebSocketMessage(
        message_type="connection",
        data={
            "status": "connected",
            "client_id": client_id,
            "server_time": datetime.now().isoformat()
        },
        client_id=client_id
    )
    await manager.send_personal_message(welcome_msg.to_json(), websocket)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            await handle_client_message(data, client_id, websocket)
            
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {str(e)}")
        manager.disconnect(websocket)

async def handle_client_message(data: str, client_id: str, websocket: WebSocket):
    """处理客户端消息"""
    try:
        message = json.loads(data)
        message_type = message.get('type')
        message_data = message.get('data', {})
        
        logger.info(f"Received message from {client_id}: {message_type}")
        
        # 根据消息类型分发处理
        if message_type == "subscribe":
            await handle_subscribe(message_data, client_id, websocket)
        elif message_type == "unsubscribe":
            await handle_unsubscribe(message_data, client_id, websocket)
        elif message_type == "backtest_control":
            await handle_backtest_control(message_data, client_id, websocket)
        elif message_type == "ping":
            await handle_ping(client_id, websocket)
        elif message_type == "get_status":
            await handle_get_status(message_data, client_id, websocket)
        else:
            # 未知消息类型
            error_msg = WebSocketMessage(
                message_type="error",
                data={"message": f"Unknown message type: {message_type}"},
                client_id=client_id
            )
            await manager.send_personal_message(error_msg.to_json(), websocket)
            
    except json.JSONDecodeError:
        error_msg = WebSocketMessage(
            message_type="error",
            data={"message": "Invalid JSON format"},
            client_id=client_id
        )
        await manager.send_personal_message(error_msg.to_json(), websocket)
    except Exception as e:
        logger.error(f"Error handling message from {client_id}: {str(e)}")
        error_msg = WebSocketMessage(
            message_type="error",
            data={"message": f"Server error: {str(e)}"},
            client_id=client_id
        )
        await manager.send_personal_message(error_msg.to_json(), websocket)

async def handle_subscribe(data: Dict[str, Any], client_id: str, websocket: WebSocket):
    """处理订阅请求"""
    topics = data.get('topics', [])
    
    for topic in topics:
        manager.subscribe_client(client_id, topic)
        logger.info(f"Client {client_id} subscribed to {topic}")
    
    # 发送订阅确认
    response = WebSocketMessage(
        message_type="subscribe_ack",
        data={
            "topics": topics,
            "message": f"Successfully subscribed to {len(topics)} topics"
        },
        client_id=client_id
    )
    await manager.send_personal_message(response.to_json(), websocket)

async def handle_unsubscribe(data: Dict[str, Any], client_id: str, websocket: WebSocket):
    """处理取消订阅请求"""
    topics = data.get('topics', [])
    
    for topic in topics:
        manager.unsubscribe_client(client_id, topic)
        logger.info(f"Client {client_id} unsubscribed from {topic}")
    
    # 发送取消订阅确认
    response = WebSocketMessage(
        message_type="unsubscribe_ack",
        data={
            "topics": topics,
            "message": f"Successfully unsubscribed from {len(topics)} topics"
        },
        client_id=client_id
    )
    await manager.send_personal_message(response.to_json(), websocket)

async def handle_backtest_control(data: Dict[str, Any], client_id: str, websocket: WebSocket):
    """处理回测控制消息"""
    action = data.get('action')
    task_id = data.get('task_id')
    
    if not task_id:
        error_msg = WebSocketMessage(
            message_type="error",
            data={"message": "Missing task_id"},
            client_id=client_id
        )
        await manager.send_personal_message(error_msg.to_json(), websocket)
        return
    
    # 导入回测模块来访问running_backtests
    from .backtest import running_backtests
    
    if action == "status":
        # 获取回测状态
        if task_id in running_backtests:
            task_info = running_backtests[task_id]
            status_msg = WebSocketMessage(
                message_type="backtest_status",
                data={
                    "task_id": task_id,
                    "status": task_info['status'],
                    "progress": task_info['progress'],
                    "created_at": task_info['created_at'].isoformat()
                },
                client_id=client_id
            )
            await manager.send_personal_message(status_msg.to_json(), websocket)
        else:
            error_msg = WebSocketMessage(
                message_type="error",
                data={"message": f"Task {task_id} not found"},
                client_id=client_id
            )
            await manager.send_personal_message(error_msg.to_json(), websocket)
            
    elif action == "cancel":
        # 取消回测
        if task_id in running_backtests:
            task_info = running_backtests[task_id]
            if task_info['status'] not in ['completed', 'failed', 'cancelled']:
                task_info['status'] = 'cancelled'
                task_info['completed_at'] = datetime.now()
                
                cancel_msg = WebSocketMessage(
                    message_type="backtest_cancelled",
                    data={"task_id": task_id},
                    client_id=client_id
                )
                await manager.send_personal_message(cancel_msg.to_json(), websocket)
            else:
                error_msg = WebSocketMessage(
                    message_type="error",
                    data={"message": f"Task {task_id} cannot be cancelled"},
                    client_id=client_id
                )
                await manager.send_personal_message(error_msg.to_json(), websocket)
        else:
            error_msg = WebSocketMessage(
                message_type="error",
                data={"message": f"Task {task_id} not found"},
                client_id=client_id
            )
            await manager.send_personal_message(error_msg.to_json(), websocket)

async def handle_ping(client_id: str, websocket: WebSocket):
    """处理ping消息"""
    pong_msg = WebSocketMessage(
        message_type="pong",
        data={"server_time": datetime.now().isoformat()},
        client_id=client_id
    )
    await manager.send_personal_message(pong_msg.to_json(), websocket)

async def handle_get_status(data: Dict[str, Any], client_id: str, websocket: WebSocket):
    """处理获取状态请求"""
    status_type = data.get('status_type')
    
    if status_type == "server":
        # 服务器状态
        status_msg = WebSocketMessage(
            message_type="server_status",
            data={
                "connected_clients": manager.get_connection_count(),
                "server_time": datetime.now().isoformat(),
                "version": "1.0.0"
            },
            client_id=client_id
        )
        await manager.send_personal_message(status_msg.to_json(), websocket)
        
    elif status_type == "backtest_summary":
        # 回测任务摘要
        from .backtest import running_backtests
        
        summary = {
            "total_tasks": len(running_backtests),
            "running_tasks": len([t for t in running_backtests.values() if t['status'] == 'running']),
            "completed_tasks": len([t for t in running_backtests.values() if t['status'] == 'completed']),
            "failed_tasks": len([t for t in running_backtests.values() if t['status'] == 'failed'])
        }
        
        summary_msg = WebSocketMessage(
            message_type="backtest_summary",
            data=summary,
            client_id=client_id
        )
        await manager.send_personal_message(summary_msg.to_json(), websocket)

# 后台任务：定期推送数据
async def background_data_publisher():
    """后台数据推送任务"""
    while True:
        try:
            # 推送市场数据更新
            if manager.has_subscribers("market_data"):
                market_data = {
                    "timestamp": datetime.now().isoformat(),
                    "indices": {
                        "000300.SH": {"price": 4000 + (datetime.now().second % 100), "change": 0.5},
                        "000905.SH": {"price": 2000 + (datetime.now().second % 50), "change": -0.2}
                    }
                }
                
                market_msg = WebSocketMessage(
                    message_type="market_data",
                    data=market_data
                )
                await manager.broadcast_to_topic("market_data", market_msg.to_json())
            
            # 推送回测进度更新
            if manager.has_subscribers("backtest_progress"):
                from .backtest import running_backtests
                
                for task_id, task_info in running_backtests.items():
                    if task_info['status'] == 'running':
                        progress_msg = WebSocketMessage(
                            message_type="backtest_progress",
                            data={
                                "task_id": task_id,
                                "progress": task_info['progress'],
                                "status": task_info['status']
                            }
                        )
                        await manager.broadcast_to_topic("backtest_progress", progress_msg.to_json())
            
            await asyncio.sleep(5)  # 每5秒推送一次
            
        except Exception as e:
            logger.error(f"Error in background data publisher: {str(e)}")
            await asyncio.sleep(10)  # 出错后等待更长时间

# 启动后台任务
async def start_background_tasks():
    """启动后台任务"""
    asyncio.create_task(background_data_publisher())

# WebSocket管理API
@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    return {
        "total_connections": manager.get_connection_count(),
        "subscriptions": manager.get_subscription_stats(),
        "active_topics": manager.get_active_topics()
    }

@router.post("/ws/broadcast")
async def broadcast_message(message_type: str, data: Dict[str, Any], topic: Optional[str] = None):
    """广播消息到所有连接或特定主题"""
    msg = WebSocketMessage(
        message_type=message_type,
        data=data
    )
    
    if topic:
        await manager.broadcast_to_topic(topic, msg.to_json())
        return {"message": f"Message broadcasted to topic '{topic}'"}
    else:
        await manager.broadcast(msg.to_json())
        return {"message": "Message broadcasted to all connections"}

@router.post("/ws/send/{client_id}")
async def send_personal_message(client_id: str, message_type: str, data: Dict[str, Any]):
    """发送个人消息"""
    websocket = manager.get_connection(client_id)
    if not websocket:
        return {"error": f"Client {client_id} not found"}
    
    msg = WebSocketMessage(
        message_type=message_type,
        data=data,
        client_id=client_id
    )
    
    await manager.send_personal_message(msg.to_json(), websocket)
    return {"message": f"Message sent to client {client_id}"}

@router.delete("/ws/disconnect/{client_id}")
async def disconnect_client(client_id: str):
    """断开指定客户端连接"""
    websocket = manager.get_connection(client_id)
    if websocket:
        await websocket.close()
        return {"message": f"Client {client_id} disconnected"}
    else:
        return {"error": f"Client {client_id} not found"}

# 注意：后台任务应该在应用启动时启动，而不是在模块导入时启动
# 在 main.py 中调用 start_background_tasks()