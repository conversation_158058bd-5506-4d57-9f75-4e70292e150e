#!/bin/bash

# Puppeteer MCP Server 启动脚本
# 用于启动 Puppeteer MCP Server

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🚀 启动 Puppeteer MCP Server"
print_message $BLUE "================================"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    print_message $RED "❌ 虚拟环境不存在，请先运行安装脚本"
    exit 1
fi

# 激活虚拟环境
print_message $YELLOW "📦 激活虚拟环境..."
source venv/bin/activate

# 检查依赖
print_message $YELLOW "🔍 检查依赖..."
if ! python -c "import mcp, playwright" 2>/dev/null; then
    print_message $RED "❌ 依赖未安装，请先运行安装脚本"
    exit 1
fi

# 检查 Playwright 浏览器
print_message $YELLOW "🌐 检查 Playwright 浏览器..."
if ! python -c "from playwright.sync_api import sync_playwright; sync_playwright().start().chromium.launch()" 2>/dev/null; then
    print_message $YELLOW "⚠️  Playwright 浏览器未安装或安装不完整"
    print_message $YELLOW "🔧 正在安装 Playwright 浏览器..."
    playwright install
fi

# 启动服务器
print_message $GREEN "✅ 启动 Puppeteer MCP Server..."
print_message $BLUE "📝 服务器信息:"
print_message $BLUE "  - 协议: Model Context Protocol (MCP)"
print_message $BLUE "  - 功能: 网页自动化 (导航、截图、点击、填写、JavaScript执行)"
print_message $BLUE "  - 浏览器: Chromium (通过 Playwright)"
print_message $BLUE ""
print_message $YELLOW "⚠️  注意: 服务器将在标准输入/输出上运行 MCP 协议"
print_message $YELLOW "    请通过 MCP 客户端连接，而不是直接在终端中交互"
print_message $BLUE ""
print_message $GREEN "🎯 服务器已启动，等待 MCP 客户端连接..."

# 运行服务器
exec python puppeteer.py
