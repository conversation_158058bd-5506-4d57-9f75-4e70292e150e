{"analysis_timestamp": "2025-07-27T15:14:20.651015", "overall_score": 50.0, "detailed_results": {"platform_overview": {"title": "量化交易平台", "main_url": "http://localhost:5173/", "load_time": 3.19, "accessible": true}, "authentication_system": {"elements": {"login_form": true, "username_input": true, "password_input": true, "login_button": true, "demo_login_button": true}, "demo_login_success": true, "redirect_url": "http://localhost:5173/puzzle-verify"}, "puzzle_verification": {"page_title": "量化交易平台", "page_url": "http://localhost:5173/puzzle-verify", "canvas_count": 2, "puzzle_canvas": true, "block_canvas": true, "slider_track": true, "slider_button": true, "slider_text": true, "continue_button": true, "back_button": true, "refresh_button": true}, "backend_services": {"health_check": {"status": "success", "data": {"message": "Welcome to Quant Platform API (Dev)", "version": "1.0.0", "docs": "/docs"}}, "login_api": {"status": "success", "has_token": true}, "endpoint_register": {"status": "accessible", "code": 405}, "endpoint_profile": {"status": "accessible", "code": 404}, "endpoint_accounts": {"status": "accessible", "code": 404}}, "performance_metrics": {"page_load_time": 239, "dom_content_loaded": 233, "memory_used": ********, "memory_total": ********, "memory_limit": **********, "total_requests": 198, "avg_response_time": 38.**************}, "security_assessment": {}, "user_experience": {}, "technical_implementation": {}, "issues_found": [{"type": "console_error", "message": "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "timestamp": "2025-07-27T15:14:07.011691"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.378016"}, {"type": "console_error", "message": "获取市场概览失败: Error: API请求失败: 404 Not Found\n    at fetchMarketOverview (http://localhost:5173/src/stores/modules/market.ts:292:15)\n    at async Promise.allSettled (index 0)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue:187:9", "timestamp": "2025-07-27T15:14:07.380071"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.380545"}, {"type": "console_error", "message": "获取排行榜失败: Error: API请求失败: 404 Not Found\n    at fetchRankings (http://localhost:5173/src/stores/modules/market.ts:450:15)\n    at async Promise.allSettled (index 4)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue:187:9", "timestamp": "2025-07-27T15:14:07.381121"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.381379"}, {"type": "console_error", "message": "获取板块数据失败: Error: API请求失败: 404 Not Found\n    at fetchSectors (http://localhost:5173/src/stores/modules/market.ts:402:15)\n    at async Promise.allSettled (index 2)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue:187:9", "timestamp": "2025-07-27T15:14:07.382249"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.382525"}, {"type": "console_error", "message": "获取排行榜失败: Error: API请求失败: 404 Not Found\n    at fetchRankings (http://localhost:5173/src/stores/modules/market.ts:450:15)\n    at async Promise.allSettled (index 5)\n    at async Proxy.initialize (http://localhost:5173/src/stores/modules/market.ts:612:7)\n    at async Promise.allSettled (index 1)\n    at async http://localhost:5173/src/views/Dashboard/DashboardView.vue:187:9", "timestamp": "2025-07-27T15:14:07.383323"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:14:07.384Z}", "timestamp": "2025-07-27T15:14:07.392631"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue:351:82\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:14:07.393657"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:14:07.389Z}", "timestamp": "2025-07-27T15:14:07.394772"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue:395:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:14:07.395364"}, {"type": "console_error", "message": "🚨 全局错误捕获: {error: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5…, errorInfo: render function, instance: ElCard, timestamp: 2025-07-27T07:14:07.390Z}", "timestamp": "2025-07-27T15:14:07.395679"}, {"type": "console_error", "message": "错误堆栈: TypeError: Cannot read properties of undefined (reading 'toLocaleString')\n    at http://localhost:5173/src/views/Dashboard/DashboardView.vue:439:83\n    at renderFnWithContext (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2772:13)\n    at renderSlot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:5095:53)\n    at Proxy.<anonymous> (http://localhost:5173/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=46d9fc1c:8463:11)\n    at renderComponentRoot (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:8648:17)\n    at ReactiveEffect.componentUpdateFn [as fn] (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:7522:26)\n    at ReactiveEffect.run (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:488:19)\n    at ReactiveEffect.runIfDirty (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:526:12)\n    at callWithErrorHandling (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2272:33)\n    at flushJobs (http://localhost:5173/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=46d9fc1c:2480:9)", "timestamp": "2025-07-27T15:14:07.395973"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.396095"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:14:07.396423"}, {"type": "console_error", "message": "fetchStockList error AxiosError", "timestamp": "2025-07-27T15:14:07.401564"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.401950"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:14:07.402319"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "timestamp": "2025-07-27T15:14:07.410572"}, {"type": "console_error", "message": "[HTTP Response Error] {status: 404, data: Object, config: Object}", "timestamp": "2025-07-27T15:14:07.411530"}, {"type": "console_error", "message": "WebSocket connection to 'ws://localhost:8000/ws' failed: Error during WebSocket handshake: Unexpected response code: 403", "timestamp": "2025-07-27T15:14:08.931545"}, {"type": "console_error", "message": "WebSocket错误: Event", "timestamp": "2025-07-27T15:14:08.935702"}, {"type": "console_error", "message": "Error: {type: SY<PERSON><PERSON>, code: <PERSON>rror, message: Error: WebSocket连接错误, details: undefined, context: Object}", "timestamp": "2025-07-27T15:14:08.939953"}, {"type": "console_error", "message": "🚨 未处理的Promise异常: Error: WebSocket连接错误\n    at ws.onerror (http://localhost:5173/src/utils/websocket.ts:90:18)", "timestamp": "2025-07-27T15:14:08.944716"}, {"type": "console_error", "message": "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "timestamp": "2025-07-27T15:14:10.079069"}, {"type": "console_error", "message": "The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.", "timestamp": "2025-07-27T15:14:11.087406"}], "recommendations": ["优化拼图验证算法，提高验证成功率", "检查继续访问功能的路由配置", "修复发现的JavaScript错误和网络请求问题"]}}