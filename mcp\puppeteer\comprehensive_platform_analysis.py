#!/usr/bin/env python3
"""
量化交易平台综合功能分析脚本
检查所有功能模块的完成情况和存在问题
"""

import asyncio
import logging
import json
import time
from playwright.async_api import async_playwright
import aiohttp
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PlatformAnalyzer:
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "frontend_tests": {},
            "backend_tests": {},
            "integration_tests": {},
            "overall_score": 0,
            "issues": [],
            "recommendations": []
        }
        
    async def test_backend_apis(self):
        """测试后端API功能"""
        logger.info("🔧 测试后端API功能...")
        
        apis_to_test = [
            {"url": "http://localhost:8000/health", "method": "GET", "name": "健康检查"},
            {"url": "http://localhost:8000/api/v1/auth/login", "method": "POST", "name": "登录API", 
             "data": {"username": "admin", "password": "admin123"}},
            {"url": "http://localhost:8000/api/v1/auth/register", "method": "POST", "name": "注册API",
             "data": {"username": "test", "email": "<EMAIL>", "password": "test123"}},
            {"url": "http://localhost:8000/api/v1/market/overview", "method": "GET", "name": "市场概览API"},
            {"url": "http://localhost:8000/api/v1/trading/accounts", "method": "GET", "name": "交易账户API"},
            {"url": "http://localhost:8000/api/v1/market/sectors", "method": "GET", "name": "板块数据API"},
            {"url": "http://localhost:8000/api/v1/market/rankings", "method": "GET", "name": "排行榜API"},
            {"url": "http://localhost:8000/api/v1/market/watchlist", "method": "GET", "name": "自选股API"},
            {"url": "http://localhost:8000/api/v1/market/news", "method": "GET", "name": "市场新闻API"},
        ]
        
        api_results = {}
        
        async with aiohttp.ClientSession() as session:
            for api in apis_to_test:
                try:
                    start_time = time.time()
                    
                    if api["method"] == "GET":
                        async with session.get(api["url"]) as response:
                            status = response.status
                            response_time = time.time() - start_time
                            try:
                                data = await response.json()
                            except:
                                data = await response.text()
                    else:  # POST
                        async with session.post(api["url"], json=api.get("data", {})) as response:
                            status = response.status
                            response_time = time.time() - start_time
                            try:
                                data = await response.json()
                            except:
                                data = await response.text()
                    
                    api_results[api["name"]] = {
                        "status": status,
                        "response_time": response_time,
                        "success": status < 400,
                        "data_preview": str(data)[:200] if data else "No data"
                    }
                    
                    if status < 400:
                        logger.info(f"✅ {api['name']} - {status} ({response_time:.2f}s)")
                    else:
                        logger.error(f"❌ {api['name']} - {status} ({response_time:.2f}s)")
                        
                except Exception as e:
                    api_results[api["name"]] = {
                        "status": "ERROR",
                        "response_time": 0,
                        "success": False,
                        "error": str(e)
                    }
                    logger.error(f"❌ {api['name']} - 连接失败: {e}")
        
        self.results["backend_tests"] = api_results
        return api_results

    async def test_frontend_pages(self):
        """测试前端页面功能"""
        logger.info("🖥️ 测试前端页面功能...")
        
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        page = await browser.new_page()
        
        pages_to_test = [
            {"url": "http://localhost:5173/", "name": "主页"},
            {"url": "http://localhost:5173/login", "name": "登录页面"},
            {"url": "http://localhost:5173/market", "name": "行情页面"},
            {"url": "http://localhost:5173/trading", "name": "交易页面"},
            {"url": "http://localhost:5173/strategy", "name": "策略页面"},
            {"url": "http://localhost:5173/backtest", "name": "回测页面"},
            {"url": "http://localhost:5173/risk", "name": "风控页面"},
            {"url": "http://localhost:5173/portfolio", "name": "投资组合页面"},
        ]
        
        page_results = {}
        
        for page_info in pages_to_test:
            try:
                start_time = time.time()
                await page.goto(page_info["url"], timeout=15000)
                await page.wait_for_load_state('networkidle', timeout=10000)
                load_time = time.time() - start_time
                
                # 检查页面标题和基本元素
                title = await page.title()
                has_content = await page.query_selector('body') is not None
                
                page_results[page_info["name"]] = {
                    "accessible": True,
                    "load_time": load_time,
                    "title": title,
                    "has_content": has_content,
                    "url": page_info["url"]
                }
                
                logger.info(f"✅ {page_info['name']} - 加载成功 ({load_time:.2f}s)")
                
            except Exception as e:
                page_results[page_info["name"]] = {
                    "accessible": False,
                    "load_time": 0,
                    "error": str(e),
                    "url": page_info["url"]
                }
                logger.error(f"❌ {page_info['name']} - 加载失败: {e}")
        
        await browser.close()
        self.results["frontend_tests"] = page_results
        return page_results

    async def test_authentication_flow(self):
        """测试完整的认证流程"""
        logger.info("🔐 测试认证流程...")
        
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        page = await browser.new_page()
        
        auth_results = {
            "login_page_access": False,
            "demo_login_available": False,
            "puzzle_verification": False,
            "main_app_access": False,
            "overall_flow": False
        }
        
        try:
            # 1. 访问登录页面
            await page.goto("http://localhost:5173/login")
            await page.wait_for_load_state('networkidle')
            auth_results["login_page_access"] = True
            logger.info("✅ 登录页面访问成功")
            
            # 2. 检查演示登录按钮
            demo_btn = await page.query_selector('button:has-text("演示登录")')
            if demo_btn:
                auth_results["demo_login_available"] = True
                logger.info("✅ 演示登录按钮可用")
                
                # 3. 点击演示登录
                await demo_btn.click()
                await asyncio.sleep(3)
                
                # 4. 检查是否跳转到拼图验证
                if 'puzzle-verify' in page.url:
                    logger.info("✅ 成功跳转到拼图验证页面")
                    
                    # 5. 尝试完成拼图验证
                    slider_btn = await page.query_selector('.slider-btn')
                    track = await page.query_selector('.slider-track')
                    
                    if slider_btn and track:
                        btn_box = await slider_btn.bounding_box()
                        track_box = await track.bounding_box()
                        
                        if btn_box and track_box:
                            start_x = btn_box['x'] + btn_box['width'] / 2
                            start_y = btn_box['y'] + btn_box['height'] / 2
                            end_x = track_box['x'] + track_box['width'] * 0.7
                            
                            await page.mouse.move(start_x, start_y)
                            await page.mouse.down()
                            await page.mouse.move(end_x, start_y)
                            await page.mouse.up()
                            await asyncio.sleep(2)
                            
                            # 检查验证结果
                            success_indicator = await page.query_selector('.slider-btn-success')
                            if success_indicator:
                                auth_results["puzzle_verification"] = True
                                logger.info("✅ 拼图验证成功")
                                
                                # 6. 点击继续访问
                                continue_btn = await page.query_selector('button:has-text("继续访问")')
                                if continue_btn:
                                    await continue_btn.click()
                                    await asyncio.sleep(2)
                                    
                                    # 检查是否成功进入主应用
                                    if page.url == "http://localhost:5173/" or "dashboard" in page.url:
                                        auth_results["main_app_access"] = True
                                        auth_results["overall_flow"] = True
                                        logger.info("✅ 成功进入主应用")
                                    else:
                                        logger.warning(f"⚠️ 跳转异常，当前URL: {page.url}")
                            else:
                                logger.warning("⚠️ 拼图验证失败")
                    else:
                        logger.error("❌ 拼图验证元素未找到")
                else:
                    logger.error("❌ 未跳转到拼图验证页面")
            else:
                logger.error("❌ 演示登录按钮未找到")
                
        except Exception as e:
            logger.error(f"❌ 认证流程测试失败: {e}")
        
        await browser.close()
        self.results["integration_tests"]["authentication_flow"] = auth_results
        return auth_results

    def calculate_overall_score(self):
        """计算总体完成度评分"""
        total_score = 0
        max_score = 0
        
        # 后端API评分 (40%)
        backend_tests = self.results.get("backend_tests", {})
        backend_success = sum(1 for test in backend_tests.values() if test.get("success", False))
        backend_total = len(backend_tests)
        if backend_total > 0:
            backend_score = (backend_success / backend_total) * 40
            total_score += backend_score
        max_score += 40
        
        # 前端页面评分 (30%)
        frontend_tests = self.results.get("frontend_tests", {})
        frontend_success = sum(1 for test in frontend_tests.values() if test.get("accessible", False))
        frontend_total = len(frontend_tests)
        if frontend_total > 0:
            frontend_score = (frontend_success / frontend_total) * 30
            total_score += frontend_score
        max_score += 30
        
        # 认证流程评分 (30%)
        auth_flow = self.results.get("integration_tests", {}).get("authentication_flow", {})
        auth_success = sum(1 for test in auth_flow.values() if test)
        auth_total = len(auth_flow)
        if auth_total > 0:
            auth_score = (auth_success / auth_total) * 30
            total_score += auth_score
        max_score += 30
        
        self.results["overall_score"] = round((total_score / max_score) * 100, 1) if max_score > 0 else 0
        return self.results["overall_score"]

    async def run_comprehensive_analysis(self):
        """运行综合分析"""
        logger.info("🚀 开始量化交易平台综合功能分析...")
        
        # 测试后端API
        await self.test_backend_apis()
        
        # 测试前端页面
        await self.test_frontend_pages()
        
        # 测试认证流程
        await self.test_authentication_flow()
        
        # 计算总体评分
        score = self.calculate_overall_score()
        
        # 生成报告
        self.generate_report()
        
        return self.results

    def generate_report(self):
        """生成分析报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 量化交易平台功能完成度报告")
        logger.info("="*60)
        
        # 总体评分
        score = self.results["overall_score"]
        logger.info(f"🎯 总体完成度: {score}%")
        
        if score >= 80:
            logger.info("🎉 优秀！平台功能基本完善")
        elif score >= 60:
            logger.info("✅ 良好！主要功能已实现")
        elif score >= 40:
            logger.info("⚠️ 一般，需要继续完善")
        else:
            logger.info("❌ 需要大量工作")
        
        # 后端API报告
        logger.info("\n🔧 后端API测试结果:")
        backend_tests = self.results.get("backend_tests", {})
        for name, result in backend_tests.items():
            status = "✅" if result.get("success", False) else "❌"
            logger.info(f"  {status} {name}: {result.get('status', 'ERROR')}")
        
        # 前端页面报告
        logger.info("\n🖥️ 前端页面测试结果:")
        frontend_tests = self.results.get("frontend_tests", {})
        for name, result in frontend_tests.items():
            status = "✅" if result.get("accessible", False) else "❌"
            load_time = result.get("load_time", 0)
            logger.info(f"  {status} {name}: {load_time:.2f}s")
        
        # 认证流程报告
        logger.info("\n🔐 认证流程测试结果:")
        auth_flow = self.results.get("integration_tests", {}).get("authentication_flow", {})
        for step, success in auth_flow.items():
            status = "✅" if success else "❌"
            logger.info(f"  {status} {step.replace('_', ' ').title()}")
        
        # 保存详细报告
        with open("platform_analysis_report.json", "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n📄 详细报告已保存到: platform_analysis_report.json")

async def main():
    analyzer = PlatformAnalyzer()
    await analyzer.run_comprehensive_analysis()

if __name__ == "__main__":
    asyncio.run(main())
