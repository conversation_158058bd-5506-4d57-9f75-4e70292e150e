#!/usr/bin/env python3
"""
使用MCP进行真实用户深度测试
基于现有的测试框架，模拟真实用户使用量化投资平台
"""

import asyncio
import json
import time
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from simple_puppeteer import Puppeteer
except ImportError:
    print("❌ 无法导入simple_puppeteer模块，请检查simple_puppeteer.py文件")
    sys.exit(1)

class MCPRealUserTest:
    def __init__(self):
        self.puppeteer = Puppeteer()
        self.test_results = {
            'test_session': {
                'start_time': datetime.now().isoformat(),
                'tester_profile': 'MCP Real User Simulation',
                'platform_url': 'http://localhost:5173',
                'test_objectives': [
                    '评估平台易用性',
                    '发现功能性问题',
                    '测试用户工作流',
                    '检查性能表现',
                    '验证数据准确性'
                ]
            },
            'user_journey': [],
            'critical_issues': [],
            'usability_problems': [],
            'performance_issues': [],
            'functional_bugs': [],
            'ui_inconsistencies': [],
            'accessibility_problems': []
        }
        self.current_scenario = None
        
    async def setup(self):
        """初始化测试环境"""
        print("🚀 启动MCP真实用户深度测试...")
        
        # 启动浏览器
        result = await self.puppeteer.launch_browser()
        if not result.get('success'):
            raise Exception(f"浏览器启动失败: {result.get('error')}")
        
        print("✅ 测试环境初始化完成")
        return True

    async def scenario_platform_overview(self):
        """场景1: 平台整体概览测试"""
        self.current_scenario = "平台整体概览"
        print("🎯 开始场景: 平台整体概览测试")
        
        scenario_data = {
            'name': '平台整体概览测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }
        
        try:
            # 1. 尝试访问平台首页
            print("📍 步骤1: 访问平台首页")
            
            # 首先尝试访问localhost:5173
            result = await self.puppeteer.navigate_to('http://localhost:5173')
            
            if result.get('success'):
                scenario_data['steps'].append('成功访问localhost:5173')
                await self.puppeteer.take_screenshot('01_homepage_localhost')
            else:
                print("⚠️ 无法访问localhost:5173，可能前端服务未启动")
                scenario_data['issues_found'].append('前端服务未启动或无法访问')
                scenario_data['user_feedback'].append('作为用户，我无法访问平台')
                
                # 尝试访问其他可能的地址
                alternative_urls = [
                    'http://localhost:3000',
                    'http://localhost:8080',
                    'http://127.0.0.1:5173'
                ]
                
                for url in alternative_urls:
                    print(f"📍 尝试访问: {url}")
                    result = await self.puppeteer.navigate_to(url)
                    if result.get('success'):
                        scenario_data['steps'].append(f'成功访问{url}')
                        await self.puppeteer.take_screenshot(f'01_homepage_{url.split(":")[-1]}')
                        break
                else:
                    scenario_data['issues_found'].append('所有常见端口都无法访问')
                    return scenario_data
            
            # 2. 检查页面基本元素
            print("📍 步骤2: 检查页面基本元素")
            
            # 获取页面信息
            page_info = await self.puppeteer.get_page_info()
            if page_info.get('success'):
                title = page_info.get('title', '')
                url = page_info.get('url', '')
                
                scenario_data['steps'].append(f'页面标题: {title}')
                scenario_data['steps'].append(f'当前URL: {url}')
                
                if not title or title == 'Document':
                    scenario_data['issues_found'].append('页面标题不明确')
                    scenario_data['user_feedback'].append('页面标题没有说明这是什么平台')
            
            # 3. 检查导航元素
            print("📍 步骤3: 检查导航元素")
            
            # 查找导航相关元素
            nav_selectors = ['nav', '.nav', '.navigation', '.menu', '.header']
            nav_found = False
            
            for selector in nav_selectors:
                elements = await self.puppeteer.query_selector_all(selector)
                if elements.get('success') and elements.get('elements'):
                    nav_found = True
                    scenario_data['steps'].append(f'发现导航元素: {selector}')
                    break
            
            if not nav_found:
                scenario_data['issues_found'].append('未找到明显的导航菜单')
                scenario_data['user_feedback'].append('我不知道如何浏览平台的不同功能')
            
            # 4. 检查主要功能区域
            print("📍 步骤4: 检查主要功能区域")
            
            # 查找按钮和链接
            buttons = await self.puppeteer.query_selector_all('button')
            links = await self.puppeteer.query_selector_all('a')
            
            button_count = len(buttons.get('elements', [])) if buttons.get('success') else 0
            link_count = len(links.get('elements', [])) if links.get('success') else 0
            
            scenario_data['steps'].append(f'发现{button_count}个按钮')
            scenario_data['steps'].append(f'发现{link_count}个链接')
            
            if button_count + link_count < 5:
                scenario_data['issues_found'].append('交互元素过少')
                scenario_data['user_feedback'].append('页面看起来功能有限')
            
            # 5. 检查响应性
            print("📍 步骤5: 检查页面响应性")
            
            # 等待一段时间看页面是否有动态内容
            await asyncio.sleep(3)
            
            # 再次截图对比
            await self.puppeteer.take_screenshot('02_homepage_after_wait')
            
        except Exception as e:
            scenario_data['issues_found'].append(f'测试过程异常: {str(e)}')
            print(f"❌ 平台概览测试失败: {e}")
        
        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)
        
        print(f"✅ 平台概览场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_navigation_test(self):
        """场景2: 导航功能测试"""
        self.current_scenario = "导航功能测试"
        print("🎯 开始场景: 导航功能测试")
        
        scenario_data = {
            'name': '导航功能测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }
        
        try:
            # 测试主要页面导航
            main_pages = [
                ('/market', '市场数据'),
                ('/trading', '交易'),
                ('/strategy', '策略'),
                ('/portfolio', '投资组合'),
                ('/risk', '风险管理')
            ]
            
            for path, name in main_pages:
                print(f"📍 测试导航到: {name}")
                
                # 获取当前URL
                current_info = await self.puppeteer.get_page_info()
                base_url = current_info.get('url', 'http://localhost:5173').split('?')[0].split('#')[0]
                # 确保base_url不以/结尾，避免双斜杠
                if base_url.endswith('/'):
                    base_url = base_url.rstrip('/')

                target_url = base_url + path
                
                result = await self.puppeteer.navigate_to(target_url)
                
                if result.get('success'):
                    scenario_data['steps'].append(f'成功导航到{name}页面')
                    await self.puppeteer.take_screenshot(f'03_page_{name}')
                    
                    # 等待页面加载
                    await asyncio.sleep(2)
                    
                    # 检查页面是否有内容
                    page_info = await self.puppeteer.get_page_info()
                    if page_info.get('success'):
                        title = page_info.get('title', '')
                        if name.lower() not in title.lower() and len(title) < 5:
                            scenario_data['issues_found'].append(f'{name}页面标题不明确')
                else:
                    scenario_data['issues_found'].append(f'无法导航到{name}页面')
                    scenario_data['user_feedback'].append(f'我无法访问{name}功能')
                
                # 短暂等待
                await asyncio.sleep(1)
            
        except Exception as e:
            scenario_data['issues_found'].append(f'导航测试异常: {str(e)}')
            print(f"❌ 导航测试失败: {e}")
        
        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)
        
        print(f"✅ 导航功能场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_user_interaction_test(self):
        """场景3: 用户交互测试"""
        self.current_scenario = "用户交互测试"
        print("🎯 开始场景: 用户交互测试")

        scenario_data = {
            'name': '用户交互测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 测试按钮交互
            print("📍 步骤1: 测试按钮交互")

            # 查找可点击的按钮
            buttons = await self.puppeteer.query_selector_all('button:not([disabled])')
            if buttons.get('success') and buttons.get('elements'):
                button_count = len(buttons['elements'])
                scenario_data['steps'].append(f'发现{button_count}个可点击按钮')

                # 尝试点击第一个按钮
                if button_count > 0:
                    click_result = await self.puppeteer.click_element('button:not([disabled])')
                    if click_result.get('success'):
                        scenario_data['steps'].append('成功点击按钮')
                        await asyncio.sleep(1)  # 等待响应
                    else:
                        scenario_data['issues_found'].append('按钮点击失败')
            else:
                scenario_data['issues_found'].append('未找到可点击的按钮')
                scenario_data['user_feedback'].append('页面缺少交互元素')

            # 2. 测试表单输入
            print("📍 步骤2: 测试表单输入")

            # 查找输入框
            inputs = await self.puppeteer.query_selector_all('input:not([readonly]):not([disabled])')
            if inputs.get('success') and inputs.get('elements'):
                input_count = len(inputs['elements'])
                scenario_data['steps'].append(f'发现{input_count}个输入框')

                # 尝试在第一个输入框中输入文本
                if input_count > 0:
                    fill_result = await self.puppeteer.fill_input('input:not([readonly]):not([disabled])', '测试输入')
                    if fill_result.get('success'):
                        scenario_data['steps'].append('成功填写输入框')
                    else:
                        scenario_data['issues_found'].append('输入框填写失败')
            else:
                scenario_data['steps'].append('未发现输入框')

            # 3. 测试下拉菜单
            print("📍 步骤3: 测试下拉菜单")

            selects = await self.puppeteer.query_selector_all('select')
            if selects.get('success') and selects.get('elements'):
                select_count = len(selects['elements'])
                scenario_data['steps'].append(f'发现{select_count}个下拉菜单')
            else:
                scenario_data['steps'].append('未发现下拉菜单')

            # 4. 检查响应性
            print("📍 步骤4: 检查页面响应性")

            # 等待并检查是否有动态内容更新
            await asyncio.sleep(2)
            scenario_data['steps'].append('等待页面响应')

        except Exception as e:
            scenario_data['issues_found'].append(f'用户交互测试异常: {str(e)}')
            print(f"❌ 用户交互测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        print(f"✅ 用户交互场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_performance_test(self):
        """场景4: 性能测试"""
        self.current_scenario = "性能测试"
        print("🎯 开始场景: 性能测试")

        scenario_data = {
            'name': '性能测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 测试页面加载性能
            print("📍 步骤1: 测试页面加载性能")

            # 获取性能指标
            performance_script = """
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const paint = performance.getEntriesByType('paint');
                    return {
                        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
                        loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
                        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                        memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0
                    }
                }
            """

            perf_result = await self.puppeteer.evaluate_script(performance_script)
            if perf_result.get('success'):
                perf_data = perf_result.get('result', {})
                scenario_data['steps'].append(f'DOM加载时间: {perf_data.get("domContentLoaded", 0):.2f}ms')
                scenario_data['steps'].append(f'首次绘制: {perf_data.get("firstPaint", 0):.2f}ms')
                scenario_data['steps'].append(f'首次内容绘制: {perf_data.get("firstContentfulPaint", 0):.2f}ms')

                # 检查性能问题
                if perf_data.get('firstContentfulPaint', 0) > 3000:
                    scenario_data['issues_found'].append('首次内容绘制时间过长')
                    scenario_data['user_feedback'].append('页面加载感觉很慢')

                if perf_data.get('memoryUsage', 0) > 50 * 1024 * 1024:  # 50MB
                    scenario_data['issues_found'].append('内存使用过高')

            # 2. 测试快速导航
            print("📍 步骤2: 测试快速导航性能")

            pages = ['/market', '/strategy', '/portfolio']
            for page in pages:
                start_time = time.time()

                current_info = await self.puppeteer.get_page_info()
                base_url = current_info.get('url', 'http://localhost:5173').split('?')[0].split('#')[0]
                if not base_url.endswith('/'):
                    base_url = base_url.rsplit('/', 1)[0]

                target_url = base_url + page
                result = await self.puppeteer.navigate_to(target_url)

                end_time = time.time()
                nav_time = (end_time - start_time) * 1000  # 转换为毫秒

                if result.get('success'):
                    scenario_data['steps'].append(f'导航到{page}: {nav_time:.2f}ms')
                    if nav_time > 2000:  # 2秒
                        scenario_data['issues_found'].append(f'{page}页面导航过慢')
                        scenario_data['user_feedback'].append(f'切换到{page}页面等待时间太长')

                await asyncio.sleep(0.5)  # 短暂等待

        except Exception as e:
            scenario_data['issues_found'].append(f'性能测试异常: {str(e)}')
            print(f"❌ 性能测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        print(f"✅ 性能测试场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_error_handling_test(self):
        """场景5: 错误处理测试"""
        self.current_scenario = "错误处理测试"
        print("🎯 开始场景: 错误处理测试")

        scenario_data = {
            'name': '错误处理测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 检查控制台错误
            print("📍 步骤1: 检查控制台错误")

            logs_result = await self.puppeteer.get_console_logs()
            if logs_result.get('success'):
                logs = logs_result.get('logs', [])
                error_logs = [log for log in logs if '[error]' in log]
                warning_logs = [log for log in logs if '[warning]' in log]

                scenario_data['steps'].append(f'发现{len(error_logs)}个错误日志')
                scenario_data['steps'].append(f'发现{len(warning_logs)}个警告日志')

                if len(error_logs) > 0:
                    scenario_data['issues_found'].append(f'控制台存在{len(error_logs)}个错误')
                    scenario_data['user_feedback'].append('页面可能存在功能问题')

                if len(warning_logs) > 5:
                    scenario_data['issues_found'].append(f'控制台警告过多: {len(warning_logs)}个')

            # 2. 测试无效URL访问
            print("📍 步骤2: 测试无效URL访问")

            invalid_url = 'http://localhost:5173/nonexistent-page'
            result = await self.puppeteer.navigate_to(invalid_url)

            if result.get('success'):
                # 检查是否显示了适当的404页面
                page_info = await self.puppeteer.get_page_info()
                title = page_info.get('title', '').lower()

                if '404' in title or 'not found' in title or '页面不存在' in title:
                    scenario_data['steps'].append('正确显示404页面')
                else:
                    scenario_data['issues_found'].append('无效URL未显示适当的错误页面')
                    scenario_data['user_feedback'].append('访问不存在的页面时没有明确提示')

            # 3. 测试网络错误处理
            print("📍 步骤3: 测试网络错误处理")

            # 尝试访问不存在的API端点
            network_test_script = """
                () => {
                    return fetch('/api/nonexistent-endpoint')
                        .then(response => ({success: true, status: response.status}))
                        .catch(error => ({success: false, error: error.message}))
                }
            """

            network_result = await self.puppeteer.evaluate_script(network_test_script)
            if network_result.get('success'):
                result_data = network_result.get('result', {})
                if not result_data.get('success'):
                    scenario_data['steps'].append('网络错误被正确捕获')
                else:
                    status = result_data.get('status', 0)
                    if status >= 400:
                        scenario_data['steps'].append(f'API错误状态码: {status}')

        except Exception as e:
            scenario_data['issues_found'].append(f'错误处理测试异常: {str(e)}')
            print(f"❌ 错误处理测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        print(f"✅ 错误处理测试场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_data_display_test(self):
        """场景6: 数据显示测试 - 检查之前发现的数据显示问题"""
        self.current_scenario = "数据显示测试"
        print("🎯 开始场景: 数据显示测试")

        scenario_data = {
            'name': '数据显示测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 1. 测试市场数据页面的数据表格
            print("📍 步骤1: 检查市场数据页面的数据表格")

            # 导航到市场数据页面
            current_info = await self.puppeteer.get_page_info()
            base_url = current_info.get('url', 'http://localhost:5173').split('?')[0].split('#')[0]
            # 确保base_url以/结尾，但不要重复斜杠
            if base_url.endswith('/'):
                base_url = base_url.rstrip('/')

            market_url = base_url + '/market'
            result = await self.puppeteer.navigate_to(market_url)

            if result.get('success'):
                scenario_data['steps'].append('成功导航到市场数据页面')
                await asyncio.sleep(3)  # 等待数据加载

                # 检查数据表格
                tables = await self.puppeteer.query_selector_all('table, .el-table, .data-table')
                if tables.get('success') and tables.get('elements'):
                    table_count = len(tables['elements'])
                    scenario_data['steps'].append(f'发现{table_count}个数据表格')

                    if table_count == 0:
                        scenario_data['issues_found'].append('市场页面缺少数据表格')
                        scenario_data['user_feedback'].append('无法查看股票数据列表')
                else:
                    scenario_data['issues_found'].append('未找到数据表格元素')
                    scenario_data['user_feedback'].append('市场数据页面没有显示任何数据')

                # 检查图表容器
                charts = await self.puppeteer.query_selector_all('canvas, .echarts, .chart-container, #chart')
                if charts.get('success') and charts.get('elements'):
                    chart_count = len(charts['elements'])
                    scenario_data['steps'].append(f'发现{chart_count}个图表容器')

                    if chart_count == 0:
                        scenario_data['issues_found'].append('市场页面缺少图表显示')
                        scenario_data['user_feedback'].append('无法查看价格走势图')
                else:
                    scenario_data['issues_found'].append('未找到图表容器')
                    scenario_data['user_feedback'].append('页面缺少可视化图表')
            else:
                scenario_data['issues_found'].append('无法访问市场数据页面')

            # 2. 测试交易页面的功能元素
            print("📍 步骤2: 检查交易页面的功能元素")

            trading_url = base_url + '/trading/terminal'
            result = await self.puppeteer.navigate_to(trading_url)

            if result.get('success'):
                scenario_data['steps'].append('成功导航到交易页面')
                await asyncio.sleep(3)

                # 检查交易表单
                forms = await self.puppeteer.query_selector_all('form, .trading-form, .order-form')
                if forms.get('success') and forms.get('elements'):
                    form_count = len(forms['elements'])
                    scenario_data['steps'].append(f'发现{form_count}个交易表单')
                else:
                    scenario_data['issues_found'].append('交易页面缺少交易表单')
                    scenario_data['user_feedback'].append('无法进行交易操作')

                # 检查买卖按钮
                trading_buttons = await self.puppeteer.query_selector_all('button:has-text("买入"), button:has-text("卖出"), .buy-btn, .sell-btn')
                if trading_buttons.get('success') and trading_buttons.get('elements'):
                    button_count = len(trading_buttons['elements'])
                    scenario_data['steps'].append(f'发现{button_count}个交易按钮')
                else:
                    scenario_data['issues_found'].append('交易页面缺少买卖按钮')
                    scenario_data['user_feedback'].append('找不到交易操作按钮')
            else:
                scenario_data['issues_found'].append('交易页面访问超时或失败')
                scenario_data['user_feedback'].append('无法访问交易功能')

        except Exception as e:
            scenario_data['issues_found'].append(f'数据显示测试异常: {str(e)}')
            print(f"❌ 数据显示测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        print(f"✅ 数据显示测试场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_api_connectivity_test(self):
        """场景7: API连接测试 - 检查后端服务连接"""
        self.current_scenario = "API连接测试"
        print("🎯 开始场景: API连接测试")

        scenario_data = {
            'name': 'API连接测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 测试主要API端点
            api_endpoints = [
                '/api/v1/market/stocks',
                '/api/v1/strategies',
                '/api/v1/portfolio',
                '/api/v1/risk/metrics',
                '/api/v1/auth/user'
            ]

            for endpoint in api_endpoints:
                print(f"📍 测试API端点: {endpoint}")

                # 使用JavaScript在浏览器中测试API
                api_test_script = f"""
                    () => {{
                        return fetch('http://localhost:8000{endpoint}')
                            .then(response => ({{
                                success: response.ok,
                                status: response.status,
                                endpoint: '{endpoint}'
                            }}))
                            .catch(error => ({{
                                success: false,
                                error: error.message,
                                endpoint: '{endpoint}'
                            }}))
                    }}
                """

                api_result = await self.puppeteer.evaluate_script(api_test_script)
                if api_result.get('success'):
                    result_data = api_result.get('result', {})
                    if result_data.get('success'):
                        scenario_data['steps'].append(f'{endpoint} API连接成功')
                    else:
                        status = result_data.get('status', 'unknown')
                        scenario_data['issues_found'].append(f'{endpoint} API返回错误状态: {status}')
                        scenario_data['user_feedback'].append(f'{endpoint}功能可能无法正常使用')
                else:
                    scenario_data['issues_found'].append(f'{endpoint} API连接失败')
                    scenario_data['user_feedback'].append(f'后端{endpoint}服务不可用')

                await asyncio.sleep(0.5)  # 避免请求过快

        except Exception as e:
            scenario_data['issues_found'].append(f'API连接测试异常: {str(e)}')
            print(f"❌ API连接测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        print(f"✅ API连接测试场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def scenario_mobile_responsiveness_test(self):
        """场景8: 移动端响应式测试 - 检查移动端适配问题"""
        self.current_scenario = "移动端响应式测试"
        print("🎯 开始场景: 移动端响应式测试")

        scenario_data = {
            'name': '移动端响应式测试',
            'start_time': time.time(),
            'steps': [],
            'issues_found': [],
            'user_feedback': []
        }

        try:
            # 测试不同移动设备视口
            mobile_viewports = [
                {'width': 375, 'height': 667, 'name': 'iPhone SE'},
                {'width': 414, 'height': 896, 'name': 'iPhone 11'},
                {'width': 360, 'height': 640, 'name': 'Android'}
            ]

            for viewport in mobile_viewports:
                print(f"📍 测试移动设备: {viewport['name']}")

                # 模拟设置视口（在模拟模式下）
                scenario_data['steps'].append(f'测试{viewport["name"]}视口 ({viewport["width"]}x{viewport["height"]})')

                # 检查文字大小问题
                small_text_script = """
                    () => {
                        const elements = document.querySelectorAll('*');
                        let smallTextCount = 0;
                        elements.forEach(el => {
                            const style = window.getComputedStyle(el);
                            const fontSize = parseFloat(style.fontSize);
                            if (fontSize < 14 && el.textContent.trim() && el.textContent.trim().length > 5) {
                                smallTextCount++;
                            }
                        });
                        return smallTextCount;
                    }
                """

                text_result = await self.puppeteer.evaluate_script(small_text_script)
                if text_result.get('success'):
                    small_text_count = text_result.get('result', 0)
                    scenario_data['steps'].append(f'{viewport["name"]}发现{small_text_count}个小文字元素')

                    if small_text_count > 50:  # 降低阈值，之前是120个
                        scenario_data['issues_found'].append(f'{viewport["name"]}存在{small_text_count}个小文字元素')
                        scenario_data['user_feedback'].append(f'在{viewport["name"]}上文字太小，难以阅读')

                # 检查导航菜单适配
                nav_check_script = """
                    () => {
                        const navElements = document.querySelectorAll('nav, .nav, .navigation');
                        let hasHamburgerMenu = document.querySelectorAll('.hamburger, .menu-toggle, .mobile-menu').length > 0;
                        return {
                            navCount: navElements.length,
                            hasHamburgerMenu: hasHamburgerMenu
                        };
                    }
                """

                nav_result = await self.puppeteer.evaluate_script(nav_check_script)
                if nav_result.get('success'):
                    nav_data = nav_result.get('result', {})
                    if not nav_data.get('hasHamburgerMenu') and nav_data.get('navCount', 0) > 0:
                        scenario_data['issues_found'].append(f'{viewport["name"]}缺少移动端导航菜单')
                        scenario_data['user_feedback'].append(f'在{viewport["name"]}上导航不够友好')

                await asyncio.sleep(1)

        except Exception as e:
            scenario_data['issues_found'].append(f'移动端响应式测试异常: {str(e)}')
            print(f"❌ 移动端响应式测试失败: {e}")

        scenario_data['end_time'] = time.time()
        scenario_data['duration'] = scenario_data['end_time'] - scenario_data['start_time']
        self.test_results['user_journey'].append(scenario_data)

        print(f"✅ 移动端响应式测试场景完成，耗时: {scenario_data['duration']:.2f}秒")
        return scenario_data

    async def generate_report(self):
        """生成测试报告"""
        print("📊 生成测试报告...")
        
        # 统计问题
        total_issues = sum(len(self.test_results[key]) for key in [
            'critical_issues', 'usability_problems', 'functional_bugs', 
            'ui_inconsistencies', 'accessibility_problems'
        ])
        
        # 计算总测试时间
        total_duration = sum(scenario.get('duration', 0) for scenario in self.test_results['user_journey'])
        
        # 生成报告摘要
        report_summary = {
            'test_completion_time': datetime.now().isoformat(),
            'total_test_duration': f"{total_duration:.2f}秒",
            'scenarios_tested': len(self.test_results['user_journey']),
            'total_issues_found': total_issues,
            'overall_assessment': self.assess_platform_quality()
        }
        
        self.test_results['report_summary'] = report_summary
        
        # 保存详细报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"mcp_real_user_test_report_{timestamp}.json"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"📋 详细报告已保存: {report_filename}")
        
        # 打印摘要
        self.print_report_summary(report_summary)
        
        return report_filename

    def assess_platform_quality(self):
        """评估平台质量"""
        total_issues = sum(len(scenario.get('issues_found', [])) for scenario in self.test_results['user_journey'])
        
        if total_issues > 10:
            return "需要重要改进 - 发现多个问题"
        elif total_issues > 5:
            return "需要优化 - 存在一些问题"
        elif total_issues > 0:
            return "基本可用 - 有少量问题"
        else:
            return "运行良好 - 未发现明显问题"

    def print_report_summary(self, summary):
        """打印报告摘要"""
        print("\n" + "="*60)
        print("🎯 MCP真实用户测试报告摘要")
        print("="*60)
        print(f"📅 测试完成时间: {summary['test_completion_time']}")
        print(f"⏱️  总测试时长: {summary['total_test_duration']}")
        print(f"🧪 测试场景数: {summary['scenarios_tested']}")
        print(f"🐛 发现问题总数: {summary['total_issues_found']}")
        print(f"📊 整体评估: {summary['overall_assessment']}")
        print("="*60)
        
        # 打印各场景详情
        for scenario in self.test_results['user_journey']:
            print(f"\n📋 场景: {scenario['name']}")
            print(f"   ⏱️ 耗时: {scenario.get('duration', 0):.2f}秒")
            print(f"   📝 步骤数: {len(scenario.get('steps', []))}")
            print(f"   🐛 问题数: {len(scenario.get('issues_found', []))}")
            
            if scenario.get('issues_found'):
                print("   ❌ 发现的问题:")
                for issue in scenario['issues_found']:
                    print(f"      • {issue}")

    async def cleanup(self):
        """清理资源"""
        await self.puppeteer.close_browser()
        print("🧹 测试环境已清理")

async def main():
    """主函数"""
    tester = MCPRealUserTest()
    
    try:
        # 初始化
        await tester.setup()
        
        # 执行测试场景
        await tester.scenario_platform_overview()
        await tester.scenario_navigation_test()
        await tester.scenario_user_interaction_test()
        await tester.scenario_performance_test()
        await tester.scenario_error_handling_test()
        await tester.scenario_data_display_test()
        await tester.scenario_api_connectivity_test()
        await tester.scenario_mobile_responsiveness_test()
        
        # 生成报告
        report_file = await tester.generate_report()
        print(f"🎉 MCP真实用户深度测试完成！报告文件: {report_file}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
