"""
WebSocket连接管理器
管理客户端连接、订阅和消息分发
"""
from fastapi import WebSocket
from typing import Dict, List, Set, Optional
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接：client_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 订阅关系：topic -> set of client_ids
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)
        
        # 客户端订阅：client_id -> set of topics
        self.client_subscriptions: Dict[str, Set[str]] = defaultdict(set)
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        # 找到对应的client_id
        client_id_to_remove = None
        for client_id, ws in self.active_connections.items():
            if ws == websocket:
                client_id_to_remove = client_id
                break
        
        if client_id_to_remove:
            # 移除连接
            del self.active_connections[client_id_to_remove]
            
            # 清理订阅关系
            topics_to_unsubscribe = self.client_subscriptions.get(client_id_to_remove, set()).copy()
            for topic in topics_to_unsubscribe:
                self.unsubscribe_client(client_id_to_remove, topic)
            
            # 清理客户端订阅记录
            if client_id_to_remove in self.client_subscriptions:
                del self.client_subscriptions[client_id_to_remove]
            
            logger.info(f"Client {client_id_to_remove} disconnected. Total connections: {len(self.active_connections)}")
    
    def get_connection(self, client_id: str) -> Optional[WebSocket]:
        """获取客户端连接"""
        return self.active_connections.get(client_id)
    
    def get_connection_count(self) -> int:
        """获取连接数量"""
        return len(self.active_connections)
    
    def subscribe_client(self, client_id: str, topic: str):
        """客户端订阅主题"""
        if client_id in self.active_connections:
            self.subscriptions[topic].add(client_id)
            self.client_subscriptions[client_id].add(topic)
            logger.debug(f"Client {client_id} subscribed to topic {topic}")
    
    def unsubscribe_client(self, client_id: str, topic: str):
        """客户端取消订阅主题"""
        if client_id in self.subscriptions.get(topic, set()):
            self.subscriptions[topic].discard(client_id)
            self.client_subscriptions[client_id].discard(topic)
            
            # 如果主题没有订阅者，清理主题
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
            
            logger.debug(f"Client {client_id} unsubscribed from topic {topic}")
    
    def has_subscribers(self, topic: str) -> bool:
        """检查主题是否有订阅者"""
        return topic in self.subscriptions and len(self.subscriptions[topic]) > 0
    
    def get_subscribers(self, topic: str) -> Set[str]:
        """获取主题的订阅者"""
        return self.subscriptions.get(topic, set()).copy()
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {str(e)}")
            # 连接可能已断开，触发清理
            self.disconnect(websocket)
    
    async def send_personal_message_by_id(self, message: str, client_id: str) -> bool:
        """通过客户端ID发送个人消息"""
        websocket = self.active_connections.get(client_id)
        if websocket:
            try:
                await websocket.send_text(message)
                return True
            except Exception as e:
                logger.error(f"Error sending message to client {client_id}: {str(e)}")
                self.disconnect(websocket)
                return False
        return False
    
    async def broadcast(self, message: str):
        """广播消息给所有连接的客户端"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to client {client_id}: {str(e)}")
                disconnected_clients.append(websocket)
        
        # 清理断开的连接
        for websocket in disconnected_clients:
            self.disconnect(websocket)
    
    async def broadcast_to_topic(self, topic: str, message: str):
        """向特定主题的订阅者广播消息"""
        if topic not in self.subscriptions:
            return
        
        subscribers = self.subscriptions[topic].copy()
        disconnected_clients = []
        
        for client_id in subscribers:
            websocket = self.active_connections.get(client_id)
            if websocket:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending topic message to client {client_id}: {str(e)}")
                    disconnected_clients.append(websocket)
            else:
                # 客户端连接不存在，从订阅中移除
                self.subscriptions[topic].discard(client_id)
        
        # 清理断开的连接
        for websocket in disconnected_clients:
            self.disconnect(websocket)
    
    async def broadcast_to_clients(self, message: str, client_ids: List[str]):
        """向指定的客户端列表广播消息"""
        disconnected_clients = []
        
        for client_id in client_ids:
            websocket = self.active_connections.get(client_id)
            if websocket:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending message to client {client_id}: {str(e)}")
                    disconnected_clients.append(websocket)
        
        # 清理断开的连接
        for websocket in disconnected_clients:
            self.disconnect(websocket)
    
    def get_subscription_stats(self) -> Dict[str, int]:
        """获取订阅统计"""
        return {topic: len(subscribers) for topic, subscribers in self.subscriptions.items()}
    
    def get_active_topics(self) -> List[str]:
        """获取活跃主题列表"""
        return list(self.subscriptions.keys())
    
    def get_client_subscriptions(self, client_id: str) -> List[str]:
        """获取客户端的订阅列表"""
        return list(self.client_subscriptions.get(client_id, set()))
    
    def is_client_connected(self, client_id: str) -> bool:
        """检查客户端是否连接"""
        return client_id in self.active_connections
    
    def get_all_clients(self) -> List[str]:
        """获取所有连接的客户端ID"""
        return list(self.active_connections.keys())
    
    async def send_to_multiple_topics(self, message: str, topics: List[str]):
        """向多个主题发送消息"""
        all_recipients = set()
        
        for topic in topics:
            if topic in self.subscriptions:
                all_recipients.update(self.subscriptions[topic])
        
        await self.broadcast_to_clients(message, list(all_recipients))
    
    def cleanup_empty_topics(self):
        """清理没有订阅者的主题"""
        empty_topics = [topic for topic, subscribers in self.subscriptions.items() if not subscribers]
        for topic in empty_topics:
            del self.subscriptions[topic]
        
        logger.info(f"Cleaned up {len(empty_topics)} empty topics")
    
    def get_connection_info(self) -> Dict[str, any]:
        """获取连接信息摘要"""
        return {
            "total_connections": len(self.active_connections),
            "total_topics": len(self.subscriptions),
            "total_subscriptions": sum(len(subscribers) for subscribers in self.subscriptions.values()),
            "clients": list(self.active_connections.keys()),
            "topics": list(self.subscriptions.keys()),
            "subscription_details": {
                topic: list(subscribers) 
                for topic, subscribers in self.subscriptions.items()
            }
        }
    
    async def ping_all_clients(self) -> Dict[str, bool]:
        """向所有客户端发送ping并检查连接状态"""
        ping_results = {}
        
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.ping()
                ping_results[client_id] = True
            except Exception as e:
                logger.warning(f"Client {client_id} ping failed: {str(e)}")
                ping_results[client_id] = False
                # 可以选择在这里清理断开的连接
                # self.disconnect(websocket)
        
        return ping_results
    
    def export_subscriptions(self) -> Dict[str, any]:
        """导出订阅关系（用于调试和监控）"""
        return {
            "subscriptions_by_topic": {
                topic: list(subscribers) 
                for topic, subscribers in self.subscriptions.items()
            },
            "subscriptions_by_client": {
                client_id: list(topics) 
                for client_id, topics in self.client_subscriptions.items()
            },
            "statistics": {
                "total_connections": len(self.active_connections),
                "total_topics": len(self.subscriptions),
                "average_subscriptions_per_client": (
                    sum(len(topics) for topics in self.client_subscriptions.values()) / 
                    len(self.client_subscriptions) if self.client_subscriptions else 0
                ),
                "average_subscribers_per_topic": (
                    sum(len(subscribers) for subscribers in self.subscriptions.values()) / 
                    len(self.subscriptions) if self.subscriptions else 0
                )
            }
        }