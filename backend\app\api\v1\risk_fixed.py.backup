"""
修复后的风控管理API
提供风险监控、限制设置、告警管理等功能
"""
from datetime import datetime, timedelta
from typing import List, Optional
import random

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies_fixed import get_current_active_user
from app.db.models.user import User
from app.schemas.risk import (
    RiskLimitCreate, RiskLimitUpdate, RiskLimitResponse,
    RiskMetricsResponse, RiskAlertResponse, RiskReportResponse
)

router = APIRouter()


# 临时的风控数据存储
risk_limits_db = {}
risk_alerts_db = []


@router.get("/metrics", response_model=RiskMetricsResponse)
async def get_risk_metrics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前风险指标
    
    返回账户的实时风险指标，包括：
    - 资金使用率
    - 持仓集中度
    - 最大回撤
    - VaR（风险价值）
    - 夏普比率
    """
    # 模拟风险指标数据
    metrics = {
        "user_id": current_user.id,
        "timestamp": datetime.utcnow(),
        "capital_usage_rate": random.uniform(0.3, 0.7),  # 资金使用率
        "position_concentration": random.uniform(0.1, 0.4),  # 持仓集中度
        "max_drawdown": random.uniform(-0.15, -0.05),  # 最大回撤
        "current_drawdown": random.uniform(-0.08, 0),  # 当前回撤
        "var_95": random.uniform(0.01, 0.03),  # 95% VaR
        "var_99": random.uniform(0.02, 0.05),  # 99% VaR
        "sharpe_ratio": random.uniform(1.0, 2.5),  # 夏普比率
        "leverage_ratio": random.uniform(1.0, 1.5),  # 杠杆率
        "margin_ratio": random.uniform(0.2, 0.4),  # 保证金比例
        "risk_score": random.randint(60, 90),  # 风险评分
        "risk_level": "medium",  # 风险等级
        "warnings": []
    }
    
    # 添加风险警告
    if metrics["capital_usage_rate"] > 0.8:
        metrics["warnings"].append("资金使用率过高")
    if metrics["position_concentration"] > 0.5:
        metrics["warnings"].append("持仓过于集中")
    if metrics["current_drawdown"] < -0.1:
        metrics["warnings"].append("当前回撤较大")
    
    return RiskMetricsResponse(**metrics)


@router.get("/limits", response_model=RiskLimitResponse)
async def get_risk_limits(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取风控限制设置
    """
    # 获取用户的风控限制
    user_limits = risk_limits_db.get(current_user.id)
    
    if not user_limits:
        # 返回默认限制
        user_limits = {
            "user_id": current_user.id,
            "max_position_size": 0.2,  # 单个持仓最大占比20%
            "max_leverage": 2.0,  # 最大杠杆2倍
            "max_daily_loss": 0.05,  # 单日最大亏损5%
            "max_drawdown": 0.15,  # 最大回撤15%
            "stop_loss_required": True,  # 强制止损
            "default_stop_loss": 0.05,  # 默认止损5%
            "max_orders_per_day": 100,  # 每日最大订单数
            "max_order_value": 100000,  # 单笔订单最大金额
            "allowed_symbols": [],  # 允许交易的标的（空表示不限制）
            "blocked_symbols": [],  # 禁止交易的标的
            "trading_hours_only": True,  # 仅在交易时间下单
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        risk_limits_db[current_user.id] = user_limits
    
    return RiskLimitResponse(**user_limits)


@router.put("/limits", response_model=RiskLimitResponse)
async def update_risk_limits(
    limits_update: RiskLimitUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新风控限制设置
    """
    # 获取现有限制
    user_limits = risk_limits_db.get(current_user.id, {
        "user_id": current_user.id,
        "created_at": datetime.utcnow()
    })
    
    # 更新字段
    update_data = limits_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if value is not None:
            user_limits[field] = value
    
    user_limits["updated_at"] = datetime.utcnow()
    
    # 保存更新
    risk_limits_db[current_user.id] = user_limits
    
    return RiskLimitResponse(**user_limits)


@router.get("/alerts", response_model=List[RiskAlertResponse])
async def get_risk_alerts(
    status: Optional[str] = Query(None, regex="^(active|resolved|all)$"),
    level: Optional[str] = Query(None, regex="^(high|medium|low)$"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取风险告警列表
    
    - **status**: 告警状态（active/resolved/all）
    - **level**: 告警级别（high/medium/low）
    """
    # 筛选用户的告警
    user_alerts = [
        alert for alert in risk_alerts_db 
        if alert["user_id"] == current_user.id
    ]
    
    # 应用筛选条件
    if status and status != "all":
        user_alerts = [a for a in user_alerts if a["status"] == status]
    if level:
        user_alerts = [a for a in user_alerts if a["level"] == level]
    
    # 排序（按时间倒序）
    user_alerts.sort(key=lambda x: x["created_at"], reverse=True)
    
    # 分页
    alerts = user_alerts[skip:skip + limit]
    
    return [RiskAlertResponse(**alert) for alert in alerts]


@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(
    alert_id: str,
    resolution_note: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    解决风险告警
    """
    # 查找告警
    alert = None
    for a in risk_alerts_db:
        if a["id"] == alert_id and a["user_id"] == current_user.id:
            alert = a
            break
    
    if not alert:
        raise HTTPException(status_code=404, detail="告警不存在")
    
    # 更新告警状态
    alert["status"] = "resolved"
    alert["resolved_at"] = datetime.utcnow()
    alert["resolution_note"] = resolution_note
    
    return {"message": "告警已解决", "alert_id": alert_id}


@router.get("/report", response_model=RiskReportResponse)
async def get_risk_report(
    period: str = Query("1M", regex="^(1D|1W|1M|3M|6M|1Y)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取风险报告
    
    - **period**: 报告周期（1D/1W/1M/3M/6M/1Y）
    """
    # 模拟风险报告数据
    report = {
        "user_id": current_user.id,
        "period": period,
        "generated_at": datetime.utcnow(),
        "summary": {
            "risk_score": random.randint(70, 90),
            "risk_level": "medium",
            "total_alerts": random.randint(5, 20),
            "high_risk_alerts": random.randint(0, 3),
            "compliance_score": random.uniform(0.85, 0.98)
        },
        "metrics_history": {
            "max_drawdown": random.uniform(-0.15, -0.05),
            "avg_leverage": random.uniform(1.0, 1.5),
            "var_breaches": random.randint(0, 2),
            "limit_breaches": random.randint(0, 5)
        },
        "risk_events": [
            {
                "date": (datetime.utcnow() - timedelta(days=i)).isoformat(),
                "type": random.choice(["drawdown", "leverage", "concentration"]),
                "severity": random.choice(["high", "medium", "low"]),
                "description": "风险事件描述"
            }
            for i in range(3)
        ],
        "recommendations": [
            "建议降低持仓集中度",
            "考虑设置更严格的止损",
            "监控市场波动率变化"
        ]
    }
    
    return RiskReportResponse(**report)


@router.post("/check-order")
async def check_order_risk(
    order_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    检查订单风险
    
    在下单前进行风险检查，包括：
    - 资金是否充足
    - 是否超过持仓限制
    - 是否在允许的交易时间
    - 是否为允许的交易标的
    """
    # 获取用户的风控限制
    user_limits = risk_limits_db.get(current_user.id, {})
    
    checks = []
    passed = True
    
    # 检查订单金额
    order_value = order_data.get("price", 0) * order_data.get("quantity", 0)
    max_order_value = user_limits.get("max_order_value", float("inf"))
    
    if order_value > max_order_value:
        checks.append({
            "rule": "max_order_value",
            "passed": False,
            "message": f"订单金额超过限制：{max_order_value}"
        })
        passed = False
    else:
        checks.append({
            "rule": "max_order_value",
            "passed": True,
            "message": "订单金额检查通过"
        })
    
    # 检查交易标的
    symbol = order_data.get("symbol", "")
    blocked_symbols = user_limits.get("blocked_symbols", [])
    
    if symbol in blocked_symbols:
        checks.append({
            "rule": "blocked_symbol",
            "passed": False,
            "message": f"该标的禁止交易：{symbol}"
        })
        passed = False
    
    # 检查交易时间
    if user_limits.get("trading_hours_only", True):
        current_hour = datetime.now().hour
        if current_hour < 9 or current_hour >= 15:
            checks.append({
                "rule": "trading_hours",
                "passed": False,
                "message": "当前非交易时间"
            })
            passed = False
    
    # 生成风险评分
    risk_score = random.randint(60, 95) if passed else random.randint(20, 50)
    
    return {
        "passed": passed,
        "risk_score": risk_score,
        "checks": checks,
        "recommendation": "可以下单" if passed else "建议取消下单"
    }


@router.post("/simulate-alert")
async def simulate_risk_alert(
    alert_type: str = Query(..., regex="^(drawdown|leverage|concentration|loss)$"),
    current_user: User = Depends(get_current_active_user)
):
    """
    模拟生成风险告警（仅用于测试）
    """
    import uuid
    
    alert = {
        "id": str(uuid.uuid4()),
        "user_id": current_user.id,
        "type": alert_type,
        "level": random.choice(["high", "medium", "low"]),
        "status": "active",
        "title": f"{alert_type.upper()} 风险告警",
        "message": f"检测到{alert_type}风险超过阈值",
        "details": {
            "current_value": random.uniform(0.1, 0.5),
            "threshold": random.uniform(0.05, 0.3),
            "symbol": random.choice(["AAPL", "GOOGL", "MSFT", "AMZN"])
        },
        "created_at": datetime.utcnow(),
        "resolved_at": None,
        "resolution_note": None
    }
    
    risk_alerts_db.append(alert)
    
    return {
        "message": "告警已生成",
        "alert": RiskAlertResponse(**alert)
    }


@router.get("/health")
async def health_check():
    """
    风控模块健康检查
    """
    return {
        "status": "healthy",
        "module": "risk",
        "timestamp": datetime.now().isoformat(),
        "active_alerts": len([a for a in risk_alerts_db if a.get("status") == "active"])
    }