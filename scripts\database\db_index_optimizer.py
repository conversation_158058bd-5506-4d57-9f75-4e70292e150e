"""
数据库索引优化脚本
为量化交易平台的数据库创建和管理索引以提升查询性能
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from sqlalchemy import Index, MetaData, Table, inspect, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import db_manager, get_db


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseIndexOptimizer:
    """数据库索引优化器"""
    
    def __init__(self):
        self.session: Optional[AsyncSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = db_manager.async_session_maker()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def analyze_query_performance(self) -> Dict[str, any]:
        """分析当前查询性能"""
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        performance_data = {}
        
        try:
            # 分析表大小
            tables_info = await self._get_table_sizes()
            performance_data['table_sizes'] = tables_info
            
            # 分析索引使用情况
            index_usage = await self._analyze_index_usage()
            performance_data['index_usage'] = index_usage
            
            # 分析查询执行计划
            query_plans = await self._analyze_query_plans()
            performance_data['query_plans'] = query_plans
            
            logger.info("查询性能分析完成")
            return performance_data
            
        except Exception as e:
            logger.error(f"性能分析失败: {e}")
            raise
    
    async def _get_table_sizes(self) -> Dict[str, int]:
        """获取表大小统计"""
        tables_info = {}
        
        # SQLite查询表行数
        tables = [
            'users', 'orders', 'trades', 'positions', 'accounts', 
            'portfolios', 'symbols', 'market_data', 'kline_data', 
            'depth_data', 'trade_ticks', 'transaction_logs'
        ]
        
        for table in tables:
            try:
                result = await self.session.execute(
                    text(f"SELECT COUNT(*) as count FROM {table}")
                )
                count = result.scalar()
                tables_info[table] = count
            except Exception as e:
                logger.warning(f"无法获取表 {table} 的大小: {e}")
                tables_info[table] = 0
                
        return tables_info
    
    async def _analyze_index_usage(self) -> Dict[str, any]:
        """分析索引使用情况"""
        index_info = {}
        
        try:
            # 获取所有索引信息
            result = await self.session.execute(text("""
                SELECT name, tbl_name, sql 
                FROM sqlite_master 
                WHERE type = 'index' AND sql IS NOT NULL
            """))
            
            indexes = result.fetchall()
            index_info['total_indexes'] = len(indexes)
            index_info['indexes'] = [
                {'name': idx.name, 'table': idx.tbl_name, 'sql': idx.sql}
                for idx in indexes
            ]
            
        except Exception as e:
            logger.warning(f"索引分析失败: {e}")
            index_info = {'total_indexes': 0, 'indexes': []}
            
        return index_info
    
    async def _analyze_query_plans(self) -> List[Dict[str, any]]:
        """分析常用查询的执行计划"""
        query_plans = []
        
        # 定义关键查询
        key_queries = [
            {
                'name': 'user_orders_by_status',
                'sql': """
                    SELECT * FROM orders 
                    WHERE user_id = 1 AND status = 'pending' 
                    ORDER BY created_at DESC LIMIT 10
                """
            },
            {
                'name': 'symbol_kline_data',
                'sql': """
                    SELECT * FROM kline_data 
                    WHERE symbol_code = '000001' AND kline_type = '1d'
                    ORDER BY trading_date DESC LIMIT 100
                """
            },
            {
                'name': 'user_positions',
                'sql': """
                    SELECT * FROM positions 
                    WHERE user_id = 1 AND quantity > 0
                """
            },
            {
                'name': 'market_data_latest',
                'sql': """
                    SELECT * FROM market_data 
                    WHERE symbol_code = '000001'
                    ORDER BY timestamp DESC LIMIT 1
                """
            }
        ]
        
        for query in key_queries:
            try:
                # 使用EXPLAIN QUERY PLAN分析查询
                explain_sql = f"EXPLAIN QUERY PLAN {query['sql']}"
                result = await self.session.execute(text(explain_sql))
                plan = result.fetchall()
                
                query_plans.append({
                    'name': query['name'],
                    'sql': query['sql'],
                    'plan': [row._asdict() for row in plan]
                })
                
            except Exception as e:
                logger.warning(f"查询计划分析失败 {query['name']}: {e}")
                
        return query_plans
    
    async def create_optimized_indexes(self) -> List[str]:
        """创建优化索引"""
        created_indexes = []
        
        # 定义优化索引
        optimization_indexes = [
            # 订单相关索引
            {
                'table': 'orders',
                'name': 'ix_orders_user_status_created_optimized',
                'columns': ['user_id', 'status', 'created_at', 'symbol_code'],
                'description': '用户订单状态查询优化'
            },
            {
                'table': 'orders',
                'name': 'ix_orders_symbol_status_time',
                'columns': ['symbol_code', 'status', 'submit_time'],
                'description': '标的订单状态时间查询优化'
            },
            
            # 成交记录索引
            {
                'table': 'trades',
                'name': 'ix_trades_user_symbol_time',
                'columns': ['user_id', 'symbol_code', 'trade_time'],
                'description': '用户成交记录查询优化'
            },
            {
                'table': 'trades',
                'name': 'ix_trades_order_time',
                'columns': ['order_id', 'trade_time'],
                'description': '订单成交时间查询优化'
            },
            
            # 持仓相关索引
            {
                'table': 'positions',
                'name': 'ix_positions_user_active',
                'columns': ['user_id', 'quantity'],
                'description': '用户活跃持仓查询优化'
            },
            
            # 市场数据索引
            {
                'table': 'market_data',
                'name': 'ix_market_data_symbol_timestamp_optimized',
                'columns': ['symbol_code', 'timestamp', 'trading_date'],
                'description': '市场数据时间查询优化'
            },
            
            # K线数据索引
            {
                'table': 'kline_data',
                'name': 'ix_kline_data_symbol_type_period',
                'columns': ['symbol_code', 'kline_type', 'period_start', 'period_end'],
                'description': 'K线数据周期查询优化'
            },
            {
                'table': 'kline_data',
                'name': 'ix_kline_data_trading_date_type',
                'columns': ['trading_date', 'kline_type', 'symbol_code'],
                'description': 'K线数据交易日查询优化'
            },
            
            # 深度数据索引
            {
                'table': 'depth_data',
                'name': 'ix_depth_data_symbol_timestamp_optimized',
                'columns': ['symbol_code', 'timestamp'],
                'description': '深度数据时间查询优化'
            },
            
            # Tick数据索引
            {
                'table': 'trade_ticks',
                'name': 'ix_trade_ticks_symbol_trade_time',
                'columns': ['symbol_code', 'trade_time', 'timestamp'],
                'description': 'Tick数据时间查询优化'
            },
            
            # 账户相关索引
            {
                'table': 'accounts',
                'name': 'ix_accounts_user_active',
                'columns': ['user_id', 'is_active', 'is_tradable'],
                'description': '用户活跃账户查询优化'
            },
            
            # 交易日志索引
            {
                'table': 'transaction_logs',
                'name': 'ix_transaction_logs_user_type_created',
                'columns': ['user_id', 'transaction_type', 'created_at'],
                'description': '交易日志查询优化'
            },
        ]
        
        for index_def in optimization_indexes:
            try:
                # 检查索引是否已存在
                if await self._index_exists(index_def['name']):
                    logger.info(f"索引 {index_def['name']} 已存在，跳过创建")
                    continue
                
                # 创建索引
                columns_str = ', '.join(index_def['columns'])
                create_sql = f"""
                    CREATE INDEX IF NOT EXISTS {index_def['name']} 
                    ON {index_def['table']} ({columns_str})
                """
                
                await self.session.execute(text(create_sql))
                await self.session.commit()
                
                created_indexes.append(index_def['name'])
                logger.info(f"创建索引成功: {index_def['name']} - {index_def['description']}")
                
            except Exception as e:
                logger.error(f"创建索引失败 {index_def['name']}: {e}")
                await self.session.rollback()
        
        return created_indexes
    
    async def _index_exists(self, index_name: str) -> bool:
        """检查索引是否存在"""
        try:
            result = await self.session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type = 'index' AND name = :index_name
            """), {'index_name': index_name})
            
            return result.first() is not None
            
        except Exception:
            return False
    
    async def drop_unused_indexes(self) -> List[str]:
        """删除未使用的索引"""
        dropped_indexes = []
        
        try:
            # 获取所有用户创建的索引
            result = await self.session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type = 'index' AND sql IS NOT NULL
                AND name NOT LIKE 'sqlite_%'
            """))
            
            indexes = [row.name for row in result.fetchall()]
            
            # 这里可以根据实际使用情况判断哪些索引需要删除
            # 为了安全起见，这里只是记录，不实际删除
            logger.info(f"发现 {len(indexes)} 个索引，需要人工审核是否删除")
            
        except Exception as e:
            logger.error(f"分析未使用索引失败: {e}")
        
        return dropped_indexes
    
    async def optimize_table_statistics(self) -> bool:
        """优化表统计信息"""
        try:
            # SQLite的ANALYZE命令
            await self.session.execute(text("ANALYZE"))
            await self.session.commit()
            
            logger.info("表统计信息优化完成")
            return True
            
        except Exception as e:
            logger.error(f"优化表统计信息失败: {e}")
            return False
    
    async def generate_optimization_report(self) -> Dict[str, any]:
        """生成优化报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'database_type': 'SQLite',
            'optimization_summary': {}
        }
        
        try:
            # 分析性能
            performance_data = await self.analyze_query_performance()
            report['performance_analysis'] = performance_data
            
            # 创建索引
            created_indexes = await self.create_optimized_indexes()
            report['created_indexes'] = created_indexes
            
            # 优化统计信息
            stats_optimized = await self.optimize_table_statistics()
            report['statistics_optimized'] = stats_optimized
            
            # 生成摘要
            report['optimization_summary'] = {
                'total_tables': len(performance_data.get('table_sizes', {})),
                'total_indexes_created': len(created_indexes),
                'statistics_updated': stats_optimized,
                'recommendations': self._generate_recommendations(performance_data)
            }
            
            logger.info("优化报告生成完成")
            
        except Exception as e:
            logger.error(f"生成优化报告失败: {e}")
            report['error'] = str(e)
        
        return report
    
    def _generate_recommendations(self, performance_data: Dict[str, any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于表大小的建议
        table_sizes = performance_data.get('table_sizes', {})
        large_tables = [table for table, size in table_sizes.items() if size > 10000]
        
        if large_tables:
            recommendations.append(
                f"大数据量表 {', '.join(large_tables)} 建议定期清理历史数据"
            )
        
        # 基于索引的建议
        index_usage = performance_data.get('index_usage', {})
        if index_usage.get('total_indexes', 0) < 10:
            recommendations.append("建议增加更多索引以提升查询性能")
        
        # 基于查询计划的建议
        query_plans = performance_data.get('query_plans', [])
        for plan in query_plans:
            if any('SCAN TABLE' in str(step) for step in plan.get('plan', [])):
                recommendations.append(f"查询 {plan['name']} 可能需要优化索引")
        
        # 通用建议
        recommendations.extend([
            "定期执行VACUUM命令清理数据库碎片",
            "监控慢查询日志并优化相关查询",
            "考虑使用分区表处理大数据量",
            "定期备份和恢复测试数据库"
        ])
        
        return recommendations


async def main():
    """主函数 - 执行数据库索引优化"""
    print("=" * 60)
    print("数据库索引优化工具")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db_manager.initialize()
        
        async with DatabaseIndexOptimizer() as optimizer:
            # 生成优化报告
            report = await optimizer.generate_optimization_report()
            
            # 打印报告
            print(f"\n优化时间: {report['timestamp']}")
            print(f"数据库类型: {report['database_type']}")
            
            summary = report.get('optimization_summary', {})
            print(f"\n优化摘要:")
            print(f"  - 总表数: {summary.get('total_tables', 0)}")
            print(f"  - 创建索引数: {summary.get('total_indexes_created', 0)}")
            print(f"  - 统计信息已更新: {summary.get('statistics_updated', False)}")
            
            created_indexes = report.get('created_indexes', [])
            if created_indexes:
                print(f"\n新创建的索引:")
                for idx in created_indexes:
                    print(f"  - {idx}")
            
            recommendations = summary.get('recommendations', [])
            if recommendations:
                print(f"\n优化建议:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec}")
            
            # 保存报告到文件
            import json
            report_file = f"/Users/<USER>/Desktop/quant-platform/backend/logs/db_optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n详细报告已保存到: {report_file}")
            
    except Exception as e:
        logger.error(f"优化过程失败: {e}")
        raise
    finally:
        await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main())