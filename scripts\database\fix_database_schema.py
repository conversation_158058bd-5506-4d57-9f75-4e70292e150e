#!/usr/bin/env python3
"""
修复数据库模式脚本
解决ORM模型和数据库表结构不匹配的问题
"""

import sqlite3
import bcrypt
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_table_structure():
    """检查当前表结构"""
    print("🔍 检查当前数据库表结构...")
    
    try:
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        # 检查users表结构
        cursor.execute('PRAGMA table_info(users)')
        columns = cursor.fetchall()
        
        print("📋 当前users表结构:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # 检查数据
        cursor.execute('SELECT COUNT(*) FROM users')
        user_count = cursor.fetchone()[0]
        print(f"📊 用户数量: {user_count}")
        
        conn.close()
        return columns
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return []

def create_compatible_table():
    """创建兼容的用户表"""
    print("🔧 创建兼容的用户表...")
    
    try:
        conn = sqlite3.connect('test.db')
        cursor = conn.cursor()
        
        # 备份现有数据
        cursor.execute('SELECT * FROM users')
        existing_users = cursor.fetchall()
        print(f"📦 备份 {len(existing_users)} 个用户")
        
        # 删除旧表
        cursor.execute('DROP TABLE IF EXISTS users_backup')
        cursor.execute('CREATE TABLE users_backup AS SELECT * FROM users')
        cursor.execute('DROP TABLE IF EXISTS users')
        
        # 创建新的users表（兼容ORM模型）
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                hashed_password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                phone VARCHAR(20),
                avatar VARCHAR(255),
                is_active BOOLEAN DEFAULT 1 NOT NULL,
                is_superuser BOOLEAN DEFAULT 0 NOT NULL,
                is_verified BOOLEAN DEFAULT 0 NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                login_count INTEGER DEFAULT 0,
                failed_login_count INTEGER DEFAULT 0,
                last_failed_login DATETIME,
                password_changed_at DATETIME,
                email_verified_at DATETIME,
                phone_verified_at DATETIME,
                two_factor_enabled BOOLEAN DEFAULT 0,
                two_factor_secret VARCHAR(255),
                backup_codes TEXT,
                preferences TEXT,
                metadata TEXT,
                status VARCHAR(20) DEFAULT 'active',
                role VARCHAR(20) DEFAULT 'viewer',
                department VARCHAR(100),
                position VARCHAR(100),
                manager_id INTEGER,
                timezone VARCHAR(50) DEFAULT 'UTC',
                language VARCHAR(10) DEFAULT 'zh-CN',
                theme VARCHAR(20) DEFAULT 'light'
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX idx_users_username ON users(username)')
        cursor.execute('CREATE INDEX idx_users_email ON users(email)')
        cursor.execute('CREATE INDEX idx_users_status ON users(status)')
        cursor.execute('CREATE INDEX idx_users_role ON users(role)')
        
        print("✅ 新用户表创建完成")
        
        # 迁移数据
        if existing_users:
            print("📥 迁移现有用户数据...")
            for user in existing_users:
                # 假设旧表结构: id, username, email, hashed_password, is_active, is_superuser
                if len(user) >= 6:
                    cursor.execute('''
                        INSERT INTO users (
                            id, username, email, hashed_password, is_active, is_superuser,
                            full_name, is_verified, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    ''', (user[0], user[1], user[2], user[3], user[4], user[5], user[1]))
        
        # 确保admin用户存在且密码正确
        admin_password = "admin123"
        admin_hash = bcrypt.hashpw(admin_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            print("👤 创建admin用户...")
            cursor.execute('''
                INSERT INTO users (
                    username, email, hashed_password, full_name, is_active, is_superuser,
                    is_verified, role, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, 1, 1, 1, 'admin', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', ('admin', '<EMAIL>', admin_hash, '系统管理员'))
        else:
            print("🔄 更新admin用户密码...")
            cursor.execute('''
                UPDATE users 
                SET hashed_password = ?, is_active = 1, is_superuser = 1, is_verified = 1,
                    role = 'admin', status = 'active', updated_at = CURRENT_TIMESTAMP
                WHERE username = 'admin'
            ''', (admin_hash,))
        
        # 创建demo用户
        demo_password = "demo123"
        demo_hash = bcrypt.hashpw(demo_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('demo',))
        if cursor.fetchone()[0] == 0:
            print("👤 创建demo用户...")
            cursor.execute('''
                INSERT INTO users (
                    username, email, hashed_password, full_name, is_active, is_superuser,
                    is_verified, role, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, 1, 0, 1, 'viewer', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', ('demo', '<EMAIL>', demo_hash, '演示用户'))
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库模式修复完成")
        print(f"   - admin用户密码: {admin_password}")
        print(f"   - demo用户密码: {demo_password}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        return False

def test_orm_compatibility():
    """测试ORM兼容性"""
    print("🧪 测试ORM兼容性...")
    
    try:
        # 测试SQLAlchemy查询
        import asyncio
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from sqlalchemy import select
        
        # 创建异步引擎
        engine = create_async_engine("sqlite+aiosqlite:///test.db")
        async_session = sessionmaker(engine, class_=AsyncSession)
        
        async def test_query():
            async with async_session() as session:
                # 导入User模型
                from app.db.models.user import User
                
                # 测试查询admin用户
                stmt = select(User).where(User.username == 'admin')
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                if user:
                    print(f"✅ ORM查询成功: {user.username} ({user.email})")
                    
                    # 测试密码验证
                    from app.core.security import verify_password
                    if verify_password('admin123', user.hashed_password):
                        print("✅ 密码验证成功")
                        return True
                    else:
                        print("❌ 密码验证失败")
                        return False
                else:
                    print("❌ 未找到admin用户")
                    return False
        
        # 运行异步测试
        return asyncio.run(test_query())
        
    except Exception as e:
        print(f"❌ ORM测试失败: {e}")
        return False

def test_api_login():
    """测试API登录"""
    print("🧪 测试API登录...")
    
    try:
        import requests
        import json
        
        response = requests.post(
            'http://localhost:8000/api/v1/auth/login',
            json={'username': 'admin', 'password': 'admin123'},
            timeout=10
        )
        
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 登录成功!")
            print(f"   Token: {data.get('access_token', '')[:50]}...")
            print(f"   用户: {data.get('user', {}).get('username', '')}")
            return True
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主修复流程"""
    print("🚀 开始修复数据库模式和认证系统...")
    print("=" * 50)
    
    # 1. 检查当前表结构
    current_columns = check_table_structure()
    
    # 2. 创建兼容的表结构
    if not create_compatible_table():
        print("❌ 数据库修复失败，退出")
        return False
    
    # 3. 测试ORM兼容性
    print("\n" + "=" * 30)
    if test_orm_compatibility():
        print("✅ ORM兼容性测试通过")
    else:
        print("⚠️ ORM兼容性测试失败，但可能是异步问题")
    
    # 4. 测试API登录
    print("\n" + "=" * 30)
    if test_api_login():
        print("✅ API登录测试通过")
    else:
        print("⚠️ API登录测试失败，可能需要重启后端服务")
    
    print("\n🎉 数据库模式修复完成！")
    print("📋 可用账户:")
    print("   - admin / admin123 (管理员)")
    print("   - demo / demo123 (演示用户)")
    print("\n💡 建议:")
    print("   1. 重启后端服务以确保所有更改生效")
    print("   2. 测试前端登录功能")
    print("   3. 验证API访问权限")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 修复成功！")
    else:
        print("\n❌ 修复失败！")
