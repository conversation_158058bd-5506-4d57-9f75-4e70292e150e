{"summary": {"totalTests": 10, "passed": 3, "failed": 6, "warnings": 1, "totalDuration": 13277, "timestamp": "2025-08-03T04:42:52.970Z"}, "testResults": [{"name": "首页基本结构", "status": "PASS", "details": {"title": "仪表盘 - 量化投资平台", "hasHeader": true, "hasNavigation": true, "hasMainContent": true, "hasFooter": false, "linkCount": 0, "buttonCount": 9, "contentLength": 393}, "duration": 2752, "timestamp": "2025-08-03T04:42:43.826Z"}, {"name": "导航链接检测", "status": "FAIL", "details": {"linkCount": 0, "links": []}, "duration": 0, "timestamp": "2025-08-03T04:42:43.849Z"}, {"name": "模拟交易页面结构", "status": "FAIL", "details": {"hasHeader": false, "hasSearchBox": false, "hasAccountDashboard": false, "hasTradingWorkspace": false, "hasLeftPanel": true, "hasRightPanel": true, "hasBottomPanel": false, "hasTradeForm": false}, "duration": 0, "timestamp": "2025-08-03T04:42:46.642Z"}, {"name": "搜索框存在性", "status": "FAIL", "details": {"error": "搜索框未找到"}, "duration": 0, "timestamp": "2025-08-03T04:42:46.656Z"}, {"name": "买入表单显示", "status": "FAIL", "details": {"error": "买入表单未找到"}, "duration": 0, "timestamp": "2025-08-03T04:42:49.668Z"}, {"name": "账户信息显示", "status": "FAIL", "details": {"accountInfo": null}, "duration": 0, "timestamp": "2025-08-03T04:42:49.670Z"}, {"name": "实盘交易页面结构", "status": "FAIL", "details": {"hasHeader": false, "hasSearchBox": false, "hasTradingTerminal": false, "hasConnectionStatus": true, "hasOrderPanel": false, "hasDepthPanel": false}, "duration": 0, "timestamp": "2025-08-03T04:42:51.387Z"}, {"name": "策略页面访问", "status": "PASS", "details": {"url": "http://localhost:5173/strategy"}, "duration": 0, "timestamp": "2025-08-03T04:42:52.967Z"}, {"name": "页面加载性能", "status": "PASS", "details": {"domContentLoaded": 0.*****************, "loadComplete": 0.****************, "totalLoadTime": 187, "resourceCount": 250}, "duration": 0, "timestamp": "2025-08-03T04:42:52.969Z"}, {"name": "内存使用情况", "status": "WARN", "details": {"usedJSHeapSize": *********, "totalJSHeapSize": *********, "jsHeapSizeLimit": **********}, "duration": 0, "timestamp": "2025-08-03T04:42:52.970Z"}], "errors": [{"type": "console", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:42:41.593Z"}, {"type": "console", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:42:43.914Z"}, {"type": "console", "message": "🚨 页面错误: ", "timestamp": "2025-08-03T04:42:46.467Z"}, {"type": "console", "message": "🚨 全局异常: JSHandle@object", "timestamp": "2025-08-03T04:42:46.467Z"}, {"type": "console", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:42:49.732Z"}, {"type": "console", "message": "X-Frame-Options may only be set via an HTTP header sent along with a document. It may not be set inside <meta>.", "timestamp": "2025-08-03T04:42:51.441Z"}], "screenshots": ["homepage_initial_1754196163546.png", "simulated_trading_page_1754196166360.png", "live_trading_page_1754196171179.png", "strategy_page_1754196172681.png"], "recommendations": ["修复控制台错误和页面错误，提高稳定性", "修复失败的功能测试，确保核心功能正常"]}