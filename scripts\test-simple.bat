@echo off
echo Testing Quantitative Investment Platform...
echo.

echo Checking Python...
python --version
if errorlevel 1 (
    echo Python not found
) else (
    echo Python OK
)

echo.
echo Checking backend service...
powershell -Command "try { $response = Invoke-WebRequest -Uri http://localhost:8000/health -UseBasicParsing -TimeoutSec 3; Write-Host 'Backend service OK - Status:' $response.StatusCode } catch { Write-Host 'Backend service not responding' }"

echo.
echo Checking API endpoint...
powershell -Command "try { $response = Invoke-WebRequest -Uri http://localhost:8000/api/v1/market/stocks -UseBasicParsing -TimeoutSec 3; Write-Host 'API endpoint OK - Status:' $response.StatusCode } catch { Write-Host 'API endpoint not responding' }"

echo.
echo Test completed.
pause
