const puppeteer = require('puppeteer-core');

class RealtimeMarketTester {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await puppeteer.launch({
      executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ 控制台 [${type}]: ${text}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.log(`🚨 页面错误: ${error.message}`);
    });

    // 监听网络请求失败
    this.page.on('requestfailed', request => {
      console.log(`🌐 请求失败: ${request.url()} - ${request.failure().errorText}`);
    });
  }

  async testRealtimeMarket() {
    try {
      console.log('🚀 启动浏览器...');
      await this.init();

      console.log('📊 访问实时行情页面...');
      await this.page.goto('http://localhost:5173/market/realtime', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 检查页面基本信息
      const pageInfo = await this.page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          contentLength: document.body.textContent.length,
          hasVueApp: !!document.querySelector('#app'),
          totalElements: document.querySelectorAll('*').length,
          hasError: !!document.querySelector('.error-container'),
          hasLoading: !!document.querySelector('.loading-container'),
          hasContent: !!document.querySelector('.market-view-optimized'),
          errorMessage: document.querySelector('.el-result__title')?.textContent || '',
          pageContent: document.body.textContent.substring(0, 500)
        };
      });

      console.log('📄 页面基本信息:', pageInfo);

      // 检查市场组件
      const marketComponents = await this.page.evaluate(() => {
        return {
          hasMarketHeader: !!document.querySelector('.market-header'),
          hasMarketIndices: !!document.querySelector('.market-indices'),
          hasStockList: !!document.querySelector('.stock-list'),
          hasSearchInput: !!document.querySelector('input[placeholder*="搜索"]'),
          hasRefreshButton: !!document.querySelector('button'),
          
          // 检查具体的市场数据
          indicesCount: document.querySelectorAll('.index-card').length,
          stockRowsCount: document.querySelectorAll('.stock-row, .el-table__row').length,
          
          // 检查加载状态
          isLoading: !!document.querySelector('.el-loading-mask'),
          hasSkeletonLoading: !!document.querySelector('.el-skeleton'),
          
          // 检查错误状态
          hasErrorResult: !!document.querySelector('.el-result'),
          errorTitle: document.querySelector('.el-result__title')?.textContent || '',
          
          // 检查表格数据
          hasTable: !!document.querySelector('.el-table'),
          tableHeaders: Array.from(document.querySelectorAll('.el-table th')).map(th => th.textContent),
          
          // 检查图表
          hasCharts: document.querySelectorAll('[_echarts_instance_]').length
        };
      });

      console.log('💼 市场组件检查:', marketComponents);

      // 检查网络请求状态
      const networkInfo = await this.page.evaluate(() => {
        return {
          // 检查是否有API调用
          hasApiCalls: window.performance.getEntriesByType('resource')
            .filter(entry => entry.name.includes('/api/')).length,
          
          // 检查WebSocket连接
          hasWebSocket: !!window.WebSocket,
          
          // 检查本地存储
          hasLocalStorage: !!window.localStorage,
          localStorageKeys: Object.keys(window.localStorage || {})
        };
      });

      console.log('🌐 网络信息:', networkInfo);

      // 尝试搜索功能
      console.log('🔍 测试搜索功能...');
      const searchInput = await this.page.$('input[placeholder*="搜索"]');
      if (searchInput) {
        await searchInput.type('000001');
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('✅ 搜索输入测试完成');
      } else {
        console.log('❌ 未找到搜索输入框');
      }

      // 尝试刷新功能
      console.log('🔄 测试刷新功能...');
      const refreshButton = await this.page.$('button:has-text("刷新"), button[loading]');
      if (refreshButton) {
        await refreshButton.click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('✅ 刷新按钮测试完成');
      }

      // 生成测试报告
      const report = this.generateReport({
        pageInfo,
        marketComponents,
        networkInfo
      });

      console.log('\n📋 实时行情页面测试报告:');
      console.log('='.repeat(50));
      console.log(`📊 页面状态: ${pageInfo.hasError ? '❌ 错误' : pageInfo.hasLoading ? '⏳ 加载中' : '✅ 正常'}`);
      console.log(`📈 市场指数: ${marketComponents.indicesCount} 个`);
      console.log(`📊 股票数据: ${marketComponents.stockRowsCount} 行`);
      console.log(`🔍 搜索功能: ${marketComponents.hasSearchInput ? '✅' : '❌'}`);
      console.log(`📊 图表组件: ${marketComponents.hasCharts} 个`);
      console.log(`🌐 API调用: ${networkInfo.hasApiCalls} 个`);
      console.log(`📈 总体评分: ${report.overallScore}/100`);
      
      if (pageInfo.hasError) {
        console.log(`❌ 错误信息: ${pageInfo.errorMessage}`);
      }
      
      console.log('='.repeat(50));

      return report;

    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
      throw error;
    }
  }

  generateReport(data) {
    let score = 0;
    
    // 页面基础功能 (30分)
    if (!data.pageInfo.hasError) score += 15;
    if (data.pageInfo.hasContent) score += 15;
    
    // 市场组件 (40分)
    if (data.marketComponents.hasMarketHeader) score += 10;
    if (data.marketComponents.indicesCount > 0) score += 10;
    if (data.marketComponents.stockRowsCount > 0) score += 10;
    if (data.marketComponents.hasSearchInput) score += 10;
    
    // 交互功能 (20分)
    if (data.marketComponents.hasRefreshButton) score += 10;
    if (data.marketComponents.hasCharts > 0) score += 10;
    
    // 数据加载 (10分)
    if (data.networkInfo.hasApiCalls > 0) score += 10;
    
    return {
      overallScore: Math.min(100, score),
      details: data
    };
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

async function runTest() {
  const tester = new RealtimeMarketTester();
  
  try {
    const report = await tester.testRealtimeMarket();
    return report;
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  } finally {
    await tester.close();
  }
}

// 运行测试
runTest();
