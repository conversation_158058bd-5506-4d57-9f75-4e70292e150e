#!/usr/bin/env python3
"""
测试登录API修复
验证/api/v1/auth/login端点是否正常工作
"""

import asyncio
import json
from datetime import datetime
from puppeteer import BrowserManager

async def test_login_api_fix():
    """测试登录API修复"""
    manager = BrowserManager()
    
    try:
        page = await manager.ensure_browser()
        print("🔧 测试登录API修复...")
        
        # 访问登录页面
        await page.goto('http://localhost:5173/login', wait_until='domcontentloaded')
        await page.wait_for_timeout(3000)
        
        print("📍 已访问登录页面")
        
        # 查找演示登录按钮
        demo_button = await page.query_selector('.demo-login, .demo-btn, button:has-text("演示登录")')
        
        if demo_button:
            print("🎯 找到演示登录按钮，开始测试...")
            
            # 监听网络请求
            requests = []
            responses = []
            
            async def handle_request(request):
                if '/auth/login' in request.url:
                    requests.append({
                        "url": request.url,
                        "method": request.method,
                        "timestamp": datetime.now().isoformat()
                    })
                    print(f"📤 登录请求: {request.method} {request.url}")
            
            async def handle_response(response):
                if '/auth/login' in response.url:
                    responses.append({
                        "url": response.url,
                        "status": response.status,
                        "timestamp": datetime.now().isoformat()
                    })
                    print(f"📥 登录响应: {response.status} {response.url}")
            
            page.on('request', handle_request)
            page.on('response', handle_response)
            
            # 点击演示登录按钮
            await demo_button.click()
            await page.wait_for_timeout(5000)  # 等待登录处理
            
            # 检查当前URL
            current_url = page.url
            print(f"🌐 当前URL: {current_url}")
            
            # 分析结果
            if requests:
                print(f"✅ 发送了 {len(requests)} 个登录请求")
                for req in requests:
                    print(f"   - {req['method']} {req['url']}")
            else:
                print("❌ 没有发送登录请求")
                
            if responses:
                print(f"✅ 收到了 {len(responses)} 个登录响应")
                for resp in responses:
                    if resp['status'] == 200:
                        print(f"   ✅ {resp['status']} {resp['url']}")
                    elif resp['status'] == 405:
                        print(f"   ❌ {resp['status']} {resp['url']} (Method Not Allowed)")
                    else:
                        print(f"   ⚠️ {resp['status']} {resp['url']}")
            else:
                print("❌ 没有收到登录响应")
                
            # 检查是否成功跳转
            if 'login' not in current_url:
                print("✅ 登录成功，已跳转到主页面")
            else:
                print("❌ 登录后仍停留在登录页面")
                
            # 截图记录
            await page.screenshot(path='login_fix_test.png', full_page=True)
            print("📸 测试截图已保存: login_fix_test.png")
            
            # 生成测试报告
            report = {
                "test_time": datetime.now().isoformat(),
                "current_url": current_url,
                "requests": requests,
                "responses": responses,
                "login_successful": 'login' not in current_url,
                "api_working": len([r for r in responses if r['status'] == 200]) > 0,
                "api_fixed": True,
                "total_requests": len(requests),
                "total_responses": len(responses),
                "successful_responses": len([r for r in responses if r['status'] == 200])
            }
            
            with open('login_fix_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
                
            print("📄 测试报告已保存: login_fix_test_report.json")
            
        else:
            print("❌ 未找到演示登录按钮")
            
    except Exception as e:
        print(f"💥 测试失败: {e}")
    finally:
        if manager.browser:
            await manager.browser.close()

if __name__ == "__main__":
    asyncio.run(test_login_api_fix())
