#!/usr/bin/env python3
"""
测试环境部署和集成测试脚本
"""
import asyncio
import logging
import sys
import time
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class TestDeployment:
    """测试环境部署管理器"""

    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.services = ["backend", "db", "redis"]
        self.health_endpoints = ["/health", "/api/v1/monitoring/health", "/metrics"]

    def check_docker_compose(self) -> bool:
        """检查docker-compose是否可用"""
        try:
            result = subprocess.run(
                ["docker-compose", "--version"], capture_output=True, text=True
            )
            if result.returncode == 0:
                logger.info(f"Docker Compose available: {result.stdout.strip()}")
                return True
            else:
                logger.error("Docker Compose not available")
                return False
        except FileNotFoundError:
            logger.error("Docker Compose not found")
            return False

    def start_services(self) -> bool:
        """启动测试环境服务"""
        logger.info("🚀 Starting test environment services...")

        try:
            # 停止现有服务
            subprocess.run(["docker-compose", "down", "-v"], cwd=".", check=False)

            # 构建并启动服务
            result = subprocess.run(
                ["docker-compose", "up", "--build", "-d"],
                cwd=".",
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                logger.info("✅ Services started successfully")
                return True
            else:
                logger.error(f"❌ Failed to start services: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"❌ Error starting services: {e}")
            return False

    def wait_for_services(self, timeout: int = 120) -> bool:
        """等待服务启动完成"""
        logger.info("⏳ Waiting for services to be ready...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 检查基础健康端点
                response = requests.get(f"{self.base_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ Backend service is ready")
                    return True
            except requests.exceptions.RequestException:
                pass

            logger.info("⏳ Services not ready yet, waiting...")
            time.sleep(5)

        logger.error("❌ Services failed to start within timeout")
        return False

    def run_health_checks(self) -> Dict[str, bool]:
        """运行健康检查"""
        logger.info("🔍 Running health checks...")

        results = {}
        for endpoint in self.health_endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                success = response.status_code == 200
                results[endpoint] = success

                if success:
                    logger.info(f"✅ {endpoint}: OK")
                else:
                    logger.error(f"❌ {endpoint}: Failed ({response.status_code})")

            except Exception as e:
                results[endpoint] = False
                logger.error(f"❌ {endpoint}: Error - {e}")

        return results

    def run_api_tests(self) -> Dict[str, bool]:
        """运行API集成测试"""
        logger.info("🧪 Running API integration tests...")

        test_cases = [
            {
                "name": "Market Data API",
                "endpoint": "/api/v1/market/contracts",
                "method": "GET",
                "expected_status": 200,
            },
            {
                "name": "Auth API",
                "endpoint": "/api/v1/auth/register",
                "method": "POST",
                "data": {
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "password": "TestPass123!",
                    "confirm_password": "TestPass123!",
                },
                "expected_status": [200, 201, 400],  # 可能已存在
            },
            {
                "name": "Monitoring API",
                "endpoint": "/api/v1/monitoring/system-status",
                "method": "GET",
                "expected_status": 200,
            },
        ]

        results = {}
        for test in test_cases:
            try:
                if test["method"] == "GET":
                    response = requests.get(
                        f"{self.base_url}{test['endpoint']}", timeout=10
                    )
                elif test["method"] == "POST":
                    response = requests.post(
                        f"{self.base_url}{test['endpoint']}",
                        json=test.get("data", {}),
                        timeout=10,
                    )

                expected = test["expected_status"]
                if isinstance(expected, list):
                    success = response.status_code in expected
                else:
                    success = response.status_code == expected

                results[test["name"]] = success

                if success:
                    logger.info(f"✅ {test['name']}: OK ({response.status_code})")
                else:
                    logger.error(f"❌ {test['name']}: Failed ({response.status_code})")

            except Exception as e:
                results[test["name"]] = False
                logger.error(f"❌ {test['name']}: Error - {e}")

        return results

    def run_performance_tests(self) -> Dict[str, bool]:
        """运行性能测试"""
        logger.info("⚡ Running performance tests...")

        # 简单的响应时间测试
        results = {}
        endpoints = ["/health", "/api/v1/market/contracts"]

        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                response_time = time.time() - start_time

                # 响应时间应该小于1秒
                success = response.status_code == 200 and response_time < 1.0
                results[f"{endpoint} (response_time)"] = success

                if success:
                    logger.info(f"✅ {endpoint}: {response_time:.3f}s")
                else:
                    logger.error(
                        f"❌ {endpoint}: {response_time:.3f}s (too slow or failed)"
                    )

            except Exception as e:
                results[f"{endpoint} (response_time)"] = False
                logger.error(f"❌ {endpoint}: Error - {e}")

        return results

    def generate_report(
        self, health_results: Dict, api_results: Dict, perf_results: Dict
    ):
        """生成测试报告"""
        logger.info("📊 Generating test report...")

        total_tests = len(health_results) + len(api_results) + len(perf_results)
        passed_tests = sum(
            [
                sum(health_results.values()),
                sum(api_results.values()),
                sum(perf_results.values()),
            ]
        )

        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        report = f"""
🎯 测试环境集成测试报告
{'='*50}

📈 总体结果:
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 成功率: {success_rate:.1f}%

🔍 健康检查结果:
{self._format_results(health_results)}

🧪 API测试结果:
{self._format_results(api_results)}

⚡ 性能测试结果:
{self._format_results(perf_results)}

{'='*50}
"""

        print(report)

        # 保存报告到文件
        with open("test_report.txt", "w", encoding="utf-8") as f:
            f.write(report)

        return success_rate >= 80  # 80%以上通过率认为成功

    def _format_results(self, results: Dict[str, bool]) -> str:
        """格式化测试结果"""
        lines = []
        for test, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            lines.append(f"  {status} {test}")
        return "\n".join(lines)

    def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 Cleaning up test environment...")
        try:
            subprocess.run(["docker-compose", "down", "-v"], cwd=".", check=False)
            logger.info("✅ Cleanup completed")
        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")


def main():
    """主函数"""
    deployment = TestDeployment()

    try:
        # 检查前置条件
        if not deployment.check_docker_compose():
            sys.exit(1)

        # 启动服务
        if not deployment.start_services():
            sys.exit(1)

        # 等待服务就绪
        if not deployment.wait_for_services():
            deployment.cleanup()
            sys.exit(1)

        # 运行测试
        health_results = deployment.run_health_checks()
        api_results = deployment.run_api_tests()
        perf_results = deployment.run_performance_tests()

        # 生成报告
        success = deployment.generate_report(health_results, api_results, perf_results)

        if success:
            logger.info("🎉 Integration tests passed!")
            return 0
        else:
            logger.error("❌ Integration tests failed!")
            return 1

    except KeyboardInterrupt:
        logger.info("⏹️ Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return 1
    finally:
        # 询问是否清理环境
        try:
            cleanup = input("\n🤔 Clean up test environment? (y/N): ").lower().strip()
            if cleanup in ["y", "yes"]:
                deployment.cleanup()
        except KeyboardInterrupt:
            deployment.cleanup()


if __name__ == "__main__":
    sys.exit(main())
