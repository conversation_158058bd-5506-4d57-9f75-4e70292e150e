"""
测试历史数据API
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.historical_data_service import historical_data_service


async def test_historical_api():
    """测试历史数据API"""
    print("开始测试历史数据API...")
    
    # 1. 测试获取统计信息
    print("\n1. 测试获取统计信息:")
    try:
        stats = await historical_data_service.get_stats()
        print(f"- 总股票数: {stats.get('total_stocks', 0)}")
        print(f"- 市场分布: {stats.get('markets', {})}")
        print(f"- 行业数量: {len(stats.get('industries', {}))}")
        print(f"- 数据范围: {stats.get('data_range', {})}")
    except Exception as e:
        print(f"获取统计信息失败: {e}")
    
    # 2. 测试获取股票列表
    print("\n2. 测试获取股票列表:")
    try:
        result = await historical_data_service.get_stock_list(page=1, page_size=10)
        print(f"- 股票总数: {result.get('total', 0)}")
        print(f"- 返回数量: {len(result.get('stocks', []))}")
        if result.get('stocks'):
            stock = result['stocks'][0]
            print(f"- 示例股票: {stock.get('symbol')} - {stock.get('name')}")
    except Exception as e:
        print(f"获取股票列表失败: {e}")
    
    # 3. 测试搜索功能
    print("\n3. 测试搜索功能:")
    try:
        result = await historical_data_service.get_stock_list(keyword="平安", page=1, page_size=10)
        print(f"- 搜索'平安'结果数: {result.get('total', 0)}")
        if result.get('stocks'):
            for stock in result['stocks'][:3]:
                print(f"  - {stock.get('symbol')} - {stock.get('name')}")
    except Exception as e:
        print(f"搜索失败: {e}")
    
    # 4. 测试市场筛选
    print("\n4. 测试市场筛选:")
    try:
        result = await historical_data_service.get_stock_list(market="SH", page=1, page_size=10)
        print(f"- 上交所股票数: {result.get('total', 0)}")
    except Exception as e:
        print(f"市场筛选失败: {e}")
    
    # 5. 测试获取股票数据
    print("\n5. 测试获取股票数据:")
    try:
        # 先获取一只股票
        stocks_result = await historical_data_service.get_stock_list(page=1, page_size=1)
        if stocks_result.get('stocks'):
            symbol = stocks_result['stocks'][0]['symbol']
            print(f"- 测试股票: {symbol}")
            
            # 获取该股票的历史数据
            data_result = await historical_data_service.get_stock_data(symbol, page=1, page_size=5)
            if data_result:
                print(f"- 数据总量: {data_result.get('total', 0)}")
                print(f"- 返回记录数: {len(data_result.get('data', []))}")
                if data_result.get('data'):
                    record = data_result['data'][0]
                    print(f"- 最新日期: {record.get('日期', 'N/A')}")
                    print(f"- 收盘价: {record.get('收盘价', 'N/A')}")
    except Exception as e:
        print(f"获取股票数据失败: {e}")
    
    # 6. 测试获取行业列表
    print("\n6. 测试获取行业列表:")
    try:
        industries = await historical_data_service.get_industries()
        print(f"- 行业总数: {len(industries)}")
        print(f"- 前5个行业: {industries[:5]}")
    except Exception as e:
        print(f"获取行业列表失败: {e}")
    
    # 7. 测试获取热门股票
    print("\n7. 测试获取热门股票:")
    try:
        hot_stocks = await historical_data_service.get_hot_stocks(category="银行股", limit=5)
        print(f"- 银行股数量: {len(hot_stocks)}")
        for stock in hot_stocks:
            print(f"  - {stock.get('symbol')} - {stock.get('name')}")
    except Exception as e:
        print(f"获取热门股票失败: {e}")
    
    print("\n测试完成!")


if __name__ == "__main__":
    asyncio.run(test_historical_api())